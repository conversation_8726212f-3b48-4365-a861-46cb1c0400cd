* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  overflow: visible !important;
  background-color: #fff;
  color: #000;
  font-family: "Messina Sans", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  @media (max-width: 992px) {
    overflow: hidden !important;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
    font-family: "Messina Sans", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  h2 {
    text-align: center;
    font-weight: 700;
    font-size: 3.5rem;
    @media (max-width: 768px) {
      font-size: 24px;
    }
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .text-purple {
    color: #7a57ee;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .btn-wrapper {
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }
  }

  .btn-wrapper .btn {
    margin: 0;
    border-radius: 8px;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 158px;
    @media (min-width: 1280px) {
      &.btn-lg {
        min-width: 224px;
      }
    }
    @media (max-width: 768px) {
      width: 280px;
      height: 48px;
    }
  }

  @media (max-width: 768px) {
    .btn-wrapper .btn {
      display: block;
      vertical-align: baseline;
    }
  }
  @keyframes gradientAnimation {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  .btn-white {
    color: #7a57ee;
    &:hover {
      color: #7a57ee;
    }
  }

  .btn-colorful {
    background: linear-gradient(94.2deg, #7a57ee 41.38%, #39a0fa 73.42%, #24e3c8 96.58%);
    background-size: 200% 200%;
    animation: gradientAnimation 3s infinite linear;
    transition: transform 0.2s ease-in-out;
    color: #fff;
    &:hover {
      transform: scale(1.05);
      color: #fff;
    }
  }

  .btn-purple-bg {
    border: none;
    color: #fff;
    background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg.png) no-repeat center center / contain;
    aspect-ratio: 232 / 64;
    width: 232px;
    transition: transform 0.3s ease-in-out;
    @media (max-width: 768px) {
      background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg-mobile.png) no-repeat center center / contain;
      aspect-ratio: 280 / 48;

      margin: 0 auto;
    }

    @media (any-hover: hover) {
      &:hover {
        transform: translateY(-8px);
        color: #fff;
      }
    }
  }

  .btn-purple-bg2 {
    border: none;
    color: #fff;
    background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg2.png) no-repeat center center / contain;
    aspect-ratio: 280 / 64;
    width: 280px;
    transition: transform 0.3s ease-in-out;
    display: flex;
    gap: 0.5rem;
    box-shadow: 0px 14px 19.8px 0px #7858ff42;
    &:hover {
      color: #fff;
    }
  }

  .swiper-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background: hsla(254, 82%, 64%, 0.3);

    opacity: 1;
  }

  .swiper-pagination .swiper-pagination-bullet-active {
    width: 54px;
    background: linear-gradient(90deg, #ad8fff 0%, #7c65fe 100%);
    border-radius: 8px;
  }
  .part-banner {
    background: linear-gradient(177.83deg, rgba(204, 195, 255, 0.38) 39.74%, rgba(132, 113, 255, 0.38) 89.8%);
    @media (max-width: 992px) {
      background: linear-gradient(185.96deg, rgba(186, 176, 255, 0.38) 14.77%, rgba(146, 129, 255, 0.38) 50.84%, rgba(34, 0, 255, 0.38) 82.21%);
    }

    h1 {
      font-weight: 700;
      font-size: 3.75rem;
      line-height: 100%;
      @media (max-width: 768px) {
        font-size: 32px;
      }
    }
    .system-list {
      display: flex;
      gap: 1rem;
      @media (max-width: 992px) {
        justify-content: center;
      }

      a {
        text-decoration: none;
        &:hover {
          color: #7a57ee;
        }
      }
    }
  }
  .part-honour {
    background-color: #fbf8ff;
    @keyframes ToRight {
      0% {
        transform: translate3d(0, 0, 0);
      }
      100% {
        transform: translate3d(-50%, 0, 0);
        -webkit-transform: translate3d(-50%, 0, 0);
        -moz-transform: translate3d(-50%, 0, 0);
        -ms-transform: translate3d(-50%, 0, 0);
        -o-transform: translate3d(-50%, 0, 0);
      }
    }
    .honour-list {
      display: flex;
      flex-wrap: nowrap;
      animation: ToRight 18s linear infinite;
      width: fit-content;
      .honour-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: auto;
        margin: 0 3rem;
        @media (max-width: 768px) {
          margin: 0 15px;
        }
        .honour-logo {
          height: 64px;
          width: 64px;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        }
        .honour-intro {
          white-space: nowrap;
          text-align: center;
          @media (max-width: 768px) {
            display: none;
          }
        }
      }
    }
  }

  .part-supervise {
    .num-wrapper {
      background: linear-gradient(282.22deg, #cfffff 20.03%, #efe7ff 57.69%);
      border-radius: 1rem;
      overflow: hidden;
      display: flex;
      justify-content: center;
      padding: 1.5rem;
      .num-list {
        display: flex;
        width: 100%;
        max-width: 764px;
        justify-content: space-between;
        .num-box {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .count-box {
            font-size: 3rem;
            font-weight: 700;
            color: #7a57ee;
            margin-bottom: 0.25rem;
            line-height: 1;
            @media (max-width: 576px) {
              font-size: 32px;
            }
          }
          .num-desc {
            font-size: 1.25rem;
            @media (max-width: 576px) {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .part-ability {
    background: rgba(244, 243, 255, 0.64);
    .ability-content {
      display: flex;
      justify-content: center;
      flex-direction: column;
      height: 100%;
      max-width: 565px;
      margin: 0 auto;
      @media (max-width: 992px) {
        max-width: unset;
        align-items: center;
        text-align: center;
      }
      .ability-content-title {
        font-weight: 700;
        font-size: 2.5rem;
        color: #7a57ee;
      }
      .ability-content-list {
        margin-top: 1.875rem;
        display: flex;
        justify-content: center;
        flex-direction: column;
        text-align: left;
        @media (max-width: 992px) {
          margin-top: 1rem;
        }
        .ability-content-item {
          display: flex;
          align-items: flex-start;
          gap: 4px;
          .ability-icon {
            flex-shrink: 0;
            margin-top: 6px;
            @media (max-width: 576px) {
              margin-top: 3px;
            }
          }
          .ability-content-text {
            color: rgba(0, 0, 0, 0.6);
            line-height: 150%;
          }
        }
      }
    }
  }

  .part-solutions {
    @media (max-width: 576px) {
      #swiper-solutions {
        margin-right: -15px;
      }
    }
    .solution-item {
      position: relative;
      .solution-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding: 1.5rem;
        text-align: center;
        z-index: 3;
        .solution-title {
          font-weight: 700;
          font-size: 1.125rem;
          color: #000;
          margin-bottom: 4px;
        }
        .solution-description {
          font-size: 0.875rem;
        }
      }
    }
  }

  .part-step {
    background: rgba(244, 243, 255, 0.64);
    .step-box {
      border-radius: 1rem;
      padding: 1.125rem;
      background-color: #fff;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100%;
      transition: all 0.3s ease-in-out;
      @media (any-hover: hover) {
        &:hover {
          box-shadow: 2px 13px 22.8px 0px rgba(233, 231, 245, 1);
          transform: translateY(-5px);
        }
      }
      .step-box-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.25rem;
        font-weight: 700;
        display: flex;
        gap: 0.5rem;
        margin-top: 1.125rem;
        margin-bottom: 0.75rem;
      }
      .step-box-content {
        color: #444444;
        padding: 0 1rem 1rem;
        text-align: center;
      }
      .step-box-img {
        margin-top: auto;
      }
    }
  }
  .part-parents {
    .testimonial-card {
      border-radius: 1rem;
      overflow: hidden;
      position: relative;
      @media (max-width: 992px) {
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid hsla(0, 0%, 0%, 0.2);
      }
      &-content {
        position: absolute;
        left: 12.05%;
        top: 50%;
        transform: translateY(-50%);
        border-radius: 1.25rem;
        background: rgba(122, 87, 238, 0.85);
        padding: 1.75rem;
        width: 40%;
        max-width: 392px;
        z-index: 3;
        color: #fff;
        @media (max-width: 992px) {
          position: relative;
          left: unset;
          top: unset;
          transform: unset;
          width: 100%;
          max-width: unset;
          padding: 1.5rem;
          border-radius: 0;
          flex: 1;
          background-color: #fff;
          color: #000;
        }
        &::before {
          content: "";
          position: absolute;
          top: 50%;
          right: 0;
          border-radius: 4px;
          width: 16px;
          aspect-ratio: 16 / 38;
          transform: translate(98%, -50%);
          background-color: transparent;
          background: url(https://famisafe.wondershare.com/images/images-2025/screen-time/right-triangle.svg) no-repeat center center / contain;
          @media (max-width: 992px) {
            content: unset;
          }
        }

        .testimonial-card-quote-left {
          position: absolute;
          top: 0;
          right: 11.23%;
          width: 11.23%;
          transform: translateY(-50%);
          @media (max-width: 992px) {
            display: none;
          }
        }
        .testimonial-card-author {
          font-weight: 700;
          font-size: 1.125rem;
          margin-bottom: 0.5rem;
        }
        .testimonial-card-role {
          font-weight: 700;
          color: #09d7be;
          @media (max-width: 992px) {
            font-size: 12px;
            color: #787878;
            font-weight: 400;
          }
        }
        .testimonial-card-text {
          font-size: 1rem;
          color: rgba(255, 255, 255, 0.7);
          @media (max-width: 992px) {
            font-size: 14px;
            color: #444444;
          }
        }
        .testimonial-card-quote-right {
          position: absolute;
          bottom: 0;
          left: 11.23%;
          width: 11.23%;
          transform: translateY(50%);
          @media (max-width: 992px) {
            display: none;
          }
        }
      }
    }
  }

  .part-feature {
    .feature-item {
      position: relative;
      border-radius: 1rem;
      overflow: hidden;
      @media (any-hover: hover) {
        &:hover {
          box-shadow: 0px 7px 14px 0px rgba(212, 207, 247, 1);
          .feature-detail-card {
            opacity: 1;
          }
        }
      }

      &-title {
        font-weight: 700;
        font-size: 1.25rem;
        padding: 1.5rem;
        position: absolute;
        left: 0;
        top: 0;
      }
      @media (min-width: 576px) {
        .feature-detail-card {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          z-index: 5;
          text-decoration: none;
          opacity: 0;
          transition: opacity 0.3s ease-in-out;
          background: linear-gradient(180deg, rgba(122, 87, 238, 0.6) 0%, #7a57ee 64.68%);

          backdrop-filter: blur(20px);

          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 1.5rem;
          padding: 1.5rem 3rem;

          margin: 0 auto;
          color: #fff;
          text-align: center;
        }

        .feature-detail-card-title {
          font-weight: 700;
          font-size: 1.5rem;
        }
        .feature-detail-card-description {
          font-size: 1.125rem;
        }
        .feature-detail-card-arrow {
          flex-shrink: 0;
          color: #fff;
          width: 2.5rem;
          height: 2.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          border: 2px solid rgba(255, 255, 255, 0.6);
          &:hover {
            border-color: #fff;
            background-color: #fff;
            color: #7a57ee;
          }
        }
      }
    }

    @media (max-width: 576px) {
      #feature-swiper {
        margin-left: -15px;
        margin-right: -15px;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          left: 0;
          bottom: 0;
          z-index: 2;
          width: 11.2%;
          height: 100%;
          background: linear-gradient(90deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);
          pointer-events: none;
        }
        &::after {
          content: "";
          position: absolute;
          right: 0;
          bottom: 0;
          z-index: 2;
          width: 11.2%;
          height: 100%;
          background: linear-gradient(270deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);
          pointer-events: none;
        }
      }
      #feature-swiper .swiper-slide {
        overflow: visible;
      }

      #feature-swiper .swiper-slide.swiper-slide-active {
        z-index: 5;
        .feature-item {
          overflow: visible;
          img {
            border: 4.28px solid #ffffff;
            box-shadow: 0px 10.69px 19.36px 0px #2803ec3d;
            border-radius: 1rem;
          }
        }
      }

      #feature-swiper .feature-detail-card {
        display: none;
      }
      .feature-item-mobile {
        height: 100%;
      }
      #feature-text-mobile-swiper .feature-detail-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-decoration: none;
      }

      .mobile-feature-text {
        margin-top: 2.125rem;
        border-radius: 1rem;
        background: linear-gradient(360deg, #7d58f9 15.62%, #a78dff 100.05%);
        box-shadow: 0px 6px 12.9px 0px #e3dffe;
        padding: 1rem;
        text-align: center;
        color: #fff;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%, -100%);
          width: 18px;
          height: 6px;
          background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-top.png) no-repeat center center / contain;
        }
        .feature-detail-card-title {
          font-weight: 700;
          font-size: 18px;
          line-height: 32px;
          margin-bottom: 8px;
          color: #fff;
        }
        .feature-detail-card-description {
          font-size: 16px;
          line-height: 20px;
          color: #fff;
          margin-bottom: 8px;
        }
        .feature-detail-card-arrow {
          width: 32px;
          height: 32px;
          color: #7a57ee;
          background-color: #fff;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 auto;
        }
      }
    }
  }

  .part-tips {
    .tip-wrapper {
      border-radius: 1rem;
      background: linear-gradient(325.88deg, #cfffff -6.18%, #efe7ff 66.9%);
      padding: 4rem 1.5rem;
      .tips-list {
        max-width: 1200px;
        display: flex;
        gap: 1.5rem;
        flex-wrap: wrap;
        margin: 4rem auto 0;
        @media (max-width: 768px) {
          gap: 8px;
          margin-top: 24px;
        }
        .tips-item {
          flex: 0 0 calc(50% - 1.5rem / 2);
          display: flex;
          align-items: center;
          background-color: #fff;
          padding: 0.75rem 1.25rem;
          border-radius: 0.5rem;
          gap: 0.5rem;
          color: rgba(0, 0, 0, 0.72);
          @media (max-width: 768px) {
            flex: 0 0 100%;
          }

          &:hover {
            color: #7a57ee;
          }

          .tips-title {
            font-weight: 700;
            font-size: 1.125rem;
            color: inherit;
          }
          @media (max-width: 768px) {
            .tips-item-circle {
              display: none;
            }
          }
          .tips-item-arrow {
            display: none;
            @media (max-width: 768px) {
              display: block;
              flex-shrink: 0;
              margin-left: auto;
            }
          }
        }
      }
    }
  }

  .part-faq {
    background-color: rgba(244, 243, 255, 0.64);
    .accordion-item {
      padding: 1.5rem 0;
      border-bottom: 1px solid #dddddd;
      @media (max-width: 768px) {
        padding: 16px 0;
      }
      .faq-icon {
        color: #b3afc1;
        flex-shrink: 0;
        transition: transform 0.2s ease-in-out;
        @media (max-width: 768px) {
          svg {
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    .accordion-item [aria-expanded="true"] {
      .faq-icon {
        color: #7a57ee;
        transform: rotate(45deg);
      }
      .faq-title {
        color: #7a57ee;
      }
    }

    .accordion-item .faq-title {
      flex-shrink: 0;
      font-weight: 700;
      font-size: 1.5rem;
      color: #07273d;
      @media (max-width: 768px) {
        flex-shrink: initial;
        margin-right: 1rem;
        font-size: 14px;
      }
    }
    .faq-content {
      color: #07273d;
    }
  }

  .part-footer {
    .footer-box {
      border-radius: 1rem;
      overflow: hidden;
      background-color: #e2dfff;
      background: url(https://famisafe.wondershare.com/images/images-2025/index/footer-bg.jpg) no-repeat center center / cover;
      margin: 0 2.625rem;
      padding: 6rem 3rem;
      text-align: center;
      @media (max-width: 768px) {
        margin: 0 15px;
        padding: 30px 15px;
      }
      .footer-logo {
      }
    }
  }
}
.section-footer {
  display: none !important;
}
