* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #fff;
  color: #000;

  @media (max-width: 1280px) {
    background-color: #f4f7ff;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
  }

  h2 {
    text-align: center;
    font-weight: 800;
    font-size: 2.25rem;
  }

  .display-3 {
    @media (max-width: 576px) {
      font-size: 2.5rem;
    }
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .text-blue {
    color: #046fff;
  }
  .text-blue2 {
    color: #046fff;
  }
  .text-blue3 {
    color: #3b8eff;
  }

  .btn-wrapper {
    display: flex;
    gap: 1rem;
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
      align-items: center;
    }
  }
  .btn {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
    gap: 8px;
    svg {
      max-width: 100%;
      height: 100%;
    }
    @media (max-width: 768px) {
      display: block;
    }
    &.dev-mobile {
      width: 168.75px;
      min-width: unset !important;
    }
  }
  .btn-action {
    border-radius: 0.5rem;
  }
  .btn-download {
    border: 1px solid #ffffff;
    background: linear-gradient(89.57deg, #00c8ff -10.59%, #0084ff 15.01%, #006fff 83.38%, #00c8ff 107.75%);
    box-shadow: 0px 4.5px 6.97px 0px #ffffff6e inset, 0px -6.75px 16.65px 0px #00e5ffd6 inset, 0px 4.5px 13.84px 0px #0059ff40;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-transform: capitalize;
    border-radius: 0.875rem;
    color: #fff;
    min-width: 220px;
    overflow: hidden;
    position: relative;

    &:focus,
    &:active {
      color: #fff;
    }
    @media (min-width: 992px) {
      height: 3.5rem;
      &.btn-lg {
        height: 4rem;
      }
    }

    .btn-text-wrap {
      position: relative;
      overflow: hidden;
      color: inherit;
      .btn-hover-text-wrap {
        transition: transform 0.4s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: inherit;
        &.rel {
          position: relative;
          transform: translateY(0);
        }
        &.abs {
          position: absolute;
          top: 120%;
          transform: translateY(0);
          transition-duration: 0.45s;
        }
      }
    }

    @media (any-hover: hover) {
      &:hover {
        color: #fff;
      }

      &:hover .btn-hover-text-wrap.rel {
        color: inherit;
        transform: translateY(-100%);
      }

      &:hover .btn-hover-text-wrap.abs {
        color: inherit;
        transform: translateY(-120%);
      }
    }

    @keyframes marquee1 {
      0% {
        transform: translateX(0);
      }

      100% {
        transform: translateX(-50%);
      }
    }
    @keyframes marquee2 {
      0% {
        transform: translateX(-50%);
      }

      100% {
        transform: translateX(0%);
      }
    }
    @keyframes marquee1-vertical {
      0% {
        transform: translateY(0);
      }

      100% {
        transform: translateY(-50%);
      }
    }
    @keyframes marquee2-vertical {
      0% {
        transform: translateY(-50%);
      }

      100% {
        transform: translateY(0%);
      }
    }
  }

  .qr-code-icon-wrapper {
    position: relative;
    z-index: 5;
    @media (max-width: 1280px) {
      display: none;
    }
    .active-icon {
      display: none;
    }
    .qrcode-box {
      width: max-content;
      position: absolute;
      top: -8px;
      max-width: 128px;
      left: 50%;
      transform: translate(-50%, -100%);
      transition: opacity 0.2s ease-in-out;
      opacity: 0;
      pointer-events: none;
    }
    &:hover {
      .active-icon {
        display: inline-block;
      }
      .default-icon {
        display: none;
      }
      .qrcode-box {
        opacity: 1;
      }
    }
  }
  .swiper-pagination {
    .swiper-pagination-bullet {
      width: 10px;
      height: 10px;
      border-radius: 100px;
      background-color: rgba($color: #006dff, $alpha: 0.3);
      opacity: 1;
      &.swiper-pagination-bullet-active {
        width: 40px;
        border-radius: 100px;
        opacity: 1;
        background-color: rgba($color: #006dff, $alpha: 0.7);
      }
    }
  }

  .system-list {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 1.5rem;
    color: #b2b2b2;
    @media (max-width: 1280px) {
      justify-content: center;
    }
    .qrcode-box {
      width: max-content;
      position: absolute;
      bottom: -8px;
      max-width: 128px;
      left: 50%;
      transform: translate(-50%, 100%) rotate(180deg);
      transition: opacity 0.2s ease-in-out;
      opacity: 0;
      pointer-events: none;
    }
    a {
      color: #b2b2b2;
      text-decoration: none;
      position: relative;

      @media (any-hover: hover) {
        &:hover {
          color: #000;
          .qrcode-box {
            opacity: 1;
          }
        }
      }
    }
  }

  .part-banner {
    background: linear-gradient(294.17deg, rgba(255, 255, 255, 0) 59.67%, rgba(99, 255, 204, 0.2) 99.14%),
      linear-gradient(50.88deg, #ffffff 56.75%, #bde2ff 88.21%);

    .btn {
      border-radius: 0.75rem;
    }
    .btn-action {
      background-color: #1da4ff;
      border-color: #1da4ff;
      color: #fff;
      &:hover {
        color: #fff;
        background-color: #005dd9;
        border-color: #0057cc;
      }
    }
    .banner-img-wrapper {
      position: relative;
      @keyframes banner-diffuse1 {
        0% {
          transform: translate(-50%, -50%) scale(0.2) rotate(4.5deg);
          opacity: 0.1;
        }

        60% {
          transform: translate(-50%, -50%) scale(0.7) rotate(4.5deg);
          opacity: 0.5;
        }

        100% {
          transform: translate(-50%, -50%) scale(1) rotate(4.5deg);
          opacity: 0;
        }
      }

      @keyframes banner-diffuse2 {
        0% {
          transform: translate(-50%, -50%) scale(0.2) rotate(4.5deg);
          opacity: 0.1;
        }

        60% {
          transform: translate(-50%, -50%) scale(0.9) rotate(4.5deg);
          opacity: 0.5;
        }

        100% {
          transform: translate(-50%, -50%) scale(1) rotate(4.5deg);
          opacity: 0;
        }
      }
      .part-wave-icon-box {
        position: absolute;
        right: 36%;
        top: 29%;
        width: 16%;
        .hover-icon {
          display: none;
        }
        @media (any-hover: hover) {
          &:hover {
            .hover-icon {
              display: block;
            }
            .default-icon {
              display: none;
            }
          }
        }
      }

      .part-wave-icon-box .wave1 {
        width: 115%;
        aspect-ratio: 120 / 80;
        border-radius: 43%;
        border: 3px solid rgb(255, 255, 255);
        z-index: 1;

        backdrop-filter: blur(6px);
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0) rotate(-4.5deg);
        animation: banner-diffuse1 2s linear infinite;
      }
      .part-wave-icon-box .wave2 {
        width: 130%;
        aspect-ratio: 120 / 80;
        border-radius: 43%;
        border: 3px solid rgb(255, 255, 255);
        z-index: 1;

        backdrop-filter: blur(6px);
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0) rotate(-4.5deg);
        animation: banner-diffuse1 2s linear infinite;
      }

      .part-wave-icon-box .wave3 {
        width: 150%;
        aspect-ratio: 120 / 80;
        border-radius: 43%;
        border: 3px solid rgb(255, 255, 255);
        z-index: 1;

        backdrop-filter: blur(6px);
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0);
        animation: banner-diffuse1 2s linear infinite;
      }
    }
  }

  .part-scenarios {
    background-color: #edf7ff;
    .tab-list {
      display: flex;
      border-radius: 0.5rem;
      background-color: #fff;
      @media (max-width: 768px) {
        overflow-x: auto;
        gap: 8px;
        background: unset;
      }
      .tab-item {
        padding: 1rem;
        flex: 1 1 25%;
        font-weight: 700;
        font-size: 1.375rem;
        color: #000;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        @media (max-width: 768px) {
          font-size: 12px;
          padding: 12px;
          white-space: nowrap;
          flex: auto;
          background-color: #fff;
        }
        &.active {
          background-color: #046fff;
          color: #fff;
        }
      }
    }
    .scenarios-item {
      border-radius: 1rem;
      overflow: hidden;
      position: relative;
      color: #fff;
      height: 100%;
      @media (max-width: 1280px) {
        display: flex;
        flex-direction: column;
      }
      .scenarios-item-content {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        width: 50%;
        background: rgba(0, 0, 0, 0.7);
        max-width: 607px;
        padding: 0 6.75rem 0 3.75rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        @media (max-width: 1280px) {
          flex: 1;
          justify-content: flex-start;
          position: relative;
          width: 100%;
          max-width: 100%;
          padding: 1.5rem;
          max-width: unset;
          background-color: #222222;
        }
        .scenarios-item-title {
          display: flex;
          align-items: center;
          gap: 1rem;
          color: #09ffae;
          font-size: 2rem;
          font-weight: 700;
          @media (max-width: 576px) {
            font-size: 22px;
          }
        }
      }
    }
    .left-btn,
    .right-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      cursor: pointer;
      width: 3.5rem;
      height: 3.5rem;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      &:hover {
        background: rgba(255, 255, 255, 0.7);
      }
    }
    .left-btn {
      left: 1.5rem;
    }
    .right-btn {
      right: 1.5rem;
    }
  }

  .part-solutions {
    background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/solutions-bg2.png) no-repeat left top;
    background-size: 33.6%;
    background-color: #222222;
    color: #fff;
    @media (max-width: 992px) {
      background: #edf7ff;
      color: #000;
    }
    .solutions-box {
      display: flex;

      justify-content: center;
      gap: 1.875rem;
      @media (max-width: 1600px) {
        gap: unset;
      }
      .accordion-wrapper {
        flex: 1 1 41.9%;
        max-width: 455px;
        margin-left: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        @media (max-width: 1600px) {
          flex: 0 1 30%;
        }
        .nav {
          display: flex;
          flex-direction: column;
          gap: 8px;
          .divider {
            width: 100%;
            height: 1px;
            background-color: #414141;
          }
          .nav-item {
            border-radius: 8px;
            text-decoration: none;
            color: #fff;
            overflow: hidden;
            transition: none;

            padding-bottom: 1rem;
            .collapse-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 1rem;
              color: #fff;
              padding-bottom: 0;
              transition: none;
              .nav-item-title {
                font-size: 1.25rem;
                font-weight: 700;
                color: inherit;
              }
            }
            .collapse-detail {
              padding: 0 1rem;
              font-weight: 300;
              font-size: 14px;
            }
            &.active {
              background-color: #fff;
              color: #000;

              .nav-item-title {
                color: #000;
              }
              // .collapse-item {
              //   padding-bottom: 4px;
              // }
            }

            &:has([aria-expanded="true"]) svg {
              transform: rotate(180deg);
            }
          }
        }
      }
      .img-wrapper {
        flex: 0 1 58.1%;
        min-height: 648px;
        @media (max-width: 1600px) {
          flex: 0 1 65%;
          display: flex;
          align-items: center;
        }
      }
    }
    @media (min-width: 768px) {
      .step-wrapper {
        display: none;
        .step-list {
          display: flex;
          justify-content: center;
          .step-item {
            flex: auto;
            height: 5.25rem;
            padding: 0 1rem;
            font-weight: 600;
            font-size: 1rem;
            color: #fff;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;

            &.step1 {
              background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step1-bg.png) no-repeat center center;
              background-size: 100% 100%;
            }
            &.step2 {
              background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step2-bg.png) no-repeat center center;
              background-size: 100% 100%;
            }
            &.step3 {
              background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step3-bg.png) no-repeat center center;
              background-size: 100% 100%;
            }
          }
        }
      }
    }
    @media (max-width: 768px) {
      .step-wrapper {
        display: none;
        border-radius: 16px;
        background-color: #222222;
        padding: 2.25rem 1.875rem;
        color: #fff;
        margin-bottom: 24px;
        margin-top: 32px;
        h2 {
          font-size: 24px;
        }
        p {
          font-size: 14px;
          opacity: 0.5;
        }
        .step-list {
          display: flex;
          justify-content: center;
          flex-direction: column;
          gap: 46px;
          .step-item {
            height: 75px;
            background-color: #4e9aff;
            box-shadow: 0px 0px 9.9px 0px #ffffff7d inset;
            border-radius: 16px;
            text-align: center;
            padding: 0 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            &:not(:last-child)::after {
              content: "";
              position: absolute;
              bottom: -6px;
              left: 50%;
              width: 10px;
              transform: translate(-50%, 100%);
              aspect-ratio: 10 / 32;
              background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/arrow-bottom2.svg) no-repeat center center / contain;
            }
            &.step1 {
              background-color: #4e9aff;
            }
            &.step2 {
              background-color: #2a85ff;
            }
            &.step3 {
              background-color: #005cd7;
            }
          }
        }
      }
    }

    .arrow {
      width: 2.5rem;
      cursor: pointer;
      animation: arrow-bottom 2s linear infinite;
      @keyframes arrow-bottom {
        0% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(10px);
        }
        100% {
          transform: translateY(0);
        }
      }

      &.active {
        transform: rotate(180deg);
        animation: unset;
      }
    }

    .solution-box-list-mobile {
      display: flex;
      flex-direction: column;
      gap: 48px;
      .solution-box-item {
        border-radius: 1rem;
        background-color: #222222;
        overflow: hidden;
        .solution-box-item-title {
          font-weight: 700;
          font-size: 28px;
          text-align: center;
          color: #fff;
        }
        .solution-box-item-content {
          padding: 16px 24px;
          font-size: 14px;
          color: rgba($color: #fff, $alpha: 0.5);
        }
      }
    }
    .grey-arrow-wrapper {
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.1);
      text-align: center;
      cursor: pointer;
      &.active {
        img {
          transform: rotate(180deg);
        }
      }
    }
  }

  .part-methods {
    @media (min-width: 1280px) {
      #swiper-methods .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: wrap;
        justify-content: center;
      }

      #swiper-methods .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(25% - 1.875rem);
        max-width: 330px;
      }
    }
    .feature-item {
      width: 100%;
      border-radius: 1rem;
      position: relative;
      background-color: #046fff;
      color: #fff;
      transition: unset;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      text-align: left;
      text-decoration: none;
      display: block;
      margin-top: 32%;
      padding-top: 33%;
      min-height: 400px;
      @media (max-width: 992px) {
        max-width: unset;
      }

      @media (any-hover: hover) {
        &:hover {
          background-color: #222222;
          .method-detail {
            display: none;
          }
          .step-detail {
            display: block;
          }
        }
      }

      .feature-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;

        transform: translateY(-50%);
      }
      .method-detail {
        padding: 0 1rem 2.375rem;

        .divider {
          width: 100%;
          height: 1px;
          background-color: #ffffff4d;
        }
        .right-content-item,
        .fault-content-item {
          display: flex;
          align-items: flex-start;
          gap: 4px;
          font-weight: 300;
          font-size: 14px;
          color: #fff;
        }
      }
      .step-btn {
        margin: 0 auto;
        width: 204px;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.36);
        color: #fff;
        font-size: 18px;
        font-weight: 800;
        text-align: center;
        margin-bottom: 30px;
        &.active {
          svg {
            transform: rotate(180deg);
          }
        }
      }
      .step-detail {
        display: none;
        padding: 0 2rem 0 2.5rem;
        @media (max-width: 576px) {
          margin-bottom: 24px;
        }

        ul {
          list-style: decimal;
          li {
            font-weight: 300;
            font-size: 12px;
            color: #fff;
          }
        }
      }
    }
  }

  .part-table {
    @keyframes marquee1 {
      0% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(-50%);
      }
    }
    .table-wrapper {
      border-radius: 24px;
      width: 100%;
      position: relative;
      z-index: 1;
      overflow: hidden;
      background-color: #0085ff;
      padding: 1px;
      overflow: visible;
      margin-top: 5rem;

      .inner-table,
      .table-blue {
        border-collapse: collapse;
        border-style: hidden;
        width: 100%;
        background: #fff;
        border-radius: 24px;
        overflow: hidden;
      }

      .inner-table .opacity-5,
      .table-blue .opacity-5 {
        opacity: 0.5;
      }

      .inner-table tr:not(:last-child),
      .table-blue tr:not(:last-child) {
        border-bottom: 1px solid rgba(0, 133, 255, 1);
      }

      .inner-table th,
      .table-blue th {
        height: 6rem;
        width: calc(100% / 4);
        font-weight: 700;
        font-size: 1.375rem;
        background-color: rgba(0, 133, 255, 0.1);
        vertical-align: middle;
        text-align: center;
        border-bottom: 1px solid #0085ff;
        &:not(:last-child) {
          border-right: 1px solid #0085ff;
        }
      }

      .inner-table td,
      .table-blue td {
        height: 5.3125rem;
        vertical-align: middle;
        text-align: center;
        font-weight: 600;
        font-size: 1.125rem;

        &:first-child {
          background-color: rgba(0, 133, 255, 0.1);
          font-size: 1.375rem;
          font-weight: 700;
        }
        &:not(:last-child) {
          border-right: 1px solid rgba(0, 133, 255, 1);
        }
      }

      @media (max-width: 1280px) {
        .inner-table th,
        .table-blue th {
          font-size: 1.5rem;
        }
      }

      @media (max-width: 1280px) {
        .inner-table td,
        .table-blue td {
          font-size: 1.25rem;
        }
      }

      .inner-table td div,
      .table-blue td div {
        display: flex;
        gap: 15px;
        align-items: center;
        justify-content: flex-start;
        max-width: 75%;
        margin: 0 auto;
      }

      .blue-table-wrapper {
        position: absolute;
        top: 0;
        left: calc(100% / 4);
        width: calc(100% / 4);
        z-index: 4;
        border-radius: 1.5rem;
        padding: 1.5rem 1rem 1.5rem;
        transform: translateY(-1.5rem);
        background-color: #046fff;
      }

      .blue-table-wrapper .doc-logo {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .blue-table-wrapper .doc-logo img {
        width: 5rem;
      }

      .blue-table-wrapper .table-blue {
        width: 100%;
        border-collapse: collapse;
        border-style: hidden;
        background-color: #046fff;
      }

      .blue-table-wrapper .table-blue th {
        color: #fff;
        border-bottom: 1px solid #ffffff;
      }

      .blue-table-wrapper .table-blue tr:last-child td {
        border-bottom: none;
      }

      .blue-table-wrapper .table-blue td {
        color: #fff;
        border-bottom: 1px solid #ffffff;
        font-size: 1.125rem;
        font-weight: 600;
      }

      .blue-table-wrapper .table-blue td div {
        all: unset;
        display: flex;
        gap: 15px;
        color: #fff;
      }

      .blue-table-wrapper .table-blue td div span {
        text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      }
    }
    .marquee-wrapper {
      display: flex;
      flex-wrap: nowrap;
      width: fit-content;
      .marquee-item {
        font-weight: 600;
        font-size: 1.25rem;
        color: #000;
        white-space: nowrap;
        margin: 0 1.125rem;
      }
    }
    .marquee-wrapper {
      animation: marquee1 40s linear infinite;
    }
  }

  .part-features {
    @media (min-width: 1280px) {
      #swiper-features .swiper-wrapper {
        gap: 1.25rem;
        flex-wrap: wrap;
        justify-content: center;
      }

      #swiper-features .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(50% - 1.25rem);
      }
    }
    .feature-box {
      display: flex;
      justify-content: center;
      border-radius: 1rem;
      background-color: #edf7ff;
      overflow: hidden;
      height: 100%;
      @media (max-width: 1280px) {
        flex-direction: column;
        justify-content: flex-start;
      }
      .img-wrapper {
        flex: 0 0 42%;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .text-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 3rem;
        font-weight: 500;
        font-size: 1.125rem;
        color: #707070;
        @media (max-width: 1600px) {
          padding: 0 1rem;
        }
        @media (max-width: 1280px) {
          padding: 32px 24px;
        }
      }
    }
  }

  .part-helps {
    @media (min-width: 1280px) {
      .swiper-wrapper {
        display: block;
        transform: initial;
      }
    }
    .scrollCard {
      overflow: hidden;
      position: relative;
      height: 100%;
      .scrollCard-detail {
        display: flex;
        background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/card-right-bg.jpg) no-repeat center center / cover;
        border-radius: 1rem;
        overflow: hidden;
        @media (max-width: 1280px) {
          flex-direction: column;
          background: #222222;
          height: 100%;
          justify-content: flex-start;
          align-items: stretch;
        }
        &.scrollCard-detail2 {
          background-image: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/card-left-bg.jpg);
          justify-content: space-between;
          @media (max-width: 1280px) {
            background: #222222;
            justify-content: flex-start;
          }
        }
        .scrollCard-userImg {
          overflow: hidden;
          flex: 0 1 46.78%;
          @media (max-width: 1280px) {
            flex: unset;
          }
          img {
            height: 100%;
            width: 100%;
            object-fit: cover;
          }
        }
        .scrollCard-Intro {
          flex: 1 1 53.22%;
          display: flex;
          flex-direction: column;
          color: #fff;
          justify-content: center;
          margin: 0 5.9375rem;
          max-width: 480px;
          text-align: left;
          align-items: flex-start;
          position: relative;
          padding: 1.5rem 0;
          @media (max-width: 1280px) {
            margin: 0;
            padding: 24px 16px;
            flex: 1;
            max-width: unset;
            justify-content: flex-start;
          }
          @media (max-width: 768px) {
            .font-size-super {
              font-size: 16px;
            }
            p.font-size-large {
              font-size: 12px;
              opacity: 0.5;
            }
          }
          &::after {
            content: "";
            position: absolute;
            left: -15%;
            top: 10%;
            width: 10%;
            background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/tip-left.svg) no-repeat center center / contain;
            aspect-ratio: 51 / 40;
            @media (max-width: 1280px) {
              display: none;
            }
          }
          &::before {
            content: "";
            position: absolute;
            right: -28%;
            top: 10%;
            width: 10%;
            background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/tip-right.svg) no-repeat center center / contain;
            aspect-ratio: 51 / 40;
            @media (max-width: 1280px) {
              display: none;
            }
          }
        }
      }
    }
  }

  .part-faq {
    .accordion-item {
      padding: 1.5rem 0;
      border-bottom: 1px solid #cfd3d5;
      @media (max-width: 576px) {
        padding: 14px;
      }
    }
    .accordion-item [aria-expanded="true"] svg {
      transform: rotate(180deg);
    }

    .accordion-item .faq-title {
      display: flex;
      align-items: center;
      justify-content: left;
      gap: 8px;
      flex-shrink: 0;
      max-width: 90%;
      font-size: 1.5rem;
      font-weight: 700;
      @media (max-width: 576px) {
        font-size: 14px;
      }
    }
    .topic-box {
      height: 100%;
      border-radius: 1rem;
      background-color: #edf7ff;
      padding: 2.125rem;
      color: #0084ff;
      .link-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        .link-item {
          font-weight: 700;
          color: #0084ff;
          font-size: 1rem;
        }
      }
    }
  }

  .part-footer {
    .btn-wrapper .btn-white {
      color: #0080ff;
    }

    .btn-wrapper .btn-outline-white:hover {
      color: #0080ff;
    }

    .footer-box {
      border-radius: 1.25rem;
      background: url(https://mobiletrans.wondershare.com/images/images2025/index/footer-bg.jpg) no-repeat center center/cover;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #fff;
      padding: 4rem 0;
      color: #fff;
    }

    @media (max-width: 768px) {
      .footer-box {
        padding: 2.5rem 1rem;
        margin: 0 15px;
        text-align: center;
      }
    }

    .footer-box .btn {
      min-width: 210px;
      border-radius: 4px;
    }
  }
}
