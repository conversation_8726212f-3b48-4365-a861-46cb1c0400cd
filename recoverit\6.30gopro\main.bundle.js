/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 787:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

eval("\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\nvar injectStylesIntoStyleTag = __webpack_require__(72);\nvar injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleDomAPI.js\nvar styleDomAPI = __webpack_require__(825);\nvar styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertBySelector.js\nvar insertBySelector = __webpack_require__(659);\nvar insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\nvar setAttributesWithoutAttributes = __webpack_require__(56);\nvar setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertStyleElement.js\nvar insertStyleElement = __webpack_require__(540);\nvar insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleTagTransform.js\nvar styleTagTransform = __webpack_require__(113);\nvar styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);\n// EXTERNAL MODULE: ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/index.scss\nvar cjs_js_src = __webpack_require__(2);\n;// CONCATENATED MODULE: ./src/index.scss\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (styleTagTransform_default());\noptions.setAttributes = (setAttributesWithoutAttributes_default());\noptions.insert = insertBySelector_default().bind(null, \"head\");\noptions.domAPI = (styleDomAPI_default());\noptions.insertStyleElement = (insertStyleElement_default());\n\nvar update = injectStylesIntoStyleTag_default()(cjs_js_src/* default */.A, options);\n\n\n\n\n       /* harmony default export */ const src = (cjs_js_src/* default */.A && cjs_js_src/* default */.A.locals ? cjs_js_src/* default */.A.locals : undefined);\n\n;// CONCATENATED MODULE: ./src/index.js\n\n$(function () {\n  // ========== 全局配置 ==========\n  const CONFIG = {\n    mobileBreakpoint: 992,\n    // 移动端断点\n    largeBreakpoint: 1280,\n    // 大屏断点\n    autoplayDelay: 3000,\n    // 自动播放延迟\n    transitionDuration: 300,\n    // 过渡动画时长\n    debounceDelay: 100 // 防抖延迟\n  };\n\n  // ========== AOS初始化 ==========\n\n  /**\n   * 初始化AOS动画库\n   * 确保AOS库加载完成后再初始化，在移动端禁用动画以提升性能\n   */\n  function initAOS() {\n    // 检查AOS是否已加载\n    if (typeof AOS === \"undefined\") {\n      console.warn(\"AOS库尚未加载完成，延迟初始化...\");\n      // 延迟100ms后重试\n      setTimeout(initAOS, 100);\n      return;\n    }\n\n    // 初始化AOS，恢复原始配置参数\n    AOS.init({\n      disable: window.innerWidth < CONFIG.mobileBreakpoint\n    });\n\n    // 移动端强制禁用AOS动画样式（双重保险）\n    disableAOSOnMobile();\n  }\n\n  /**\n   * 在移动端强制禁用AOS动画\n   * 通过CSS样式覆盖的方式确保移动端不显示动画\n   */\n  function disableAOSOnMobile() {\n    if (window.innerWidth <= CONFIG.mobileBreakpoint) {\n      const style = document.createElement(\"style\");\n      style.id = \"aos-mobile-disable\";\n      style.textContent = `\n        [data-aos] {\n          transition-duration: 0s !important;\n          transform: none !important;\n          opacity: 1 !important;\n          animation: none !important;\n        }\n      `;\n      document.head.appendChild(style);\n    }\n  }\n\n  // ========== 视频控制模块 ==========\n\n  /**\n   * 初始化视频播放控制\n   * 点击视频区域切换播放/暂停状态\n   */\n  function initVideoControl() {\n    $(\".part-video .video-wrapper\").on(\"click\", function () {\n      const $video = $(this).find(\"video\");\n      const video = $video[0];\n      const $mask = $(this).find(\".video-mask\");\n      const $playBtn = $(this).find(\".video-play\");\n      if (video.paused) {\n        video.play();\n        $mask.hide();\n        $playBtn.hide();\n      } else {\n        video.pause();\n        $mask.show();\n        $playBtn.show();\n      }\n    });\n  }\n\n  // ========== 轮播图模块 ==========\n\n  /**\n   * 初始化电脑端轮播图\n   * 根据屏幕尺寸使用不同的配置\n   */\n  function initComputerSwiper() {\n    const isLargeScreen = window.innerWidth > CONFIG.largeBreakpoint;\n    const commonConfig = {\n      loop: true,\n      autoplay: {\n        delay: CONFIG.autoplayDelay,\n        disableOnInteraction: false\n      },\n      pagination: {\n        el: \".swiper-pagination\",\n        clickable: true\n      },\n      on: {\n        slideChange: function (swiper) {\n          updateActiveTab(swiper.realIndex);\n        }\n      }\n    };\n    const desktopConfig = {\n      ...commonConfig,\n      effect: \"fade\",\n      fadeEffect: {\n        crossFade: true\n      }\n    };\n    const mobileConfig = {\n      ...commonConfig,\n      slidesPerView: 1,\n      spaceBetween: 30\n    };\n    return new Swiper(\"#swiper-computer\", isLargeScreen ? desktopConfig : mobileConfig);\n  }\n\n  /**\n   * 更新活动标签状态\n   * @param {number} activeIndex - 当前活动的索引\n   */\n  function updateActiveTab(activeIndex) {\n    // 桌面端标签\n    $(\".type-item-tab\").each(function () {\n      const $tab = $(this);\n      $tab.toggleClass(\"active\", $tab.data(\"index\") === activeIndex);\n    });\n\n    // 移动端标签\n    $(\".type-tab-mobile\").each(function (index) {\n      $(this).toggleClass(\"active\", index === activeIndex);\n    });\n  }\n\n  /**\n   * 初始化标签点击事件\n   * @param {Swiper} swiperInstance - Swiper实例\n   */\n  function initTabControls(swiperInstance) {\n    // 桌面端标签点击\n    $(\".type-item-tab\").on(\"click\", function () {\n      const index = $(this).data(\"index\");\n      $(this).addClass(\"active\").siblings().removeClass(\"active\");\n      swiperInstance.slideTo(index + 1, CONFIG.transitionDuration, false);\n    });\n\n    // 移动端标签点击\n    $(\".type-tab-mobile\").on(\"click\", function () {\n      const index = $(this).index();\n      $(this).addClass(\"active\").siblings().removeClass(\"active\");\n      swiperInstance.slideTo(index + 1, CONFIG.transitionDuration, false);\n    });\n  }\n\n  /**\n   * 初始化GoPro轮播图\n   */\n  function initGoProSwiper() {\n    return new Swiper(\"#swiper-gopro\", {\n      slidesPerView: 2,\n      spaceBetween: 10,\n      loop: true,\n      autoplay: {\n        delay: CONFIG.autoplayDelay,\n        disableOnInteraction: false\n      },\n      pagination: {\n        el: \"#swiper-gopro .swiper-pagination\",\n        clickable: true\n      },\n      breakpoints: {\n        1280: {\n          loop: false,\n          slidesPerView: 6,\n          spaceBetween: 30\n        },\n        992: {\n          slidesPerView: 4,\n          spaceBetween: 30\n        }\n      }\n    });\n  }\n\n  /**\n   * 初始化Everyone轮播图\n   * 大屏使用鼠标悬停效果，小屏使用轮播\n   */\n  function initEveryoneSection() {\n    if (window.innerWidth > CONFIG.largeBreakpoint) {\n      // 大屏幕使用鼠标悬停效果\n      $(\"#swiper-everyone .swiper-slide\").on(\"mouseenter\", function () {\n        $(this).addClass(\"active\").siblings().removeClass(\"active\");\n      });\n    } else {\n      // 小屏幕使用轮播\n      return new Swiper(\"#swiper-everyone\", {\n        loop: true,\n        slidesPerView: 1,\n        spaceBetween: 10,\n        autoplay: {\n          delay: CONFIG.autoplayDelay,\n          disableOnInteraction: false\n        },\n        pagination: {\n          el: \".swiper-pagination\",\n          clickable: true\n        },\n        breakpoints: {\n          768: {\n            slidesPerView: 2,\n            spaceBetween: 20\n          },\n          992: {\n            slidesPerView: 3,\n            spaceBetween: 15\n          }\n        }\n      });\n    }\n  }\n\n  /**\n   * 初始化无人机轮播图\n   */\n  function initDroneSwiper() {\n    const swiper = new Swiper(\"#drone-swiper\", {\n      slidesPerView: 1,\n      effect: \"fade\",\n      fadeEffect: {\n        crossFade: true\n      },\n      autoplay: {\n        delay: CONFIG.autoplayDelay,\n        disableOnInteraction: false\n      },\n      loop: true,\n      pagination: {\n        el: \".swiper-pagination\",\n        clickable: true\n      }\n    });\n\n    // 添加鼠标悬浮和触摸控制自动播放的逻辑\n    $(\"#drone-swiper\").on(\"mouseenter touchstart\", function () {\n      // 鼠标进入或触摸开始时停止自动播放\n      swiper.autoplay.stop();\n    }).on(\"mouseleave touchend\", function () {\n      // 鼠标离开或触摸结束时恢复自动播放\n      swiper.autoplay.start();\n    });\n    return swiper;\n  }\n\n  /**\n   * 初始化Best轮播图（仅移动端）\n   */\n  function initBestSwiper() {\n    if (window.innerWidth < CONFIG.largeBreakpoint) {\n      return new Swiper(\"#best-swiper\", {\n        slidesPerView: 1,\n        spaceBetween: 10,\n        loop: true,\n        autoplay: {\n          delay: CONFIG.autoplayDelay,\n          disableOnInteraction: false\n        },\n        breakpoints: {\n          768: {\n            slidesPerView: 3,\n            spaceBetween: 15\n          },\n          576: {\n            slidesPerView: 2,\n            spaceBetween: 15\n          }\n        },\n        pagination: {\n          el: \"#best-swiper .swiper-pagination\",\n          clickable: true\n        }\n      });\n    }\n  }\n\n  // ========== 滚动监听模块 ==========\n\n  /**\n   * 检查元素是否有一半在视口内\n   * @param {Element} el - DOM元素\n   * @returns {boolean} 是否有一半在视口内\n   */\n  function isElementHalfInViewport(el) {\n    const rect = el.getBoundingClientRect();\n    const windowHeight = window.innerHeight || document.documentElement.clientHeight;\n    const elementHeight = rect.height;\n    // 计算元素可见部分的高度\n    const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);\n    return visibleHeight >= elementHeight / 2;\n  }\n\n  /**\n   * 检查元素是否完全在视口内\n   * @param {Element} el - DOM元素\n   * @returns {boolean} 是否完全在视口内\n   */\n  function isElementFullyInViewport(el) {\n    const rect = el.getBoundingClientRect();\n    const windowHeight = window.innerHeight || document.documentElement.clientHeight;\n    const windowWidth = window.innerWidth || document.documentElement.clientWidth;\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= windowHeight && rect.right <= windowWidth;\n  }\n\n  /**\n   * 初始化滚动动画效果\n   * 包括logo图标和类型列表的懒加载动画\n   */\n  function initScrollAnimations() {\n    const $logosIcons = $(\".logos-icon\");\n    const $typeLists = $(\".type-list\");\n\n    // 使用防抖优化滚动事件\n    let scrollTimeout;\n    $(window).on(\"scroll\", function () {\n      clearTimeout(scrollTimeout);\n      scrollTimeout = setTimeout(function () {\n        // Logo图标动画\n        $logosIcons.each(function () {\n          if (isElementHalfInViewport(this)) {\n            $(this).addClass(\"active\");\n          }\n        });\n\n        // 类型列表动画\n        $typeLists.each(function () {\n          if (isElementHalfInViewport(this)) {\n            $(this).addClass(\"active\");\n          }\n        });\n      }, 50); // 减少防抖延迟以提高响应速度\n    });\n  }\n\n  /**\n   * 初始化数字滚动效果\n   * 当计数框完全进入视口时触发\n   */\n  function initCountAnimation() {\n    let hasTriggered = false;\n    let scrollTimeout;\n    function checkCountBox() {\n      const $countBox = $(\".count-box\");\n      if ($countBox.length && isElementFullyInViewport($countBox[0]) && !hasTriggered) {\n        $(\".count-num\").countTo();\n        hasTriggered = true;\n        // 触发后移除监听器以节省性能\n        $(window).off(\"scroll.countAnimation\");\n      }\n    }\n\n    // 使用命名空间避免事件冲突\n    $(window).on(\"scroll.countAnimation\", function () {\n      clearTimeout(scrollTimeout);\n      scrollTimeout = setTimeout(checkCountBox, CONFIG.debounceDelay);\n    });\n  }\n\n  // ========== 初始化所有功能 ==========\n\n  // 初始化视频控制\n  initVideoControl();\n\n  // 初始化各种轮播图\n  const swiperComputer = initComputerSwiper();\n  initTabControls(swiperComputer);\n  initGoProSwiper();\n  initEveryoneSection();\n  initDroneSwiper();\n  initBestSwiper();\n\n  // 初始化滚动相关功能\n  initScrollAnimations();\n  initCountAnimation();\n  initAOS();\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///787\n");

/***/ }),

/***/ 2:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(354);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(314);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `*{margin:0;padding:0;box-sizing:border-box}main{color:#fff;background-color:#000}main h1,main h2,main h3,main h4,main h5,main h6,main p,main div,main span{margin-bottom:0}main h1,main h2,main h3{text-align:center}main h2{font-size:2.25rem;font-weight:800}main .opacity-6{opacity:.6}main .opacity-7{opacity:.7}main .blue-text{color:#006dff}main .btn-wrapper{display:flex;justify-content:center;gap:1rem}@media(max-width: 768px){main .btn-wrapper{flex-direction:column;gap:8px}}main .btn-wrapper .btn{margin:0;border-radius:4px;text-transform:capitalize;display:flex;align-items:center;justify-content:center;min-width:160px}@media(max-width: 768px){main .btn-wrapper .btn{display:block;vertical-align:baseline}}main .btn-outline-secondary{color:#349eff;border-color:#349eff}main .btn-outline-secondary:hover{color:#fff;background-color:#349eff;border-color:#349eff}main .btn-download{background:linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%);border:none;color:#fff;transition:unset;text-transform:capitalize;gap:.5rem;display:inline-flex;align-items:center;justify-content:center}main .btn-download:hover{color:#fff;background:#0085ff}main .btn-white{color:#000}main .btn-white:hover,main .btn-white:focus,main .btn-white:active{background:#006dff;color:#fff;border-color:#006dff}main .part-banner{position:relative}@media(max-width: 768px){main .part-banner .banner-top-img{height:212px;margin-bottom:1.5rem;object-fit:cover;width:auto}}@media(min-width: 992px){main .part-banner .btn{height:48px}}main .part-banner .wsc-icon{height:1.125rem}main .part-banner .content-wrapper{text-align:center;margin-top:-2.5rem}main .part-banner .content-wrapper h1{text-align:center;background:linear-gradient(180deg, rgba(255, 255, 255, 0) 20.83%, #ffffff 84.03%),linear-gradient(90.99deg, #3fa8ff 1.8%, #4bf3ff 99.78%);-webkit-text-fill-color:rgba(0,0,0,0);-webkit-background-clip:text;background-clip:text;font-weight:900;font-size:4rem}@media(max-width: 768px){main .part-banner .content-wrapper h1{font-size:3rem}}main .part-banner .content-wrapper .btn-wrapper .btn-white{min-width:11.5rem}main .part-banner .content-wrapper .btn-wrapper .btn-download{min-width:11.5rem}main .part-banner .content-wrapper .download-list{display:flex;align-items:center;justify-content:center;color:#787878}main .part-banner .content-wrapper .download-list a{color:inherit;text-decoration:none}main .part-banner .content-wrapper .download-list a:hover{color:#fff}main .part-company{text-align:center}@keyframes marquee{0%{transform:translateX(0)}100%{transform:translateX(-50%)}}main .part-company .logo-list{display:flex;flex-wrap:nowrap;align-items:stretch;justify-content:center}@media(max-width: 992px){main .part-company .logo-list{width:fit-content;animation:marquee 30s linear infinite}}main .part-company .logo-list .logo-item{flex:1 1 auto;height:auto}@media(max-width: 992px){main .part-company .logo-list .logo-item{height:32px;flex:0 0 auto}}main .part-company .logo-list .logo-item img{width:auto;max-width:100%;height:100%;object-fit:contain}main .part-video{background:url(https://images.wondershare.com/recoverit/images2025/GoPro/mountain.jpg) no-repeat center bottom/cover;min-height:818px}@media(max-width: 1280px){main .part-video{min-height:unset;padding-bottom:3rem}}main .part-video .video-wrapper{position:relative;overflow:hidden;border-radius:1rem;line-height:0;font-size:0;cursor:pointer}main .part-video .video-wrapper video{width:100%;height:auto;max-width:100%}main .part-video .video-wrapper .video-mask{position:absolute;width:100%;height:100%;background:rgba(0,0,0,.5);top:0;left:0;pointer-events:none}main .part-video .video-wrapper .video-play{position:absolute;left:50%;top:50%;transform:translate(-50%, -50%)}main .part-scenarios .scenarios-box{position:relative;background:url(https://images.wondershare.com/recoverit/images2025/GoPro/scenarios-bg.jpg) no-repeat center center/cover;overflow:hidden;border-radius:1rem;overflow:hidden}main .part-scenarios .scenarios-box .type-list-mobile{display:flex;gap:4px;align-items:center;width:fit-content;overflow-x:scroll;padding:0 16px}main .part-scenarios .scenarios-box .type-list-mobile .type-tab-mobile{padding:10px 16px;border-radius:8px;background-color:#fff;border:1px solid #fff;color:#8babc8;font-weight:800;flex-shrink:0;white-space:nowrap;cursor:pointer}main .part-scenarios .scenarios-box .type-list-mobile .type-tab-mobile.active{border:1px solid #3ca2ff;color:#0085ff}main .part-scenarios .scenarios-box .type-list{display:flex;flex-direction:column;align-items:center;justify-content:space-evenly;height:100%;position:absolute;height:120%;top:-10%;transition:all .5s linear}@media(max-width: 1280px){main .part-scenarios .scenarios-box .type-list{display:none}}main .part-scenarios .scenarios-box .type-list.left{left:40%}main .part-scenarios .scenarios-box .type-list.left.active{left:20%}main .part-scenarios .scenarios-box .type-list.left.active .type-item-tab{top:unset !important;bottom:unset !important}main .part-scenarios .scenarios-box .type-list.right{right:40%}main .part-scenarios .scenarios-box .type-list.right.active{right:20%}main .part-scenarios .scenarios-box .type-list.right.active .type-item-tab{top:unset !important;bottom:unset !important}@media(max-width: 992px){main .part-scenarios .scenarios-box .type-list{flex-direction:row}}main .part-scenarios .scenarios-box .type-list .type-item-tab{border-radius:10000px;aspect-ratio:1/1;background-color:#fff;display:flex;align-items:center;justify-content:center;flex-direction:column;cursor:pointer;font-weight:800;overflow:hidden;color:#8babc8;width:116px;text-align:center;position:relative;padding:10px;transition:bottom .5s linear,top .5s linear}main .part-scenarios .scenarios-box .type-list .type-item-tab[data-index=\"0\"]{right:-75%;top:33%}main .part-scenarios .scenarios-box .type-list .type-item-tab[data-index=\"2\"]{right:-75%;bottom:33%}main .part-scenarios .scenarios-box .type-list .type-item-tab[data-index=\"3\"]{left:-75%;top:33%}main .part-scenarios .scenarios-box .type-list .type-item-tab[data-index=\"5\"]{left:-75%;bottom:33%}main .part-scenarios .scenarios-box .type-list .type-item-tab.active{border:2px solid #3ca2ff;color:#0085ff}main .part-scenarios .scenarios-box .type-list .type-item-tab img{width:40%}main .part-scenarios .scenarios-box .blue-cricle{position:relative;transform:scale(1.8);z-index:2;pointer-events:none}@keyframes wave{0%{transform:scale(0.8);opacity:1}100%{transform:scale(1.5);opacity:0}}main .part-scenarios .scenarios-box .wave{position:absolute;left:0%;top:0%;width:100%;padding-bottom:100%;border-radius:50%;border:3px solid rgba(255,255,255,.4);animation:wave 4s linear infinite}main .part-scenarios .scenarios-box .wave.wave2{animation-delay:1s}main .part-scenarios .scenarios-box .wave.wave3{animation-delay:2s}main .part-scenarios .scenarios-box .wave.wave4{animation-delay:3s}main .part-scenarios .scenarios-box .computer-content{position:absolute;top:50%;left:50%;width:100%;padding:1rem;z-index:5;transform:translate(-50%, -50%);color:#fff;text-align:center}main .part-scenarios .scenarios-box .computer-content .email-icon{width:6rem}@media(max-width: 1280px){main .part-scenarios .scenarios-box .computer-content{all:initial}}main .part-scenarios .scenarios-box .computer-content .type-item{height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;width:81.79%;margin:0 auto}@media(max-width: 1280px){main .part-scenarios .scenarios-box .computer-content .type-item{all:inherit;height:100%;border-radius:1rem;background:linear-gradient(146.6deg, #0070ff 24.12%, #98d8ff 85.64%);overflow:hidden;padding:32px 16px;color:#fff;text-align:center}}@keyframes banner-diffuse1{0%{transform:translate(-50%, -50%) scale(0.2);opacity:.1}60%{transform:translate(-50%, -50%) scale(0.7);opacity:.5}100%{transform:translate(-50%, -50%) scale(1);opacity:0}}@keyframes banner-diffuse2{0%{transform:translate(-50%, -50%) scale(0.2);opacity:.1}60%{transform:translate(-50%, -50%) scale(0.9);opacity:.5}100%{transform:translate(-50%, -50%) scale(1);opacity:0}}main .part-tool .tool-box{border-radius:1rem;background:url(https://images.wondershare.com/recoverit/images2025/GoPro/part-tool-bg.jpg) no-repeat center center/cover;overflow:hidden}@media(max-width: 1280px){main .part-tool .tool-box{padding:0 1rem}}main .part-tool .tool-box ul li{list-style:none;position:relative;padding-left:1rem}main .part-tool .tool-box ul li::before{content:\"•\";position:absolute;left:4px;top:-2px}main .part-tool .img-wrapper{position:relative;overflow:hidden}main .part-tool .img-wrapper .email-icon-wrapper:hover .email-icon{transform:scale(1.2)}main .part-tool .img-wrapper .email-icon{position:absolute;width:18.4%;transition:all .2s linear}main .part-tool .img-wrapper .email-icon-1{right:8%;bottom:60%}main .part-tool .img-wrapper .email-icon-2{right:10%;bottom:49%}main .part-tool .img-wrapper .email-icon-3{right:12%;bottom:38%}main .part-tool .img-wrapper .email-icon-4{right:10%;bottom:27%}main .part-tool .img-wrapper .email-icon-5{right:8%;bottom:17%}main .part-tool .img-wrapper .email-icon-6{right:6%;bottom:7%}main .part-tool .part-banner-wave-icon-box{position:absolute;bottom:8%;left:9%;width:11%}@media(max-width: 1600px){main .part-tool .part-banner-wave-icon-box{width:14%}}@media(max-width: 576px){main .part-tool .part-banner-wave-icon-box{width:17%}}main .part-tool .part-banner-wave-icon-box .download-content{position:absolute;bottom:8%;width:100%}main .part-tool .part-banner-wave-icon-box .download-content .num{color:#006dff;font-weight:700;font-size:14px;text-align:center;line-height:100%}@media(max-width: 576px){main .part-tool .part-banner-wave-icon-box .download-content .num{font-size:10px}}main .part-tool .part-banner-wave-icon-box .download-content .text{text-align:center;color:#416fb4;font-size:12px;line-height:100%}@media(max-width: 576px){main .part-tool .part-banner-wave-icon-box .download-content .text{font-size:10px}}main .part-tool .part-banner-wave-icon-box .wave1{width:130%;aspect-ratio:72/74;border-radius:20%;border:3px solid #fff;z-index:1;opacity:.8;backdrop-filter:blur(6px);position:absolute;left:50%;top:50%;transform:translate(-50%, -50%) scale(0);animation:banner-diffuse1 2s linear infinite}main .part-tool .part-banner-wave-icon-box .wave2{width:160%;aspect-ratio:72/74;border-radius:20%;border:3px solid #fff;z-index:1;opacity:.8;backdrop-filter:blur(6px);position:absolute;left:50%;top:50%;transform:translate(-50%, -50%) scale(0);animation:banner-diffuse1 2s linear infinite}main .part-gopro .gopro-card{border-radius:.75rem;background-color:#14181d;padding:1rem 1.5rem 1rem;overflow:hidden;text-align:center;color:#fff;height:100%}main .part-gopro .gopro-card .gopro-name{margin-top:.5rem}main .part-gopro .swiper-pagination-bullet{background-color:#fff;opacity:.5}main .part-gopro .swiper-pagination-bullet.swiper-pagination-bullet-active{background-color:#007aff;opacity:1}@media(min-width: 1280px){main .part-everyone .swiper-wrapper{display:flex;gap:.5rem;flex-wrap:nowrap;aspect-ratio:1410/640;justify-content:space-between}main .part-everyone .swiper-slide{width:11.92%;transition:all .4s ease-in-out;height:100%;overflow:hidden}main .part-everyone .swiper-slide.active{width:37.59%}main .part-everyone .swiper-slide.active .content-wrapper{padding:2rem}main .part-everyone .swiper-slide.active .content-wrapper .sport-desc{display:block}}main .part-everyone .swiper-pagination-bullet{background-color:#fff;opacity:.5}main .part-everyone .swiper-pagination-bullet.swiper-pagination-bullet-active{background-color:#007aff;opacity:1}main .part-everyone .user-card{width:100%;height:100%;background-size:cover;background-position:center;background-repeat:no-repeat;border-radius:1rem;overflow:hidden}@media(max-width: 1280px){main .part-everyone .user-card{aspect-ratio:530/640;width:100%}}main .part-everyone .user-card::after{position:absolute;content:\"\";left:0;top:0;z-index:1;background:linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 60%) no-repeat center bottom/100% 50%;width:102%;height:102%}main .part-everyone .user-card.card-surf{background-image:url(https://images.wondershare.com/recoverit/images2025/GoPro/surf.jpg)}main .part-everyone .user-card.card-snowboard{background-image:url(https://images.wondershare.com/recoverit/images2025/GoPro/snowboard.jpg)}main .part-everyone .user-card.card-ski{background-image:url(https://images.wondershare.com/recoverit/images2025/GoPro/ski.jpg)}main .part-everyone .user-card.card-bike{background-image:url(https://images.wondershare.com/recoverit/images2025/GoPro/bike.jpg)}main .part-everyone .user-card.card-hiking{background-image:url(https://images.wondershare.com/recoverit/images2025/GoPro/hiking.jpg)}main .part-everyone .user-card.card-diving{background-image:url(https://images.wondershare.com/recoverit/images2025/GoPro/diving.jpg)}main .part-everyone .user-card .content-wrapper{position:absolute;bottom:0;left:0;z-index:2;max-height:400px;overflow:hidden;padding:2rem .75rem}main .part-everyone .user-card .content-wrapper .sport-name{font-weight:800;font-size:1.5rem;color:#fff;margin-bottom:1rem}@media(max-width: 1600px){main .part-everyone .user-card .content-wrapper .sport-name{font-size:1.25rem}}main .part-everyone .user-card .content-wrapper .sport-desc{font-size:.875rem;opacity:.7;display:none}@media(max-width: 1280px){main .part-everyone .user-card .content-wrapper .sport-desc{display:block}}main .part-everyone .sd-wrapper{border-radius:1rem;overflow:hidden;background:linear-gradient(0deg, #23262a, #23262a),linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);padding-left:1.5rem;padding-right:1.5rem}main .part-everyone .sd-wrapper .sd-list{display:flex;justify-content:center;align-items:center;padding-top:1rem;gap:1.875rem;max-width:1120px;margin:0 auto}@media(max-width: 768px){main .part-everyone .sd-wrapper .sd-list{flex-wrap:wrap}}main .part-everyone .sd-wrapper .sd-list .sd-item{flex:1 1 calc(25% - 1.875rem)}@media(max-width: 768px){main .part-everyone .sd-wrapper .sd-list .sd-item{flex:1 1 calc(50% - 1.875rem)}}main .part-logos{background-color:#f2fbff}@media(max-width: 1280px){main .part-logos{background:url(https://images.wondershare.com/recoverit/images2025/email/logo-bg.png) no-repeat center center/cover;background-color:#f2fbff}}main .part-logos .logo-box .logo-content{position:absolute;max-width:570px;left:50%;bottom:0;transform:translateX(-50%);z-index:4;color:#000}@media(max-width: 1280px){main .part-logos .logo-box .logo-content{position:relative}}@media(max-width: 1280px){main .part-logos .logo-box .logos-bg{display:none}}main .part-logos .logo-box .logos-icon{transition:all 1.5s linear}@media(max-width: 1280px){main .part-logos .logo-box .logos-icon{display:none}}main .part-logos .logo-box .top-logos{position:absolute;top:1%;left:33%;transform:scale(0.8);transform-origin:bottom center;pointer-events:none;width:41%}main .part-logos .logo-box .top-logos.active{bottom:0;top:-8%;left:33%;pointer-events:none;transform:unset}main .part-logos .logo-box .left-logos{position:absolute;bottom:0;left:8%;pointer-events:none;transform:scale(0.8);width:19%}main .part-logos .logo-box .left-logos.active{bottom:0;left:6%;pointer-events:none;transform:unset}main .part-logos .logo-box .right-logos{position:absolute;right:5%;bottom:0;pointer-events:none;transform:scale(0.8);width:19%}main .part-logos .logo-box .right-logos.active{position:absolute;right:4%;bottom:0;transform:unset;pointer-events:none}@keyframes ToRight{0%{transform:translate3d(0, 0, 0)}100%{transform:translate3d(-50%, 0, 0);-webkit-transform:translate3d(-50%, 0, 0);-moz-transform:translate3d(-50%, 0, 0);-ms-transform:translate3d(-50%, 0, 0);-o-transform:translate3d(-50%, 0, 0)}}main .part-logos .logo-list-mobile{display:none}@media(max-width: 1280px){main .part-logos .logo-list-mobile{display:flex;flex-wrap:nowrap;width:fit-content;align-items:center;gap:16px;animation:ToRight 60s linear infinite;padding-bottom:2rem}main .part-logos .logo-list-mobile img{width:56px}}main .part-table{background-color:#f2fbff;color:#000}@media(max-width: 1280px){main .part-table .table-box{overflow:auto;overflow-y:hidden}}main .part-table .table-wrapper{--table-theme-color: #cdeeff;--table-theme-color-border: #ace5ff;--table-side-col-bg: #f2fbff;--table-main-bg: #f2fbff;--table-text-primary: #000;--table-text-secondary: #000;--table-border-radius: 8px;--table-decorator-height: 1.5rem;border-radius:var(--table-border-radius);width:100%;position:relative;overflow:visible;background-color:var(--table-theme-color-border);padding:1.2px;margin-top:3.5rem;margin-bottom:6.25rem;min-width:928px}@media(max-width: 576px){main .part-table .table-wrapper{min-width:728px}}main .part-table .inner-table{border-collapse:collapse;border-style:hidden;width:100%;background:var(--table-main-bg);border-radius:var(--table-border-radius);overflow:auto}main .part-table .inner-table th,main .part-table .inner-table td{position:relative;padding:1.5rem 2rem;text-align:center;vertical-align:middle;width:16.6666666667%}@media(max-width: 1600px){main .part-table .inner-table th,main .part-table .inner-table td{padding:1rem 8px}}main .part-table .inner-table th{font-weight:700;font-size:1rem;color:var(--table-text-primary);background-color:var(--table-side-col-bg)}main .part-table .inner-table td{font-size:1rem}@media(max-width: 1280px){main .part-table .inner-table td{font-size:12px}}main .part-table .inner-table td:first-child{background-color:var(--table-side-col-bg);font-size:1rem;font-weight:700;color:var(--table-text-primary);padding:1.5rem}@media(max-width: 1280px){main .part-table .inner-table td:first-child{font-size:12px;padding:1rem 8px}}main .part-table .inner-table .highlight-col{background-color:var(--table-theme-color);color:var(--table-text-secondary)}@media(max-width: 1280px){main .part-table .inner-table .highlight-col{min-width:10.75rem}}main .part-table .highlight-col-top::before{content:\"\";position:absolute;top:0;left:-1px;width:calc(100% + 2px);height:var(--table-decorator-height);background-color:var(--table-theme-color);border-radius:.5rem .5rem 0 0;transform:translateY(-98%)}main .part-table .highlight-col-bottom .download-item{position:absolute;bottom:0;left:-1px;width:calc(100% + 2px);padding:1.5rem 0;background-color:var(--table-theme-color);border-radius:0 0 .5rem .5rem;transform:translateY(98%)}main .part-table .inner-table th:not(:last-child),main .part-table .inner-table td:not(:last-child){border-right:1px solid var(--table-theme-color-border)}main .part-table .inner-table th:not(.highlight-col)::after,main .part-table .inner-table tr:not(:last-child) td:not(.highlight-col)::after{content:\"\";position:absolute;bottom:0;left:0;right:0;height:1.1px;background-color:var(--table-theme-color-border)}main .part-table .inner-table th.highlight-col::after,main .part-table .inner-table tr:not(:last-child) td.highlight-col::after{content:\"\";position:absolute;bottom:0;left:2rem;right:2rem;height:1.1px;background-color:#8ed9ff}@media(max-width: 768px){main .part-table .inner-table th.highlight-col::after,main .part-table .inner-table tr:not(:last-child) td.highlight-col::after{left:8px;right:8px}}main .part-table .inner-table td span{vertical-align:middle;margin-left:8px}@media(max-width: 768px){main .part-table .inner-table th{font-size:10px;padding:8px 4px}main .part-table .inner-table td{font-size:9px;padding:8px 4px}main .part-table .inner-table td:first-child{font-size:10px;padding:8px}main .part-table .inner-table td img{width:14px}main .part-table .mt-logo{width:60%}}main .part-table .blue-tip{background-color:#4dacff;color:#fff;border-radius:4px;padding:.1875rem .25rem;color:#fff;font-size:.75rem;line-height:100%;margin:auto 4px}@media(max-width: 576px){main .part-table .blue-tip{font-size:8px}}main .part-table .blue-tip2{background-color:#a8c4dd;color:#fff;border-radius:4px;padding:.1875rem .25rem;color:#fff;font-size:.75rem;line-height:100%;margin:auto 4px}@media(max-width: 576px){main .part-table .blue-tip2{font-size:8px}}main .part-how{background-color:#f2fbff;margin-bottom:-2px}main .part-how .nav{border-left:2px solid #c1edff;padding-left:3.75rem;height:100%;justify-content:space-between;align-items:stretch;gap:1rem}main .part-how .nav .nav-item{height:fit-content}main .part-how .nav .nav-item h4{font-weight:900;font-size:1.5rem;line-height:100%;color:rgba(0,0,0,.8);position:relative}main .part-how .nav .nav-item h4 .before-icon{position:absolute;left:-0.5rem;top:50%;transform:translate(-100%, -50%);color:inherit}main .part-how .nav .nav-item h4 .before-icon img{width:1.5rem;height:1.5rem;display:inline-block}main .part-how .nav .nav-item h4 .before-icon img.active-icon{display:none}main .part-how .nav .nav-item .description{font-size:1rem;color:rgba(0,0,0,.8)}main .part-how .nav .nav-item.active{position:relative}main .part-how .nav .nav-item.active:before{content:\"\";height:100%;border-left:2px solid #006dff;position:absolute;left:-3.75rem;top:0}main .part-how .nav .nav-item.active h4{color:#006dff}main .part-how .nav .nav-item.active .description{color:#006dff}main .part-how .nav .nav-item.active .before-icon img.active-icon{display:inline-block}main .part-how .nav .nav-item.active .before-icon img.default-icon{display:none}main .part-faq{background-color:#000}main .part-faq .wsc-icon{position:relative;height:1rem;line-height:.5;vertical-align:inherit}main .part-faq .accordion i{transition:.2s}main .part-faq .accordion [aria-expanded=true] i{transform:rotate(180deg);color:#1891ff}main .part-count{color:#fff}main .part-count .count-list{display:flex;justify-content:center;color:#fff;gap:1rem}main .part-count .count-list .count-divider{width:1px;background-color:rgba(0,0,0,.3);margin:1rem 0;height:auto}@media(max-width: 768px){main .part-count .count-list .count-divider{display:none}}@media(max-width: 768px){main .part-count .count-list{flex-wrap:wrap;gap:4px}}main .part-count .count-list .count-box{flex:1 1 calc(25% - 1rem);display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative;color:#fff}@media(max-width: 768px){main .part-count .count-list .count-box{flex:1 1 45%;border-radius:1.125rem;min-height:10rem}}main .part-count .count-list .count-box:not(:last-child)::after{content:\"\";width:1px;height:40%;background-color:rgba(255,255,255,.2);position:absolute;right:0;top:50%;transform:translateY(-50%)}@media(max-width: 768px){main .part-count .count-list .count-box:not(:last-child)::after{content:unset}}main .part-count .count-list .count-box .count{display:flex;align-items:top;justify-content:center;font-weight:900;font-size:4.5rem;line-height:110%;letter-spacing:0%;text-align:center}@media(max-width: 1280px){main .part-count .count-list .count-box .count{font-size:3.5rem}}main .part-count .count-list .count-box .count .count-num{font-weight:900;font-size:4.5rem;line-height:110%;letter-spacing:0%;text-align:center}@media(max-width: 1280px){main .part-count .count-list .count-box .count .count-num{font-size:3.5rem}}main .part-count .count-list .count-box .count .count-plus{font-weight:1000;font-size:3rem;line-height:130%;letter-spacing:0%;text-align:center}@media(max-width: 1280px){main .part-count .count-list .count-box .count .count-plus{font-size:2rem}}main .part-count .count-list .count-box .count-desc{font-weight:400;font-size:1.5rem;line-height:130%;letter-spacing:0%;text-align:center;opacity:.5;color:rgba(255,255,255,.8)}main .part-best{color:#fff}@media(min-width: 1280px){main .part-best #best-swiper .swiper-wrapper{gap:1.875rem}main .part-best #best-swiper .swiper-wrapper .swiper-slide{flex:1 1 calc(25% - 1.875rem)}}main .part-best .best-box{height:100%;display:flex;align-items:flex-start;padding:1.5rem;position:relative;border:1px solid #fff;border-radius:1rem;gap:1.25rem;text-decoration:none}main .part-best .best-box .best-box-link{position:absolute;top:0;left:0;width:100%;height:100%;z-index:2}@media(max-width: 1600px){main .part-best .best-box{gap:.9375rem}}main .part-best .best-box .best-icon{width:3.5rem}@media(max-width: 992px){main .part-best .best-box .best-icon svg{width:2.5rem;height:2.5rem}}main .part-best .best-box .best-icon .active-img{display:none}main .part-best .best-box .best-item-title{font-size:1.125rem;font-weight:700;margin-bottom:1rem}main .part-best .best-box .best-item-desc{font-size:.875rem;color:inherit;text-align:left;opacity:.8}main .part-best .best-box .download-icon{position:absolute;right:1rem;top:1rem;width:1.5rem}main .part-best .best-box .download-icon .active-img{display:none}@media(any-hover: hover){main .part-best .best-box:hover{background-color:#3e90ff;color:#fff}main .part-best .best-box:hover .best-icon .active-img{display:inline-block}main .part-best .best-box:hover .best-icon .default-img{display:none}main .part-best .best-box:hover .download-icon .active-img{display:inline-block}main .part-best .best-box:hover .download-icon .default-img{display:none}}@media(max-width: 992px){main .part-best .best-box{background-color:#3e90ff;color:#fff;border:unset}main .part-best .best-box .best-icon .active-img{display:inline-block;max-width:100%}main .part-best .best-box .best-icon .default-img{display:none;max-width:100%}main .part-best .best-box .download-icon .active-img{display:inline-block;max-width:100%}main .part-best .best-box .download-icon .default-img{display:none;max-width:100%}}main .part-drfone{background:url(https://images.wondershare.com/recoverit/images2025/GoPro/camera-bg.jpg) no-repeat center center/cover;color:#fff}@media(max-width: 768px){main .part-drfone{background:unset}}main .part-drfone .swiper{padding:3rem;padding-right:.5rem;border-radius:1rem;background:rgba(0,0,0,.5);backdrop-filter:blur(16px);overflow:hidden}main .part-drfone .swiper .drfone-content{max-height:690px;overflow:auto;padding-right:2.5rem}@media(max-width: 576px){main .part-drfone .swiper .drfone-content{max-height:520px}}main .part-drfone .swiper .drfone-content::-webkit-scrollbar{width:2px}main .part-drfone .swiper .drfone-content::-webkit-scrollbar-track{background:rgba(0,0,0,.2);border-radius:1.25rem}main .part-drfone .swiper .drfone-content::-webkit-scrollbar-thumb{background:rgba(255,255,255,.3);border-radius:1.25rem}main .part-drfone .swiper .drfone-content::-webkit-scrollbar-thumb:hover{background:rgba(255,255,255,.5)}main .part-drfone .swiper .drfone-content h4{font-size:1.25rem;font-weight:700}main .part-drfone .swiper .drfone-content .method-content{padding-left:1rem;color:#d0d0d0;display:flex;flex-direction:column;gap:.5rem;padding-bottom:1rem}main .part-drfone .swiper .drfone-content .method-content .feature-list{display:flex;justify-content:center;gap:.875rem}main .part-drfone .swiper .drfone-content .method-content .feature-list .advantage-box{border:1px solid #7bde4e;padding:.75rem 1rem;background-color:rgba(123,222,78,.1019607843);border-radius:.5rem;color:#d0d0d0;flex:1 1 50%}main .part-drfone .swiper .drfone-content .method-content .feature-list .disadvantage-box{border:1px solid #f44949;padding:.75rem 1rem;background:rgba(244,73,73,.1019607843);border-radius:.5rem;flex:1 1 50%}@media(max-width: 1600px){main .part-drfone .swiper{padding:1.5rem;padding-bottom:2.5rem}}@media(max-width: 768px){main .part-drfone .swiper{margin-top:-85%;background-color:#292929}}main .part-drfone .swiper .swiper-slide{height:auto}main .part-drfone .swiper .swiper-slide h2{margin-bottom:2rem;text-align:left;font-weight:700}main .part-drfone .swiper .swiper-slide h4{font-weight:400;position:relative}main .part-drfone .swiper .swiper-slide h4::before{content:attr(data-before);position:absolute;left:0;top:50%;transform:translate(-120%, -50%)}main .part-drfone .swiper .swiper-slide p{text-align:left;color:#d0d0d0;font-size:1.125rem}main .part-drfone .swiper .drfone-content-2 p{color:#d0d0d0;position:relative;padding-left:1rem}main .part-drfone .swiper .drfone-content-2 p::before{content:\"•\";position:absolute;left:0;top:15px;transform:translate(0, -50%)}main .part-drfone .swiper .swiper-pagination{bottom:1.2rem;left:0;text-align:left;left:75px}@media(max-width: 768px){main .part-drfone .swiper .swiper-pagination{left:8px;bottom:8px}}main .part-drfone .swiper .swiper-pagination .swiper-pagination-bullet{width:20px;height:12px;border-radius:100px;background:rgba(121,178,255,.3)}main .part-drfone .swiper .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active{background:#006dff;width:48px}main .part-footer .footer-box{background:url(https://images.wondershare.com/recoverit/images2025/email/footer-bg.jpg) no-repeat center center/cover;border-radius:1rem;overflow:hidden;padding:0 2.25rem;color:#fff}@media(max-width: 768px){main .part-footer .footer-box{padding:2rem}}main .part-footer .footer-box .footer-title{font-weight:700;font-size:2rem}main .part-footer .btn-white{color:#349eff}main .part-footer .btn-white:hover,main .part-footer .btn-white:focus{background-color:#ececec;border-color:#e6e6e6}main .part-footer .btn-outline-white:hover,main .part-footer .btn-outline-white:focus{color:#349eff}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/index.scss\"],\"names\":[],\"mappings\":\"AAAA,EAAA,QACE,CAAA,SACA,CAAA,qBACA,CAAA,KAGF,UACE,CAAA,qBACA,CAAA,0EAEA,eASE,CAAA,wBAGF,iBAGE,CAAA,QAEF,iBACE,CAAA,eACA,CAAA,gBAGF,UACE,CAAA,gBAGF,UACE,CAAA,gBAGF,aACE,CAAA,kBAGF,YACE,CAAA,sBAEA,CAAA,QACA,CAAA,yBACA,kBALF,qBAMI,CAAA,OACA,CAAA,CAAA,uBAEF,QACE,CAAA,iBACA,CAAA,yBACA,CAAA,YACA,CAAA,kBACA,CAAA,sBACA,CAAA,eACA,CAAA,yBACA,uBARF,aASI,CAAA,uBACA,CAAA,CAAA,4BAKN,aACE,CAAA,oBACA,CAAA,kCAGF,UACE,CAAA,wBACA,CAAA,oBACA,CAAA,mBAGF,mEACE,CAAA,WACA,CAAA,UACA,CAAA,gBACA,CAAA,yBACA,CAAA,SACA,CAAA,mBACA,CAAA,kBACA,CAAA,sBACA,CAAA,yBAEA,UACE,CAAA,kBACA,CAAA,gBAIJ,UACE,CAAA,mEACA,kBAGE,CAAA,UACA,CAAA,oBACA,CAAA,kBAIJ,iBACE,CAAA,yBAEE,kCADF,YAEI,CAAA,oBACA,CAAA,gBACA,CAAA,UACA,CAAA,CAAA,yBAKN,uBACE,WACE,CAAA,CAAA,4BAIJ,eACE,CAAA,mCAEF,iBACE,CAAA,kBACA,CAAA,sCAGF,iBACE,CAAA,yIACA,CAAA,qCACA,CAAA,4BACA,CAAA,oBACA,CAAA,eACA,CAAA,cACA,CAAA,yBACA,sCARF,cASI,CAAA,CAAA,2DAIJ,iBACE,CAAA,8DAGF,iBACE,CAAA,kDAGF,YACE,CAAA,kBACA,CAAA,sBACA,CAAA,aACA,CAAA,oDAGF,aACE,CAAA,oBACA,CAAA,0DAGF,UACE,CAAA,mBAGF,iBACE,CAAA,mBAGF,GACE,uBACE,CAAA,KAGF,0BACE,CAAA,CAAA,8BAIJ,YACE,CAAA,gBACA,CAAA,mBACA,CAAA,sBACA,CAAA,yBAGF,8BACE,iBACE,CAAA,qCACA,CAAA,CAAA,yCAIJ,aACE,CAAA,WACA,CAAA,yBAGF,yCACE,WACE,CAAA,aACA,CAAA,CAAA,6CAIJ,UACE,CAAA,cACA,CAAA,WACA,CAAA,kBACA,CAAA,iBAGF,oHACE,CAAA,gBACA,CAAA,0BACA,iBAHF,gBAII,CAAA,mBACA,CAAA,CAAA,gCAEF,iBACE,CAAA,eACA,CAAA,kBACA,CAAA,aACA,CAAA,WACA,CAAA,cACA,CAAA,sCAGF,UACE,CAAA,WACA,CAAA,cACA,CAAA,4CAGF,iBACE,CAAA,UACA,CAAA,WACA,CAAA,yBACA,CAAA,KACA,CAAA,MACA,CAAA,mBACA,CAAA,4CAGF,iBACE,CAAA,QACA,CAAA,OACA,CAAA,+BACA,CAAA,oCAKF,iBACE,CAAA,wHACA,CAAA,eACA,CAAA,kBACA,CAAA,eACA,CAAA,sDAGF,YACE,CAAA,OACA,CAAA,kBACA,CAAA,iBACA,CAAA,iBACA,CAAA,cACA,CAAA,uEAGF,iBACE,CAAA,iBACA,CAAA,qBACA,CAAA,qBACA,CAAA,aACA,CAAA,eACA,CAAA,aACA,CAAA,kBACA,CAAA,cACA,CAAA,8EAGF,wBACE,CAAA,aACA,CAAA,+CAGF,YACE,CAAA,qBACA,CAAA,kBACA,CAAA,4BACA,CAAA,WACA,CAAA,iBACA,CAAA,WACA,CAAA,QACA,CAAA,yBACA,CAAA,0BAGF,+CACE,YACE,CAAA,CAAA,oDAIJ,QACE,CAAA,2DAGF,QACE,CAAA,0EAGF,oBACE,CAAA,uBACA,CAAA,qDAGF,SACE,CAAA,4DAGF,SACE,CAAA,2EAGF,oBACE,CAAA,uBACA,CAAA,yBAGF,+CACE,kBACE,CAAA,CAAA,8DAIJ,qBACE,CAAA,gBACA,CAAA,qBACA,CAAA,YACA,CAAA,kBACA,CAAA,sBACA,CAAA,qBACA,CAAA,cACA,CAAA,eACA,CAAA,eACA,CAAA,aACA,CAAA,WACA,CAAA,iBACA,CAAA,iBACA,CAAA,YACA,CAAA,2CACA,CAAA,8EAGF,UACE,CAAA,OACA,CAAA,8EAGF,UACE,CAAA,UACA,CAAA,8EAGF,SACE,CAAA,OACA,CAAA,8EAGF,SACE,CAAA,UACA,CAAA,qEAGF,wBACE,CAAA,aACA,CAAA,kEAGF,SACE,CAAA,iDAGF,iBACE,CAAA,oBACA,CAAA,SACA,CAAA,mBACA,CAAA,gBAGF,GACE,oBACE,CAAA,SACA,CAAA,KAGF,oBACE,CAAA,SACA,CAAA,CAAA,0CAIJ,iBACE,CAAA,OACA,CAAA,MACA,CAAA,UACA,CAAA,mBACA,CAAA,iBACA,CAAA,qCACA,CAAA,iCACA,CAAA,gDAGF,kBACE,CAAA,gDAGF,kBACE,CAAA,gDAGF,kBACE,CAAA,sDAGF,iBACE,CAAA,OACA,CAAA,QACA,CAAA,UACA,CAAA,YACA,CAAA,SACA,CAAA,+BACA,CAAA,UACA,CAAA,iBACA,CAAA,kEAGF,UACE,CAAA,0BAGF,sDACE,WACE,CAAA,CAAA,iEAIJ,WACE,CAAA,YACA,CAAA,qBACA,CAAA,kBACA,CAAA,sBACA,CAAA,YACA,CAAA,aACA,CAAA,0BAGF,iEACE,WACE,CAAA,WACA,CAAA,kBACA,CAAA,oEACA,CAAA,eACA,CAAA,iBACA,CAAA,UACA,CAAA,iBACA,CAAA,CAAA,2BAMJ,GACE,0CACE,CAAA,UACA,CAAA,IAGF,0CACE,CAAA,UACA,CAAA,KAGF,wCACE,CAAA,SACA,CAAA,CAAA,2BAIJ,GACE,0CACE,CAAA,UACA,CAAA,IAGF,0CACE,CAAA,UACA,CAAA,KAGF,wCACE,CAAA,SACA,CAAA,CAAA,0BAIJ,kBACE,CAAA,wHACA,CAAA,eACA,CAAA,0BACA,0BAJF,cAKI,CAAA,CAAA,gCAIJ,eACE,CAAA,iBACA,CAAA,iBACA,CAAA,wCAGF,WACE,CAAA,iBACA,CAAA,QACA,CAAA,QACA,CAAA,6BAGF,iBACE,CAAA,eACA,CAAA,mEAGF,oBACE,CAAA,yCAGF,iBACE,CAAA,WACA,CAAA,yBACA,CAAA,2CAGF,QACE,CAAA,UACA,CAAA,2CAGF,SACE,CAAA,UACA,CAAA,2CAGF,SACE,CAAA,UACA,CAAA,2CAGF,SACE,CAAA,UACA,CAAA,2CAGF,QACE,CAAA,UACA,CAAA,2CAGF,QACE,CAAA,SACA,CAAA,2CAGF,iBACE,CAAA,SACA,CAAA,OACA,CAAA,SACA,CAAA,0BAGF,2CACE,SACE,CAAA,CAAA,yBAIJ,2CACE,SACE,CAAA,CAAA,6DAIJ,iBACE,CAAA,SACA,CAAA,UACA,CAAA,kEAGF,aACE,CAAA,eACA,CAAA,cACA,CAAA,iBACA,CAAA,gBACA,CAAA,yBAGF,kEACE,cACE,CAAA,CAAA,mEAIJ,iBACE,CAAA,aACA,CAAA,cACA,CAAA,gBACA,CAAA,yBAGF,mEACE,cACE,CAAA,CAAA,kDAIJ,UACE,CAAA,kBACA,CAAA,iBACA,CAAA,qBACA,CAAA,SACA,CAAA,UACA,CAAA,yBACA,CAAA,iBACA,CAAA,QACA,CAAA,OACA,CAAA,wCACA,CAAA,4CACA,CAAA,kDAGF,UACE,CAAA,kBACA,CAAA,iBACA,CAAA,qBACA,CAAA,SACA,CAAA,UACA,CAAA,yBACA,CAAA,iBACA,CAAA,QACA,CAAA,OACA,CAAA,wCACA,CAAA,4CACA,CAAA,6BAKF,oBACE,CAAA,wBACA,CAAA,wBACA,CAAA,eACA,CAAA,iBACA,CAAA,UACA,CAAA,WACA,CAAA,yCACA,gBACE,CAAA,2CAGJ,qBACE,CAAA,UACA,CAAA,2EACA,wBACE,CAAA,SACA,CAAA,0BAMJ,oCACE,YACE,CAAA,SACA,CAAA,gBACA,CAAA,qBACA,CAAA,6BACA,CAAA,kCAEF,YACE,CAAA,8BACA,CAAA,WACA,CAAA,eACA,CAAA,yCACA,YACE,CAAA,0DACA,YACE,CAAA,sEACA,aACE,CAAA,CAAA,8CAMV,qBACE,CAAA,UACA,CAAA,8EACA,wBACE,CAAA,SACA,CAAA,+BAGJ,UACE,CAAA,WACA,CAAA,qBACA,CAAA,0BACA,CAAA,2BACA,CAAA,kBACA,CAAA,eACA,CAAA,0BACA,+BARF,oBASI,CAAA,UACA,CAAA,CAAA,sCAEF,iBACE,CAAA,UACA,CAAA,MACA,CAAA,KACA,CAAA,SACA,CAAA,gHACA,CAAA,UACA,CAAA,WACA,CAAA,yCAGF,wFACE,CAAA,8CAEF,6FACE,CAAA,wCAEF,uFACE,CAAA,yCAEF,wFACE,CAAA,2CAEF,0FACE,CAAA,2CAEF,0FACE,CAAA,gDAGF,iBACE,CAAA,QACA,CAAA,MACA,CAAA,SACA,CAAA,gBACA,CAAA,eACA,CAAA,mBACA,CAAA,4DAEA,eACE,CAAA,gBACA,CAAA,UACA,CAAA,kBACA,CAAA,0BACA,4DALF,iBAMI,CAAA,CAAA,4DAGJ,iBACE,CAAA,UACA,CAAA,YACA,CAAA,0BACA,4DAJF,aAKI,CAAA,CAAA,gCAKR,kBACE,CAAA,eACA,CAAA,wHACA,CAAA,mBACA,CAAA,oBACA,CAAA,yCACA,YACE,CAAA,sBACA,CAAA,kBACA,CAAA,gBACA,CAAA,YACA,CAAA,gBACA,CAAA,aACA,CAAA,yBACA,yCARF,cASI,CAAA,CAAA,kDAEF,6BACE,CAAA,yBACA,kDAFF,6BAGI,CAAA,CAAA,iBAOV,wBACE,CAAA,0BAEA,iBAHF,mHAII,CAAA,wBACA,CAAA,CAAA,yCAGF,iBACE,CAAA,eACA,CAAA,QACA,CAAA,QACA,CAAA,0BACA,CAAA,SACA,CAAA,UACA,CAAA,0BAGF,yCACE,iBACE,CAAA,CAAA,0BAIJ,qCACE,YACE,CAAA,CAAA,uCAIJ,0BACE,CAAA,0BAGF,uCACE,YACE,CAAA,CAAA,sCAIJ,iBACE,CAAA,MACA,CAAA,QACA,CAAA,oBACA,CAAA,8BACA,CAAA,mBACA,CAAA,SACA,CAAA,6CAGF,QACE,CAAA,OACA,CAAA,QACA,CAAA,mBACA,CAAA,eACA,CAAA,uCAGF,iBACE,CAAA,QACA,CAAA,OACA,CAAA,mBACA,CAAA,oBACA,CAAA,SACA,CAAA,8CAGF,QACE,CAAA,OACA,CAAA,mBACA,CAAA,eACA,CAAA,wCAGF,iBACE,CAAA,QACA,CAAA,QACA,CAAA,mBACA,CAAA,oBACA,CAAA,SACA,CAAA,+CAGF,iBACE,CAAA,QACA,CAAA,QACA,CAAA,eACA,CAAA,mBACA,CAAA,mBAGF,GACE,8BACE,CAAA,KAGF,iCACE,CAAA,yCACA,CAAA,sCACA,CAAA,qCACA,CAAA,oCACA,CAAA,CAAA,mCAIJ,YACE,CAAA,0BAGF,mCACE,YACE,CAAA,gBACA,CAAA,iBACA,CAAA,kBACA,CAAA,QACA,CAAA,qCACA,CAAA,mBACA,CAAA,uCAGF,UACE,CAAA,CAAA,iBAKN,wBACE,CAAA,UACA,CAAA,0BAEE,4BADF,aAEI,CAAA,iBACA,CAAA,CAAA,gCAGJ,4BAEE,CAAA,mCACA,CAAA,4BACA,CAAA,wBACA,CAAA,0BACA,CAAA,4BACA,CAAA,0BACA,CAAA,gCACA,CAAA,wCAGA,CAAA,UACA,CAAA,iBACA,CAAA,gBACA,CAAA,gDACA,CAAA,aACA,CAAA,iBACA,CAAA,qBACA,CAAA,eACA,CAAA,yBACA,gCArBF,eAsBI,CAAA,CAAA,8BAIJ,wBACE,CAAA,mBACA,CAAA,UACA,CAAA,+BACA,CAAA,wCACA,CAAA,aACA,CAAA,kEAIF,iBAEE,CAAA,mBACA,CAAA,iBACA,CAAA,qBACA,CAAA,oBACA,CAAA,0BAEA,kEARF,gBASI,CAAA,CAAA,iCAIJ,eACE,CAAA,cACA,CAAA,+BACA,CAAA,yCACA,CAAA,iCAGF,cACE,CAAA,0BACA,iCAFF,cAGI,CAAA,CAAA,6CAKJ,yCACE,CAAA,cACA,CAAA,eACA,CAAA,+BACA,CAAA,cACA,CAAA,0BACA,6CANF,cAOI,CAAA,gBACA,CAAA,CAAA,6CAMJ,yCACE,CAAA,iCACA,CAAA,0BACA,6CAHF,kBAII,CAAA,CAAA,4CAKJ,UACE,CAAA,iBACA,CAAA,KACA,CAAA,SACA,CAAA,sBACA,CAAA,oCACA,CAAA,yCACA,CAAA,6BACA,CAAA,0BACA,CAAA,sDAKA,iBACE,CAAA,QACA,CAAA,SACA,CAAA,sBACA,CAAA,gBACA,CAAA,yCACA,CAAA,6BACA,CAAA,yBACA,CAAA,oGAMJ,sDAEE,CAAA,4IAIF,UAEE,CAAA,iBACA,CAAA,QACA,CAAA,MACA,CAAA,OACA,CAAA,YACA,CAAA,gDACA,CAAA,gIAIF,UAEE,CAAA,iBACA,CAAA,QACA,CAAA,SACA,CAAA,UACA,CAAA,YACA,CAAA,wBACA,CAAA,yBACA,gIATF,QAUI,CAAA,SACA,CAAA,CAAA,sCAKJ,qBACE,CAAA,eACA,CAAA,yBAIF,iCACE,cACE,CAAA,eACA,CAAA,iCAEF,aACE,CAAA,eACA,CAAA,6CAEF,cACE,CAAA,WACA,CAAA,qCAEF,UACE,CAAA,0BAEF,SACE,CAAA,CAAA,2BAIJ,wBACE,CAAA,UACA,CAAA,iBACA,CAAA,uBACA,CAAA,UACA,CAAA,gBACA,CAAA,gBACA,CAAA,eACA,CAAA,yBACA,2BATF,aAUI,CAAA,CAAA,4BAIJ,wBACE,CAAA,UACA,CAAA,iBACA,CAAA,uBACA,CAAA,UACA,CAAA,gBACA,CAAA,gBACA,CAAA,eACA,CAAA,yBACA,4BATF,aAUI,CAAA,CAAA,eAKN,wBACE,CAAA,kBACA,CAAA,oBAGF,6BACE,CAAA,oBACA,CAAA,WACA,CAAA,6BACA,CAAA,mBACA,CAAA,QACA,CAAA,8BAGF,kBACE,CAAA,iCAGF,eACE,CAAA,gBACA,CAAA,gBACA,CAAA,oBACA,CAAA,iBACA,CAAA,8CAGF,iBACE,CAAA,YACA,CAAA,OACA,CAAA,gCACA,CAAA,aACA,CAAA,kDAGF,YACE,CAAA,aACA,CAAA,oBACA,CAAA,8DAGF,YACE,CAAA,2CAGF,cACE,CAAA,oBACA,CAAA,qCAGF,iBACE,CAAA,4CAGF,UACE,CAAA,WACA,CAAA,6BACA,CAAA,iBACA,CAAA,aACA,CAAA,KACA,CAAA,wCAGF,aACE,CAAA,kDAGF,aACE,CAAA,kEAGF,oBACE,CAAA,mEAGF,YACE,CAAA,eAGF,qBACE,CAAA,yBAGF,iBACE,CAAA,WACA,CAAA,cACA,CAAA,sBACA,CAAA,4BAGF,cACE,CAAA,iDAGF,wBACE,CAAA,aACA,CAAA,iBAGF,UACE,CAAA,6BAGF,YACE,CAAA,sBAEA,CAAA,UACA,CAAA,QAEA,CAAA,4CACA,SACE,CAAA,+BACA,CAAA,aACA,CAAA,WACA,CAAA,yBACA,4CALF,YAMI,CAAA,CAAA,yBAKN,6BACE,cACE,CAAA,OACA,CAAA,CAAA,wCAIJ,yBACE,CAAA,YACA,CAAA,qBACA,CAAA,kBACA,CAAA,sBACA,CAAA,iBACA,CAAA,UACA,CAAA,yBAGF,wCACE,YACE,CAAA,sBACA,CAAA,gBACA,CAAA,CAAA,gEAIJ,UACE,CAAA,SACA,CAAA,UACA,CAAA,qCACA,CAAA,iBACA,CAAA,OACA,CAAA,OACA,CAAA,0BACA,CAAA,yBAEF,gEACE,aACE,CAAA,CAAA,+CAIJ,YACE,CAAA,eACA,CAAA,sBACA,CAAA,eACA,CAAA,gBACA,CAAA,gBACA,CAAA,iBACA,CAAA,iBACA,CAAA,0BAGF,+CACE,gBACE,CAAA,CAAA,0DAIJ,eACE,CAAA,gBACA,CAAA,gBACA,CAAA,iBACA,CAAA,iBACA,CAAA,0BAGF,0DACE,gBACE,CAAA,CAAA,2DAIJ,gBACE,CAAA,cACA,CAAA,gBACA,CAAA,iBACA,CAAA,iBACA,CAAA,0BAGF,2DACE,cACE,CAAA,CAAA,oDAIJ,eACE,CAAA,gBACA,CAAA,gBACA,CAAA,iBACA,CAAA,iBACA,CAAA,UACA,CAAA,0BACA,CAAA,gBAGF,UACE,CAAA,0BACA,6CACE,YACE,CAAA,2DAEF,6BACE,CAAA,CAAA,0BAKN,WACE,CAAA,YACA,CAAA,sBACA,CAAA,cACA,CAAA,iBACA,CAAA,qBACA,CAAA,kBACA,CAAA,WACA,CAAA,oBACA,CAAA,yCAEA,iBACE,CAAA,KACA,CAAA,MACA,CAAA,UACA,CAAA,WACA,CAAA,SACA,CAAA,0BAIJ,0BACE,YACE,CAAA,CAAA,qCAIJ,YACE,CAAA,yBACA,yCACE,YACE,CAAA,aACA,CAAA,CAAA,iDAKN,YACE,CAAA,2CAGF,kBACE,CAAA,eACA,CAAA,kBACA,CAAA,0CAGF,iBACE,CAAA,aACA,CAAA,eACA,CAAA,UACA,CAAA,yCAGF,iBACE,CAAA,UACA,CAAA,QACA,CAAA,YACA,CAAA,qDAGF,YACE,CAAA,yBAGF,gCACE,wBACE,CAAA,UACA,CAAA,uDAGF,oBACE,CAAA,wDAGF,YACE,CAAA,2DAGF,oBACE,CAAA,4DAGF,YACE,CAAA,CAAA,yBAIJ,0BACE,wBACE,CAAA,UACA,CAAA,YACA,CAAA,iDAGF,oBACE,CAAA,cACA,CAAA,kDAGF,YACE,CAAA,cACA,CAAA,qDAGF,oBACE,CAAA,cACA,CAAA,sDAGF,YACE,CAAA,cACA,CAAA,CAAA,kBAIJ,qHACE,CAAA,UACA,CAAA,yBAGF,kBACE,gBACE,CAAA,CAAA,0BAIJ,YACE,CAAA,mBACA,CAAA,kBACA,CAAA,yBACA,CAAA,0BACA,CAAA,eACA,CAAA,0CACA,gBACE,CAAA,aACA,CAAA,oBACA,CAAA,yBACA,0CAJF,gBAKI,CAAA,CAAA,6DAIF,SACE,CAAA,mEAGF,yBACE,CAAA,qBACA,CAAA,mEAGF,+BACE,CAAA,qBACA,CAAA,yEAEA,+BACE,CAAA,6CAIJ,iBACE,CAAA,eACA,CAAA,0DAEF,iBACE,CAAA,aACA,CAAA,YACA,CAAA,qBACA,CAAA,SACA,CAAA,mBACA,CAAA,wEACA,YACE,CAAA,sBACA,CAAA,WACA,CAAA,uFACA,wBACE,CAAA,mBACA,CAAA,6CACA,CAAA,mBACA,CAAA,aACA,CAAA,YACA,CAAA,0FAEF,wBACE,CAAA,mBACA,CAAA,sCACA,CAAA,mBACA,CAAA,YACA,CAAA,0BAOV,0BACE,cACE,CAAA,qBACA,CAAA,CAAA,yBAIJ,0BACE,eACE,CAAA,wBACA,CAAA,CAAA,wCAIJ,WACE,CAAA,2CAGF,kBACE,CAAA,eACA,CAAA,eACA,CAAA,2CAGF,eACE,CAAA,iBACA,CAAA,mDAGF,yBACE,CAAA,iBACA,CAAA,MACA,CAAA,OACA,CAAA,gCACA,CAAA,0CAGF,eACE,CAAA,aACA,CAAA,kBACA,CAAA,8CAGF,aACE,CAAA,iBACA,CAAA,iBACA,CAAA,sDAGF,WACE,CAAA,iBACA,CAAA,MACA,CAAA,QACA,CAAA,4BACA,CAAA,6CAGF,aACE,CAAA,MACA,CAAA,eACA,CAAA,SACA,CAAA,yBACA,6CALF,QAMI,CAAA,UACA,CAAA,CAAA,uEAIJ,UACE,CAAA,WACA,CAAA,mBACA,CAAA,+BACA,CAAA,uGAGF,kBACE,CAAA,UACA,CAAA,8BAGF,qHACE,CAAA,kBACA,CAAA,eACA,CAAA,iBACA,CAAA,UACA,CAAA,yBACA,8BANF,YAOI,CAAA,CAAA,4CAIJ,eACE,CAAA,cACA,CAAA,6BAGF,aACE,CAAA,sEACA,wBAEE,CAAA,oBACA,CAAA,sFAIJ,aAEE\",\"sourcesContent\":[\"* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nmain {\\n  color: #fff;\\n  background-color: #000;\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6,\\n  p,\\n  div,\\n  span {\\n    margin-bottom: 0;\\n  }\\n\\n  h1,\\n  h2,\\n  h3 {\\n    text-align: center;\\n  }\\n  h2 {\\n    font-size: 2.25rem;\\n    font-weight: 800;\\n  }\\n\\n  .opacity-6 {\\n    opacity: 0.6;\\n  }\\n\\n  .opacity-7 {\\n    opacity: 0.7;\\n  }\\n\\n  .blue-text {\\n    color: #006dff;\\n  }\\n\\n  .btn-wrapper {\\n    display: flex;\\n\\n    justify-content: center;\\n    gap: 1rem;\\n    @media (max-width: 768px) {\\n      flex-direction: column;\\n      gap: 8px;\\n    }\\n    .btn {\\n      margin: 0;\\n      border-radius: 4px;\\n      text-transform: capitalize;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      min-width: 160px;\\n      @media (max-width: 768px) {\\n        display: block;\\n        vertical-align: baseline;\\n      }\\n    }\\n  }\\n\\n  .btn-outline-secondary {\\n    color: #349eff;\\n    border-color: #349eff;\\n  }\\n\\n  .btn-outline-secondary:hover {\\n    color: #fff;\\n    background-color: #349eff;\\n    border-color: #349eff;\\n  }\\n\\n  .btn-download {\\n    background: linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%);\\n    border: none;\\n    color: #fff;\\n    transition: unset;\\n    text-transform: capitalize;\\n    gap: 0.5rem;\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n\\n    &:hover {\\n      color: #fff;\\n      background: #0085ff;\\n    }\\n  }\\n\\n  .btn-white {\\n    color: #000;\\n    &:hover,\\n    &:focus,\\n    &:active {\\n      background: #006dff;\\n      color: #fff;\\n      border-color: #006dff;\\n    }\\n  }\\n\\n  .part-banner {\\n    position: relative;\\n    .banner-top-img {\\n      @media (max-width: 768px) {\\n        height: 212px;\\n        margin-bottom: 1.5rem;\\n        object-fit: cover;\\n        width: auto;\\n      }\\n    }\\n  }\\n\\n  @media (min-width: 992px) {\\n    .part-banner .btn {\\n      height: 48px;\\n    }\\n  }\\n\\n  .part-banner .wsc-icon {\\n    height: 1.125rem;\\n  }\\n  .part-banner .content-wrapper {\\n    text-align: center;\\n    margin-top: -2.5rem;\\n  }\\n\\n  .part-banner .content-wrapper h1 {\\n    text-align: center;\\n    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 20.83%, #ffffff 84.03%), linear-gradient(90.99deg, #3fa8ff 1.8%, #4bf3ff 99.78%);\\n    -webkit-text-fill-color: transparent;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    font-weight: 900;\\n    font-size: 4rem;\\n    @media (max-width: 768px) {\\n      font-size: 3rem;\\n    }\\n  }\\n\\n  .part-banner .content-wrapper .btn-wrapper .btn-white {\\n    min-width: 11.5rem;\\n  }\\n\\n  .part-banner .content-wrapper .btn-wrapper .btn-download {\\n    min-width: 11.5rem;\\n  }\\n\\n  .part-banner .content-wrapper .download-list {\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    color: #787878;\\n  }\\n\\n  .part-banner .content-wrapper .download-list a {\\n    color: inherit;\\n    text-decoration: none;\\n  }\\n\\n  .part-banner .content-wrapper .download-list a:hover {\\n    color: #fff;\\n  }\\n\\n  .part-company {\\n    text-align: center;\\n  }\\n\\n  @keyframes marquee {\\n    0% {\\n      transform: translateX(0);\\n    }\\n\\n    100% {\\n      transform: translateX(-50%);\\n    }\\n  }\\n\\n  .part-company .logo-list {\\n    display: flex;\\n    flex-wrap: nowrap;\\n    align-items: stretch;\\n    justify-content: center;\\n  }\\n\\n  @media (max-width: 992px) {\\n    .part-company .logo-list {\\n      width: fit-content;\\n      animation: marquee 30s linear infinite;\\n    }\\n  }\\n\\n  .part-company .logo-list .logo-item {\\n    flex: 1 1 auto;\\n    height: auto;\\n  }\\n\\n  @media (max-width: 992px) {\\n    .part-company .logo-list .logo-item {\\n      height: 32px;\\n      flex: 0 0 auto;\\n    }\\n  }\\n\\n  .part-company .logo-list .logo-item img {\\n    width: auto;\\n    max-width: 100%;\\n    height: 100%;\\n    object-fit: contain;\\n  }\\n\\n  .part-video {\\n    background: url(https://images.wondershare.com/recoverit/images2025/GoPro/mountain.jpg) no-repeat center bottom / cover;\\n    min-height: 818px;\\n    @media (max-width: 1280px) {\\n      min-height: unset;\\n      padding-bottom: 3rem;\\n    }\\n    .video-wrapper {\\n      position: relative;\\n      overflow: hidden;\\n      border-radius: 1rem;\\n      line-height: 0;\\n      font-size: 0;\\n      cursor: pointer;\\n    }\\n\\n    .video-wrapper video {\\n      width: 100%;\\n      height: auto;\\n      max-width: 100%;\\n    }\\n\\n    .video-wrapper .video-mask {\\n      position: absolute;\\n      width: 100%;\\n      height: 100%;\\n      background: rgba(0, 0, 0, 0.5);\\n      top: 0;\\n      left: 0;\\n      pointer-events: none;\\n    }\\n\\n    .video-wrapper .video-play {\\n      position: absolute;\\n      left: 50%;\\n      top: 50%;\\n      transform: translate(-50%, -50%);\\n    }\\n  }\\n\\n  .part-scenarios {\\n    .scenarios-box {\\n      position: relative;\\n      background: url(https://images.wondershare.com/recoverit/images2025/GoPro/scenarios-bg.jpg) no-repeat center center/cover;\\n      overflow: hidden;\\n      border-radius: 1rem;\\n      overflow: hidden;\\n    }\\n\\n    .scenarios-box .type-list-mobile {\\n      display: flex;\\n      gap: 4px;\\n      align-items: center;\\n      width: fit-content;\\n      overflow-x: scroll;\\n      padding: 0 16px;\\n    }\\n\\n    .scenarios-box .type-list-mobile .type-tab-mobile {\\n      padding: 10px 16px;\\n      border-radius: 8px;\\n      background-color: #fff;\\n      border: 1px solid #fff;\\n      color: #8babc8;\\n      font-weight: 800;\\n      flex-shrink: 0;\\n      white-space: nowrap;\\n      cursor: pointer;\\n    }\\n\\n    .scenarios-box .type-list-mobile .type-tab-mobile.active {\\n      border: 1px solid #3ca2ff;\\n      color: #0085ff;\\n    }\\n\\n    .scenarios-box .type-list {\\n      display: flex;\\n      flex-direction: column;\\n      align-items: center;\\n      justify-content: space-evenly;\\n      height: 100%;\\n      position: absolute;\\n      height: 120%;\\n      top: -10%;\\n      transition: all 0.5s linear;\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .scenarios-box .type-list {\\n        display: none;\\n      }\\n    }\\n\\n    .scenarios-box .type-list.left {\\n      left: 40%;\\n    }\\n\\n    .scenarios-box .type-list.left.active {\\n      left: 20%;\\n    }\\n\\n    .scenarios-box .type-list.left.active .type-item-tab {\\n      top: unset !important;\\n      bottom: unset !important;\\n    }\\n\\n    .scenarios-box .type-list.right {\\n      right: 40%;\\n    }\\n\\n    .scenarios-box .type-list.right.active {\\n      right: 20%;\\n    }\\n\\n    .scenarios-box .type-list.right.active .type-item-tab {\\n      top: unset !important;\\n      bottom: unset !important;\\n    }\\n\\n    @media (max-width: 992px) {\\n      .scenarios-box .type-list {\\n        flex-direction: row;\\n      }\\n    }\\n\\n    .scenarios-box .type-list .type-item-tab {\\n      border-radius: 10000px;\\n      aspect-ratio: 1/1;\\n      background-color: #fff;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      flex-direction: column;\\n      cursor: pointer;\\n      font-weight: 800;\\n      overflow: hidden;\\n      color: #8babc8;\\n      width: 116px;\\n      text-align: center;\\n      position: relative;\\n      padding: 10px;\\n      transition: bottom 0.5s linear, top 0.5s linear;\\n    }\\n\\n    .scenarios-box .type-list .type-item-tab[data-index=\\\"0\\\"] {\\n      right: -75%;\\n      top: 33%;\\n    }\\n\\n    .scenarios-box .type-list .type-item-tab[data-index=\\\"2\\\"] {\\n      right: -75%;\\n      bottom: 33%;\\n    }\\n\\n    .scenarios-box .type-list .type-item-tab[data-index=\\\"3\\\"] {\\n      left: -75%;\\n      top: 33%;\\n    }\\n\\n    .scenarios-box .type-list .type-item-tab[data-index=\\\"5\\\"] {\\n      left: -75%;\\n      bottom: 33%;\\n    }\\n\\n    .scenarios-box .type-list .type-item-tab.active {\\n      border: 2px solid #3ca2ff;\\n      color: #0085ff;\\n    }\\n\\n    .scenarios-box .type-list .type-item-tab img {\\n      width: 40%;\\n    }\\n\\n    .scenarios-box .blue-cricle {\\n      position: relative;\\n      transform: scale(1.8);\\n      z-index: 2;\\n      pointer-events: none;\\n    }\\n\\n    @keyframes wave {\\n      0% {\\n        transform: scale(0.8);\\n        opacity: 1;\\n      }\\n\\n      100% {\\n        transform: scale(1.5);\\n        opacity: 0;\\n      }\\n    }\\n\\n    .scenarios-box .wave {\\n      position: absolute;\\n      left: 0%;\\n      top: 0%;\\n      width: 100%;\\n      padding-bottom: 100%;\\n      border-radius: 50%;\\n      border: 3px solid rgba(255, 255, 255, 0.4);\\n      animation: wave 4s linear infinite;\\n    }\\n\\n    .scenarios-box .wave.wave2 {\\n      animation-delay: 1s;\\n    }\\n\\n    .scenarios-box .wave.wave3 {\\n      animation-delay: 2s;\\n    }\\n\\n    .scenarios-box .wave.wave4 {\\n      animation-delay: 3s;\\n    }\\n\\n    .scenarios-box .computer-content {\\n      position: absolute;\\n      top: 50%;\\n      left: 50%;\\n      width: 100%;\\n      padding: 1rem;\\n      z-index: 5;\\n      transform: translate(-50%, -50%);\\n      color: #fff;\\n      text-align: center;\\n    }\\n\\n    .scenarios-box .computer-content .email-icon {\\n      width: 6rem;\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .scenarios-box .computer-content {\\n        all: initial;\\n      }\\n    }\\n\\n    .scenarios-box .computer-content .type-item {\\n      height: 100%;\\n      display: flex;\\n      flex-direction: column;\\n      align-items: center;\\n      justify-content: center;\\n      width: 81.79%;\\n      margin: 0 auto;\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .scenarios-box .computer-content .type-item {\\n        all: inherit;\\n        height: 100%;\\n        border-radius: 1rem;\\n        background: linear-gradient(146.6deg, #0070ff 24.12%, #98d8ff 85.64%);\\n        overflow: hidden;\\n        padding: 32px 16px;\\n        color: #fff;\\n        text-align: center;\\n      }\\n    }\\n  }\\n\\n  .part-tool {\\n    @keyframes banner-diffuse1 {\\n      0% {\\n        transform: translate(-50%, -50%) scale(0.2);\\n        opacity: 0.1;\\n      }\\n\\n      60% {\\n        transform: translate(-50%, -50%) scale(0.7);\\n        opacity: 0.5;\\n      }\\n\\n      100% {\\n        transform: translate(-50%, -50%) scale(1);\\n        opacity: 0;\\n      }\\n    }\\n\\n    @keyframes banner-diffuse2 {\\n      0% {\\n        transform: translate(-50%, -50%) scale(0.2);\\n        opacity: 0.1;\\n      }\\n\\n      60% {\\n        transform: translate(-50%, -50%) scale(0.9);\\n        opacity: 0.5;\\n      }\\n\\n      100% {\\n        transform: translate(-50%, -50%) scale(1);\\n        opacity: 0;\\n      }\\n    }\\n\\n    .tool-box {\\n      border-radius: 1rem;\\n      background: url(https://images.wondershare.com/recoverit/images2025/GoPro/part-tool-bg.jpg) no-repeat center center/cover;\\n      overflow: hidden;\\n      @media (max-width: 1280px) {\\n        padding: 0 1rem;\\n      }\\n    }\\n\\n    .tool-box ul li {\\n      list-style: none;\\n      position: relative;\\n      padding-left: 1rem;\\n    }\\n\\n    .tool-box ul li::before {\\n      content: \\\"•\\\";\\n      position: absolute;\\n      left: 4px;\\n      top: -2px;\\n    }\\n\\n    .img-wrapper {\\n      position: relative;\\n      overflow: hidden;\\n    }\\n\\n    .img-wrapper .email-icon-wrapper:hover .email-icon {\\n      transform: scale(1.2);\\n    }\\n\\n    .img-wrapper .email-icon {\\n      position: absolute;\\n      width: 18.4%;\\n      transition: all 0.2s linear;\\n    }\\n\\n    .img-wrapper .email-icon-1 {\\n      right: 8%;\\n      bottom: 60%;\\n    }\\n\\n    .img-wrapper .email-icon-2 {\\n      right: 10%;\\n      bottom: 49%;\\n    }\\n\\n    .img-wrapper .email-icon-3 {\\n      right: 12%;\\n      bottom: 38%;\\n    }\\n\\n    .img-wrapper .email-icon-4 {\\n      right: 10%;\\n      bottom: 27%;\\n    }\\n\\n    .img-wrapper .email-icon-5 {\\n      right: 8%;\\n      bottom: 17%;\\n    }\\n\\n    .img-wrapper .email-icon-6 {\\n      right: 6%;\\n      bottom: 7%;\\n    }\\n\\n    .part-banner-wave-icon-box {\\n      position: absolute;\\n      bottom: 8%;\\n      left: 9%;\\n      width: 11%;\\n    }\\n\\n    @media (max-width: 1600px) {\\n      .part-banner-wave-icon-box {\\n        width: 14%;\\n      }\\n    }\\n\\n    @media (max-width: 576px) {\\n      .part-banner-wave-icon-box {\\n        width: 17%;\\n      }\\n    }\\n\\n    .part-banner-wave-icon-box .download-content {\\n      position: absolute;\\n      bottom: 8%;\\n      width: 100%;\\n    }\\n\\n    .part-banner-wave-icon-box .download-content .num {\\n      color: #006dff;\\n      font-weight: 700;\\n      font-size: 14px;\\n      text-align: center;\\n      line-height: 100%;\\n    }\\n\\n    @media (max-width: 576px) {\\n      .part-banner-wave-icon-box .download-content .num {\\n        font-size: 10px;\\n      }\\n    }\\n\\n    .part-banner-wave-icon-box .download-content .text {\\n      text-align: center;\\n      color: #416fb4;\\n      font-size: 12px;\\n      line-height: 100%;\\n    }\\n\\n    @media (max-width: 576px) {\\n      .part-banner-wave-icon-box .download-content .text {\\n        font-size: 10px;\\n      }\\n    }\\n\\n    .part-banner-wave-icon-box .wave1 {\\n      width: 130%;\\n      aspect-ratio: 72 / 74;\\n      border-radius: 20%;\\n      border: 3px solid #fff;\\n      z-index: 1;\\n      opacity: 0.8;\\n      backdrop-filter: blur(6px);\\n      position: absolute;\\n      left: 50%;\\n      top: 50%;\\n      transform: translate(-50%, -50%) scale(0);\\n      animation: banner-diffuse1 2s linear infinite;\\n    }\\n\\n    .part-banner-wave-icon-box .wave2 {\\n      width: 160%;\\n      aspect-ratio: 72 / 74;\\n      border-radius: 20%;\\n      border: 3px solid #fff;\\n      z-index: 1;\\n      opacity: 0.8;\\n      backdrop-filter: blur(6px);\\n      position: absolute;\\n      left: 50%;\\n      top: 50%;\\n      transform: translate(-50%, -50%) scale(0);\\n      animation: banner-diffuse1 2s linear infinite;\\n    }\\n  }\\n\\n  .part-gopro {\\n    .gopro-card {\\n      border-radius: 0.75rem;\\n      background-color: #14181d;\\n      padding: 1rem 1.5rem 1rem;\\n      overflow: hidden;\\n      text-align: center;\\n      color: #fff;\\n      height: 100%;\\n      .gopro-name {\\n        margin-top: 0.5rem;\\n      }\\n    }\\n    .swiper-pagination-bullet {\\n      background-color: #fff;\\n      opacity: 0.5;\\n      &.swiper-pagination-bullet-active {\\n        background-color: #007aff;\\n        opacity: 1;\\n      }\\n    }\\n  }\\n\\n  .part-everyone {\\n    @media (min-width: 1280px) {\\n      .swiper-wrapper {\\n        display: flex;\\n        gap: 0.5rem;\\n        flex-wrap: nowrap;\\n        aspect-ratio: 1410 / 640;\\n        justify-content: space-between;\\n      }\\n      .swiper-slide {\\n        width: 11.92%;\\n        transition: all 0.4s ease-in-out;\\n        height: 100%;\\n        overflow: hidden;\\n        &.active {\\n          width: 37.59%;\\n          .content-wrapper {\\n            padding: 2rem;\\n            .sport-desc {\\n              display: block;\\n            }\\n          }\\n        }\\n      }\\n    }\\n    .swiper-pagination-bullet {\\n      background-color: #fff;\\n      opacity: 0.5;\\n      &.swiper-pagination-bullet-active {\\n        background-color: #007aff;\\n        opacity: 1;\\n      }\\n    }\\n    .user-card {\\n      width: 100%;\\n      height: 100%;\\n      background-size: cover;\\n      background-position: center;\\n      background-repeat: no-repeat;\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      @media (max-width: 1280px) {\\n        aspect-ratio: 530 / 640;\\n        width: 100%;\\n      }\\n      &::after {\\n        position: absolute;\\n        content: \\\"\\\";\\n        left: 0;\\n        top: 0;\\n        z-index: 1;\\n        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 60%) no-repeat center bottom/ 100% 50%;\\n        width: 102%;\\n        height: 102%;\\n      }\\n\\n      &.card-surf {\\n        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/surf.jpg);\\n      }\\n      &.card-snowboard {\\n        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/snowboard.jpg);\\n      }\\n      &.card-ski {\\n        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/ski.jpg);\\n      }\\n      &.card-bike {\\n        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/bike.jpg);\\n      }\\n      &.card-hiking {\\n        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/hiking.jpg);\\n      }\\n      &.card-diving {\\n        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/diving.jpg);\\n      }\\n\\n      .content-wrapper {\\n        position: absolute;\\n        bottom: 0;\\n        left: 0;\\n        z-index: 2;\\n        max-height: 400px;\\n        overflow: hidden;\\n        padding: 2rem 0.75rem;\\n\\n        .sport-name {\\n          font-weight: 800;\\n          font-size: 1.5rem;\\n          color: #fff;\\n          margin-bottom: 1rem;\\n          @media (max-width: 1600px) {\\n            font-size: 1.25rem;\\n          }\\n        }\\n        .sport-desc {\\n          font-size: 0.875rem;\\n          opacity: 0.7;\\n          display: none;\\n          @media (max-width: 1280px) {\\n            display: block;\\n          }\\n        }\\n      }\\n    }\\n    .sd-wrapper {\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      background: linear-gradient(0deg, #23262a, #23262a), linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);\\n      padding-left: 1.5rem;\\n      padding-right: 1.5rem;\\n      .sd-list {\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        padding-top: 1rem;\\n        gap: 1.875rem;\\n        max-width: 1120px;\\n        margin: 0 auto;\\n        @media (max-width: 768px) {\\n          flex-wrap: wrap;\\n        }\\n        .sd-item {\\n          flex: 1 1 calc(25% - 1.875rem);\\n          @media (max-width: 768px) {\\n            flex: 1 1 calc(50% - 1.875rem);\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-logos {\\n    background-color: #f2fbff;\\n\\n    @media (max-width: 1280px) {\\n      background: url(https://images.wondershare.com/recoverit/images2025/email/logo-bg.png) no-repeat center center/cover;\\n      background-color: #f2fbff;\\n    }\\n\\n    .logo-box .logo-content {\\n      position: absolute;\\n      max-width: 570px;\\n      left: 50%;\\n      bottom: 0;\\n      transform: translateX(-50%);\\n      z-index: 4;\\n      color: #000;\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .logo-box .logo-content {\\n        position: relative;\\n      }\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .logo-box .logos-bg {\\n        display: none;\\n      }\\n    }\\n\\n    .logo-box .logos-icon {\\n      transition: all 1.5s linear;\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .logo-box .logos-icon {\\n        display: none;\\n      }\\n    }\\n\\n    .logo-box .top-logos {\\n      position: absolute;\\n      top: 1%;\\n      left: 33%;\\n      transform: scale(0.8);\\n      transform-origin: bottom center;\\n      pointer-events: none;\\n      width: 41%;\\n    }\\n\\n    .logo-box .top-logos.active {\\n      bottom: 0;\\n      top: -8%;\\n      left: 33%;\\n      pointer-events: none;\\n      transform: unset;\\n    }\\n\\n    .logo-box .left-logos {\\n      position: absolute;\\n      bottom: 0;\\n      left: 8%;\\n      pointer-events: none;\\n      transform: scale(0.8);\\n      width: 19%;\\n    }\\n\\n    .logo-box .left-logos.active {\\n      bottom: 0;\\n      left: 6%;\\n      pointer-events: none;\\n      transform: unset;\\n    }\\n\\n    .logo-box .right-logos {\\n      position: absolute;\\n      right: 5%;\\n      bottom: 0;\\n      pointer-events: none;\\n      transform: scale(0.8);\\n      width: 19%;\\n    }\\n\\n    .logo-box .right-logos.active {\\n      position: absolute;\\n      right: 4%;\\n      bottom: 0;\\n      transform: unset;\\n      pointer-events: none;\\n    }\\n\\n    @keyframes ToRight {\\n      0% {\\n        transform: translate3d(0, 0, 0);\\n      }\\n\\n      100% {\\n        transform: translate3d(-50%, 0, 0);\\n        -webkit-transform: translate3d(-50%, 0, 0);\\n        -moz-transform: translate3d(-50%, 0, 0);\\n        -ms-transform: translate3d(-50%, 0, 0);\\n        -o-transform: translate3d(-50%, 0, 0);\\n      }\\n    }\\n\\n    .logo-list-mobile {\\n      display: none;\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .logo-list-mobile {\\n        display: flex;\\n        flex-wrap: nowrap;\\n        width: fit-content;\\n        align-items: center;\\n        gap: 16px;\\n        animation: ToRight 60s linear infinite;\\n        padding-bottom: 2rem;\\n      }\\n\\n      .logo-list-mobile img {\\n        width: 56px;\\n      }\\n    }\\n  }\\n\\n  .part-table {\\n    background-color: #f2fbff;\\n    color: #000;\\n    .table-box {\\n      @media (max-width: 1280px) {\\n        overflow: auto;\\n        overflow-y: hidden;\\n      }\\n    }\\n    .table-wrapper {\\n      /* --- 1. 定制化变量 --- */\\n      --table-theme-color: #cdeeff; /* 主要主题色，用于高亮列 */\\n      --table-theme-color-border: #ace5ff; /* 边框和外框颜色 */\\n      --table-side-col-bg: #f2fbff; /* 侧边和头部非高亮背景色 */\\n      --table-main-bg: #f2fbff; /* 表格主体背景色 */\\n      --table-text-primary: #000; /* 主要文字颜色 */\\n      --table-text-secondary: #000; /* 次要文字颜色 (高亮列上) */\\n      --table-border-radius: 8px; /* 表格圆角大小 */\\n      --table-decorator-height: 1.5rem; /* 高亮列顶部和底部的装饰条高度 */\\n\\n      /* --- 2. 基础布局和容器 --- */\\n      border-radius: var(--table-border-radius);\\n      width: 100%;\\n      position: relative;\\n      overflow: visible;\\n      background-color: var(--table-theme-color-border); /* 用背景色模拟外边框 */\\n      padding: 1.2px; /* 关键：让背景色显示为1px的边框 */\\n      margin-top: 3.5rem;\\n      margin-bottom: 6.25rem;\\n      min-width: 928px;\\n      @media (max-width: 576px) {\\n        min-width: 728px;\\n      }\\n    }\\n\\n    .inner-table {\\n      border-collapse: collapse; /* 合并边框 */\\n      border-style: hidden; /* 隐藏表格默认边框，由wrapper接管 */\\n      width: 100%;\\n      background: var(--table-main-bg);\\n      border-radius: var(--table-border-radius);\\n      overflow: auto; /* 必须设置，以显示高亮列的装饰条 */\\n    }\\n\\n    /* --- 3. 单元格通用样式 --- */\\n    .inner-table th,\\n    .inner-table td {\\n      position: relative; /* 为伪元素定位提供基准 */\\n      padding: 1.5rem 2rem;\\n      text-align: center;\\n      vertical-align: middle;\\n      width: calc(100% / 6); /* 平均分配列宽，6是总列数 */\\n\\n      @media (max-width: 1600px) {\\n        padding: 1rem 8px;\\n      }\\n    }\\n\\n    .inner-table th {\\n      font-weight: 700;\\n      font-size: 1rem;\\n      color: var(--table-text-primary);\\n      background-color: var(--table-side-col-bg);\\n    }\\n\\n    .inner-table td {\\n      font-size: 1rem;\\n      @media (max-width: 1280px) {\\n        font-size: 12px;\\n      }\\n    }\\n\\n    /* 行标题列 (第一列) 的特殊样式 */\\n    .inner-table td:first-child {\\n      background-color: var(--table-side-col-bg);\\n      font-size: 1rem;\\n      font-weight: 700;\\n      color: var(--table-text-primary);\\n      padding: 1.5rem;\\n      @media (max-width: 1280px) {\\n        font-size: 12px;\\n        padding: 1rem 8px;\\n      }\\n    }\\n\\n    /* --- 4. 高亮列样式 --- */\\n    /* 这是核心！通过添加 .highlight-col 类来指定高亮列 */\\n    .inner-table .highlight-col {\\n      background-color: var(--table-theme-color);\\n      color: var(--table-text-secondary);\\n      @media (max-width: 1280px) {\\n        min-width: 10.75rem;\\n      }\\n    }\\n\\n    /* 高亮列顶部的装饰条 */\\n    .highlight-col-top::before {\\n      content: \\\"\\\";\\n      position: absolute;\\n      top: 0;\\n      left: -1px;\\n      width: calc(100% + 2px);\\n      height: var(--table-decorator-height);\\n      background-color: var(--table-theme-color);\\n      border-radius: 0.5rem 0.5rem 0 0;\\n      transform: translateY(-98%);\\n    }\\n\\n    /* 高亮列底部的装饰条 */\\n    .highlight-col-bottom {\\n      .download-item {\\n        position: absolute;\\n        bottom: 0;\\n        left: -1px;\\n        width: calc(100% + 2px);\\n        padding: 1.5rem 0;\\n        background-color: var(--table-theme-color);\\n        border-radius: 0 0 0.5rem 0.5rem;\\n        transform: translateY(98%);\\n      }\\n    }\\n\\n    /* --- 5. 边框样式 --- */\\n    /* 垂直边框 */\\n    .inner-table th:not(:last-child),\\n    .inner-table td:not(:last-child) {\\n      border-right: 1px solid var(--table-theme-color-border);\\n    }\\n\\n    /* 水平边框 (非高亮列) */\\n    .inner-table th:not(.highlight-col)::after,\\n    .inner-table tr:not(:last-child) td:not(.highlight-col)::after {\\n      content: \\\"\\\";\\n      position: absolute;\\n      bottom: 0;\\n      left: 0;\\n      right: 0;\\n      height: 1.1px;\\n      background-color: var(--table-theme-color-border);\\n    }\\n\\n    /* 水平边框 (高亮列)，颜色稍浅 */\\n    .inner-table th.highlight-col::after,\\n    .inner-table tr:not(:last-child) td.highlight-col::after {\\n      content: \\\"\\\";\\n      position: absolute;\\n      bottom: 0;\\n      left: 2rem;\\n      right: 2rem;\\n      height: 1.1px;\\n      background-color: #8ed9ff;\\n      @media (max-width: 768px) {\\n        left: 8px;\\n        right: 8px;\\n      }\\n    }\\n\\n    /* --- 6. 内容对齐和响应式 --- */\\n    .inner-table td span {\\n      vertical-align: middle;\\n      margin-left: 8px;\\n    }\\n\\n    /* 移动端响应式调整 */\\n    @media (max-width: 768px) {\\n      .inner-table th {\\n        font-size: 10px;\\n        padding: 8px 4px;\\n      }\\n      .inner-table td {\\n        font-size: 9px;\\n        padding: 8px 4px;\\n      }\\n      .inner-table td:first-child {\\n        font-size: 10px;\\n        padding: 8px;\\n      }\\n      .inner-table td img {\\n        width: 14px;\\n      }\\n      .mt-logo {\\n        width: 60%;\\n      }\\n    }\\n\\n    .blue-tip {\\n      background-color: #4dacff;\\n      color: #fff;\\n      border-radius: 4px;\\n      padding: 0.1875rem 0.25rem;\\n      color: #fff;\\n      font-size: 0.75rem;\\n      line-height: 100%;\\n      margin: auto 4px;\\n      @media (max-width: 576px) {\\n        font-size: 8px;\\n      }\\n    }\\n\\n    .blue-tip2 {\\n      background-color: #a8c4dd;\\n      color: #fff;\\n      border-radius: 4px;\\n      padding: 0.1875rem 0.25rem;\\n      color: #fff;\\n      font-size: 0.75rem;\\n      line-height: 100%;\\n      margin: auto 4px;\\n      @media (max-width: 576px) {\\n        font-size: 8px;\\n      }\\n    }\\n  }\\n\\n  .part-how {\\n    background-color: #f2fbff;\\n    margin-bottom: -2px;\\n  }\\n\\n  .part-how .nav {\\n    border-left: 2px solid #c1edff;\\n    padding-left: 3.75rem;\\n    height: 100%;\\n    justify-content: space-between;\\n    align-items: stretch;\\n    gap: 1rem;\\n  }\\n\\n  .part-how .nav .nav-item {\\n    height: fit-content;\\n  }\\n\\n  .part-how .nav .nav-item h4 {\\n    font-weight: 900;\\n    font-size: 1.5rem;\\n    line-height: 100%;\\n    color: rgba(0, 0, 0, 0.8);\\n    position: relative;\\n  }\\n\\n  .part-how .nav .nav-item h4 .before-icon {\\n    position: absolute;\\n    left: -0.5rem;\\n    top: 50%;\\n    transform: translate(-100%, -50%);\\n    color: inherit;\\n  }\\n\\n  .part-how .nav .nav-item h4 .before-icon img {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n    display: inline-block;\\n  }\\n\\n  .part-how .nav .nav-item h4 .before-icon img.active-icon {\\n    display: none;\\n  }\\n\\n  .part-how .nav .nav-item .description {\\n    font-size: 1rem;\\n    color: rgba(0, 0, 0, 0.8);\\n  }\\n\\n  .part-how .nav .nav-item.active {\\n    position: relative;\\n  }\\n\\n  .part-how .nav .nav-item.active:before {\\n    content: \\\"\\\";\\n    height: 100%;\\n    border-left: 2px solid #006dff;\\n    position: absolute;\\n    left: -3.75rem;\\n    top: 0;\\n  }\\n\\n  .part-how .nav .nav-item.active h4 {\\n    color: #006dff;\\n  }\\n\\n  .part-how .nav .nav-item.active .description {\\n    color: #006dff;\\n  }\\n\\n  .part-how .nav .nav-item.active .before-icon img.active-icon {\\n    display: inline-block;\\n  }\\n\\n  .part-how .nav .nav-item.active .before-icon img.default-icon {\\n    display: none;\\n  }\\n\\n  .part-faq {\\n    background-color: #000;\\n  }\\n\\n  .part-faq .wsc-icon {\\n    position: relative;\\n    height: 1rem;\\n    line-height: 0.5;\\n    vertical-align: inherit;\\n  }\\n\\n  .part-faq .accordion i {\\n    transition: 0.2s;\\n  }\\n\\n  .part-faq .accordion [aria-expanded=\\\"true\\\"] i {\\n    transform: rotate(180deg);\\n    color: #1891ff;\\n  }\\n\\n  .part-count {\\n    color: #fff;\\n  }\\n\\n  .part-count .count-list {\\n    display: flex;\\n\\n    justify-content: center;\\n    color: #fff;\\n\\n    gap: 1rem;\\n    .count-divider {\\n      width: 1px;\\n      background-color: rgba($color: #000, $alpha: 0.3);\\n      margin: 1rem 0;\\n      height: auto;\\n      @media (max-width: 768px) {\\n        display: none;\\n      }\\n    }\\n  }\\n\\n  @media (max-width: 768px) {\\n    .part-count .count-list {\\n      flex-wrap: wrap;\\n      gap: 4px;\\n    }\\n  }\\n\\n  .part-count .count-list .count-box {\\n    flex: 1 1 calc(25% - 1rem);\\n    display: flex;\\n    flex-direction: column;\\n    align-items: center;\\n    justify-content: center;\\n    position: relative;\\n    color: #fff;\\n  }\\n\\n  @media (max-width: 768px) {\\n    .part-count .count-list .count-box {\\n      flex: 1 1 45%;\\n      border-radius: 1.125rem;\\n      min-height: 10rem;\\n    }\\n  }\\n\\n  .part-count .count-list .count-box:not(:last-child)::after {\\n    content: \\\"\\\";\\n    width: 1px;\\n    height: 40%;\\n    background-color: rgba(255, 255, 255, 0.2);\\n    position: absolute;\\n    right: 0;\\n    top: 50%;\\n    transform: translateY(-50%);\\n  }\\n  @media (max-width: 768px) {\\n    .part-count .count-list .count-box:not(:last-child)::after {\\n      content: unset;\\n    }\\n  }\\n\\n  .part-count .count-list .count-box .count {\\n    display: flex;\\n    align-items: top;\\n    justify-content: center;\\n    font-weight: 900;\\n    font-size: 4.5rem;\\n    line-height: 110%;\\n    letter-spacing: 0%;\\n    text-align: center;\\n  }\\n\\n  @media (max-width: 1280px) {\\n    .part-count .count-list .count-box .count {\\n      font-size: 3.5rem;\\n    }\\n  }\\n\\n  .part-count .count-list .count-box .count .count-num {\\n    font-weight: 900;\\n    font-size: 4.5rem;\\n    line-height: 110%;\\n    letter-spacing: 0%;\\n    text-align: center;\\n  }\\n\\n  @media (max-width: 1280px) {\\n    .part-count .count-list .count-box .count .count-num {\\n      font-size: 3.5rem;\\n    }\\n  }\\n\\n  .part-count .count-list .count-box .count .count-plus {\\n    font-weight: 1000;\\n    font-size: 3rem;\\n    line-height: 130%;\\n    letter-spacing: 0%;\\n    text-align: center;\\n  }\\n\\n  @media (max-width: 1280px) {\\n    .part-count .count-list .count-box .count .count-plus {\\n      font-size: 2rem;\\n    }\\n  }\\n\\n  .part-count .count-list .count-box .count-desc {\\n    font-weight: 400;\\n    font-size: 1.5rem;\\n    line-height: 130%;\\n    letter-spacing: 0%;\\n    text-align: center;\\n    opacity: 0.5;\\n    color: rgba($color: #fff, $alpha: 0.8);\\n  }\\n\\n  .part-best {\\n    color: #fff;\\n    @media (min-width: 1280px) {\\n      #best-swiper .swiper-wrapper {\\n        gap: 1.875rem;\\n      }\\n      #best-swiper .swiper-wrapper .swiper-slide {\\n        flex: 1 1 calc(25% - 1.875rem);\\n      }\\n    }\\n  }\\n\\n  .part-best .best-box {\\n    height: 100%;\\n    display: flex;\\n    align-items: flex-start;\\n    padding: 1.5rem;\\n    position: relative;\\n    border: 1px solid #fff;\\n    border-radius: 1rem;\\n    gap: 1.25rem;\\n    text-decoration: none;\\n\\n    .best-box-link {\\n      position: absolute;\\n      top: 0;\\n      left: 0;\\n      width: 100%;\\n      height: 100%;\\n      z-index: 2;\\n    }\\n  }\\n\\n  @media (max-width: 1600px) {\\n    .part-best .best-box {\\n      gap: 0.9375rem;\\n    }\\n  }\\n\\n  .part-best .best-box .best-icon {\\n    width: 3.5rem;\\n    @media (max-width: 992px) {\\n      svg {\\n        width: 2.5rem;\\n        height: 2.5rem;\\n      }\\n    }\\n  }\\n\\n  .part-best .best-box .best-icon .active-img {\\n    display: none;\\n  }\\n\\n  .part-best .best-box .best-item-title {\\n    font-size: 1.125rem;\\n    font-weight: 700;\\n    margin-bottom: 1rem;\\n  }\\n\\n  .part-best .best-box .best-item-desc {\\n    font-size: 0.875rem;\\n    color: inherit;\\n    text-align: left;\\n    opacity: 0.8;\\n  }\\n\\n  .part-best .best-box .download-icon {\\n    position: absolute;\\n    right: 1rem;\\n    top: 1rem;\\n    width: 1.5rem;\\n  }\\n\\n  .part-best .best-box .download-icon .active-img {\\n    display: none;\\n  }\\n\\n  @media (any-hover: hover) {\\n    .part-best .best-box:hover {\\n      background-color: #3e90ff;\\n      color: #fff;\\n    }\\n\\n    .part-best .best-box:hover .best-icon .active-img {\\n      display: inline-block;\\n    }\\n\\n    .part-best .best-box:hover .best-icon .default-img {\\n      display: none;\\n    }\\n\\n    .part-best .best-box:hover .download-icon .active-img {\\n      display: inline-block;\\n    }\\n\\n    .part-best .best-box:hover .download-icon .default-img {\\n      display: none;\\n    }\\n  }\\n\\n  @media (max-width: 992px) {\\n    .part-best .best-box {\\n      background-color: #3e90ff;\\n      color: #fff;\\n      border: unset;\\n    }\\n\\n    .part-best .best-box .best-icon .active-img {\\n      display: inline-block;\\n      max-width: 100%;\\n    }\\n\\n    .part-best .best-box .best-icon .default-img {\\n      display: none;\\n      max-width: 100%;\\n    }\\n\\n    .part-best .best-box .download-icon .active-img {\\n      display: inline-block;\\n      max-width: 100%;\\n    }\\n\\n    .part-best .best-box .download-icon .default-img {\\n      display: none;\\n      max-width: 100%;\\n    }\\n  }\\n\\n  .part-drfone {\\n    background: url(https://images.wondershare.com/recoverit/images2025/GoPro/camera-bg.jpg) no-repeat center center/cover;\\n    color: #fff;\\n  }\\n\\n  @media (max-width: 768px) {\\n    .part-drfone {\\n      background: unset;\\n    }\\n  }\\n\\n  .part-drfone .swiper {\\n    padding: 3rem;\\n    padding-right: 0.5rem;\\n    border-radius: 1rem;\\n    background: rgba(0, 0, 0, 0.5);\\n    backdrop-filter: blur(16px);\\n    overflow: hidden;\\n    .drfone-content {\\n      max-height: 690px;\\n      overflow: auto;\\n      padding-right: 2.5rem;\\n      @media (max-width: 576px) {\\n        max-height: 520px;\\n      }\\n      //滚动条样式\\n\\n      &::-webkit-scrollbar {\\n        width: 2px;\\n      }\\n\\n      &::-webkit-scrollbar-track {\\n        background: rgba(0, 0, 0, 0.2);\\n        border-radius: 1.25rem;\\n      }\\n\\n      &::-webkit-scrollbar-thumb {\\n        background: rgba(255, 255, 255, 0.3);\\n        border-radius: 1.25rem;\\n\\n        &:hover {\\n          background: rgba(255, 255, 255, 0.5);\\n        }\\n      }\\n\\n      h4 {\\n        font-size: 1.25rem;\\n        font-weight: 700;\\n      }\\n      .method-content {\\n        padding-left: 1rem;\\n        color: #d0d0d0;\\n        display: flex;\\n        flex-direction: column;\\n        gap: 0.5rem;\\n        padding-bottom: 1rem;\\n        .feature-list {\\n          display: flex;\\n          justify-content: center;\\n          gap: 0.875rem;\\n          .advantage-box {\\n            border: 1px solid #7bde4e;\\n            padding: 0.75rem 1rem;\\n            background-color: #7bde4e1a;\\n            border-radius: 0.5rem;\\n            color: #d0d0d0;\\n            flex: 1 1 50%;\\n          }\\n          .disadvantage-box {\\n            border: 1px solid #f44949;\\n            padding: 0.75rem 1rem;\\n            background: #f449491a;\\n            border-radius: 0.5rem;\\n            flex: 1 1 50%;\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  @media (max-width: 1600px) {\\n    .part-drfone .swiper {\\n      padding: 1.5rem;\\n      padding-bottom: 2.5rem;\\n    }\\n  }\\n\\n  @media (max-width: 768px) {\\n    .part-drfone .swiper {\\n      margin-top: -85%;\\n      background-color: #292929;\\n    }\\n  }\\n\\n  .part-drfone .swiper .swiper-slide {\\n    height: auto;\\n  }\\n\\n  .part-drfone .swiper .swiper-slide h2 {\\n    margin-bottom: 2rem;\\n    text-align: left;\\n    font-weight: 700;\\n  }\\n\\n  .part-drfone .swiper .swiper-slide h4 {\\n    font-weight: 400;\\n    position: relative;\\n  }\\n\\n  .part-drfone .swiper .swiper-slide h4::before {\\n    content: attr(data-before);\\n    position: absolute;\\n    left: 0;\\n    top: 50%;\\n    transform: translate(-120%, -50%);\\n  }\\n\\n  .part-drfone .swiper .swiper-slide p {\\n    text-align: left;\\n    color: #d0d0d0;\\n    font-size: 1.125rem;\\n  }\\n\\n  .part-drfone .swiper .drfone-content-2 p {\\n    color: #d0d0d0;\\n    position: relative;\\n    padding-left: 1rem;\\n  }\\n\\n  .part-drfone .swiper .drfone-content-2 p::before {\\n    content: \\\"•\\\";\\n    position: absolute;\\n    left: 0;\\n    top: 15px;\\n    transform: translate(0, -50%);\\n  }\\n\\n  .part-drfone .swiper .swiper-pagination {\\n    bottom: 1.2rem;\\n    left: 0;\\n    text-align: left;\\n    left: 75px;\\n    @media (max-width: 768px) {\\n      left: 8px;\\n      bottom: 8px;\\n    }\\n  }\\n\\n  .part-drfone .swiper .swiper-pagination .swiper-pagination-bullet {\\n    width: 20px;\\n    height: 12px;\\n    border-radius: 100px;\\n    background: rgba(121, 178, 255, 0.3);\\n  }\\n\\n  .part-drfone .swiper .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {\\n    background: #006dff;\\n    width: 48px;\\n  }\\n\\n  .part-footer .footer-box {\\n    background: url(https://images.wondershare.com/recoverit/images2025/email/footer-bg.jpg) no-repeat center center/cover;\\n    border-radius: 1rem;\\n    overflow: hidden;\\n    padding: 0 2.25rem;\\n    color: #fff;\\n    @media (max-width: 768px) {\\n      padding: 2rem;\\n    }\\n  }\\n\\n  .part-footer .footer-box .footer-title {\\n    font-weight: 700;\\n    font-size: 2rem;\\n  }\\n\\n  .part-footer .btn-white {\\n    color: #349eff;\\n    &:hover,\\n    &:focus {\\n      background-color: #ececec;\\n      border-color: #e6e6e6;\\n    }\\n  }\\n\\n  .part-footer .btn-outline-white:hover,\\n  .part-footer .btn-outline-white:focus {\\n    color: #349eff;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2\n");

/***/ }),

/***/ 314:
/***/ ((module) => {

eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///314\n");

/***/ }),

/***/ 354:
/***/ ((module) => {

eval("\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzU0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsY0FBYztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2Nzcy1sb2FkZXIvZGlzdC9ydW50aW1lL3NvdXJjZU1hcHMuanM/YWYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoaXRlbSkge1xuICB2YXIgY29udGVudCA9IGl0ZW1bMV07XG4gIHZhciBjc3NNYXBwaW5nID0gaXRlbVszXTtcbiAgaWYgKCFjc3NNYXBwaW5nKSB7XG4gICAgcmV0dXJuIGNvbnRlbnQ7XG4gIH1cbiAgaWYgKHR5cGVvZiBidG9hID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB2YXIgYmFzZTY0ID0gYnRvYSh1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoY3NzTWFwcGluZykpKSk7XG4gICAgdmFyIGRhdGEgPSBcInNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LFwiLmNvbmNhdChiYXNlNjQpO1xuICAgIHZhciBzb3VyY2VNYXBwaW5nID0gXCIvKiMgXCIuY29uY2F0KGRhdGEsIFwiICovXCIpO1xuICAgIHJldHVybiBbY29udGVudF0uY29uY2F0KFtzb3VyY2VNYXBwaW5nXSkuam9pbihcIlxcblwiKTtcbiAgfVxuICByZXR1cm4gW2NvbnRlbnRdLmpvaW4oXCJcXG5cIik7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///354\n");

/***/ }),

/***/ 72:
/***/ ((module) => {

eval("\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzIuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHdCQUF3QjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixpQkFBaUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiw0QkFBNEI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiw2QkFBNkI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbmplY3RTdHlsZXNJbnRvU3R5bGVUYWcuanM/MmRiYSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIHN0eWxlc0luRE9NID0gW107XG5mdW5jdGlvbiBnZXRJbmRleEJ5SWRlbnRpZmllcihpZGVudGlmaWVyKSB7XG4gIHZhciByZXN1bHQgPSAtMTtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBzdHlsZXNJbkRPTS5sZW5ndGg7IGkrKykge1xuICAgIGlmIChzdHlsZXNJbkRPTVtpXS5pZGVudGlmaWVyID09PSBpZGVudGlmaWVyKSB7XG4gICAgICByZXN1bHQgPSBpO1xuICAgICAgYnJlYWs7XG4gICAgfVxuICB9XG4gIHJldHVybiByZXN1bHQ7XG59XG5mdW5jdGlvbiBtb2R1bGVzVG9Eb20obGlzdCwgb3B0aW9ucykge1xuICB2YXIgaWRDb3VudE1hcCA9IHt9O1xuICB2YXIgaWRlbnRpZmllcnMgPSBbXTtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsaXN0Lmxlbmd0aDsgaSsrKSB7XG4gICAgdmFyIGl0ZW0gPSBsaXN0W2ldO1xuICAgIHZhciBpZCA9IG9wdGlvbnMuYmFzZSA/IGl0ZW1bMF0gKyBvcHRpb25zLmJhc2UgOiBpdGVtWzBdO1xuICAgIHZhciBjb3VudCA9IGlkQ291bnRNYXBbaWRdIHx8IDA7XG4gICAgdmFyIGlkZW50aWZpZXIgPSBcIlwiLmNvbmNhdChpZCwgXCIgXCIpLmNvbmNhdChjb3VudCk7XG4gICAgaWRDb3VudE1hcFtpZF0gPSBjb3VudCArIDE7XG4gICAgdmFyIGluZGV4QnlJZGVudGlmaWVyID0gZ2V0SW5kZXhCeUlkZW50aWZpZXIoaWRlbnRpZmllcik7XG4gICAgdmFyIG9iaiA9IHtcbiAgICAgIGNzczogaXRlbVsxXSxcbiAgICAgIG1lZGlhOiBpdGVtWzJdLFxuICAgICAgc291cmNlTWFwOiBpdGVtWzNdLFxuICAgICAgc3VwcG9ydHM6IGl0ZW1bNF0sXG4gICAgICBsYXllcjogaXRlbVs1XVxuICAgIH07XG4gICAgaWYgKGluZGV4QnlJZGVudGlmaWVyICE9PSAtMSkge1xuICAgICAgc3R5bGVzSW5ET01baW5kZXhCeUlkZW50aWZpZXJdLnJlZmVyZW5jZXMrKztcbiAgICAgIHN0eWxlc0luRE9NW2luZGV4QnlJZGVudGlmaWVyXS51cGRhdGVyKG9iaik7XG4gICAgfSBlbHNlIHtcbiAgICAgIHZhciB1cGRhdGVyID0gYWRkRWxlbWVudFN0eWxlKG9iaiwgb3B0aW9ucyk7XG4gICAgICBvcHRpb25zLmJ5SW5kZXggPSBpO1xuICAgICAgc3R5bGVzSW5ET00uc3BsaWNlKGksIDAsIHtcbiAgICAgICAgaWRlbnRpZmllcjogaWRlbnRpZmllcixcbiAgICAgICAgdXBkYXRlcjogdXBkYXRlcixcbiAgICAgICAgcmVmZXJlbmNlczogMVxuICAgICAgfSk7XG4gICAgfVxuICAgIGlkZW50aWZpZXJzLnB1c2goaWRlbnRpZmllcik7XG4gIH1cbiAgcmV0dXJuIGlkZW50aWZpZXJzO1xufVxuZnVuY3Rpb24gYWRkRWxlbWVudFN0eWxlKG9iaiwgb3B0aW9ucykge1xuICB2YXIgYXBpID0gb3B0aW9ucy5kb21BUEkob3B0aW9ucyk7XG4gIGFwaS51cGRhdGUob2JqKTtcbiAgdmFyIHVwZGF0ZXIgPSBmdW5jdGlvbiB1cGRhdGVyKG5ld09iaikge1xuICAgIGlmIChuZXdPYmopIHtcbiAgICAgIGlmIChuZXdPYmouY3NzID09PSBvYmouY3NzICYmIG5ld09iai5tZWRpYSA9PT0gb2JqLm1lZGlhICYmIG5ld09iai5zb3VyY2VNYXAgPT09IG9iai5zb3VyY2VNYXAgJiYgbmV3T2JqLnN1cHBvcnRzID09PSBvYmouc3VwcG9ydHMgJiYgbmV3T2JqLmxheWVyID09PSBvYmoubGF5ZXIpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgYXBpLnVwZGF0ZShvYmogPSBuZXdPYmopO1xuICAgIH0gZWxzZSB7XG4gICAgICBhcGkucmVtb3ZlKCk7XG4gICAgfVxuICB9O1xuICByZXR1cm4gdXBkYXRlcjtcbn1cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKGxpc3QsIG9wdGlvbnMpIHtcbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gIGxpc3QgPSBsaXN0IHx8IFtdO1xuICB2YXIgbGFzdElkZW50aWZpZXJzID0gbW9kdWxlc1RvRG9tKGxpc3QsIG9wdGlvbnMpO1xuICByZXR1cm4gZnVuY3Rpb24gdXBkYXRlKG5ld0xpc3QpIHtcbiAgICBuZXdMaXN0ID0gbmV3TGlzdCB8fCBbXTtcbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxhc3RJZGVudGlmaWVycy5sZW5ndGg7IGkrKykge1xuICAgICAgdmFyIGlkZW50aWZpZXIgPSBsYXN0SWRlbnRpZmllcnNbaV07XG4gICAgICB2YXIgaW5kZXggPSBnZXRJbmRleEJ5SWRlbnRpZmllcihpZGVudGlmaWVyKTtcbiAgICAgIHN0eWxlc0luRE9NW2luZGV4XS5yZWZlcmVuY2VzLS07XG4gICAgfVxuICAgIHZhciBuZXdMYXN0SWRlbnRpZmllcnMgPSBtb2R1bGVzVG9Eb20obmV3TGlzdCwgb3B0aW9ucyk7XG4gICAgZm9yICh2YXIgX2kgPSAwOyBfaSA8IGxhc3RJZGVudGlmaWVycy5sZW5ndGg7IF9pKyspIHtcbiAgICAgIHZhciBfaWRlbnRpZmllciA9IGxhc3RJZGVudGlmaWVyc1tfaV07XG4gICAgICB2YXIgX2luZGV4ID0gZ2V0SW5kZXhCeUlkZW50aWZpZXIoX2lkZW50aWZpZXIpO1xuICAgICAgaWYgKHN0eWxlc0luRE9NW19pbmRleF0ucmVmZXJlbmNlcyA9PT0gMCkge1xuICAgICAgICBzdHlsZXNJbkRPTVtfaW5kZXhdLnVwZGF0ZXIoKTtcbiAgICAgICAgc3R5bGVzSW5ET00uc3BsaWNlKF9pbmRleCwgMSk7XG4gICAgICB9XG4gICAgfVxuICAgIGxhc3RJZGVudGlmaWVycyA9IG5ld0xhc3RJZGVudGlmaWVycztcbiAgfTtcbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///72\n");

/***/ }),

/***/ 659:
/***/ ((module) => {

eval("\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU5LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanM/YjIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIG1lbW8gPSB7fTtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBnZXRUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0eXBlb2YgbWVtb1t0YXJnZXRdID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIHN0eWxlVGFyZ2V0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcih0YXJnZXQpO1xuXG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIHJldHVybiBoZWFkIG9mIGlmcmFtZSBpbnN0ZWFkIG9mIGlmcmFtZSBpdHNlbGZcbiAgICBpZiAod2luZG93LkhUTUxJRnJhbWVFbGVtZW50ICYmIHN0eWxlVGFyZ2V0IGluc3RhbmNlb2Ygd2luZG93LkhUTUxJRnJhbWVFbGVtZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUaGlzIHdpbGwgdGhyb3cgYW4gZXhjZXB0aW9uIGlmIGFjY2VzcyB0byBpZnJhbWUgaXMgYmxvY2tlZFxuICAgICAgICAvLyBkdWUgdG8gY3Jvc3Mtb3JpZ2luIHJlc3RyaWN0aW9uc1xuICAgICAgICBzdHlsZVRhcmdldCA9IHN0eWxlVGFyZ2V0LmNvbnRlbnREb2N1bWVudC5oZWFkO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgICAgICBzdHlsZVRhcmdldCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG1lbW9bdGFyZ2V0XSA9IHN0eWxlVGFyZ2V0O1xuICB9XG4gIHJldHVybiBtZW1vW3RhcmdldF07XG59XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gaW5zZXJ0QnlTZWxlY3RvcihpbnNlcnQsIHN0eWxlKSB7XG4gIHZhciB0YXJnZXQgPSBnZXRUYXJnZXQoaW5zZXJ0KTtcbiAgaWYgKCF0YXJnZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBmaW5kIGEgc3R5bGUgdGFyZ2V0LiBUaGlzIHByb2JhYmx5IG1lYW5zIHRoYXQgdGhlIHZhbHVlIGZvciB0aGUgJ2luc2VydCcgcGFyYW1ldGVyIGlzIGludmFsaWQuXCIpO1xuICB9XG4gIHRhcmdldC5hcHBlbmRDaGlsZChzdHlsZSk7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydEJ5U2VsZWN0b3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///659\n");

/***/ }),

/***/ 540:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTQwLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRTdHlsZUVsZW1lbnQuanM/ZGU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBpbnNlcnRTdHlsZUVsZW1lbnQob3B0aW9ucykge1xuICB2YXIgZWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgb3B0aW9ucy5zZXRBdHRyaWJ1dGVzKGVsZW1lbnQsIG9wdGlvbnMuYXR0cmlidXRlcyk7XG4gIG9wdGlvbnMuaW5zZXJ0KGVsZW1lbnQsIG9wdGlvbnMub3B0aW9ucyk7XG4gIHJldHVybiBlbGVtZW50O1xufVxubW9kdWxlLmV4cG9ydHMgPSBpbnNlcnRTdHlsZUVsZW1lbnQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///540\n");

/***/ }),

/***/ 56:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLGNBQWMsS0FBd0MsR0FBRyxzQkFBaUIsR0FBRyxDQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanM/ZGRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMoc3R5bGVFbGVtZW50KSB7XG4gIHZhciBub25jZSA9IHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gXCJ1bmRlZmluZWRcIiA/IF9fd2VicGFja19ub25jZV9fIDogbnVsbDtcbiAgaWYgKG5vbmNlKSB7XG4gICAgc3R5bGVFbGVtZW50LnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///56\n");

/***/ }),

/***/ 825:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n  var needLayer = typeof obj.layer !== \"undefined\";\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += \"}\";\n  }\n  if (obj.media) {\n    css += \"}\";\n  }\n  if (obj.supports) {\n    css += \"}\";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === \"undefined\") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///825\n");

/***/ }),

/***/ 113:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTEzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc3R5bGVUYWdUcmFuc2Zvcm0uanM/MWRkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzdHlsZVRhZ1RyYW5zZm9ybShjc3MsIHN0eWxlRWxlbWVudCkge1xuICBpZiAoc3R5bGVFbGVtZW50LnN0eWxlU2hlZXQpIHtcbiAgICBzdHlsZUVsZW1lbnQuc3R5bGVTaGVldC5jc3NUZXh0ID0gY3NzO1xuICB9IGVsc2Uge1xuICAgIHdoaWxlIChzdHlsZUVsZW1lbnQuZmlyc3RDaGlsZCkge1xuICAgICAgc3R5bGVFbGVtZW50LnJlbW92ZUNoaWxkKHN0eWxlRWxlbWVudC5maXJzdENoaWxkKTtcbiAgICB9XG4gICAgc3R5bGVFbGVtZW50LmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZVRleHROb2RlKGNzcykpO1xuICB9XG59XG5tb2R1bGUuZXhwb3J0cyA9IHN0eWxlVGFnVHJhbnNmb3JtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///113\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__(787);
/******/ 	
/******/ })()
;