/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 787:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

eval("\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\nvar injectStylesIntoStyleTag = __webpack_require__(72);\nvar injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleDomAPI.js\nvar styleDomAPI = __webpack_require__(825);\nvar styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertBySelector.js\nvar insertBySelector = __webpack_require__(659);\nvar insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\nvar setAttributesWithoutAttributes = __webpack_require__(56);\nvar setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertStyleElement.js\nvar insertStyleElement = __webpack_require__(540);\nvar insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleTagTransform.js\nvar styleTagTransform = __webpack_require__(113);\nvar styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);\n// EXTERNAL MODULE: ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/index.scss\nvar cjs_js_src = __webpack_require__(2);\n;// ./src/index.scss\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (styleTagTransform_default());\noptions.setAttributes = (setAttributesWithoutAttributes_default());\noptions.insert = insertBySelector_default().bind(null, \"head\");\noptions.domAPI = (styleDomAPI_default());\noptions.insertStyleElement = (insertStyleElement_default());\n\nvar update = injectStylesIntoStyleTag_default()(cjs_js_src/* default */.A, options);\n\n\n\n\n       /* harmony default export */ const src = (cjs_js_src/* default */.A && cjs_js_src/* default */.A.locals ? cjs_js_src/* default */.A.locals : undefined);\n\n;// ./src/index.js\n\n$(() => {\n  // 文字轮播\n  const bannerTextSwiper = new Swiper(\"#banner-text-swiper\", {\n    slidesPerView: 1,\n    spaceBetween: 10,\n    loop: true,\n    direction: \"vertical\",\n    allowTouchMove: false,\n    // 禁止手动滑动\n    autoplay: {\n      delay: 2500,\n      disableOnInteraction: false\n    }\n  });\n  if (window.innerWidth > 1280) {\n    $(\".document-wrapper .document-card\").on(\"mouseenter\", function () {\n      $(this).addClass(\"active\").siblings().removeClass(\"active\");\n    });\n    $(\".archive-wrapper .archive-card\").on(\"mouseenter\", function () {\n      $(this).addClass(\"active\").siblings().removeClass(\"active\");\n    });\n  } else {\n    $(\".document-wrapper .document-card\").addClass(\"active\");\n    $(\".archive-wrapper .archive-card\").addClass(\"active\");\n  }\n  const corruptionSwiper = new Swiper(\"#corruption-swiper\", {\n    slidesPerView: 1,\n    spaceBetween: 10,\n    loop: true,\n    breakpoints: {\n      1600: {\n        slidesPerView: 4\n      },\n      1280: {\n        slidesPerView: 3.5,\n        spaceBetween: 20\n      },\n      992: {\n        slidesPerView: 3,\n        spaceBetween: 20\n      },\n      576: {\n        slidesPerView: 2,\n        spaceBetween: 10\n      }\n    },\n    pagination: {\n      el: \".part-corruption .swiper-pagination-number\",\n      type: \"custom\",\n      renderCustom: function (swiper, current, total) {\n        return current + \"/\" + total;\n      }\n    },\n    navigation: {\n      nextEl: \".part-corruption .swiper-next\",\n      prevEl: \".part-corruption .swiper-prev\"\n    }\n  });\n  if (window.innerWidth < 1280) {\n    const cardSwiper = new Swiper(\"#card-swiper\", {\n      slidesPerView: 1,\n      spaceBetween: 30,\n      loop: true,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      breakpoints: {\n        768: {\n          slidesPerView: 3,\n          spaceBetween: 30\n        }\n      },\n      pagination: {\n        el: \"#card-swiper .swiper-pagination\",\n        clickable: true\n      }\n    });\n  }\n\n  // 进度条设置\n\n  const storiesSwiper = new Swiper(\"#stories-swiper\", {\n    slidesPerView: 1,\n    centeredSlides: true,\n    spaceBetween: 30,\n    loop: true,\n    pagination: {\n      el: \"#stories-swiper .swiper-pagination\",\n      clickable: true\n    },\n    breakpoints: {\n      1280: {\n        slidesPerView: 1.9,\n        spaceBetween: 30\n      }\n    }\n  });\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///787\n");

/***/ }),

/***/ 2:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(354);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(314);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `*{margin:0;padding:0;box-sizing:border-box}main{background-color:#f5f8ff;color:#000}@media(max-width: 1280px){main{background-color:#f4f7ff}}main video{height:100%;width:100%;object-fit:cover;line-height:0;font-size:0;filter:grayscale(0);clip-path:fill-box}main h1,main h2,main h3,main h4,main h5,main h6,main p,main div,main span,main ul,main li{margin-bottom:0}main h2{text-align:center;font-weight:800;font-size:2.25rem}main h1,main h2,main h3{text-align:center}main h2{font-size:2.25rem;font-weight:800}main .opacity-7{opacity:.7}main .blue-text{color:#2e8eff}main .gray-text{color:#454545}main .btn-wrapper{display:flex;justify-content:center;gap:1rem}@media(max-width: 768px){main .btn-wrapper{flex-direction:column;gap:8px}}main .btn-wrapper .btn{margin:0;border-radius:8px;text-transform:capitalize;display:flex;align-items:center;justify-content:center;min-width:158px;gap:.5rem}main .btn-wrapper .btn.btn-lg{min-width:228px}@media(max-width: 768px){main .btn-wrapper .btn{display:block;vertical-align:baseline}}main .btn-download{background:linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);border:none;color:#fff;background-color:#0458ff}main .btn-download:hover{color:#fff;background:linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)),linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),linear-gradient(0deg, #0055fb, #0055fb);background-color:#0458ff}main .swiper-pagination{bottom:-4px !important}main .swiper-pagination .swiper-pagination-bullet{width:8px;height:8px;background-color:#c2cee9;opacity:1}main .swiper-pagination .swiper-pagination-bullet-active{width:64px;background:linear-gradient(89.5deg, #0458ff 0%, #0499ff 100%);border-radius:8px}main .part-banner{text-align:center;background:url(https://images.wondershare.com/repairit/images2025/File-Repair/banner-bg.jpg) no-repeat center center/cover;margin-bottom:-3rem;position:relative;z-index:1}main .part-banner .sub-title{display:flex;gap:8px;align-items:center;justify-content:center}main .part-banner .sub-title .blue-tip{background:linear-gradient(100.2deg, #0055fb 0%, #00c1ff 100%),linear-gradient(0deg, #d9d9d9, #d9d9d9);border-radius:24px;padding:4px 12px;font-weight:700;font-size:1.25rem;line-height:100%;color:#fff}main .part-banner h1{background:linear-gradient(95.44deg, #0055fb -28.33%, #00c1ff 96.36%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;text-fill-color:transparent}main .part-banner .feature-list{display:inline-flex;align-items:center;justify-content:center;padding:4px 8px;border-radius:1.5rem;border:1.5px solid #0055fb;min-width:602px;flex-wrap:wrap}@media(max-width: 768px){main .part-banner .feature-list{min-width:unset}}main .part-banner .feature-list .feature-item{flex:1;font-weight:600;font-size:1.125rem;color:#0055fb;padding:0 1rem;position:relative}main .part-banner .feature-list .feature-item.text-gradient{background:linear-gradient(92.01deg, #e046ff 4.9%, #7970ff 52.55%, #0066ff 99.48%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;text-fill-color:transparent}@media(max-width: 768px){main .part-banner .feature-list .feature-item{flex:1 1 50%}}main .part-banner .feature-list .feature-item:not(:last-child)::after{content:\"\";display:inline-block;width:1px;height:80%;background-color:#0055fb;top:50%;transform:translateY(-50%);position:absolute;right:0}@media(max-width: 768px){main .part-banner .feature-list .feature-item:not(:last-child)::after{content:unset}}@keyframes banner-icon-animation{0%{transform:translateY(0)}50%{transform:translateY(10px)}100%{transform:translateY(0)}}@keyframes banner-diffuse1{0%{transform:scale(0.8);opacity:.1}60%{transform:scale(0.9);opacity:.8}100%{transform:scale(1);opacity:0}}main .part-banner .white-wave1{width:15.93%;aspect-ratio:306/214;border-radius:1.5rem;border:3px solid #fff;z-index:1;position:absolute;left:8.45%;top:37%;animation:banner-diffuse1 2s linear infinite}main .part-banner .white-wave2{width:15.93%;aspect-ratio:306/214;border-radius:1.5rem;border:3px solid #fff;z-index:1;position:absolute;right:4.79%;top:30%;animation:banner-diffuse1 2s linear infinite}main .part-banner .banner-icon{position:absolute;animation:banner-icon-animation 2s linear infinite;z-index:3}main .part-banner .banner-icon:nth-child(1){animation-delay:0s}main .part-banner .banner-icon:nth-child(2){animation-delay:.5s}main .part-banner .banner-icon:nth-child(3){animation-delay:1s}main .part-banner #banner-text-swiper{height:22px;overflow:hidden}@media(max-width: 576px){main .part-banner #banner-text-swiper{height:44px}}main .part-banner .detail-item{display:flex;align-items:flex-start;justify-content:center;gap:10px;font-size:14px}main .part-logos{position:relative;z-index:2}main .part-logos .logos-wrapper{background-color:#fff;border-radius:1rem;padding:2rem .5rem;display:flex;align-items:center;justify-content:center}@media(max-width: 768px){main .part-logos .logos-wrapper{padding:1.5rem .5rem}}main .part-logos .logos-wrapper .logo-item{flex:1 1 33%;max-height:2rem;text-align:center}main .part-logos .logos-wrapper .logo-item:not(:last-child){border-right:1px solid rgba(0,0,0,.1)}main .part-logos .logos-wrapper .logo-item img{max-width:100%;max-height:2rem;object-fit:contain}@media(max-width: 768px){main .part-logos .logos-wrapper .logo-item img{max-height:1.1rem}}@keyframes right-icon-animation{0%{transform:translateX(0)}50%{transform:translateX(8px)}100%{transform:translateX(0)}}main .part-productivity .document-wrapper{border-radius:1rem;overflow:hidden;background-color:#fff;padding:2.125rem}@media(max-width: 576px){main .part-productivity .document-wrapper{padding:1rem}}main .part-productivity .document-wrapper .wrapper-top{display:flex;justify-content:space-between;align-items:flex-end;gap:3rem}@media(max-width: 768px){main .part-productivity .document-wrapper .wrapper-top{flex-direction:column;gap:1rem;align-items:stretch}}main .part-productivity .document-wrapper .wrapper-top .left-content{max-width:866px}main .part-productivity .document-wrapper .wrapper-top .left-content .wrapper-title{font-size:2rem;font-weight:800;color:#000;text-align:left}main .part-productivity .document-wrapper .wrapper-top .btn-wrapper{flex-shrink:0}main .part-productivity .document-wrapper .card-wrapper{display:flex;gap:16px;flex-wrap:nowrap;margin-top:2.125rem;height:422px}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper{flex-wrap:wrap;height:auto}}main .part-productivity .document-wrapper .card-wrapper .document-card{display:block;border-radius:1rem;position:relative;overflow:hidden;flex:1 1 15%;background-repeat:no-repeat;background-position:bottom center,center center;background-size:auto 45%,100% 100%;color:#000;text-decoration:none;transition:all .3s cubic-bezier(0.05, 0.61, 0.41, 0.95)}@media(any-hover: hover){main .part-productivity .document-wrapper .card-wrapper .document-card:hover .right-icon{animation:right-icon-animation 1s linear infinite}}@media(max-width: 1600px){main .part-productivity .document-wrapper .card-wrapper .document-card{background-size:auto 36%,100% 100%}}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .document-card{flex:0 1 calc(50% - 8px)}}@media(max-width: 768px){main .part-productivity .document-wrapper .card-wrapper .document-card{flex:1 1 100%}}main .part-productivity .document-wrapper .card-wrapper .document-card.pdf-card{background-color:#ffd7db}main .part-productivity .document-wrapper .card-wrapper .document-card.pdf-card.active{background-image:url(https://images.wondershare.com/repairit/images2025/File-Repair/pdf-repair.png),linear-gradient(100.64deg, #ffd7db 33.42%, #fefff9 132.61%)}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .document-card.pdf-card.active{background-image:linear-gradient(100.64deg, #ffd7db 33.42%, #fefff9 132.61%);background-size:100% 100%}}main .part-productivity .document-wrapper .card-wrapper .document-card.word-card{background-color:#f0f9ff}main .part-productivity .document-wrapper .card-wrapper .document-card.word-card.active{background-image:url(https://images.wondershare.com/repairit/images2025/File-Repair/word-repair.png),linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%)}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .document-card.word-card.active{background-image:linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);background-size:100% 100%}}main .part-productivity .document-wrapper .card-wrapper .document-card.excel-card{background-color:#f0fff5}main .part-productivity .document-wrapper .card-wrapper .document-card.excel-card.active{background-image:url(https://images.wondershare.com/repairit/images2025/File-Repair/excel-repair.png),linear-gradient(304.71deg, #f0fff5 17.16%, #c3f9d4 77.96%)}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .document-card.excel-card.active{background-image:linear-gradient(304.71deg, #f0fff5 17.16%, #c3f9d4 77.96%);background-size:100% 100%}}main .part-productivity .document-wrapper .card-wrapper .document-card.ppt-card{background-color:#fff9f0}main .part-productivity .document-wrapper .card-wrapper .document-card.ppt-card.active{background-image:url(https://images.wondershare.com/repairit/images2025/File-Repair/powerpoint-repair.png),linear-gradient(128.81deg, #ffccb0 2.03%, #fff6e9 88.07%)}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .document-card.ppt-card.active{background-image:linear-gradient(128.81deg, #ffccb0 2.03%, #fff6e9 88.07%);background-size:100% 100%}}main .part-productivity .document-wrapper .card-wrapper .document-card.iwork-card{background-color:#f0f9ff}main .part-productivity .document-wrapper .card-wrapper .document-card.iwork-card.active{background-image:url(https://images.wondershare.com/repairit/images2025/File-Repair/iwork-repair.png),linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%)}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .document-card.iwork-card.active{background-image:linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);background-size:100% 100%}}@media(min-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .document-card.active{flex:1 1 40%}main .part-productivity .document-wrapper .card-wrapper .document-card.active .show-content{display:block}main .part-productivity .document-wrapper .card-wrapper .document-card.active .hide-content{display:none}}main .part-productivity .document-wrapper .card-wrapper .hide-content{position:absolute;left:0;top:0;width:100%;padding:1.75rem .625rem;display:flex;height:100%;flex-direction:column;align-items:center;justify-content:flex-start;text-align:center;z-index:1;transition:all .3s cubic-bezier(0.05, 0.61, 0.41, 0.95)}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .hide-content{display:none}}main .part-productivity .document-wrapper .card-wrapper .hide-content .card-hide-icon{width:5.875rem;margin-top:auto;margin-bottom:auto}main .part-productivity .document-wrapper .card-wrapper .hide-content .card-hide-title{font-weight:500;font-size:1.25rem}main .part-productivity .document-wrapper .card-wrapper .show-content{display:none;width:100%;height:100%;padding:1.25rem 1rem}@media(max-width: 1280px){main .part-productivity .document-wrapper .card-wrapper .show-content{display:block;padding-bottom:0;display:flex;flex-direction:column}}main .part-productivity .document-wrapper .card-wrapper .show-content .card-show-title{font-size:1.5rem;font-weight:800}main .part-productivity .document-wrapper .card-wrapper .show-content .card-show-desc{list-style:none;padding-left:1rem;max-width:417px}main .part-productivity .document-wrapper .card-wrapper .show-content .card-show-desc li{position:relative;font-size:1rem;line-height:150%}main .part-productivity .document-wrapper .card-wrapper .show-content .card-show-desc li::before{content:\"•\";position:absolute;left:-1rem;top:-0.15rem}main .part-productivity .archive-wrapper{border-radius:1rem;overflow:hidden;background-color:#fff;padding:2.125rem}@media(max-width: 576px){main .part-productivity .archive-wrapper{padding:1rem}}main .part-productivity .archive-wrapper .wrapper-top{display:flex;justify-content:space-between;align-items:flex-end;gap:3rem}@media(max-width: 768px){main .part-productivity .archive-wrapper .wrapper-top{flex-direction:column;gap:1rem;align-items:stretch}}main .part-productivity .archive-wrapper .wrapper-top .left-content{max-width:866px}main .part-productivity .archive-wrapper .wrapper-top .left-content .wrapper-title{font-size:2rem;font-weight:800;color:#000;text-align:left}main .part-productivity .archive-wrapper .wrapper-top .btn-wrapper{flex-shrink:0}main .part-productivity .archive-wrapper .card-wrapper{display:flex;gap:16px;flex-wrap:nowrap;margin-top:2.125rem;min-height:283px}@media(max-width: 1600px){main .part-productivity .archive-wrapper .card-wrapper{min-height:340px}}@media(max-width: 1280px){main .part-productivity .archive-wrapper .card-wrapper{flex-wrap:wrap}}main .part-productivity .archive-wrapper .card-wrapper .archive-card{display:block;border-radius:1rem;position:relative;overflow:hidden;padding:1rem;flex:1 1 31.86%;background-repeat:no-repeat;background-position:right 5% bottom 50%,center center;background-size:auto 60%,100% 100%;color:#636363;text-decoration:none;transition:all .3s cubic-bezier(0.05, 0.61, 0.41, 0.95)}@media(any-hover: hover){main .part-productivity .archive-wrapper .card-wrapper .archive-card:hover .right-icon{animation:right-icon-animation 1s linear infinite}}main .part-productivity .archive-wrapper .card-wrapper .archive-card .archive-card-title{font-weight:800;font-size:1.5rem;color:#000}main .part-productivity .archive-wrapper .card-wrapper .archive-card .archive-card-content{max-width:460px;padding-right:1.25rem}@media(max-width: 768px){main .part-productivity .archive-wrapper .card-wrapper .archive-card .archive-card-content{max-width:unset;padding-right:0}}main .part-productivity .archive-wrapper .card-wrapper .archive-card .archive-card-content .archive-card-desc{font-size:14px;color:inherit;margin-bottom:16px}main .part-productivity .archive-wrapper .card-wrapper .archive-card .archive-card-content .archive-card-list{list-style:none;padding-left:1rem}main .part-productivity .archive-wrapper .card-wrapper .archive-card .archive-card-content .archive-card-list li{position:relative;font-size:14px;line-height:150%;color:inherit}main .part-productivity .archive-wrapper .card-wrapper .archive-card .archive-card-content .archive-card-list li::before{content:\"•\";position:absolute;left:-1rem;top:-0.15rem}main .part-productivity .archive-wrapper .card-wrapper .archive-card.rar-repair{background-color:#eeefff}main .part-productivity .archive-wrapper .card-wrapper .archive-card.rar-repair.active{background-image:url(https://images.wondershare.com/repairit/images2025/File-Repair/rar-repair.png),linear-gradient(280.74deg, #eeeeff 50.38%, #a3b9ff 140.34%)}@media(max-width: 768px){main .part-productivity .archive-wrapper .card-wrapper .archive-card.rar-repair.active{background-image:linear-gradient(280.74deg, #eeeeff 50.38%, #a3b9ff 140.34%);background-size:100% 100%}}main .part-productivity .archive-wrapper .card-wrapper .archive-card.zip-repair{background-color:#fff5d0}main .part-productivity .archive-wrapper .card-wrapper .archive-card.zip-repair.active{background-image:url(https://images.wondershare.com/repairit/images2025/File-Repair/zip-repair.png),linear-gradient(90deg, #ffe3a7 0%, #fff8d3 100%);background-size:auto 100%,100% 100%}@media(max-width: 768px){main .part-productivity .archive-wrapper .card-wrapper .archive-card.zip-repair.active{background-image:linear-gradient(90deg, #ffe3a7 0%, #fff8d3 100%);background-size:100% 100%}}main .part-productivity .archive-wrapper .card-wrapper .archive-card.active{flex:1 1 68.14%;color:#000}main .part-productivity .engineering-wrapper{border-radius:1rem;overflow:hidden;background-color:#fff;padding:2.125rem}@media(max-width: 576px){main .part-productivity .engineering-wrapper{padding:1rem}}main .part-productivity .engineering-wrapper .wrapper-top{display:flex;justify-content:space-between;align-items:flex-end;gap:3rem}@media(max-width: 768px){main .part-productivity .engineering-wrapper .wrapper-top{flex-direction:column;gap:1rem;align-items:stretch}}main .part-productivity .engineering-wrapper .wrapper-top .left-content{max-width:866px}main .part-productivity .engineering-wrapper .wrapper-top .left-content .wrapper-title{font-size:2rem;font-weight:800;color:#000;text-align:left}main .part-productivity .engineering-wrapper .wrapper-top .btn-wrapper{flex-shrink:0}main .part-productivity .engineering-wrapper .card-wrapper{display:flex;gap:16px;flex-wrap:nowrap;margin-top:2.125rem;min-height:315px}@media(max-width: 1280px){main .part-productivity .engineering-wrapper .card-wrapper{flex-wrap:wrap}}main .part-productivity .engineering-wrapper .card-wrapper .engineering-card{display:block;border-radius:1rem;position:relative;overflow:hidden;padding:1rem;flex:1 1 31.86%;color:#636363;text-decoration:none;transition:all .3s cubic-bezier(0.05, 0.61, 0.41, 0.95)}@media(any-hover: hover){main .part-productivity .engineering-wrapper .card-wrapper .engineering-card:hover .right-icon{animation:right-icon-animation 1s linear infinite}}main .part-productivity .engineering-wrapper .card-wrapper .engineering-card .engineering-card-title{font-weight:800;font-size:1.5rem;color:#000}main .part-productivity .engineering-wrapper .card-wrapper .engineering-card .engineering-card-content{max-width:425px;padding-right:1.25rem}@media(max-width: 768px){main .part-productivity .engineering-wrapper .card-wrapper .engineering-card .engineering-card-content{padding-right:0;max-width:unset}}main .part-productivity .engineering-wrapper .card-wrapper .engineering-card .engineering-card-content .engineering-card-desc{font-size:14px;color:#636363;margin-bottom:16px}main .part-productivity .engineering-wrapper .card-wrapper .engineering-card.adobe-repair{flex:1 1 68.14%;background-image:url(https://images.wondershare.com/repairit/images2025/File-Repair/adobe-file-repair.png),linear-gradient(100.16deg, #ddefff 63.73%, #a3d3ff 119.29%);background-repeat:no-repeat;background-position:right 6% bottom 15%,center center;background-size:auto 80%,100% 100%}@media(max-width: 768px){main .part-productivity .engineering-wrapper .card-wrapper .engineering-card.adobe-repair{background-image:linear-gradient(100.16deg, #ddefff 63.73%, #a3d3ff 119.29%);background-size:100% 100%}}main .part-productivity .engineering-wrapper .card-wrapper .engineering-card.autocad-repair{background-color:#ebf6ff;flex:1 1 50%}main .part-productivity .engineering-wrapper .card-wrapper .engineering-card.sketchup-repair{background-color:#ebf6ff;flex:1 1 50%}main .part-productivity .engineering-wrapper .card-wrapper .autocad-sketchup-wrapper{display:flex;flex-direction:column;gap:16px}@media(max-width: 1280px){main .part-productivity .engineering-wrapper .card-wrapper .autocad-sketchup-wrapper{width:100%}}main .part-corruption{background-color:#fff}main .part-corruption .wrapper-top{display:flex;justify-content:space-between;gap:3rem}@media(max-width: 768px){main .part-corruption .wrapper-top{flex-direction:column;gap:1rem;align-items:stretch}}main .part-corruption .wrapper-top .wrapper-title{font-weight:800;color:#000;text-align:left}main .part-corruption .swiper-corruption-box{margin-right:-20vw}@media(max-width: 576px){main .part-corruption .swiper-corruption-box{margin-right:unset}}@media(min-width: 2000px){main .part-corruption .swiper-box{margin-right:-30vw}}main .part-corruption .corruption-box{border-radius:1rem;overflow:hidden;position:relative}main .part-corruption .corruption-box .corruption-box-title{position:absolute;bottom:6.4%;left:0;font-size:1.5rem;color:#000;width:100%;text-align:center}main .part-corruption .corruption-box:hover{box-shadow:0px 0px 12px 0px #00d1ff4d}main .part-corruption .corruption-box:hover .hide-box{transform:translateY(0)}main .part-corruption .hide-box{position:absolute;width:100%;height:100%;top:0;left:0;border-radius:1rem;transform:translateY(110%);transition:all .3s ease-in-out;padding:2.375rem;background:linear-gradient(147.08deg, #ffffff 34.81%, #c3ecff 100.92%);z-index:1}@media(max-width: 992px){main .part-corruption .hide-box{padding:1rem}}main .part-corruption .hide-box::after{content:\"\";position:absolute;inset:0;padding:1px;border-radius:1rem;background:linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);mask:linear-gradient(#000 0 0) content-box,linear-gradient(#000 0 0);mask-composite:exclude;pointer-events:none}main .part-corruption .hide-box .hide-box-title{font-weight:800;font-size:1.5rem;margin-bottom:.5rem}main .part-corruption .hide-box .hide-box-desc{font-size:1.25rem;color:#636363}@media(max-width: 992px){main .part-corruption .hide-box .hide-box-desc{font-size:1rem}}main .part-corruption .swiper-pagination-box{display:flex;align-items:center;gap:1rem;padding-bottom:1.25rem}@media(max-width: 576px){main .part-corruption .swiper-pagination-box{justify-content:center}}main .part-corruption .swiper-pagination-box .swiper-prev,main .part-corruption .swiper-pagination-box .swiper-next{cursor:pointer}main .part-corruption .swiper-pagination-box .swiper-prev:hover,main .part-corruption .swiper-pagination-box .swiper-next:hover{color:#0496ff}main .part-corruption .swiper-pagination-box .swiper-pagination-number{display:inline-flex;width:auto}@media(min-width: 1280px){main .part-corruption #card-swiper .swiper-wrapper{gap:1.875rem;flex-wrap:nowrap}main .part-corruption #card-swiper .swiper-wrapper .swiper-slide{flex:1 1 calc(20% - 1.875rem)}}main .part-corruption .card-box{padding:2rem 1.5rem;border-radius:1.5rem;background-color:#eaf8ff;overflow:hidden;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:.5rem;height:100%}main .part-corruption .card-box .card-icon{width:6rem}main .part-corruption .card-box .card-title{font-size:1.125rem;font-weight:800;color:#000}main .part-corruption .card-box .card-desc{font-size:14px;color:#000;opacity:.6;text-align:center}main .part-steps{background:radial-gradient(82.32% 135.56% at 31.17% -26.53%, #b6e0ff 0%, #e3f3ff 50%, #e0f2ff 100%)}main .part-steps .nav-item{padding:1.5rem;border-radius:12px;width:100%}main .part-steps .nav-item.active{background-color:#fff}main .part-steps .nav-item .nav-item-content{display:flex;align-items:flex-start}main .part-faq .accordion-box{background-color:#fff;border-radius:1.5rem;padding:.5rem 4rem}@media(max-width: 992px){main .part-faq .accordion-box{padding:.5rem 2rem}}@media(max-width: 768px){main .part-faq .accordion-box{padding:.5rem 1rem}}main .part-faq .accordion-box .accordion-item{padding:1.5rem}main .part-faq .accordion-box .accordion-item:not(:last-child){border-bottom:1px solid rgba(0,0,0,.1)}main .part-faq .accordion-box .accordion-item svg{transition:all .2s linear}@media(max-width: 768px){main .part-faq .accordion-box .accordion-item svg{width:1rem}}@media(max-width: 768px){main .part-faq .accordion-box .accordion-item{padding:1rem .5rem}}main .part-faq .accordion-box .accordion-item [aria-expanded=true] svg{transform:rotate(180deg)}main .part-faq .accordion-box .accordion-item .serial-number{display:inline-flex;width:22px;height:22px;align-items:center;justify-content:center;color:#fff;background:linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);border-radius:50%;margin-right:8px;font-size:1rem;font-weight:800;flex-shrink:0}@media(max-width: 768px){main .part-faq .accordion-box .accordion-item .serial-number{width:16px;height:16px;color:#fff}}main .part-faq .accordion-box .accordion-item .faq-detail{font-size:14px;padding-top:1rem;opacity:.7;padding-left:30px;padding-right:32px}@media(max-width: 768px){main .part-faq .accordion-box .accordion-item .faq-detail{padding-left:20px;padding-right:16px}}main .part-stories .swiper{margin:2rem}@media(max-width: 768px){main .part-stories .swiper{margin:.5rem}}@media(min-width: 768px){main .part-stories .swiper-slide .user-wrapper::before{content:\"\";position:absolute;width:100%;height:100%;left:0;top:0;background-color:rgba(210,223,255,.3);z-index:2}main .part-stories .swiper-slide.swiper-slide-active .user-wrapper::before{content:unset}}main .part-stories .user-wrapper{border-radius:1rem;overflow:hidden;position:relative}@media(max-width: 768px){main .part-stories .user-wrapper::before{content:\"\";position:absolute;width:100%;height:100%;left:0;top:0;background-color:rgba(0,0,0,.6)}}main .part-stories .user-wrapper .user-story{position:absolute;right:4rem;top:3rem;max-width:360px}@media(max-width: 768px){main .part-stories .user-wrapper .user-story{right:0;top:0;width:100%;height:100%;padding:8px;color:#fff}}main .part-stories .user-wrapper .user-story .user-occupation{font-size:14px;margin-bottom:16px}main .part-stories .user-wrapper .user-story .user-comments{font-size:12px;color:rgba(0,0,0,.7)}@media(max-width: 768px){main .part-stories .user-wrapper .user-story .user-comments{color:#fff}}main .part-stories .swiper-pagination{bottom:-2.5rem !important}main .part-links .part-links-line{height:100%;display:flex;flex-direction:column;justify-content:center}main .part-links .line-border{border-right:1px solid rgba(0,0,0,.3);border-left:1px solid rgba(0,0,0,.3)}@media(max-width: 1280px){main .part-links .line-border{border-right:unset}}@media(max-width: 768px){main .part-links .line-border{border-left:unset}}main .part-links .text-link{font-size:14px;color:rgba(0,0,0,.7);margin-top:1.5rem;display:block;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}main .part-links .text-link:hover{color:#0055fb}main .part-links .part-links-videos{height:100%;display:flex;flex-direction:column;justify-content:space-between}main .part-links .part-links-videos .video-wrapper{border-radius:.75rem}@media(max-width: 1280px){main .part-links .part-links-videos{flex-direction:row;padding-top:2rem}}@media(max-width: 576px){main .part-links .part-links-videos{display:block}}main .part-links .text-line4{display:-webkit-box;-webkit-line-clamp:4;-webkit-box-orient:vertical;overflow:hidden}main .part-footer{background-image:url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);background-size:cover;background-position:center;background-repeat:no-repeat}@media(max-width: 576px){main .part-footer .display-2{font-size:2.5rem}}main .part-footer-logo{height:4rem;width:14.5rem;margin:0 auto}@media(max-width: 576px){main .part-footer .btn-outline-action{background-color:#fff;vertical-align:text-bottom}}main .part-advanced .advanced-item{border-radius:1rem;background-color:#fff;overflow:hidden;height:100%}main .part-advanced .advanced-item .compare-before{position:absolute;width:50%;height:100%;left:0;top:0;background-size:auto 100%;background-repeat:no-repeat;z-index:2}main .part-advanced .advanced-item .compare-before::after{content:\"\";width:2px;height:100%;background:#fff;position:absolute;right:0;top:0}main .part-advanced .advanced-item .compare-before::before{content:\"\";background-image:url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);background-size:contain;background-position:center;width:4.25rem;height:2.5rem;position:absolute;right:0;top:50%;transform:translate(50%, -50%);z-index:3}@keyframes changeWidth{0%{width:0}50%{width:100%}100%{width:0}}main .part-advanced .advanced-item .compare-before.compare-before-1{background-image:url(https://images.wondershare.com/repairit/images2024/index/File-Repair-before.png);animation:changeWidth 8s linear infinite;aspect-ratio:328/192}main .part-advanced .advanced-item .compare-before.compare-before-2{background-image:url(https://images.wondershare.com/repairit/images2024/index/Video-Repair-before.png);animation:changeWidth 7s linear infinite}main .part-advanced .advanced-item .compare-before.compare-before-3{background-image:url(https://images.wondershare.com/repairit/images2024/index/Photo-Repair-before.png);animation:changeWidth 7s linear infinite 1s}main .part-advanced .advanced-item .compare-before.compare-before-4{background-image:url(https://images.wondershare.com/repairit/images2024/index/Audio-Repair-before.png);animation:changeWidth 6s linear infinite}main .part-advanced .advanced-item .slider{-webkit-appearance:none;appearance:none;outline:0;margin:0;background:0 0;z-index:3;position:absolute;width:100%;height:100%;top:0;left:0}main .part-advanced .advanced-item .slider::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;width:2px;height:auto;background:transparent}main .part-advanced .advanced-item .item-link{color:#000}main .part-advanced .advanced-item .item-link .normal-arrow{display:inline}main .part-advanced .advanced-item .item-link .active-arrow{display:none}main .part-advanced .advanced-item .item-link .arrow-icon{width:2rem;display:inline-block}@media(max-width: 576px){main .part-advanced .advanced-item .item-link .arrow-icon{display:block}}main .part-advanced .advanced-item .item-link:hover{color:#0458ff}main .part-advanced .advanced-item .item-link:hover .normal-arrow{display:none}main .part-advanced .advanced-item .item-link:hover .active-arrow{display:inline}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/index.scss\"],\"names\":[],\"mappings\":\"AAAA,EAAA,QACE,CAAA,SACA,CAAA,qBACA,CAAA,KAGF,wBACE,CAAA,UACA,CAAA,0BAEA,KAJF,wBAKI,CAAA,CAAA,WAEF,WACE,CAAA,UACA,CAAA,gBACA,CAAA,aACA,CAAA,WACA,CAAA,mBAEA,CAAA,kBAEA,CAAA,0FAGF,eAWE,CAAA,QAGF,iBACE,CAAA,eACA,CAAA,iBACA,CAAA,wBAGF,iBAGE,CAAA,QAGF,iBACE,CAAA,eACA,CAAA,gBAGF,UACE,CAAA,gBAGF,aACE,CAAA,gBAGF,aACE,CAAA,kBAGF,YACE,CAAA,sBACA,CAAA,QACA,CAAA,yBAGF,kBACE,qBACE,CAAA,OACA,CAAA,CAAA,uBAIJ,QACE,CAAA,iBACA,CAAA,yBACA,CAAA,YACA,CAAA,kBACA,CAAA,sBACA,CAAA,eACA,CAAA,SACA,CAAA,8BACA,eACE,CAAA,yBAIJ,uBACE,aACE,CAAA,uBACA,CAAA,CAAA,mBAIJ,8DACE,CAAA,WACA,CAAA,UACA,CAAA,wBACA,CAAA,yBAGF,UACE,CAAA,qLACA,CAAA,wBAEA,CAAA,wBAGF,sBACE,CAAA,kDAGF,SACE,CAAA,UACA,CAAA,wBACA,CAAA,SACA,CAAA,yDAGF,UACE,CAAA,6DACA,CAAA,iBACA,CAAA,kBAEF,iBACE,CAAA,0HACA,CAAA,mBACA,CAAA,iBACA,CAAA,SACA,CAAA,6BACA,YACE,CAAA,OACA,CAAA,kBACA,CAAA,sBACA,CAAA,uCACA,sGACE,CAAA,kBACA,CAAA,gBACA,CAAA,eACA,CAAA,iBACA,CAAA,gBACA,CAAA,UACA,CAAA,qBAGJ,qEACE,CAAA,4BACA,CAAA,mCACA,CAAA,oBACA,CAAA,2BACA,CAAA,gCAEF,mBACE,CAAA,kBACA,CAAA,sBACA,CAAA,eACA,CAAA,oBACA,CAAA,0BACA,CAAA,eACA,CAAA,cACA,CAAA,yBACA,gCATF,eAUI,CAAA,CAAA,8CAEF,MACE,CAAA,eACA,CAAA,kBACA,CAAA,aACA,CAAA,cACA,CAAA,iBACA,CAAA,4DACA,kFACE,CAAA,4BACA,CAAA,mCACA,CAAA,oBACA,CAAA,2BACA,CAAA,yBAEF,8CAdF,YAeI,CAAA,CAAA,sEAEF,UACE,CAAA,oBACA,CAAA,SACA,CAAA,UACA,CAAA,wBACA,CAAA,OACA,CAAA,0BACA,CAAA,iBACA,CAAA,OACA,CAAA,yBACA,sEAVF,aAWI,CAAA,CAAA,iCAKR,GACE,uBACE,CAAA,IAEF,0BACE,CAAA,KAEF,uBACE,CAAA,CAAA,2BAGJ,GACE,oBACE,CAAA,UACA,CAAA,IAGF,oBACE,CAAA,UACA,CAAA,KAGF,kBACE,CAAA,SACA,CAAA,CAAA,+BAGJ,YACE,CAAA,oBACA,CAAA,oBACA,CAAA,qBACA,CAAA,SACA,CAAA,iBACA,CAAA,UACA,CAAA,OACA,CAAA,4CACA,CAAA,+BAEF,YACE,CAAA,oBACA,CAAA,oBACA,CAAA,qBACA,CAAA,SACA,CAAA,iBACA,CAAA,WACA,CAAA,OACA,CAAA,4CACA,CAAA,+BAEF,iBACE,CAAA,kDACA,CAAA,SACA,CAAA,4CACA,kBACE,CAAA,4CAEF,mBACE,CAAA,4CAEF,kBACE,CAAA,sCAGJ,WACE,CAAA,eACA,CAAA,yBACA,sCAHF,WAII,CAAA,CAAA,+BAGJ,YACE,CAAA,sBACA,CAAA,sBACA,CAAA,QACA,CAAA,cACA,CAAA,iBAGJ,iBACE,CAAA,SACA,CAAA,gCACA,qBACE,CAAA,kBACA,CAAA,kBACA,CAAA,YACA,CAAA,kBACA,CAAA,sBACA,CAAA,yBAGF,gCACE,oBACE,CAAA,CAAA,2CAIJ,YACE,CAAA,eACA,CAAA,iBACA,CAAA,4DAGF,qCACE,CAAA,+CAGF,cACE,CAAA,eACA,CAAA,kBACA,CAAA,yBAGF,+CACE,iBACE,CAAA,CAAA,gCAMJ,GACE,uBACE,CAAA,IAEF,yBACE,CAAA,KAEF,uBACE,CAAA,CAAA,0CAGJ,kBACE,CAAA,eACA,CAAA,qBACA,CAAA,gBACA,CAAA,yBACA,0CALF,YAMI,CAAA,CAAA,uDAGF,YACE,CAAA,6BACA,CAAA,oBACA,CAAA,QACA,CAAA,yBACA,uDALF,qBAMI,CAAA,QACA,CAAA,mBACA,CAAA,CAAA,qEAEF,eACE,CAAA,oFACA,cACE,CAAA,eACA,CAAA,UACA,CAAA,eACA,CAAA,oEAGJ,aACE,CAAA,wDAGJ,YACE,CAAA,QACA,CAAA,gBACA,CAAA,mBACA,CAAA,YACA,CAAA,0BACA,wDANF,cAOI,CAAA,WACA,CAAA,CAAA,uEAGF,aACE,CAAA,kBACA,CAAA,iBACA,CAAA,eACA,CAAA,YACA,CAAA,2BACA,CAAA,+CACA,CAAA,kCACA,CAAA,UACA,CAAA,oBACA,CAAA,uDACA,CAAA,yBACA,yFAEI,iDACE,CAAA,CAAA,0BAIN,uEAnBF,kCAoBI,CAAA,CAAA,0BAEF,uEAtBF,wBAuBI,CAAA,CAAA,yBAEF,uEAzBF,aA0BI,CAAA,CAAA,gFAGF,wBACE,CAAA,uFACA,+JACE,CAAA,0BAEA,uFAHF,4EAII,CAAA,yBACA,CAAA,CAAA,iFAIN,wBACE,CAAA,wFACA,+JACE,CAAA,0BAEA,wFAHF,2EAII,CAAA,yBACA,CAAA,CAAA,kFAIN,wBACE,CAAA,yFACA,gKACE,CAAA,0BAEA,yFAHF,2EAII,CAAA,yBACA,CAAA,CAAA,gFAIN,wBACE,CAAA,uFACA,oKACE,CAAA,0BAEA,uFAHF,0EAII,CAAA,yBACA,CAAA,CAAA,kFAIN,wBACE,CAAA,yFACA,gKACE,CAAA,0BAEA,yFAHF,2EAII,CAAA,yBACA,CAAA,CAAA,0BAKN,8EACE,YACE,CAAA,4FAEA,aACE,CAAA,4FAEF,YACE,CAAA,CAAA,sEAMR,iBACE,CAAA,MACA,CAAA,KACA,CAAA,UACA,CAAA,uBACA,CAAA,YACA,CAAA,WACA,CAAA,qBACA,CAAA,kBACA,CAAA,0BACA,CAAA,iBACA,CAAA,SACA,CAAA,uDACA,CAAA,0BACA,sEAdF,YAeI,CAAA,CAAA,sFAEF,cACE,CAAA,eACA,CAAA,kBACA,CAAA,uFAEF,eACE,CAAA,iBACA,CAAA,sEAGJ,YACE,CAAA,UACA,CAAA,WACA,CAAA,oBACA,CAAA,0BAEA,sEANF,aAOI,CAAA,gBACA,CAAA,YACA,CAAA,qBACA,CAAA,CAAA,uFAEF,gBACE,CAAA,eACA,CAAA,sFAEF,eACE,CAAA,iBACA,CAAA,eACA,CAAA,yFAEA,iBACE,CAAA,cACA,CAAA,gBACA,CAAA,iGACA,WACE,CAAA,iBACA,CAAA,UACA,CAAA,YACA,CAAA,yCAOZ,kBACE,CAAA,eACA,CAAA,qBACA,CAAA,gBACA,CAAA,yBACA,yCALF,YAMI,CAAA,CAAA,sDAEF,YACE,CAAA,6BACA,CAAA,oBACA,CAAA,QACA,CAAA,yBACA,sDALF,qBAMI,CAAA,QACA,CAAA,mBACA,CAAA,CAAA,oEAEF,eACE,CAAA,mFACA,cACE,CAAA,eACA,CAAA,UACA,CAAA,eACA,CAAA,mEAGJ,aACE,CAAA,uDAGJ,YACE,CAAA,QACA,CAAA,gBACA,CAAA,mBACA,CAAA,gBACA,CAAA,0BACA,uDANF,gBAOI,CAAA,CAAA,0BAEF,uDATF,cAUI,CAAA,CAAA,qEAGF,aACE,CAAA,kBACA,CAAA,iBACA,CAAA,eACA,CAAA,YACA,CAAA,eACA,CAAA,2BACA,CAAA,qDACA,CAAA,kCACA,CAAA,aACA,CAAA,oBACA,CAAA,uDACA,CAAA,yBACA,uFAEI,iDACE,CAAA,CAAA,yFAIN,eACE,CAAA,gBACA,CAAA,UACA,CAAA,2FAEF,eACE,CAAA,qBACA,CAAA,yBACA,2FAHF,eAII,CAAA,eACA,CAAA,CAAA,8GAGF,cACE,CAAA,aACA,CAAA,kBACA,CAAA,8GAEF,eACE,CAAA,iBACA,CAAA,iHACA,iBACE,CAAA,cACA,CAAA,gBACA,CAAA,aACA,CAAA,yHACA,WACE,CAAA,iBACA,CAAA,UACA,CAAA,YACA,CAAA,gFAMR,wBACE,CAAA,uFACA,+JACE,CAAA,yBAEA,uFAHF,4EAII,CAAA,yBACA,CAAA,CAAA,gFAIN,wBACE,CAAA,uFACA,oJACE,CAAA,mCAEA,CAAA,yBACA,uFAJF,iEAKI,CAAA,yBACA,CAAA,CAAA,4EAIN,eACE,CAAA,UACA,CAAA,6CAKR,kBACE,CAAA,eACA,CAAA,qBACA,CAAA,gBACA,CAAA,yBACA,6CALF,YAMI,CAAA,CAAA,0DAGF,YACE,CAAA,6BACA,CAAA,oBACA,CAAA,QACA,CAAA,yBACA,0DALF,qBAMI,CAAA,QACA,CAAA,mBACA,CAAA,CAAA,wEAEF,eACE,CAAA,uFACA,cACE,CAAA,eACA,CAAA,UACA,CAAA,eACA,CAAA,uEAGJ,aACE,CAAA,2DAGJ,YACE,CAAA,QACA,CAAA,gBACA,CAAA,mBACA,CAAA,gBACA,CAAA,0BACA,2DANF,cAOI,CAAA,CAAA,6EAGF,aACE,CAAA,kBACA,CAAA,iBACA,CAAA,eACA,CAAA,YACA,CAAA,eACA,CAAA,aAEA,CAAA,oBACA,CAAA,uDACA,CAAA,yBACA,+FAEI,iDACE,CAAA,CAAA,qGAIN,eACE,CAAA,gBACA,CAAA,UACA,CAAA,uGAEF,eACE,CAAA,qBACA,CAAA,yBACA,uGAHF,eAII,CAAA,eACA,CAAA,CAAA,8HAGF,cACE,CAAA,aACA,CAAA,kBACA,CAAA,0FAIJ,eACE,CAAA,sKACA,CAAA,2BAEA,CAAA,qDACA,CAAA,kCACA,CAAA,yBACA,0FAPF,4EAQI,CAAA,yBACA,CAAA,CAAA,4FAGJ,wBACE,CAAA,YACA,CAAA,6FAEF,wBACE,CAAA,YACA,CAAA,qFAGJ,YACE,CAAA,qBACA,CAAA,QACA,CAAA,0BACA,qFAJF,UAKI,CAAA,CAAA,sBAOV,qBACE,CAAA,mCACA,YACE,CAAA,6BACA,CAAA,QACA,CAAA,yBACA,mCAJF,qBAKI,CAAA,QACA,CAAA,mBACA,CAAA,CAAA,kDAEF,eACE,CAAA,UACA,CAAA,eACA,CAAA,6CAIJ,kBACE,CAAA,yBACA,6CAFF,kBAGI,CAAA,CAAA,0BAGJ,kCACE,kBACE,CAAA,CAAA,sCAIJ,kBACE,CAAA,eACA,CAAA,iBACA,CAAA,4DACA,iBACE,CAAA,WACA,CAAA,MACA,CAAA,gBACA,CAAA,UACA,CAAA,UACA,CAAA,iBACA,CAAA,4CAEF,qCACE,CAAA,sDAEA,uBACE,CAAA,gCAIN,iBACE,CAAA,UACA,CAAA,WACA,CAAA,KACA,CAAA,MACA,CAAA,kBACA,CAAA,0BACA,CAAA,8BACA,CAAA,gBACA,CAAA,sEACA,CAAA,SACA,CAAA,yBACA,gCAZF,YAaI,CAAA,CAAA,uCAEF,UACE,CAAA,iBACA,CAAA,OACA,CAAA,WACA,CAAA,kBACA,CAAA,mEACA,CAAA,oEACA,CAAA,sBACA,CAAA,mBACA,CAAA,gDAEF,eACE,CAAA,gBACA,CAAA,mBACA,CAAA,+CAEF,iBACE,CAAA,aACA,CAAA,yBACA,+CAHF,cAII,CAAA,CAAA,6CAIN,YACE,CAAA,kBACA,CAAA,QACA,CAAA,sBACA,CAAA,yBACA,6CALF,sBAMI,CAAA,CAAA,oHAEF,cAEE,CAAA,gIACA,aACE,CAAA,uEAGJ,mBACE,CAAA,UACA,CAAA,0BAIJ,mDACE,YACE,CAAA,gBACA,CAAA,iEAGF,6BACE,CAAA,CAAA,gCAGJ,mBACE,CAAA,oBACA,CAAA,wBACA,CAAA,eACA,CAAA,YACA,CAAA,qBACA,CAAA,kBACA,CAAA,sBACA,CAAA,SACA,CAAA,WACA,CAAA,2CACA,UACE,CAAA,4CAEF,kBACE,CAAA,eACA,CAAA,UACA,CAAA,2CAEF,cACE,CAAA,UACA,CAAA,UACA,CAAA,iBACA,CAAA,iBAKN,mGACE,CAAA,2BAEA,cACE,CAAA,kBACA,CAAA,UACA,CAAA,kCAGF,qBACE,CAAA,6CAGF,YACE,CAAA,sBACA,CAAA,8BAIN,qBACE,CAAA,oBACA,CAAA,kBACA,CAAA,yBAGF,8BACE,kBACE,CAAA,CAAA,yBAIJ,8BACE,kBACE,CAAA,CAAA,8CAIJ,cACE,CAAA,+DAGF,sCACE,CAAA,kDAGF,yBACE,CAAA,yBAGF,kDACE,UACE,CAAA,CAAA,yBAIJ,8CACE,kBACE,CAAA,CAAA,uEAIJ,wBACE,CAAA,6DAGF,mBACE,CAAA,UACA,CAAA,WACA,CAAA,kBACA,CAAA,sBACA,CAAA,UACA,CAAA,mEACA,CAAA,iBACA,CAAA,gBACA,CAAA,cACA,CAAA,eACA,CAAA,aACA,CAAA,yBAGF,6DACE,UACE,CAAA,WACA,CAAA,UACA,CAAA,CAAA,0DAIJ,cACE,CAAA,gBACA,CAAA,UACA,CAAA,iBACA,CAAA,kBACA,CAAA,yBAGF,0DACE,iBACE,CAAA,kBACA,CAAA,CAAA,2BAIJ,WACE,CAAA,yBAGF,2BACE,YACE,CAAA,CAAA,yBAIJ,uDACE,UACE,CAAA,iBACA,CAAA,UACA,CAAA,WACA,CAAA,MACA,CAAA,KACA,CAAA,qCACA,CAAA,SACA,CAAA,2EAGF,aACE,CAAA,CAAA,iCAIJ,kBACE,CAAA,eACA,CAAA,iBACA,CAAA,yBAGF,yCACE,UACE,CAAA,iBACA,CAAA,UACA,CAAA,WACA,CAAA,MACA,CAAA,KACA,CAAA,+BACA,CAAA,CAAA,6CAIJ,iBACE,CAAA,UACA,CAAA,QACA,CAAA,eACA,CAAA,yBAGF,6CACE,OACE,CAAA,KACA,CAAA,UACA,CAAA,WACA,CAAA,WACA,CAAA,UACA,CAAA,CAAA,8DAIJ,cACE,CAAA,kBACA,CAAA,4DAGF,cACE,CAAA,oBACA,CAAA,yBAGF,4DACE,UACE,CAAA,CAAA,sCAIJ,yBACE,CAAA,kCAGF,WACE,CAAA,YACA,CAAA,qBACA,CAAA,sBACA,CAAA,8BAGF,qCACE,CAAA,oCACA,CAAA,0BAGF,8BACE,kBACE,CAAA,CAAA,yBAIJ,8BACE,iBACE,CAAA,CAAA,4BAIJ,cACE,CAAA,oBACA,CAAA,iBACA,CAAA,aACA,CAAA,eACA,CAAA,kBACA,CAAA,sBACA,CAAA,kCAGF,aACE,CAAA,oCAGF,WACE,CAAA,YACA,CAAA,qBACA,CAAA,6BACA,CAAA,mDAGF,oBACE,CAAA,0BAGF,oCACE,kBACE,CAAA,gBACA,CAAA,CAAA,yBAIJ,oCACE,aACE,CAAA,CAAA,6BAIJ,mBACE,CAAA,oBACA,CAAA,2BACA,CAAA,eACA,CAAA,kBAGF,oGACE,CAAA,qBACA,CAAA,0BACA,CAAA,2BACA,CAAA,yBACA,6BACE,gBACE,CAAA,CAAA,uBAKN,WACE,CAAA,aACA,CAAA,aACA,CAAA,yBAGF,sCACE,qBACE,CAAA,0BACA,CAAA,CAAA,mCAIJ,kBACE,CAAA,qBACA,CAAA,eACA,CAAA,WACA,CAAA,mDAGF,iBACE,CAAA,SACA,CAAA,WACA,CAAA,MACA,CAAA,KACA,CAAA,yBACA,CAAA,2BACA,CAAA,SACA,CAAA,0DAGF,UACE,CAAA,SACA,CAAA,WACA,CAAA,eACA,CAAA,iBACA,CAAA,OACA,CAAA,KACA,CAAA,2DAGF,UACE,CAAA,4FACA,CAAA,uBACA,CAAA,0BACA,CAAA,aACA,CAAA,aACA,CAAA,iBACA,CAAA,OACA,CAAA,OACA,CAAA,8BACA,CAAA,SACA,CAAA,uBAGF,GACE,OACE,CAAA,IAGF,UACE,CAAA,KAGF,OACE,CAAA,CAAA,oEAIJ,qGACE,CAAA,wCACA,CAAA,oBACA,CAAA,oEAGF,sGACE,CAAA,wCACA,CAAA,oEAGF,sGACE,CAAA,2CACA,CAAA,oEAGF,sGACE,CAAA,wCACA,CAAA,2CAGF,uBACE,CAAA,eACA,CAAA,SACA,CAAA,QACA,CAAA,cACA,CAAA,SACA,CAAA,iBACA,CAAA,UACA,CAAA,WACA,CAAA,KACA,CAAA,MACA,CAAA,iEAGF,uBACE,CAAA,eACA,CAAA,SACA,CAAA,WACA,CAAA,sBACA,CAAA,8CAGF,UACE,CAAA,4DAGF,cACE,CAAA,4DAGF,YACE,CAAA,0DAGF,UACE,CAAA,oBACA,CAAA,yBAGF,0DACE,aACE,CAAA,CAAA,oDAIJ,aACE,CAAA,kEAGF,YACE,CAAA,kEAGF,cACE\",\"sourcesContent\":[\"* {\\r\\n  margin: 0;\\r\\n  padding: 0;\\r\\n  box-sizing: border-box;\\r\\n}\\r\\n\\r\\nmain {\\r\\n  background-color: #f5f8ff;\\r\\n  color: #000;\\r\\n\\r\\n  @media (max-width: 1280px) {\\r\\n    background-color: #f4f7ff;\\r\\n  }\\r\\n  video {\\r\\n    height: 100%;\\r\\n    width: 100%;\\r\\n    object-fit: cover;\\r\\n    line-height: 0;\\r\\n    font-size: 0;\\r\\n    // google去黑线\\r\\n    filter: grayscale(0);\\r\\n    // 火狐去黑线\\r\\n    clip-path: fill-box;\\r\\n  }\\r\\n\\r\\n  h1,\\r\\n  h2,\\r\\n  h3,\\r\\n  h4,\\r\\n  h5,\\r\\n  h6,\\r\\n  p,\\r\\n  div,\\r\\n  span,\\r\\n  ul,\\r\\n  li {\\r\\n    margin-bottom: 0;\\r\\n  }\\r\\n\\r\\n  h2 {\\r\\n    text-align: center;\\r\\n    font-weight: 800;\\r\\n    font-size: 2.25rem;\\r\\n  }\\r\\n\\r\\n  h1,\\r\\n  h2,\\r\\n  h3 {\\r\\n    text-align: center;\\r\\n  }\\r\\n\\r\\n  h2 {\\r\\n    font-size: 2.25rem;\\r\\n    font-weight: 800;\\r\\n  }\\r\\n\\r\\n  .opacity-7 {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n\\r\\n  .blue-text {\\r\\n    color: #2e8eff;\\r\\n  }\\r\\n\\r\\n  .gray-text {\\r\\n    color: #454545;\\r\\n  }\\r\\n\\r\\n  .btn-wrapper {\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    gap: 1rem;\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .btn-wrapper {\\r\\n      flex-direction: column;\\r\\n      gap: 8px;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .btn-wrapper .btn {\\r\\n    margin: 0;\\r\\n    border-radius: 8px;\\r\\n    text-transform: capitalize;\\r\\n    display: flex;\\r\\n    align-items: center;\\r\\n    justify-content: center;\\r\\n    min-width: 158px;\\r\\n    gap: 0.5rem;\\r\\n    &.btn-lg {\\r\\n      min-width: 228px;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .btn-wrapper .btn {\\r\\n      display: block;\\r\\n      vertical-align: baseline;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .btn-download {\\r\\n    background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);\\r\\n    border: none;\\r\\n    color: #fff;\\r\\n    background-color: #0458ff;\\r\\n  }\\r\\n\\r\\n  .btn-download:hover {\\r\\n    color: #fff;\\r\\n    background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),\\r\\n      linear-gradient(0deg, #0055fb, #0055fb);\\r\\n    background-color: #0458ff;\\r\\n  }\\r\\n\\r\\n  .swiper-pagination {\\r\\n    bottom: -4px !important;\\r\\n  }\\r\\n\\r\\n  .swiper-pagination .swiper-pagination-bullet {\\r\\n    width: 8px;\\r\\n    height: 8px;\\r\\n    background-color: #c2cee9;\\r\\n    opacity: 1;\\r\\n  }\\r\\n\\r\\n  .swiper-pagination .swiper-pagination-bullet-active {\\r\\n    width: 64px;\\r\\n    background: linear-gradient(89.5deg, #0458ff 0%, #0499ff 100%);\\r\\n    border-radius: 8px;\\r\\n  }\\r\\n  .part-banner {\\r\\n    text-align: center;\\r\\n    background: url(https://images.wondershare.com/repairit/images2025/File-Repair/banner-bg.jpg) no-repeat center center / cover;\\r\\n    margin-bottom: -3rem;\\r\\n    position: relative;\\r\\n    z-index: 1;\\r\\n    .sub-title {\\r\\n      display: flex;\\r\\n      gap: 8px;\\r\\n      align-items: center;\\r\\n      justify-content: center;\\r\\n      .blue-tip {\\r\\n        background: linear-gradient(100.2deg, #0055fb 0%, #00c1ff 100%), linear-gradient(0deg, #d9d9d9, #d9d9d9);\\r\\n        border-radius: 24px;\\r\\n        padding: 4px 12px;\\r\\n        font-weight: 700;\\r\\n        font-size: 1.25rem;\\r\\n        line-height: 100%;\\r\\n        color: #fff;\\r\\n      }\\r\\n    }\\r\\n    h1 {\\r\\n      background: linear-gradient(95.44deg, #0055fb -28.33%, #00c1ff 96.36%);\\r\\n      -webkit-background-clip: text;\\r\\n      -webkit-text-fill-color: transparent;\\r\\n      background-clip: text;\\r\\n      text-fill-color: transparent;\\r\\n    }\\r\\n    .feature-list {\\r\\n      display: inline-flex;\\r\\n      align-items: center;\\r\\n      justify-content: center;\\r\\n      padding: 4px 8px;\\r\\n      border-radius: 1.5rem;\\r\\n      border: 1.5px solid #0055fb;\\r\\n      min-width: 602px;\\r\\n      flex-wrap: wrap;\\r\\n      @media (max-width: 768px) {\\r\\n        min-width: unset;\\r\\n      }\\r\\n      .feature-item {\\r\\n        flex: 1;\\r\\n        font-weight: 600;\\r\\n        font-size: 1.125rem;\\r\\n        color: #0055fb;\\r\\n        padding: 0 1rem;\\r\\n        position: relative;\\r\\n        &.text-gradient {\\r\\n          background: linear-gradient(92.01deg, #e046ff 4.9%, #7970ff 52.55%, #0066ff 99.48%);\\r\\n          -webkit-background-clip: text;\\r\\n          -webkit-text-fill-color: transparent;\\r\\n          background-clip: text;\\r\\n          text-fill-color: transparent;\\r\\n        }\\r\\n        @media (max-width: 768px) {\\r\\n          flex: 1 1 50%;\\r\\n        }\\r\\n        &:not(:last-child)::after {\\r\\n          content: \\\"\\\";\\r\\n          display: inline-block;\\r\\n          width: 1px;\\r\\n          height: 80%;\\r\\n          background-color: #0055fb;\\r\\n          top: 50%;\\r\\n          transform: translateY(-50%);\\r\\n          position: absolute;\\r\\n          right: 0;\\r\\n          @media (max-width: 768px) {\\r\\n            content: unset;\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n    @keyframes banner-icon-animation {\\r\\n      0% {\\r\\n        transform: translateY(0);\\r\\n      }\\r\\n      50% {\\r\\n        transform: translateY(10px);\\r\\n      }\\r\\n      100% {\\r\\n        transform: translateY(0);\\r\\n      }\\r\\n    }\\r\\n    @keyframes banner-diffuse1 {\\r\\n      0% {\\r\\n        transform: scale(0.8);\\r\\n        opacity: 0.1;\\r\\n      }\\r\\n\\r\\n      60% {\\r\\n        transform: scale(0.9);\\r\\n        opacity: 0.8;\\r\\n      }\\r\\n\\r\\n      100% {\\r\\n        transform: scale(1);\\r\\n        opacity: 0;\\r\\n      }\\r\\n    }\\r\\n    .white-wave1 {\\r\\n      width: 15.93%;\\r\\n      aspect-ratio: 306 / 214;\\r\\n      border-radius: 1.5rem;\\r\\n      border: 3px solid rgba(255, 255, 255, 1);\\r\\n      z-index: 1;\\r\\n      position: absolute;\\r\\n      left: 8.45%;\\r\\n      top: 37%;\\r\\n      animation: banner-diffuse1 2s linear infinite;\\r\\n    }\\r\\n    .white-wave2 {\\r\\n      width: 15.93%;\\r\\n      aspect-ratio: 306 / 214;\\r\\n      border-radius: 1.5rem;\\r\\n      border: 3px solid rgba(255, 255, 255, 1);\\r\\n      z-index: 1;\\r\\n      position: absolute;\\r\\n      right: 4.79%;\\r\\n      top: 30%;\\r\\n      animation: banner-diffuse1 2s linear infinite;\\r\\n    }\\r\\n    .banner-icon {\\r\\n      position: absolute;\\r\\n      animation: banner-icon-animation 2s linear infinite;\\r\\n      z-index: 3;\\r\\n      &:nth-child(1) {\\r\\n        animation-delay: 0s;\\r\\n      }\\r\\n      &:nth-child(2) {\\r\\n        animation-delay: 0.5s;\\r\\n      }\\r\\n      &:nth-child(3) {\\r\\n        animation-delay: 1s;\\r\\n      }\\r\\n    }\\r\\n    #banner-text-swiper {\\r\\n      height: 22px;\\r\\n      overflow: hidden;\\r\\n      @media (max-width: 576px) {\\r\\n        height: 44px;\\r\\n      }\\r\\n    }\\r\\n    .detail-item {\\r\\n      display: flex;\\r\\n      align-items: flex-start;\\r\\n      justify-content: center;\\r\\n      gap: 10px;\\r\\n      font-size: 14px;\\r\\n    }\\r\\n  }\\r\\n  .part-logos {\\r\\n    position: relative;\\r\\n    z-index: 2;\\r\\n    .logos-wrapper {\\r\\n      background-color: #fff;\\r\\n      border-radius: 1rem;\\r\\n      padding: 2rem 0.5rem;\\r\\n      display: flex;\\r\\n      align-items: center;\\r\\n      justify-content: center;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .logos-wrapper {\\r\\n        padding: 1.5rem 0.5rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .logos-wrapper .logo-item {\\r\\n      flex: 1 1 33%;\\r\\n      max-height: 2rem;\\r\\n      text-align: center;\\r\\n    }\\r\\n\\r\\n    .logos-wrapper .logo-item:not(:last-child) {\\r\\n      border-right: 1px solid rgba(0, 0, 0, 0.1);\\r\\n    }\\r\\n\\r\\n    .logos-wrapper .logo-item img {\\r\\n      max-width: 100%;\\r\\n      max-height: 2rem;\\r\\n      object-fit: contain;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .logos-wrapper .logo-item img {\\r\\n        max-height: 1.1rem;\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-productivity {\\r\\n    @keyframes right-icon-animation {\\r\\n      0% {\\r\\n        transform: translateX(0);\\r\\n      }\\r\\n      50% {\\r\\n        transform: translateX(8px);\\r\\n      }\\r\\n      100% {\\r\\n        transform: translateX(0);\\r\\n      }\\r\\n    }\\r\\n    .document-wrapper {\\r\\n      border-radius: 1rem;\\r\\n      overflow: hidden;\\r\\n      background-color: #fff;\\r\\n      padding: 2.125rem;\\r\\n      @media (max-width: 576px) {\\r\\n        padding: 1rem;\\r\\n      }\\r\\n\\r\\n      .wrapper-top {\\r\\n        display: flex;\\r\\n        justify-content: space-between;\\r\\n        align-items: flex-end;\\r\\n        gap: 3rem;\\r\\n        @media (max-width: 768px) {\\r\\n          flex-direction: column;\\r\\n          gap: 1rem;\\r\\n          align-items: stretch;\\r\\n        }\\r\\n        .left-content {\\r\\n          max-width: 866px;\\r\\n          .wrapper-title {\\r\\n            font-size: 2rem;\\r\\n            font-weight: 800;\\r\\n            color: #000;\\r\\n            text-align: left;\\r\\n          }\\r\\n        }\\r\\n        .btn-wrapper {\\r\\n          flex-shrink: 0;\\r\\n        }\\r\\n      }\\r\\n      .card-wrapper {\\r\\n        display: flex;\\r\\n        gap: 16px;\\r\\n        flex-wrap: nowrap;\\r\\n        margin-top: 2.125rem;\\r\\n        height: 422px;\\r\\n        @media (max-width: 1280px) {\\r\\n          flex-wrap: wrap;\\r\\n          height: auto;\\r\\n        }\\r\\n\\r\\n        .document-card {\\r\\n          display: block;\\r\\n          border-radius: 1rem;\\r\\n          position: relative;\\r\\n          overflow: hidden;\\r\\n          flex: 1 1 15%;\\r\\n          background-repeat: no-repeat;\\r\\n          background-position: bottom center, center center;\\r\\n          background-size: auto 45%, 100% 100%;\\r\\n          color: #000;\\r\\n          text-decoration: none;\\r\\n          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);\\r\\n          @media (any-hover: hover) {\\r\\n            &:hover {\\r\\n              .right-icon {\\r\\n                animation: right-icon-animation 1s linear infinite;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          @media (max-width: 1600px) {\\r\\n            background-size: auto 36%, 100% 100%;\\r\\n          }\\r\\n          @media (max-width: 1280px) {\\r\\n            flex: 0 1 calc(50% - 8px);\\r\\n          }\\r\\n          @media (max-width: 768px) {\\r\\n            flex: 1 1 100%;\\r\\n          }\\r\\n\\r\\n          &.pdf-card {\\r\\n            background-color: #ffd7db;\\r\\n            &.active {\\r\\n              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/pdf-repair.png),\\r\\n                linear-gradient(100.64deg, #ffd7db 33.42%, #fefff9 132.61%);\\r\\n              @media (max-width: 1280px) {\\r\\n                background-image: linear-gradient(100.64deg, #ffd7db 33.42%, #fefff9 132.61%);\\r\\n                background-size: 100% 100%;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          &.word-card {\\r\\n            background-color: #f0f9ff;\\r\\n            &.active {\\r\\n              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/word-repair.png),\\r\\n                linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);\\r\\n              @media (max-width: 1280px) {\\r\\n                background-image: linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);\\r\\n                background-size: 100% 100%;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          &.excel-card {\\r\\n            background-color: #f0fff5;\\r\\n            &.active {\\r\\n              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/excel-repair.png),\\r\\n                linear-gradient(304.71deg, #f0fff5 17.16%, #c3f9d4 77.96%);\\r\\n              @media (max-width: 1280px) {\\r\\n                background-image: linear-gradient(304.71deg, #f0fff5 17.16%, #c3f9d4 77.96%);\\r\\n                background-size: 100% 100%;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          &.ppt-card {\\r\\n            background-color: #fff9f0;\\r\\n            &.active {\\r\\n              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/powerpoint-repair.png),\\r\\n                linear-gradient(128.81deg, #ffccb0 2.03%, #fff6e9 88.07%);\\r\\n              @media (max-width: 1280px) {\\r\\n                background-image: linear-gradient(128.81deg, #ffccb0 2.03%, #fff6e9 88.07%);\\r\\n                background-size: 100% 100%;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          &.iwork-card {\\r\\n            background-color: #f0f9ff;\\r\\n            &.active {\\r\\n              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/iwork-repair.png),\\r\\n                linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);\\r\\n              @media (max-width: 1280px) {\\r\\n                background-image: linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);\\r\\n                background-size: 100% 100%;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n\\r\\n          @media (min-width: 1280px) {\\r\\n            &.active {\\r\\n              flex: 1 1 40%;\\r\\n\\r\\n              .show-content {\\r\\n                display: block;\\r\\n              }\\r\\n              .hide-content {\\r\\n                display: none;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n        }\\r\\n\\r\\n        .hide-content {\\r\\n          position: absolute;\\r\\n          left: 0;\\r\\n          top: 0;\\r\\n          width: 100%;\\r\\n          padding: 1.75rem 0.625rem;\\r\\n          display: flex;\\r\\n          height: 100%;\\r\\n          flex-direction: column;\\r\\n          align-items: center;\\r\\n          justify-content: flex-start;\\r\\n          text-align: center;\\r\\n          z-index: 1;\\r\\n          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);\\r\\n          @media (max-width: 1280px) {\\r\\n            display: none;\\r\\n          }\\r\\n          .card-hide-icon {\\r\\n            width: 5.875rem;\\r\\n            margin-top: auto;\\r\\n            margin-bottom: auto;\\r\\n          }\\r\\n          .card-hide-title {\\r\\n            font-weight: 500;\\r\\n            font-size: 1.25rem;\\r\\n          }\\r\\n        }\\r\\n        .show-content {\\r\\n          display: none;\\r\\n          width: 100%;\\r\\n          height: 100%;\\r\\n          padding: 1.25rem 1rem;\\r\\n\\r\\n          @media (max-width: 1280px) {\\r\\n            display: block;\\r\\n            padding-bottom: 0;\\r\\n            display: flex;\\r\\n            flex-direction: column;\\r\\n          }\\r\\n          .card-show-title {\\r\\n            font-size: 1.5rem;\\r\\n            font-weight: 800;\\r\\n          }\\r\\n          .card-show-desc {\\r\\n            list-style: none;\\r\\n            padding-left: 1rem;\\r\\n            max-width: 417px;\\r\\n\\r\\n            li {\\r\\n              position: relative;\\r\\n              font-size: 1rem;\\r\\n              line-height: 150%;\\r\\n              &::before {\\r\\n                content: \\\"•\\\";\\r\\n                position: absolute;\\r\\n                left: -1rem;\\r\\n                top: -0.15rem;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n    .archive-wrapper {\\r\\n      border-radius: 1rem;\\r\\n      overflow: hidden;\\r\\n      background-color: #fff;\\r\\n      padding: 2.125rem;\\r\\n      @media (max-width: 576px) {\\r\\n        padding: 1rem;\\r\\n      }\\r\\n      .wrapper-top {\\r\\n        display: flex;\\r\\n        justify-content: space-between;\\r\\n        align-items: flex-end;\\r\\n        gap: 3rem;\\r\\n        @media (max-width: 768px) {\\r\\n          flex-direction: column;\\r\\n          gap: 1rem;\\r\\n          align-items: stretch;\\r\\n        }\\r\\n        .left-content {\\r\\n          max-width: 866px;\\r\\n          .wrapper-title {\\r\\n            font-size: 2rem;\\r\\n            font-weight: 800;\\r\\n            color: #000;\\r\\n            text-align: left;\\r\\n          }\\r\\n        }\\r\\n        .btn-wrapper {\\r\\n          flex-shrink: 0;\\r\\n        }\\r\\n      }\\r\\n      .card-wrapper {\\r\\n        display: flex;\\r\\n        gap: 16px;\\r\\n        flex-wrap: nowrap;\\r\\n        margin-top: 2.125rem;\\r\\n        min-height: 283px;\\r\\n        @media (max-width: 1600px) {\\r\\n          min-height: 340px;\\r\\n        }\\r\\n        @media (max-width: 1280px) {\\r\\n          flex-wrap: wrap;\\r\\n        }\\r\\n\\r\\n        .archive-card {\\r\\n          display: block;\\r\\n          border-radius: 1rem;\\r\\n          position: relative;\\r\\n          overflow: hidden;\\r\\n          padding: 1rem;\\r\\n          flex: 1 1 31.86%;\\r\\n          background-repeat: no-repeat;\\r\\n          background-position: right 5% bottom 50%, center center;\\r\\n          background-size: auto 60%, 100% 100%;\\r\\n          color: #636363;\\r\\n          text-decoration: none;\\r\\n          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);\\r\\n          @media (any-hover: hover) {\\r\\n            &:hover {\\r\\n              .right-icon {\\r\\n                animation: right-icon-animation 1s linear infinite;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          .archive-card-title {\\r\\n            font-weight: 800;\\r\\n            font-size: 1.5rem;\\r\\n            color: #000;\\r\\n          }\\r\\n          .archive-card-content {\\r\\n            max-width: 460px;\\r\\n            padding-right: 1.25rem;\\r\\n            @media (max-width: 768px) {\\r\\n              max-width: unset;\\r\\n              padding-right: 0;\\r\\n            }\\r\\n\\r\\n            .archive-card-desc {\\r\\n              font-size: 14px;\\r\\n              color: inherit;\\r\\n              margin-bottom: 16px;\\r\\n            }\\r\\n            .archive-card-list {\\r\\n              list-style: none;\\r\\n              padding-left: 1rem;\\r\\n              li {\\r\\n                position: relative;\\r\\n                font-size: 14px;\\r\\n                line-height: 150%;\\r\\n                color: inherit;\\r\\n                &::before {\\r\\n                  content: \\\"•\\\";\\r\\n                  position: absolute;\\r\\n                  left: -1rem;\\r\\n                  top: -0.15rem;\\r\\n                }\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n\\r\\n          &.rar-repair {\\r\\n            background-color: #eeefff;\\r\\n            &.active {\\r\\n              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/rar-repair.png),\\r\\n                linear-gradient(280.74deg, #eeeeff 50.38%, #a3b9ff 140.34%);\\r\\n              @media (max-width: 768px) {\\r\\n                background-image: linear-gradient(280.74deg, #eeeeff 50.38%, #a3b9ff 140.34%);\\r\\n                background-size: 100% 100%;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          &.zip-repair {\\r\\n            background-color: #fff5d0;\\r\\n            &.active {\\r\\n              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/zip-repair.png),\\r\\n                linear-gradient(90deg, #ffe3a7 0%, #fff8d3 100%);\\r\\n              background-size: auto 100%, 100% 100%;\\r\\n              @media (max-width: 768px) {\\r\\n                background-image: linear-gradient(90deg, #ffe3a7 0%, #fff8d3 100%);\\r\\n                background-size: 100% 100%;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          &.active {\\r\\n            flex: 1 1 68.14%;\\r\\n            color: #000;\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n    .engineering-wrapper {\\r\\n      border-radius: 1rem;\\r\\n      overflow: hidden;\\r\\n      background-color: #fff;\\r\\n      padding: 2.125rem;\\r\\n      @media (max-width: 576px) {\\r\\n        padding: 1rem;\\r\\n      }\\r\\n\\r\\n      .wrapper-top {\\r\\n        display: flex;\\r\\n        justify-content: space-between;\\r\\n        align-items: flex-end;\\r\\n        gap: 3rem;\\r\\n        @media (max-width: 768px) {\\r\\n          flex-direction: column;\\r\\n          gap: 1rem;\\r\\n          align-items: stretch;\\r\\n        }\\r\\n        .left-content {\\r\\n          max-width: 866px;\\r\\n          .wrapper-title {\\r\\n            font-size: 2rem;\\r\\n            font-weight: 800;\\r\\n            color: #000;\\r\\n            text-align: left;\\r\\n          }\\r\\n        }\\r\\n        .btn-wrapper {\\r\\n          flex-shrink: 0;\\r\\n        }\\r\\n      }\\r\\n      .card-wrapper {\\r\\n        display: flex;\\r\\n        gap: 16px;\\r\\n        flex-wrap: nowrap;\\r\\n        margin-top: 2.125rem;\\r\\n        min-height: 315px;\\r\\n        @media (max-width: 1280px) {\\r\\n          flex-wrap: wrap;\\r\\n        }\\r\\n\\r\\n        .engineering-card {\\r\\n          display: block;\\r\\n          border-radius: 1rem;\\r\\n          position: relative;\\r\\n          overflow: hidden;\\r\\n          padding: 1rem;\\r\\n          flex: 1 1 31.86%;\\r\\n\\r\\n          color: #636363;\\r\\n          text-decoration: none;\\r\\n          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);\\r\\n          @media (any-hover: hover) {\\r\\n            &:hover {\\r\\n              .right-icon {\\r\\n                animation: right-icon-animation 1s linear infinite;\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n          .engineering-card-title {\\r\\n            font-weight: 800;\\r\\n            font-size: 1.5rem;\\r\\n            color: #000;\\r\\n          }\\r\\n          .engineering-card-content {\\r\\n            max-width: 425px;\\r\\n            padding-right: 1.25rem;\\r\\n            @media (max-width: 768px) {\\r\\n              padding-right: 0;\\r\\n              max-width: unset;\\r\\n            }\\r\\n\\r\\n            .engineering-card-desc {\\r\\n              font-size: 14px;\\r\\n              color: #636363;\\r\\n              margin-bottom: 16px;\\r\\n            }\\r\\n          }\\r\\n\\r\\n          &.adobe-repair {\\r\\n            flex: 1 1 68.14%;\\r\\n            background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/adobe-file-repair.png),\\r\\n              linear-gradient(100.16deg, #ddefff 63.73%, #a3d3ff 119.29%);\\r\\n            background-repeat: no-repeat;\\r\\n            background-position: right 6% bottom 15%, center center;\\r\\n            background-size: auto 80%, 100% 100%;\\r\\n            @media (max-width: 768px) {\\r\\n              background-image: linear-gradient(100.16deg, #ddefff 63.73%, #a3d3ff 119.29%);\\r\\n              background-size: 100% 100%;\\r\\n            }\\r\\n          }\\r\\n          &.autocad-repair {\\r\\n            background-color: #ebf6ff;\\r\\n            flex: 1 1 50%;\\r\\n          }\\r\\n          &.sketchup-repair {\\r\\n            background-color: #ebf6ff;\\r\\n            flex: 1 1 50%;\\r\\n          }\\r\\n        }\\r\\n        .autocad-sketchup-wrapper {\\r\\n          display: flex;\\r\\n          flex-direction: column;\\r\\n          gap: 16px;\\r\\n          @media (max-width: 1280px) {\\r\\n            width: 100%;\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-corruption {\\r\\n    background-color: #fff;\\r\\n    .wrapper-top {\\r\\n      display: flex;\\r\\n      justify-content: space-between;\\r\\n      gap: 3rem;\\r\\n      @media (max-width: 768px) {\\r\\n        flex-direction: column;\\r\\n        gap: 1rem;\\r\\n        align-items: stretch;\\r\\n      }\\r\\n      .wrapper-title {\\r\\n        font-weight: 800;\\r\\n        color: #000;\\r\\n        text-align: left;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .swiper-corruption-box {\\r\\n      margin-right: -20vw;\\r\\n      @media (max-width: 576px) {\\r\\n        margin-right: unset;\\r\\n      }\\r\\n    }\\r\\n    @media (min-width: 2000px) {\\r\\n      .swiper-box {\\r\\n        margin-right: -30vw;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .corruption-box {\\r\\n      border-radius: 1rem;\\r\\n      overflow: hidden;\\r\\n      position: relative;\\r\\n      .corruption-box-title {\\r\\n        position: absolute;\\r\\n        bottom: 6.4%;\\r\\n        left: 0;\\r\\n        font-size: 1.5rem;\\r\\n        color: #000;\\r\\n        width: 100%;\\r\\n        text-align: center;\\r\\n      }\\r\\n      &:hover {\\r\\n        box-shadow: 0px 0px 12px 0px #00d1ff4d;\\r\\n\\r\\n        .hide-box {\\r\\n          transform: translateY(0);\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n    .hide-box {\\r\\n      position: absolute;\\r\\n      width: 100%;\\r\\n      height: 100%;\\r\\n      top: 0;\\r\\n      left: 0;\\r\\n      border-radius: 1rem;\\r\\n      transform: translateY(110%);\\r\\n      transition: all 0.3s ease-in-out;\\r\\n      padding: 2.375rem;\\r\\n      background: linear-gradient(147.08deg, #ffffff 34.81%, #c3ecff 100.92%);\\r\\n      z-index: 1;\\r\\n      @media (max-width: 992px) {\\r\\n        padding: 1rem;\\r\\n      }\\r\\n      &::after {\\r\\n        content: \\\"\\\";\\r\\n        position: absolute;\\r\\n        inset: 0;\\r\\n        padding: 1px;\\r\\n        border-radius: 1rem;\\r\\n        background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);\\r\\n        mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);\\r\\n        mask-composite: exclude;\\r\\n        pointer-events: none;\\r\\n      }\\r\\n      .hide-box-title {\\r\\n        font-weight: 800;\\r\\n        font-size: 1.5rem;\\r\\n        margin-bottom: 0.5rem;\\r\\n      }\\r\\n      .hide-box-desc {\\r\\n        font-size: 1.25rem;\\r\\n        color: #636363;\\r\\n        @media (max-width: 992px) {\\r\\n          font-size: 1rem;\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n    .swiper-pagination-box {\\r\\n      display: flex;\\r\\n      align-items: center;\\r\\n      gap: 1rem;\\r\\n      padding-bottom: 1.25rem;\\r\\n      @media (max-width: 576px) {\\r\\n        justify-content: center;\\r\\n      }\\r\\n      .swiper-prev,\\r\\n      .swiper-next {\\r\\n        cursor: pointer;\\r\\n        &:hover {\\r\\n          color: #0496ff;\\r\\n        }\\r\\n      }\\r\\n      .swiper-pagination-number {\\r\\n        display: inline-flex;\\r\\n        width: auto;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    @media (min-width: 1280px) {\\r\\n      #card-swiper .swiper-wrapper {\\r\\n        gap: 1.875rem;\\r\\n        flex-wrap: nowrap;\\r\\n      }\\r\\n\\r\\n      #card-swiper .swiper-wrapper .swiper-slide {\\r\\n        flex: 1 1 calc(20% - 1.875rem);\\r\\n      }\\r\\n    }\\r\\n    .card-box {\\r\\n      padding: 2rem 1.5rem;\\r\\n      border-radius: 1.5rem;\\r\\n      background-color: #eaf8ff;\\r\\n      overflow: hidden;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n      align-items: center;\\r\\n      justify-content: center;\\r\\n      gap: 0.5rem;\\r\\n      height: 100%;\\r\\n      .card-icon {\\r\\n        width: 6rem;\\r\\n      }\\r\\n      .card-title {\\r\\n        font-size: 1.125rem;\\r\\n        font-weight: 800;\\r\\n        color: #000;\\r\\n      }\\r\\n      .card-desc {\\r\\n        font-size: 14px;\\r\\n        color: #000;\\r\\n        opacity: 0.6;\\r\\n        text-align: center;\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-steps {\\r\\n    background: radial-gradient(82.32% 135.56% at 31.17% -26.53%, #b6e0ff 0%, #e3f3ff 50%, #e0f2ff 100%);\\r\\n\\r\\n    .nav-item {\\r\\n      padding: 1.5rem;\\r\\n      border-radius: 12px;\\r\\n      width: 100%;\\r\\n    }\\r\\n\\r\\n    .nav-item.active {\\r\\n      background-color: #fff;\\r\\n    }\\r\\n\\r\\n    .nav-item .nav-item-content {\\r\\n      display: flex;\\r\\n      align-items: flex-start;\\r\\n    }\\r\\n  }\\r\\n}\\r\\nmain .part-faq .accordion-box {\\r\\n  background-color: #fff;\\r\\n  border-radius: 1.5rem;\\r\\n  padding: 0.5rem 4rem;\\r\\n}\\r\\n\\r\\n@media (max-width: 992px) {\\r\\n  main .part-faq .accordion-box {\\r\\n    padding: 0.5rem 2rem;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-faq .accordion-box {\\r\\n    padding: 0.5rem 1rem;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-faq .accordion-box .accordion-item {\\r\\n  padding: 1.5rem;\\r\\n}\\r\\n\\r\\nmain .part-faq .accordion-box .accordion-item:not(:last-child) {\\r\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\nmain .part-faq .accordion-box .accordion-item svg {\\r\\n  transition: all 0.2s linear;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-faq .accordion-box .accordion-item svg {\\r\\n    width: 1rem;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-faq .accordion-box .accordion-item {\\r\\n    padding: 1rem 0.5rem;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-faq .accordion-box .accordion-item [aria-expanded=\\\"true\\\"] svg {\\r\\n  transform: rotate(180deg);\\r\\n}\\r\\n\\r\\nmain .part-faq .accordion-box .accordion-item .serial-number {\\r\\n  display: inline-flex;\\r\\n  width: 22px;\\r\\n  height: 22px;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  color: #fff;\\r\\n  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);\\r\\n  border-radius: 50%;\\r\\n  margin-right: 8px;\\r\\n  font-size: 1rem;\\r\\n  font-weight: 800;\\r\\n  flex-shrink: 0;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-faq .accordion-box .accordion-item .serial-number {\\r\\n    width: 16px;\\r\\n    height: 16px;\\r\\n    color: #fff;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-faq .accordion-box .accordion-item .faq-detail {\\r\\n  font-size: 14px;\\r\\n  padding-top: 1rem;\\r\\n  opacity: 0.7;\\r\\n  padding-left: 30px;\\r\\n  padding-right: 32px;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-faq .accordion-box .accordion-item .faq-detail {\\r\\n    padding-left: 20px;\\r\\n    padding-right: 16px;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-stories .swiper {\\r\\n  margin: 2rem;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-stories .swiper {\\r\\n    margin: 0.5rem;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (min-width: 768px) {\\r\\n  main .part-stories .swiper-slide .user-wrapper::before {\\r\\n    content: \\\"\\\";\\r\\n    position: absolute;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    left: 0;\\r\\n    top: 0;\\r\\n    background-color: rgba(210, 223, 255, 0.3);\\r\\n    z-index: 2;\\r\\n  }\\r\\n\\r\\n  main .part-stories .swiper-slide.swiper-slide-active .user-wrapper::before {\\r\\n    content: unset;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-stories .user-wrapper {\\r\\n  border-radius: 1rem;\\r\\n  overflow: hidden;\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-stories .user-wrapper::before {\\r\\n    content: \\\"\\\";\\r\\n    position: absolute;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    left: 0;\\r\\n    top: 0;\\r\\n    background-color: rgba(0, 0, 0, 0.6);\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-stories .user-wrapper .user-story {\\r\\n  position: absolute;\\r\\n  right: 4rem;\\r\\n  top: 3rem;\\r\\n  max-width: 360px;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-stories .user-wrapper .user-story {\\r\\n    right: 0;\\r\\n    top: 0;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    padding: 8px;\\r\\n    color: #fff;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-stories .user-wrapper .user-story .user-occupation {\\r\\n  font-size: 14px;\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\nmain .part-stories .user-wrapper .user-story .user-comments {\\r\\n  font-size: 12px;\\r\\n  color: rgba(0, 0, 0, 0.7);\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-stories .user-wrapper .user-story .user-comments {\\r\\n    color: #fff;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-stories .swiper-pagination {\\r\\n  bottom: -2.5rem !important;\\r\\n}\\r\\n\\r\\nmain .part-links .part-links-line {\\r\\n  height: 100%;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\nmain .part-links .line-border {\\r\\n  border-right: 1px solid rgba(0, 0, 0, 0.3);\\r\\n  border-left: 1px solid rgba(0, 0, 0, 0.3);\\r\\n}\\r\\n\\r\\n@media (max-width: 1280px) {\\r\\n  main .part-links .line-border {\\r\\n    border-right: unset;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  main .part-links .line-border {\\r\\n    border-left: unset;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-links .text-link {\\r\\n  font-size: 14px;\\r\\n  color: rgba(0, 0, 0, 0.7);\\r\\n  margin-top: 1.5rem;\\r\\n  display: block;\\r\\n  overflow: hidden;\\r\\n  white-space: nowrap;\\r\\n  text-overflow: ellipsis;\\r\\n}\\r\\n\\r\\nmain .part-links .text-link:hover {\\r\\n  color: #0055fb;\\r\\n}\\r\\n\\r\\nmain .part-links .part-links-videos {\\r\\n  height: 100%;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: space-between;\\r\\n}\\r\\n\\r\\nmain .part-links .part-links-videos .video-wrapper {\\r\\n  border-radius: 0.75rem;\\r\\n}\\r\\n\\r\\n@media (max-width: 1280px) {\\r\\n  main .part-links .part-links-videos {\\r\\n    flex-direction: row;\\r\\n    padding-top: 2rem;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 576px) {\\r\\n  main .part-links .part-links-videos {\\r\\n    display: block;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-links .text-line4 {\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 4;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\nmain .part-footer {\\r\\n  background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);\\r\\n  background-size: cover;\\r\\n  background-position: center;\\r\\n  background-repeat: no-repeat;\\r\\n  @media (max-width: 576px) {\\r\\n    .display-2 {\\r\\n      font-size: 2.5rem;\\r\\n    }\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-footer-logo {\\r\\n  height: 4rem;\\r\\n  width: 14.5rem;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n@media (max-width: 576px) {\\r\\n  main .part-footer .btn-outline-action {\\r\\n    background-color: #fff;\\r\\n    vertical-align: text-bottom;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item {\\r\\n  border-radius: 1rem;\\r\\n  background-color: #ffffff;\\r\\n  overflow: hidden;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .compare-before {\\r\\n  position: absolute;\\r\\n  width: 50%;\\r\\n  height: 100%;\\r\\n  left: 0;\\r\\n  top: 0;\\r\\n  background-size: auto 100%;\\r\\n  background-repeat: no-repeat;\\r\\n  z-index: 2;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .compare-before::after {\\r\\n  content: \\\"\\\";\\r\\n  width: 2px;\\r\\n  height: 100%;\\r\\n  background: #fff;\\r\\n  position: absolute;\\r\\n  right: 0;\\r\\n  top: 0;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .compare-before::before {\\r\\n  content: \\\"\\\";\\r\\n  background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);\\r\\n  background-size: contain;\\r\\n  background-position: center;\\r\\n  width: 4.25rem;\\r\\n  height: 2.5rem;\\r\\n  position: absolute;\\r\\n  right: 0;\\r\\n  top: 50%;\\r\\n  transform: translate(50%, -50%);\\r\\n  z-index: 3;\\r\\n}\\r\\n\\r\\n@keyframes changeWidth {\\r\\n  0% {\\r\\n    width: 0;\\r\\n  }\\r\\n\\r\\n  50% {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  100% {\\r\\n    width: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .compare-before.compare-before-1 {\\r\\n  background-image: url(https://images.wondershare.com/repairit/images2024/index/File-Repair-before.png);\\r\\n  animation: changeWidth 8s linear infinite;\\r\\n  aspect-ratio: 328 / 192;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .compare-before.compare-before-2 {\\r\\n  background-image: url(https://images.wondershare.com/repairit/images2024/index/Video-Repair-before.png);\\r\\n  animation: changeWidth 7s linear infinite;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .compare-before.compare-before-3 {\\r\\n  background-image: url(https://images.wondershare.com/repairit/images2024/index/Photo-Repair-before.png);\\r\\n  animation: changeWidth 7s linear infinite 1s;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .compare-before.compare-before-4 {\\r\\n  background-image: url(https://images.wondershare.com/repairit/images2024/index/Audio-Repair-before.png);\\r\\n  animation: changeWidth 6s linear infinite;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .slider {\\r\\n  -webkit-appearance: none;\\r\\n  appearance: none;\\r\\n  outline: 0;\\r\\n  margin: 0;\\r\\n  background: 0 0;\\r\\n  z-index: 3;\\r\\n  position: absolute;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .slider::-webkit-slider-thumb {\\r\\n  -webkit-appearance: none;\\r\\n  appearance: none;\\r\\n  width: 2px;\\r\\n  height: auto;\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .item-link {\\r\\n  color: #000;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .item-link .normal-arrow {\\r\\n  display: inline;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .item-link .active-arrow {\\r\\n  display: none;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .item-link .arrow-icon {\\r\\n  width: 2rem;\\r\\n  display: inline-block;\\r\\n}\\r\\n\\r\\n@media (max-width: 576px) {\\r\\n  main .part-advanced .advanced-item .item-link .arrow-icon {\\r\\n    display: block;\\r\\n  }\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .item-link:hover {\\r\\n  color: #0458ff;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .item-link:hover .normal-arrow {\\r\\n  display: none;\\r\\n}\\r\\n\\r\\nmain .part-advanced .advanced-item .item-link:hover .active-arrow {\\r\\n  display: inline;\\r\\n}\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2\n");

/***/ }),

/***/ 314:
/***/ ((module) => {

eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///314\n");

/***/ }),

/***/ 354:
/***/ ((module) => {

eval("\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzU0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsY0FBYztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2Nzcy1sb2FkZXIvZGlzdC9ydW50aW1lL3NvdXJjZU1hcHMuanM/YWYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoaXRlbSkge1xuICB2YXIgY29udGVudCA9IGl0ZW1bMV07XG4gIHZhciBjc3NNYXBwaW5nID0gaXRlbVszXTtcbiAgaWYgKCFjc3NNYXBwaW5nKSB7XG4gICAgcmV0dXJuIGNvbnRlbnQ7XG4gIH1cbiAgaWYgKHR5cGVvZiBidG9hID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB2YXIgYmFzZTY0ID0gYnRvYSh1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoY3NzTWFwcGluZykpKSk7XG4gICAgdmFyIGRhdGEgPSBcInNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LFwiLmNvbmNhdChiYXNlNjQpO1xuICAgIHZhciBzb3VyY2VNYXBwaW5nID0gXCIvKiMgXCIuY29uY2F0KGRhdGEsIFwiICovXCIpO1xuICAgIHJldHVybiBbY29udGVudF0uY29uY2F0KFtzb3VyY2VNYXBwaW5nXSkuam9pbihcIlxcblwiKTtcbiAgfVxuICByZXR1cm4gW2NvbnRlbnRdLmpvaW4oXCJcXG5cIik7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///354\n");

/***/ }),

/***/ 72:
/***/ ((module) => {

eval("\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///72\n");

/***/ }),

/***/ 659:
/***/ ((module) => {

eval("\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU5LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanM/YjIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIG1lbW8gPSB7fTtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBnZXRUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0eXBlb2YgbWVtb1t0YXJnZXRdID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIHN0eWxlVGFyZ2V0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcih0YXJnZXQpO1xuXG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIHJldHVybiBoZWFkIG9mIGlmcmFtZSBpbnN0ZWFkIG9mIGlmcmFtZSBpdHNlbGZcbiAgICBpZiAod2luZG93LkhUTUxJRnJhbWVFbGVtZW50ICYmIHN0eWxlVGFyZ2V0IGluc3RhbmNlb2Ygd2luZG93LkhUTUxJRnJhbWVFbGVtZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUaGlzIHdpbGwgdGhyb3cgYW4gZXhjZXB0aW9uIGlmIGFjY2VzcyB0byBpZnJhbWUgaXMgYmxvY2tlZFxuICAgICAgICAvLyBkdWUgdG8gY3Jvc3Mtb3JpZ2luIHJlc3RyaWN0aW9uc1xuICAgICAgICBzdHlsZVRhcmdldCA9IHN0eWxlVGFyZ2V0LmNvbnRlbnREb2N1bWVudC5oZWFkO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgICAgICBzdHlsZVRhcmdldCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG1lbW9bdGFyZ2V0XSA9IHN0eWxlVGFyZ2V0O1xuICB9XG4gIHJldHVybiBtZW1vW3RhcmdldF07XG59XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gaW5zZXJ0QnlTZWxlY3RvcihpbnNlcnQsIHN0eWxlKSB7XG4gIHZhciB0YXJnZXQgPSBnZXRUYXJnZXQoaW5zZXJ0KTtcbiAgaWYgKCF0YXJnZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBmaW5kIGEgc3R5bGUgdGFyZ2V0LiBUaGlzIHByb2JhYmx5IG1lYW5zIHRoYXQgdGhlIHZhbHVlIGZvciB0aGUgJ2luc2VydCcgcGFyYW1ldGVyIGlzIGludmFsaWQuXCIpO1xuICB9XG4gIHRhcmdldC5hcHBlbmRDaGlsZChzdHlsZSk7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydEJ5U2VsZWN0b3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///659\n");

/***/ }),

/***/ 540:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTQwLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRTdHlsZUVsZW1lbnQuanM/ZGU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBpbnNlcnRTdHlsZUVsZW1lbnQob3B0aW9ucykge1xuICB2YXIgZWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgb3B0aW9ucy5zZXRBdHRyaWJ1dGVzKGVsZW1lbnQsIG9wdGlvbnMuYXR0cmlidXRlcyk7XG4gIG9wdGlvbnMuaW5zZXJ0KGVsZW1lbnQsIG9wdGlvbnMub3B0aW9ucyk7XG4gIHJldHVybiBlbGVtZW50O1xufVxubW9kdWxlLmV4cG9ydHMgPSBpbnNlcnRTdHlsZUVsZW1lbnQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///540\n");

/***/ }),

/***/ 56:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLGNBQWMsS0FBd0MsR0FBRyxzQkFBaUIsR0FBRyxDQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanM/ZGRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMoc3R5bGVFbGVtZW50KSB7XG4gIHZhciBub25jZSA9IHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gXCJ1bmRlZmluZWRcIiA/IF9fd2VicGFja19ub25jZV9fIDogbnVsbDtcbiAgaWYgKG5vbmNlKSB7XG4gICAgc3R5bGVFbGVtZW50LnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///56\n");

/***/ }),

/***/ 825:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n  var needLayer = typeof obj.layer !== \"undefined\";\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += \"}\";\n  }\n  if (obj.media) {\n    css += \"}\";\n  }\n  if (obj.supports) {\n    css += \"}\";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === \"undefined\") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///825\n");

/***/ }),

/***/ 113:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTEzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc3R5bGVUYWdUcmFuc2Zvcm0uanM/MWRkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzdHlsZVRhZ1RyYW5zZm9ybShjc3MsIHN0eWxlRWxlbWVudCkge1xuICBpZiAoc3R5bGVFbGVtZW50LnN0eWxlU2hlZXQpIHtcbiAgICBzdHlsZUVsZW1lbnQuc3R5bGVTaGVldC5jc3NUZXh0ID0gY3NzO1xuICB9IGVsc2Uge1xuICAgIHdoaWxlIChzdHlsZUVsZW1lbnQuZmlyc3RDaGlsZCkge1xuICAgICAgc3R5bGVFbGVtZW50LnJlbW92ZUNoaWxkKHN0eWxlRWxlbWVudC5maXJzdENoaWxkKTtcbiAgICB9XG4gICAgc3R5bGVFbGVtZW50LmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZVRleHROb2RlKGNzcykpO1xuICB9XG59XG5tb2R1bGUuZXhwb3J0cyA9IHN0eWxlVGFnVHJhbnNmb3JtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///113\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__(787);
/******/ 	
/******/ })()
;