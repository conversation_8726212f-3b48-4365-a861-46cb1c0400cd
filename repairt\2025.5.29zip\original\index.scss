* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #f5f8ff;
  font-family: "Mulish", sans-serif;
}

main h1,
main h2,
main h3,
main h4,
main h5,
main h6,
main p,
main div {
  margin-bottom: 0;
  color: #000;
  font-family: "Mulish", sans-serif;
}

main h1,
main h2 {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
}

@media (max-width: 768px) {
  main h1,
  main h2 {
    font-size: 24px;
    text-align: center;
  }
}

@media (max-width: 576px) {
  main .display-3 {
    font-size: 2.5rem;
  }
}

main .opacity-7 {
  opacity: 0.7;
}

main .text-blue {
  color: #2a80ff;
}

main .btn-wrapper {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

@media (max-width: 768px) {
  main .btn-wrapper {
    flex-direction: column;
    gap: 8px;
  }
}

main .btn {
  margin: 0;
  border-radius: 12px;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 768px) {
  main .btn {
    display: block;
    vertical-align: baseline;
  }
}

main .btn-download {
  background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);
  border: none;
  color: #fff;
  background-color: #0458ff;
}

main .btn-download:hover {
  color: #fff;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),
    linear-gradient(0deg, #0055fb, #0055fb);
  background-color: #0458ff;
}

main .part-banner {
  position: relative;
}

@media (max-width: 768px) {
  main .part-banner {
    background: linear-gradient(180deg, #c0eeff 0%, #f8fbff 96.46%);
  }
}

main .part-banner .banner-left-download {
  z-index: 10;
  position: absolute;
  height: 100%;
  top: 0;
  left: 0;
  width: 33%;
}
main .part-banner .banner-right-download {
  z-index: 10;
  position: absolute;
  height: 100%;
  top: 0;
  right: 0;
  width: 33%;
}

main .part-banner .video-wrapper {
  line-height: 0;
  font-size: 0;
}

main .part-banner .video-wrapper video {
  height: 100%;
  width: 100%;
  object-fit: cover;
  min-height: 540px;
}

@media (max-width: 768px) {
  main .part-banner .video-wrapper {
    display: none;
  }
}

main .part-banner .part-banner-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 auto;

  .small-title {
    color: #13171a;
    font-size: 1.875rem;
    margin-bottom: 1rem;
    font-weight: 700;
    line-height: 1.625rem;
  }
}

@media (max-width: 768px) {
  main .part-banner .part-banner-content {
    position: relative;
    padding: 3rem 0;
    text-align: center;
  }
}

main .part-banner .part-banner-content h1 {
  color: #13171a;
}

@media (max-width: 576px) {
  main .part-banner .part-banner-content h1 {
    font-size: 26px;
  }
}

main .part-banner .part-banner-content h1 span {
  color: transparent;
  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(0deg, #0055fb, #0055fb);
  background-clip: text;
  -webkit-background-clip: text;
}

main .part-banner .part-banner-content h2 {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 100%;
  color: #13171a;
}

@media (max-width: 576px) {
  main .part-banner .part-banner-content h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }
}

main .part-banner .part-banner-content .btn {
  min-width: 282px;
}

@media (max-width: 768px) {
  main .part-banner .part-banner-content .btn {
    min-width: unset;
  }
}

main .part-banner .part-banner-content .logo-list {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

@media (max-width: 768px) {
  main .part-banner .part-banner-content .logo-list {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  main .part-banner .part-banner-content .logo-list .logo-img {
    flex: 1;
    max-height: 24px;
    object-fit: contain;
  }
}

main .part-files {
  background-color: #fff;
}

main .part-files .file-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 1rem;
  overflow: hidden;
}

main .part-files .file-box .file-box-content {
  background-color: #f9f9f9;
  border-radius: 0 0 1rem 1rem;
  padding: 1.3rem;
  flex: 1;
}

@media (max-width: 576px) {
  main .part-files .file-box .file-box-content {
    padding: 8px;
  }
}

main .part-files .file-box .file-box-content .box-title {
  font-weight: 700;
  font-size: 1.25rem;
  color: #000;
  text-decoration: none;
  display: inline-block;
}

main .part-files .file-box .file-box-content .box-title:hover {
  text-decoration: underline;
}

@media (max-width: 576px) {
  main .part-files .col-6 {
    padding-right: 8px;
    padding-left: 8px;
  }

  main .part-files .col-6:nth-child(odd) {
    padding-right: 4px;
  }

  main .part-files .col-6:nth-child(even) {
    padding-left: 4px;
  }
}

main {
  .part-unlock {
    background-color: #fff;
    @media (min-width: 992px) {
      #swiper-unlock .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: wrap;
      }

      #swiper-unlock .swiper-wrapper .swiper-slide {
        &.large-width {
          flex: 1 1 calc(57.4% - 1.875rem / 2);
        }
        &.small-width {
          flex: 1 1 calc(42.6% - 1.875rem / 2);
        }
      }
    }
    .unlock-item {
      position: relative;
      border-radius: 1.5rem;
      overflow: hidden;
      height: 100%;
      position: relative;
      @media (any-hover: hover) {
        &:hover {
          box-shadow: 0px 9px 12.8px 0px #428abb2e;
          &::after {
            content: "";
            position: absolute;
            inset: 0;
            border-radius: 1.5rem;
            padding: 1px;
            background: linear-gradient(138.99deg, #0087ff 8.45%, #6ad6ff 89.48%);
            mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
            mask-composite: exclude;
          }
        }
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .unlock-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        padding: 3rem 1.875rem;
        @media (max-width: 1280px) {
          padding: 1.5rem;
        }
        .unlock-content-title {
          font-weight: 700;
          font-size: 1.5rem;
          margin-bottom: 0.875rem;
        }
      }
    }
  }

  .part-solutions {
    background-color: #f5f8ff;

    .img-container {
      position: relative;
      line-height: 0;
      overflow: hidden;
      height: 100%;
    }

    .img-container img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    @media (max-width: 1280px) {
      .assetsSwiper .box-style .img-container img {
        all: unset;
        max-width: 100%;
        width: 100%;
      }
    }
    .assetsSwiper .box-style .assets-title {
      position: absolute;
      width: 100%;
      text-align: center;
      font-size: 2rem;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 24px;
      bottom: 0;
      left: 0;
      z-index: 2;
    }

    .assetsSwiper .swiper-slide.active .box-style .assets-title {
      display: none;
    }

    @media (max-width: 1280px) {
      .assetsSwiper .box-style .assets-title {
        display: none;
      }
    }
    @media (min-width: 1280px) {
      .assetsSwiper-box {
        position: relative;
      }

      .assetsSwiper .swiper-wrapper {
        aspect-ratio: 1920 / 625;
        gap: 6px;
        justify-content: space-between;
      }

      .assetsSwiper .swiper-slide {
        width: 18.9%;
        display: block;
        overflow: hidden;
        border-radius: 1rem;
        position: relative;
        transition: 0.6s cubic-bezier(0.05, 0.61, 0.41, 0.95);
      }

      .assetsSwiper .swiper-slide.active {
        width: 61.3%;
        opacity: 1;
      }

      .assetsSwiper .swiper-slide .box-style {
        height: 100%;
        position: relative;
        border-radius: 1rem;
        overflow: hidden;
      }

      @keyframes fadeIn {
        from {
          visibility: hidden;
        }

        to {
          visibility: visible;
        }
      }

      .assetsSwiper .swiper-slide .box-style .solutions-info-box {
        visibility: hidden;
      }

      .assetsSwiper .swiper-slide.active .box-style .solutions-info-box {
        animation: fadeIn 0.01s ease-in-out;
        animation-delay: 0.6s;
        animation-fill-mode: forwards;
        position: absolute;
        margin: 2rem;
        bottom: 0;
        left: 0;
        width: calc(100% - 2rem * 2);
        background-color: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(10px);
        border-radius: 1rem;
        padding: 1rem 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 40px;

        .solutions-info-box-download {
          position: absolute;
          top: 0;
          right: 0;
          z-index: 6;
          width: 100%;
          height: 100%;
          &:hover ~ .solutions-btn {
            .active-img {
              display: block;
            }
            .default-img {
              display: none;
            }
          }
        }
        .solutions-icon {
          max-width: 110px;
        }
        .solutions-info {
          opacity: 0.9;
          color: #fff;
          .title {
            margin-bottom: 6px;
            color: #fff;
            font-size: 1.5rem;
            font-weight: 700;
          }
          .detail {
            color: #fff;
          }
        }
        .solutions-btn {
          margin-left: auto;
          max-width: 6rem;
          .active-img {
            display: none;
          }
        }
      }

      .assetsSwiper .swiper-slide.active .box-style .solutions-info-box::after {
        content: url(https://images.wondershare.com/recoverit/images2025/solutions/white-tip.svg);
        width: 72px;
        position: absolute;
        aspect-ratio: 72 / 58;
        left: 0;
        top: 0;
        transform: translate(85%, -60%);
      }

      .assetsSwiper .swiper-slide.active .box-style .solutions-info-box .solutions-info {
        font-size: 1.125rem;
        font-weight: 700;
        color: rgba(255, 255, 255, 0.9);
        // max-width: 629px;
      }

      .assetsSwiper .swiper-slide:not(.active) .box-style::after {
        content: "";
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper {
        overflow: initial;
        padding-top: 24px;
      }
    }

    @media (max-width: 768px) {
      .assetsSwiper {
        padding-top: 0px;
      }
      .mobile-container {
        margin: 0 15px;
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper .swiper-slide {
        opacity: 0.5;
      }

      .assetsSwiper .swiper-slide-active {
        opacity: 1;
      }

      .assetsSwiper .rounded-16 {
        border-radius: 8px;
      }
      .box-style {
        border-radius: 18px;
        overflow: hidden;
        position: relative;
      }
      .solutions-info-box {
        position: absolute;
        bottom: 0;
        padding: 12px 16px;
        background: rgba(0, 0, 0, 0.69);

        .solutions-info-box-download,
        .solutions-icon,
        .solutions-btn {
          display: none;
        }
        .solutions-info {
          .title {
            font-size: 16px;
            margin-bottom: 8px;
            color: #fff;
          }
          .detail {
            font-size: 12px;
            color: #fff;
          }
        }
      }
    }
  }

  .part-step {
    background: url(https://images.wondershare.com/repairit/images2025/PDF-repair/step-bg.svg) no-repeat center center/cover;

    .nav {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      height: 100%;
    }

    @media (max-width: 768px) {
      .nav {
        gap: 1rem;
      }
    }

    .nav .nav-item {
      flex: 1;
    }

    .nav .nav-item.active .step-item {
      position: relative;
      box-shadow: 0px 8px 12px 0px #7cc5dc3d;
      border-color: transparent;
      border: unset;
    }

    .nav .nav-item.active .step-item::after {
      content: "";
      position: absolute;
      inset: 0;
      border-radius: 1rem;
      padding: 1px;
      background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
      mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
      mask-composite: exclude;
      pointer-events: none;
    }

    .nav .nav-item.active .step-item .step-item-number {
      font-size: 2.625rem;
      font-weight: 700;
    }

    .nav .nav-item.active .step-item .right-content .title {
      font-size: 1.25rem;
      font-weight: 700;
    }

    .nav .nav-item.active .step-item .right-content .detail {
      display: block;
    }

    .nav .nav-item .step-item {
      height: 100%;
      border-radius: 1rem;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 1.25rem;
      padding: 1.5rem;
      color: #000;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      background-color: #fff;
      border: 1px solid #bce0fe;
    }

    .nav .nav-item .step-item .step-item-number {
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
    }

    .nav .nav-item .step-item .right-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .nav .nav-item .step-item .right-content .detail {
      display: none;
      color: #787878;
      font-size: 14px;
    }
    .tab-pane {
      border-radius: 0.75rem;
      overflow: hidden;
    }

    .feature-list {
      display: flex;
      justify-content: center;
      gap: 1rem;
    }

    @media (max-width: 768px) {
      .feature-list {
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
      }
    }

    .feature-list .feature-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1.5rem 1rem;
      gap: 4px;
    }

    @media (max-width: 768px) {
      .feature-list .feature-item {
        padding: 0 1rem;
      }

      .feature-list .feature-item img {
        width: 36px;
      }
    }

    .feature-list .feature-item .feature-item-detail {
      font-size: 1.25rem;
      font-weight: 500;
      color: #444444;
    }

    .note-box {
      background-color: #ecfaff;
      border-radius: 1.375rem;
      padding: 1.125rem 1.5rem;
      display: flex;
      align-items: flex-start;
      gap: 0.9375rem;
    }

    .note-box .left-icon {
      flex-shrink: 0;
    }

    .note-box .right-content .content-detail {
      font-size: 0.875rem;
      font-weight: 500;
      color: #636363;
    }

    .growth-numbers-list {
      display: flex;
      gap: 1rem;
      gap: 12px;
    }

    @media (max-width: 992px) {
      .growth-numbers-list {
        flex-wrap: wrap;
      }
    }

    .growth-numbers-list .growth-numbers-item {
      flex: 1 1 25%;
      background-color: #fff;
      border-radius: 1.5rem;
      display: flex;
      flex-direction: column;
      padding: 2rem;
      align-items: center;
      justify-content: center;
    }

    @media (max-width: 768px) {
      .growth-numbers-list .growth-numbers-item {
        text-align: center;
        padding: 1rem;
        flex: 1 1 40%;
      }
    }

    .growth-numbers-list .growth-numbers-item .growth-number {
      font-weight: 800;
      font-size: 3rem;
      display: inline-block;
      color: transparent;
      background: linear-gradient(138.99deg, #0087ff 8.45%, #6ad6ff 89.48%);
      background-clip: text;
      -webkit-background-clip: text;
    }

    .growth-numbers-list .growth-numbers-item .growth-text {
      color: #787878;
      font-weight: 500;
      font-size: 1.25rem;
    }
  }

  .part-table {
    .table-box {
      overflow-x: auto;
    }
    .table-wrapper {
      margin-top: 13.125rem;
      border-radius: 24px;
      width: 100%;
      position: relative;
      z-index: 1;
      padding: 0 1.5rem;
      box-shadow: 0px 10px 20.3px 0px rgba(42, 128, 255, 0.15), inset 0 0 0 1px #2a80ff;
      @media (max-width: 1280px) {
        margin-top: 6rem;
        min-width: 660px;
      }
    }

    .table-wrapper .inner-table,
    .table-wrapper .table-blue {
      border-collapse: collapse;
      border-style: hidden;
      width: 100%;
    }

    .table-wrapper .inner-table .opacity-5,
    .table-wrapper .table-blue .opacity-5 {
      opacity: 0.5;
    }

    .table-wrapper .inner-table tr:not(:last-child),
    .table-wrapper .table-blue tr:not(:last-child) {
      border-bottom: 1px solid rgba(42, 128, 255, 0.3);
    }

    .table-wrapper .inner-table th,
    .table-wrapper .table-blue th {
      height: 6.5625rem;
      width: calc(100% / 3);
      font-weight: 700;
      font-size: 2rem;
      vertical-align: middle;
      text-align: center;
      border-bottom: 1px solid rgba(42, 128, 255, 0.3);
    }

    @media (max-width: 1280px) {
      .table-wrapper .inner-table th,
      .table-wrapper .table-blue th {
        font-size: 1.5rem;
        height: 5rem;
      }
    }

    .table-wrapper .inner-table td,
    .table-wrapper .table-blue td {
      height: 4.25rem;
      vertical-align: middle;
      text-align: center;
      font-size: 1.125rem;
    }

    @media (max-width: 1280px) {
      .table-wrapper .inner-table td,
      .table-wrapper .table-blue td {
        font-size: 1rem;
      }
    }

    .table-wrapper .inner-table td div,
    .table-wrapper .table-blue td div {
      display: flex;
      gap: 15px;
      align-items: center;
      justify-content: flex-start;
      padding: 0 3rem;
      margin: 0 auto;
      @media (max-width: 1280px) {
        padding: 0 1rem 0;
      }
    }

    .table-wrapper .blue-table-wrapper {
      position: absolute;
      bottom: 0;
      left: calc(100% / 3);
      width: calc(100% / 3);
      z-index: 2;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) -1.79%, rgba(255, 255, 255, 0) 28.14%),
        linear-gradient(14.25deg, #bde0ff -3.98%, #0084ff 18.31%);
      border: 1px solid white;
      border-radius: 1.5rem;
      padding: 3.4375rem 3rem 0;

      box-shadow: 0px 4.5px 13.84px 0px rgba(0, 89, 255, 0.25), 0px 4.5px 6.97px 0px rgba(255, 255, 255, 0.43) inset;
      @media (max-width: 1280px) {
        padding: 2.5rem 1rem 0;
      }
    }

    .table-wrapper .blue-table-wrapper .repairit-logo {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .table-wrapper .blue-table-wrapper .repairit-logo img {
      width: 7.5rem;
      @media (max-width: 1280px) {
        width: 4.5rem;
      }
    }

    .table-wrapper .blue-table-wrapper .table-blue {
      width: 100%;
      border-collapse: collapse;
      border-style: hidden;
    }

    .table-wrapper .blue-table-wrapper .table-blue th {
      color: #fff;
      border-bottom: 1px solid rgba(255, 255, 255, 0.35);
    }

    .table-wrapper .blue-table-wrapper .table-blue tr:last-child td {
      border-bottom: none;
    }

    .table-wrapper .blue-table-wrapper .table-blue td {
      color: #fff;
      border-bottom: 1px solid rgba(255, 255, 255, 0.35);
    }

    .table-wrapper .blue-table-wrapper .table-blue td div {
      all: unset;
      display: flex;
      gap: 15px;
      color: #fff;
      padding: 0 1rem;
    }

    .table-wrapper .blue-table-wrapper .table-blue td div span {
      text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      text-align: left;
    }

    .btn-white {
      color: #0170ff;
    }
  }
}

main .part-customer {
  background-color: #f5f8ff;
}

main .part-customer .customer-wrapper {
  border-radius: 2rem;
  overflow: hidden;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
}

main .part-customer .customer-wrapper .customer-img {
  position: relative;
}

main .part-customer .customer-wrapper .customer-img .customer-info-list {
  position: absolute;
  top: 20px;
  left: 40px;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #fff;
  font-weight: 600;
  gap: 24px;
}

@media (max-width: 768px) {
  main .part-customer .customer-wrapper .customer-img .customer-info-list {
    display: none;
  }
}

main .part-customer .customer-wrapper .customer-img .customer-info-list.right {
  right: 32px;
  left: unset;
}

main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-title,
main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-profession,
main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-age {
  position: relative;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.21);
  border-radius: 0 3px 3px 0;
  padding-right: 6px;
}

main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-title::before,
main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-profession::before,
main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-age::before {
  content: "";
  position: absolute;
  height: 100%;
  aspect-ratio: 22 / 31;
  left: 0;
  top: 0;
  transform: translateX(-97%);
  background: url(https://images.wondershare.com/repairit/images2025/PPT-repair/left-tip.png) no-repeat center center/contain;
}

main .part-customer .customer-wrapper .customer-detail {
  flex: 1;
  display: flex;
  gap: 0.5rem;
}

@media (max-width: 992px) {
  main .part-customer .customer-wrapper .customer-detail {
    flex-direction: column;
  }
}

main .part-customer .customer-wrapper .customer-detail .problem-wrapper {
  flex: 1 1 41.2%;
  padding: 1.875rem 1.5rem;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 12px;
}

@media (max-width: 992px) {
  main .part-customer .customer-wrapper .customer-detail .problem-wrapper {
    padding: 1rem;
    padding-bottom: 0;
  }
}

main .part-customer .customer-wrapper .customer-detail .problem-wrapper .left-icon {
  width: 4.5rem;
  flex-shrink: 0;
}

@media (max-width: 576px) {
  main .part-customer .customer-wrapper .customer-detail .problem-wrapper .left-icon {
    width: 2.5rem;
  }
}

main .part-customer .customer-wrapper .customer-detail .problem-wrapper .right-content .title {
  font-weight: 800;
  font-size: 1.5rem;
  color: #000;
  margin-bottom: 12px;
}

main .part-customer .customer-wrapper .customer-detail .problem-wrapper .right-content .detail {
  font-size: 1.125rem;
  color: #3c3c3c;
}

main .part-customer .customer-wrapper .customer-detail .customer-detail-dividing {
  border-right: 1px dashed rgba(0, 0, 0, 0.07);
}

main .part-customer .customer-wrapper .customer-detail .how-wrapper {
  flex: 1 1 58.8%;
  padding: 1.875rem 1.5rem;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 12px;
}

@media (max-width: 992px) {
  main .part-customer .customer-wrapper .customer-detail .how-wrapper {
    padding: 1rem;
    padding-top: 0;
  }
}

@keyframes icon-rotate {
  0% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

main .part-customer .customer-wrapper .customer-detail .how-wrapper .left-icon {
  text-decoration: none;
  animation: icon-rotate 3s linear infinite;
  flex-shrink: 0;
  width: 4.5rem;
}

@media (max-width: 576px) {
  main .part-customer .customer-wrapper .customer-detail .how-wrapper .left-icon {
    width: 2.5rem;
  }
}

main .part-customer .customer-wrapper .customer-detail .how-wrapper .right-content .title {
  font-weight: 800;
  font-size: 1.5rem;
  color: #000;
  margin-bottom: 12px;
}

main .part-customer .customer-wrapper .customer-detail .how-wrapper .right-content .detail {
  font-size: 1.125rem;
  color: #3c3c3c;
}

main .part-customer .left-btn,
main .part-customer .right-btn {
  background-color: #c0c0c0;
  width: 2.25rem;
  aspect-ratio: 1 / 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
  position: absolute;
  top: 36%;
}

@media (max-width: 576px) {
  main .part-customer .left-btn,
  main .part-customer .right-btn {
    display: none;
  }
}

main .part-customer .left-btn:hover,
main .part-customer .right-btn:hover {
  background-color: #006dff;
}

main .part-customer .right-btn {
  right: -3.25rem;
}

@media (max-width: 768px) {
  main .part-customer .right-btn {
    right: 1.55rem;
  }
}

main .part-customer .left-btn {
  left: -3.25rem;
}

@media (max-width: 768px) {
  main .part-customer .left-btn {
    left: 1.55rem;
  }
}

@media (min-width: 992px) {
  main .part-tip #swiper-tips .swiper-wrapper {
    gap: 1.875rem;
    flex-wrap: wrap;
  }

  main .part-tip #swiper-tips .swiper-wrapper .swiper-slide {
    flex: 1 1 calc(33% - 1.875rem);
  }
}

main .part-tip .tip-item {
  border-radius: 2rem;
  position: relative;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 3rem 2rem;
  color: #000;
  z-index: 3;
  transition: all 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background-color: #fff;
  height: 100%;
}

main .part-tip .tip-item:hover {
  box-shadow: 0px 0px 12px 0px #00d1ff4d;
}

main .part-tip .tip-item:hover::after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 2rem;
  padding: 2px;
  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(127.35deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 36.09%);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-image: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask-composite: exclude;
}

main .part-tip .tip-item:hover .text-detail {
  top: 2px;
}

main .part-tip .tip-item .tip-icon {
  height: 6rem;
  width: 6rem;
}

main .part-tip .tip-item .text-detail {
  position: absolute;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  padding: 0rem 2rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  z-index: 2;
  border-radius: 2rem;
  left: 2px;
  top: 100%;
  overflow: hidden;
  background-image: url(https://images.wondershare.com/repairit/images2025/PPT-repair/tip-card-bg.svg) !important;
  transition: all 0.3s;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

main .part-faq {
  .accordion-box {
    background-color: #fff;
    border-radius: 1.5rem;
    padding: 0.5rem 4rem;
  }

  @media (max-width: 992px) {
    .accordion-box {
      padding: 0.5rem 2rem;
    }
  }

  @media (max-width: 768px) {
    .accordion-box {
      padding: 0.5rem 1rem;
    }
  }

  .accordion-box .accordion-item {
    padding: 1.5rem 0;
  }

  .accordion-box .accordion-item:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .accordion-box .accordion-item svg {
    transition: all 0.2s linear;
  }

  @media (max-width: 768px) {
    .accordion-box .accordion-item svg {
      width: 1rem;
    }
  }

  @media (max-width: 768px) {
    .accordion-box .accordion-item {
      padding: 1rem 0.5rem;
    }
  }

  .accordion-box .accordion-item [aria-expanded="true"] svg {
    transform: rotate(180deg);
  }

  .accordion-box .accordion-item .serial-number {
    display: inline-flex;
    width: 22px;
    height: 22px;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
    border-radius: 50%;
    margin-right: 8px;
    font-size: 1rem;
    font-weight: 800;
    flex-shrink: 0;
  }

  @media (max-width: 768px) {
    .accordion-box .accordion-item .serial-number {
      width: 16px;
      height: 16px;
      color: #fff;
    }
  }

  .accordion-box .accordion-item .faq-detail {
    font-size: 14px;
    padding-top: 1rem;
    opacity: 0.7;
    padding-left: 30px;
    padding-right: 32px;
  }

  @media (max-width: 768px) {
    .accordion-box .accordion-item .faq-detail {
      padding-left: 20px;
      padding-right: 16px;
    }
  }
}

main .part-links .part-links-line {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

main .part-links .line-border {
  border-right: 1px solid rgba(0, 0, 0, 0.3);
  border-left: 1px solid rgba(0, 0, 0, 0.3);
}

@media (max-width: 1280px) {
  main .part-links .line-border {
    border-right: unset;
  }
}

@media (max-width: 768px) {
  main .part-links .line-border {
    border-left: unset;
  }
}

main .part-links .text-link {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.7);
  margin-top: 1.5rem;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

main .part-links .text-link:hover {
  color: #0055fb;
}

main .part-links .part-links-videos {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

main .part-links .part-links-videos .video-wrapper {
  border-radius: 0.75rem;
}

@media (max-width: 1280px) {
  main .part-links .part-links-videos {
    flex-direction: row;
    padding-top: 2rem;
  }
}

@media (max-width: 576px) {
  main .part-links .part-links-videos {
    display: block;
  }
}

main .part-links .text-line4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes changeWidth {
  0% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

main .part-feature .intelligence-content {
  position: absolute;
  top: 0;
  left: 0%;
  z-index: 2;
}

main .part-feature .intelligence-item {
  border-radius: 1.5rem;
  overflow: hidden;
  background-color: #fff;
  color: #000;
}

main .part-feature .intelligence-item .compare-before {
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0;
  top: 0;
  background-size: auto 100%;
  background-repeat: no-repeat;
  z-index: 2;
  animation: changeWidth 6s linear infinite;
}

main .part-feature .intelligence-item .compare-before::after {
  content: "";
  width: 2px;
  height: 100%;
  background: #fff;
  position: absolute;
  right: 0;
  top: 0;
}

main .part-feature .intelligence-item .compare-before::before {
  content: "";
  background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 5rem;
  height: 3rem;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  z-index: 3;
}

@media (max-width: 768px) {
  main .part-feature .intelligence-item .compare-before::before {
    width: 3rem;
    height: 2rem;
  }
}

main .part-feature .intelligence-item .compare-before.compare-before-1 {
  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/word-file-repair-before.jpg);
}

main .part-feature .intelligence-item .compare-before.compare-before-2 {
  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/excel-file-repair-before.jpg);
  animation: changeWidth 8s linear infinite;
}

main .part-feature .intelligence-item .compare-before.compare-before-3 {
  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/powerpoint-file-repair-before.jpg);
  animation: changeWidth 7s linear infinite;
}

main .part-feature .intelligence-item .item-link {
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 768px) {
  main .part-feature .intelligence-item .item-link {
    margin-bottom: 0.5rem;
  }
}

main .part-feature .intelligence-item .item-link .normal-arrow {
  display: inline;
}

@media (max-width: 768px) {
  main .part-feature .intelligence-item .item-link .normal-arrow,
  .active-arrow {
    height: 2.5rem;
    width: 2.5rem;
  }
}

main .part-feature .intelligence-item .item-link .active-arrow {
  display: none;
}

main .part-feature .intelligence-item .item-link:hover {
  color: #0458ff;
}

main .part-feature .intelligence-item .item-link:hover .normal-arrow {
  display: none;
}

main .part-feature .intelligence-item .item-link:hover .active-arrow {
  display: inline;
}

main .part-footer {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

main .part-footer .btn-download {
  padding-top: 18.5px;
  padding-bottom: 18.5px;
}

@media (max-width: 992px) {
  main .part-footer .btn-download {
    padding-top: 15.2px;
    padding-bottom: 15.2px;
  }
}

main .part-footer .part-footer-logo {
  height: 4rem;
  width: 14.5rem;
  margin: 0 auto;
}

@media (max-width: 576px) {
  main .part-footer .display-2 {
    font-size: 2.25rem;
  }

  main .part-footer a {
    display: block;
  }

  main .part-footer .btn-outline-action {
    background-color: #fff;
    vertical-align: text-bottom;
    &:hover {
      color: #fff;
      background-color: #006dff;
      border-color: #006dff;
    }
  }
}
