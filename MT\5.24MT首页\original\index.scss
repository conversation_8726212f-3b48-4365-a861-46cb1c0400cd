* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #fff;
  color: #000;

  @media (max-width: 1280px) {
    background-color: #f4f7ff;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
  }

  h1,
  h2 {
    text-align: center;
  }

  .display-3 {
    @media (max-width: 576px) {
      font-size: 2.5rem;
    }
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .text-blue {
    color: #71adff;
  }
  .text-blue2 {
    color: #046fff;
  }
  .text-blue3 {
    color: #3b8eff;
  }

  .btn-wrapper {
    display: flex;
    gap: 1rem;
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
      align-items: center;
    }
  }
  .btn {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
    gap: 8px;
    svg {
      max-width: 100%;
      height: 100%;
    }
    @media (max-width: 768px) {
      display: block;
    }
    &.dev-mobile {
      width: 168.75px;
      min-width: unset !important;
    }
  }
  .btn-download {
    border: 1px solid #ffffff;
    background: linear-gradient(89.57deg, #00c8ff -10.59%, #0084ff 15.01%, #006fff 83.38%, #00c8ff 107.75%);
    box-shadow: 0px 4.5px 6.97px 0px #ffffff6e inset, 0px -6.75px 16.65px 0px #00e5ffd6 inset, 0px 4.5px 13.84px 0px #0059ff40;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-transform: capitalize;
    border-radius: 0.875rem;
    color: #fff;
    min-width: 248px;
    overflow: hidden;
    position: relative;

    &:focus,
    &:active {
      color: #fff;
    }
    @media (min-width: 992px) {
      height: 4rem;
    }

    .btn-text-wrap {
      position: relative;
      overflow: hidden;
      color: inherit;
      .btn-hover-text-wrap {
        transition: transform 0.4s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: inherit;
        &.rel {
          position: relative;
          transform: translateY(0);
        }
        &.abs {
          position: absolute;
          top: 120%;
          transform: translateY(0);
          transition-duration: 0.45s;
        }
      }
    }

    @media (any-hover: hover) {
      &:hover {
        color: #fff;
      }

      &:hover .btn-hover-text-wrap.rel {
        color: inherit;
        transform: translateY(-100%);
      }

      &:hover .btn-hover-text-wrap.abs {
        color: inherit;
        transform: translateY(-120%);
      }
    }

    @keyframes marquee1 {
      0% {
        transform: translateX(0);
      }

      100% {
        transform: translateX(-50%);
      }
    }
    @keyframes marquee2 {
      0% {
        transform: translateX(-50%);
      }

      100% {
        transform: translateX(0%);
      }
    }
    @keyframes marquee1-vertical {
      0% {
        transform: translateY(0);
      }

      100% {
        transform: translateY(-50%);
      }
    }
    @keyframes marquee2-vertical {
      0% {
        transform: translateY(-50%);
      }

      100% {
        transform: translateY(0%);
      }
    }
  }

  .qr-code-icon-wrapper {
    position: relative;
    z-index: 5;
    @media (max-width: 1280px) {
      display: none;
    }
    .active-icon {
      display: none;
    }
    .qrcode-box {
      width: max-content;
      position: absolute;
      top: -8px;
      max-width: 128px;
      left: 50%;
      transform: translate(-50%, -100%);
      transition: opacity 0.2s ease-in-out;
      opacity: 0;
      pointer-events: none;
    }
    &:hover {
      .active-icon {
        display: inline-block;
      }
      .default-icon {
        display: none;
      }
      .qrcode-box {
        opacity: 1;
      }
    }
  }
  .swiper-pagination {
    bottom: -10px !important;
    .swiper-pagination-bullet {
      width: 10px;
      height: 10px;
      border-radius: 100px;
      background-color: rgba($color: #006dff, $alpha: 0.3);
      opacity: 1;
      &.swiper-pagination-bullet-active {
        width: 40px;
        border-radius: 100px;
        opacity: 1;
        background-color: rgba($color: #006dff, $alpha: 0.7);
      }
    }
  }

  .part-banner {
    h1 {
      font-size: 4rem;
      font-weight: 700;
      line-height: 110%;
      @media (max-width: 576px) {
        font-size: 40px;
      }
    }
    p {
      font-size: 0.875rem;
      font-weight: 700;
      color: #c9c9c9;
      padding: 1rem 0;
      text-align: center;
    }
    .container {
      position: relative;
      z-index: 2;
    }
    .btn {
      border-radius: 0.75rem;
    }
    .btn-action {
      background-color: #1da4ff;
      border-color: #1da4ff;
      color: #fff;
      &:hover {
        color: #fff;
        background-color: #005dd9;
        border-color: #0057cc;
      }
    }
    .system-list {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      margin-top: 1.5rem;
      a {
        color: #b5adad;
        text-decoration: none;
        position: relative;
        @media (any-hover: hover) {
          &:hover {
            color: #000;
            .qrcode-box {
              opacity: 1;
            }
          }
        }
      }
      .qrcode-box {
        width: max-content;
        position: absolute;
        bottom: -8px;
        max-width: 128px;
        left: 50%;
        transform: translate(-50%, 100%) rotate(180deg);
        transition: opacity 0.2s ease-in-out;
        opacity: 0;
        pointer-events: none;
      }
    }
    .banner-animation {
      display: flex;
      position: relative;
      margin-top: -2.5%;
      pointer-events: none;
      @media (max-width: 768px) {
        display: none;
      }
      .left-hand,
      .right-hand {
        width: 44.68%;
        flex-shrink: 0;
      }
      .left-hand {
        transform: translateX(-20%);
        &.loaded {
          transform: translateX(0);
          transition: transform 1s ease-in-out;
        }
      }
      .right-hand {
        transform: translateX(20%);

        &.loaded {
          transform: translateX(0);
          transition: transform 1s ease-in-out;
        }
      }
      .icon-list-wrapper {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        overflow: hidden;
        transform: translateY(32.4%);
        opacity: 0;
        @media (max-width: 992px) {
          gap: 0.9rem;
        }

        &.loaded {
          opacity: 1;
          transition: opacity 1s ease-in-out;
        }

        .icon-list1,
        .icon-list2,
        .icon-list3 {
          width: fit-content;
          display: flex;

          flex-shrink: 0;
          overflow: hidden;
          height: 7%;
          img {
            max-width: fit-content;
            padding: 0 6px;
            max-height: 100%;
          }
        }
        .icon-list1,
        .icon-list3 {
          animation: marquee1 30s linear infinite;
        }
        .icon-list2 {
          animation: marquee2 30s linear infinite;
        }
      }
    }
  }

  .part-video {
    background-color: #f1faff;
    @media (max-width: 768px) {
      background-color: #f4f7ff;
    }
    .video-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      line-height: 0;
      font-size: 0;
      overflow: hidden;
      background-size: cover;
      background-position: center;
      background-color: #f1faff;
      object-fit: cover;

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        filter: grayscale(0);
        clip-path: fill-box;
      }

      .video-content {
        position: absolute;
        bottom: 5%;
        left: 50%;
        width: 70%;
        height: auto;
        transform: translateX(-50%);
        z-index: 2;
        text-align: center;
        color: #777777;
        font-size: 2.5rem;
        font-weight: 700;
        line-height: 130%;
        letter-spacing: -1px;
        @media (max-width: 1600px) {
          font-size: 2rem;
        }
        @media (max-width: 1280px) {
          font-size: 1.5rem;
        }
      }
    }
    .mobile-content {
      font-weight: 700;
      line-height: 130%;
      color: #777777;
      font-size: 18px;
      margin: 0 2.625rem;
      text-align: center;
    }
    .mobile-icon-line {
      display: flex;
      flex-wrap: nowrap;
      gap: 2.375rem;
      width: fit-content;
      margin-top: 2.125rem;
      animation: marquee2 30s linear infinite;
      img {
        height: 4.25rem;
        max-width: fit-content;
        @media (max-width: 576px) {
          height: 30px;
        }
      }
      animation: marquee1 30s linear infinite;
    }
  }

  .part-number {
    background-color: #edf7ff;
    #swiper-number {
      overflow: visible;
    }
    @media (min-width: 992px) {
      #swiper-number .swiper-wrapper {
        gap: 1.875rem;
        justify-content: center;
      }

      #swiper-number .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(33% - 1.875rem);
      }
    }

    .number-item {
      border-radius: 1rem;
      position: relative;
      background-color: #046fff;
      padding: 4.625rem 2.125rem 2rem;
      height: 100%;
      color: #fff;
      z-index: 3;
      transition: unset;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: column;
      text-align: left;
      text-decoration: none;
      gap: 4rem;
      .card-link {
        position: absolute;
        z-index: 5;
        width: 100%;
        height: 100%;
        text-decoration: none;
      }
      .right-icon {
        position: absolute;
        width: 1.75rem;
        height: 1.75rem;
        top: 1.25rem;
        right: 1.25rem;
        border-radius: 50%;
        background-color: #fff;
        color: #046fff;
        opacity: 0.3;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .top-detail {
        opacity: 0.6;
      }
      .bottom-number {
        width: 100%;
        .num {
          font-weight: 700;
          font-size: 4rem;
          line-height: 90%;
          display: flex;
          align-items: flex-start;
          gap: 4px;
        }
      }
    }

    .number-item:hover {
      background: #222222;

      .right-icon {
        opacity: 1;
        background-color: #fff;
        color: #222222;
      }
    }
  }
  .part-transfer1,
  .part-transfer2,
  .part-transfer3 {
    .transfer-box {
      display: flex;
      justify-content: space-between;
      background: url(./images/transfer-bg1.jpg) no-repeat center center/cover;
      border-radius: 2rem;
      overflow: hidden;
      background-color: #222222;
      @media (max-width: 1280px) {
        flex-direction: column;
        background: unset;
        background-color: #222222;
        padding-bottom: 36px;
        .btn-wrapper {
          justify-content: center;
        }
      }
      h2 {
        line-height: 110%;
        font-size: 4rem;
        @media (max-width: 1600px) {
          font-size: 3.5rem;
        }
        @media (max-width: 576px) {
          font-size: 28px;
        }
      }
      .img-wrapper {
        @media (max-width: 1280px) {
          display: none;
        }
      }
      .transfer-box-text {
        flex: 0 0 34.75%;
        padding: 4rem 0;
        padding-left: 7.5rem;
        color: #fff;
        @media (max-width: 1600px) {
          padding-left: 3rem;
          flex: 0 0 40.75%;
        }
        @media (max-width: 1280px) {
          order: 2;
          flex: unset;
          padding: 0 15px;
          text-align: center;
        }
        .switch-tab {
          border-radius: 10px;
          background-color: #000;
          display: inline-flex;
          justify-content: center;
          margin-bottom: 2.5rem;
          @media (max-width: 1280px) {
            display: none;
          }

          .phone-box,
          .computer-box {
            flex: 1 1 50%;
            padding: 0.5rem 1.5rem;
            color: #b7bfd4;
            border-radius: 10px;
            cursor: pointer;
            &.active {
              background-color: #046fff;
              color: #fff;
            }
          }
        }
        .transfer-box-desc {
          font-size: 1.125rem;
          opacity: 0.5;
          color: #fff;
          margin-top: 0.875rem;
          margin-bottom: 0.25rem;
        }
      }
      .transfer-box-img {
        flex: 0 1 57.16%;
        padding-top: 4rem;

        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        @media (max-width: 1280px) {
          flex: auto;
          padding: 0 0 24px;
        }

        .marquee-wrapper {
          position: relative;
          overflow: hidden;
          margin-right: 5.625rem;
          @media (max-width: 1280px) {
            margin-right: 0;
          }
          &::before {
            content: "";
            position: absolute;
            display: block;
            top: 0;
            left: 0;
            width: 20%;
            height: 100%;
            z-index: 2;
            background: linear-gradient(90deg, #222222 0%, rgba(34, 34, 34, 0) 100%);
            @media (max-width: 1280px) {
              display: none;
            }
          }
          &::after {
            content: "";
            position: absolute;
            display: block;
            top: 0;
            right: 0;
            width: 20%;
            height: 100%;
            z-index: 2;
            background: linear-gradient(90deg, rgba(34, 34, 34, 0) 0%, #222222 100%);
            @media (max-width: 1280px) {
              display: none;
            }
          }
          .top-imgList,
          .bottom-imgList {
            display: flex;
            flex-wrap: nowrap;
            gap: 2.375rem;
            width: fit-content;
            .logo-line {
              height: 4.25rem;
              max-width: fit-content;
              @media (max-width: 576px) {
                height: 40px;
              }
            }
          }
          .top-imgList {
            animation: marquee1 30s linear infinite;
          }
          .bottom-imgList {
            animation: marquee2 30s linear infinite;
          }
        }
        .transfer-phone-img1 {
          padding-right: 5.625rem;
        }
      }
    }
  }
  .part-transfer2 {
    .transfer-box {
      min-height: 694px;
      @media (max-width: 1280px) {
        min-height: unset;
      }

      .transfer-box-img {
        display: flex;
        flex: 0 0 52%;
        box-sizing: border-box;
        padding-top: unset;
        justify-content: space-between;
        &.phone-box-change {
          flex-direction: row;
          padding-left: 7.5rem;
          @media (max-width: 1600px) {
            padding-left: 3rem;
          }
          @media (max-width: 1280px) {
            display: none;
          }
          .img-wrapper {
            flex: 0 0 61%;
            @media (max-width: 1600px) {
              flex: 0 0 67%;
            }
          }

          .marquee-wrapper2 {
            flex: 0 0 24%;
            display: flex;
            gap: 1rem;
            flex-shrink: 0;
            aspect-ratio: 1 / 3;
            overflow: hidden;
            position: relative;
            &::before {
              content: "";
              position: absolute;
              display: block;
              top: 0;
              left: 0;
              width: 100%;
              height: 20%;
              z-index: 2;
              background: linear-gradient(#222222 0%, rgba(34, 34, 34, 0) 100%);
            }
            &::after {
              content: "";
              position: absolute;
              display: block;
              bottom: 0;
              right: 0;
              width: 100%;
              height: 20%;
              z-index: 2;
              background: linear-gradient(rgba(34, 34, 34, 0) 0%, #222222 100%);
            }

            .left-imgList,
            .right-imgList {
              display: flex;
              flex-direction: column;
              gap: 2.375rem;
              height: fit-content;
            }
            .left-imgList {
              animation: marquee1-vertical 30s linear infinite;
            }
            .right-imgList {
              animation: marquee2-vertical 30s linear infinite;
            }
          }
        }
        &.computer-box-change {
          .marquee-wrapper {
            padding-top: 4.875rem;
            padding-left: 2.75rem;
            margin-right: 0;
            @media (max-width: 1280px) {
              padding: 0;
            }
          }
          @media (max-width: 1280px) {
            display: block !important;
          }
        }
      }
      .transfer-box-text {
        box-sizing: border-box;
        color: #fff;
        flex: 0 0 40%;
        padding: 4rem 0;
        padding-right: 3.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        @media (max-width: 1600px) {
          padding-right: 3rem;
          flex: 0 0 44%;
        }
        @media (max-width: 1280px) {
          order: 2;
          flex: unset;
          padding: 0 15px;
          text-align: center;
          align-items: stretch;
        }
      }
    }
  }
  .part-transfer3 {
    .transfer-box {
      .transfer-box-text {
        flex: 0 0 40%;
      }
    }
    .computer-box {
      background-color: #046fff;
      color: #fff;
      margin-bottom: 2.5rem;
      display: inline-block;
      border-radius: 2rem;
      padding: 0.5rem 1.5rem;
      border-radius: 10px;
      @media (max-width: 1280px) {
        display: none;
      }
    }
  }

  .part-feature {
    #swiper-features {
      overflow: visible;
    }
    @media (min-width: 992px) {
      #swiper-features .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: wrap;
        justify-content: center;
      }

      #swiper-features .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(33% - 1.875rem);
        max-width: 330px;
      }
    }

    .feature-item {
      border-radius: 1rem;
      position: relative;
      background: linear-gradient(180deg, #046fff 0%, #046fff 100%);
      padding: 1.25rem 2rem 2.875rem;
      color: #fff;
      z-index: 3;
      transition: unset;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      text-align: left;
      text-decoration: none;
      display: block;
      margin-top: 37%;
      @media (max-width: 576px) {
        margin-top: 30%;
      }
      .right-icon {
        position: absolute;
        width: 28px;
        height: 28px;
        top: 1.375rem;
        right: 1.125rem;
        border-radius: 50%;
        background-color: #fff;
        opacity: 0.3;
      }

      .feature-icon {
        margin-top: -37%;
        transform: scale(1.2);
        margin-left: auto;
        margin-right: auto;
        @media (max-width: 576px) {
          transform: scale(1);
        }
      }
    }

    .feature-item:hover {
      background: #2f2f2f;
      box-shadow: 0px 52px 49.1px -50px #046fff;
      .right-icon {
        opacity: 1;
        background-color: #06ffea;
        background: #06ffea;
      }
    }

    .feature-item .text-detail {
      opacity: 0.8;
    }
  }

  .part-customer {
    @keyframes progressAnimation {
      0% {
        background: conic-gradient(from 180deg at 50% 50%, #0072ff 0deg, rgba(0, 114, 255, 0) 0deg);
      }

      42.3% {
        // 152.31度对应360度的42.3%
        background: conic-gradient(from 180deg at 50% 50%, #0072ff 0deg, rgba(0, 114, 255, 0) 152.31deg, rgba(0, 114, 255, 0) 360deg);
      }

      100% {
        background: conic-gradient(from 180deg at 50% 50%, #0072ff 0deg, rgba(0, 114, 255, 0) 360deg);
      }
    }
    .customer-item {
      position: relative;
      z-index: 1;
      .customer-img-bg {
        min-height: 720px;
        object-fit: cover;
      }
      .black-modal {
        position: absolute;
        z-index: 2;
        width: 73.5%;
        height: 64%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(14px);
        border-radius: 1.5rem;
        min-width: 1280px;
        min-height: 520px;
      }
      .content-box {
        position: absolute;
        z-index: 4;
        width: 73.5%;
        height: 64%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 1.5rem;
        overflow: hidden;
        padding: 3rem;
        display: flex;
        justify-content: space-between;
        color: #fff;
        min-width: 1280px;
        min-height: 517px;
        .score-content {
          display: flex;
          flex-direction: column;
          justify-content: center;

          .score {
            font-weight: 700;
            font-size: 6rem;
            line-height: 110%;
            color: #09ffae;
          }
          .score-img-wrapper {
            max-width: 270px;
          }
        }
        .info-content {
          flex: 0 1 30%;
          .font-size-super {
            font-size: 1.5rem;
            line-height: 110%;
          }
        }
      }
      .customer-img {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 3;
        pointer-events: none;
      }
    }
    .customer-avatar-list {
      position: absolute;
      z-index: 6;
      left: 50%;
      transform: translateX(-50%);
      bottom: 5%;
      width: 100%;
      display: flex;
      justify-content: center;
      gap: 18px;
      @keyframes progressAnimation {
        0% {
          stroke-dashoffset: 125.6; // 初始状态，完全未填充
        }
        100% {
          stroke-dashoffset: 36; // 最终状态，完全填充
        }
      }
      .customer-avatar-item {
        width: 2.125rem;
        aspect-ratio: 1 / 1;
        border-radius: 50%;
        cursor: pointer;
        position: relative;
        svg {
          opacity: 0;
          width: 100%;
          height: 100%;

          position: absolute;
          left: 0;
          top: 0px;
          z-index: 10;
          width: 100%;
          height: 100%;
          stroke-width: 2px;
          stroke: #0373ff;
          fill: none;
          stroke-dashoffset: 125.6;
          stroke-dasharray: 125.6;
          transform: rotate(90deg);
        }

        &.active {
          position: relative;
          transform: scale(1.5);
          padding: 2px;

          svg {
            opacity: 1;
            animation: progressAnimation 4s linear forwards;
          }
        }
      }
    }
    .score-content-mobile {
      text-align: center;
      .score {
        font-weight: 700;
        font-size: 88px;
        color: #046dfb;
        line-height: 110%;
      }
      .review-count {
        font-size: 15px;
        font-weight: 500;
      }
      .score-img-wrapper {
        max-width: 250px;
        margin: 0 auto;
      }
    }
    .customer-item-mobile {
      border-radius: 16px;
      overflow: hidden;
      background-color: #222222;
      height: 100%;

      .customer-item-mobile-img {
        position: relative;
        width: 100%;

        .customer-info {
          position: absolute;
          left: 16px;
          bottom: 16px;
          font-size: 14px;
          color: #fff;
        }
      }
      .customer-item-mobile-content {
        padding: 24px 14px;
        max-height: 220px;
        @media (max-width: 1280px) {
          overflow-y: auto;
        }
        .customer-comment {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.5);
        }
        /* 针对特定容器的滚动条样式 */

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.2);
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.3);

          &:hover {
            background: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }
  }
  .part-links {
    .link-wrapper {
      margin: 0 auto;
      max-width: 94.25rem;
      display: flex;
      @media (max-width: 1600px) {
        flex-wrap: wrap;
      }
      .part-links-line-wrapper {
        flex: 1 0 30%;
        padding: 0 3rem;
        @media (max-width: 576px) {
          padding: 0 15px;
        }
      }
      .part-links-videos-wrapper {
        flex: 1 1 40%;
        padding: 0 3rem;
        @media (max-width: 1600px) {
          flex: 1 1 100%;
          margin-top: 1.5rem;
        }
        @media (max-width: 576px) {
          padding: 0 15px;
        }
      }
    }
    .part-links-line {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .line-border {
      border-right: 1px solid rgba(0, 0, 0, 0.3);
      border-left: 1px solid rgba(0, 0, 0, 0.3);
      @media (max-width: 1600px) {
        border-right: unset;
      }
    }

    @media (max-width: 1280px) {
      .line-border {
        border-right: unset;
      }
    }

    @media (max-width: 768px) {
      .line-border {
        border-left: unset;
      }
    }

    .text-link {
      font-size: 0.875rem;
      color: #000;
      margin-top: 1.5rem;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-decoration: none;
    }

    .text-link:hover {
      color: #0055fb;
    }

    .part-links-videos {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .part-links-videos .video-wrapper {
      border-radius: 0.375rem;
      min-width: 11.375rem;
    }
    .video-link-list {
      display: flex;
      gap: 0.75rem;
      margin-top: 1.5rem;
      @media (max-width: 576px) {
        flex-direction: column;
      }
      a {
        flex: 1 1 30%;
      }
      a:hover {
        color: #006dff;
        text-decoration: none;
      }
    }

    @media (max-width: 576px) {
      .part-links-videos {
        display: block;
      }
    }

    .text-line2 {
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
  .part-footer {
    .btn-wrapper .btn-white {
      color: #0080ff;
    }

    .btn-wrapper .btn-outline-white:hover {
      color: #0080ff;
    }

    .footer-box {
      border-radius: 1.25rem;
      background: url(./images/footer-bg.jpg) no-repeat center center/cover;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #fff;
      padding: 6rem 0;
      color: #fff;
    }

    @media (max-width: 768px) {
      .footer-box {
        padding: 2.5rem 1rem;
        margin: 0 15px;
        text-align: center;
      }
    }

    .footer-box .btn {
      min-width: 210px;
      border-radius: 4px;
    }
  }
}
