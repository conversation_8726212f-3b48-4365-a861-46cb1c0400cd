* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #f5f8ff;
  color: #000;
  background-color: #e3eefc;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span {
    margin-bottom: 0;
  }

  h1,
  h2,
  h3 {
    text-align: center;
  }
  h2 {
    font-size: 2.25rem;
    font-weight: 800;
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .blue-text {
    color: #006dff;
  }

  .btn-wrapper {
    display: flex;

    justify-content: center;
    gap: 1rem;
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
    }
    .btn {
      margin: 0;
      border-radius: 4px;
      text-transform: capitalize;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 160px;
      @media (max-width: 768px) {
        display: block;
        vertical-align: baseline;
      }
    }
  }
  .btn-download {
    background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);
    border: none;
    color: #fff;
    background-color: #0458ff;

    &:hover {
      color: #fff;
      background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),
        linear-gradient(0deg, #0055fb, #0055fb);
      background-color: #0458ff;
    }
  }

  .btn-white {
    color: #07273d;
    &:hover,
    &:focus,
    &:active {
      background: #006dff;
      color: #fff;
      border-color: #006dff;
    }
  }

  .part-banner {
    background: linear-gradient(180deg, #e3eefc 16.11%, #a7ceff 100%);
    @keyframes float {
      0% {
        transform: translateY(0); /* 初始位置 */
      }
      50% {
        transform: translateY(-15px); /* 上浮 */
      }
      100% {
        transform: translateY(0); /* 恢复原位 */
      }
    }
    h1 {
      color: #0085ff;
    }
    .link-list {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 6px;
      color: #000;
      font-weight: 600;
      font-size: 0.875rem;
      a {
        font-weight: 600;
        font-size: 0.875rem;
        color: #000;
      }
    }
    .img-wrapper {
      max-width: 724px;
      text-align: center;
      margin: 0 auto;
      position: relative;
      .blue-round {
        position: absolute;
        width: 14%;
        bottom: 26%;
        right: 20%;
        z-index: 3;
        /* 添加动画 */
        animation: float 2s ease-in-out infinite;
      }
    }
  }

  .part-features {
    background-color: #e3eefc;
    .features-box {
      background-color: #fff;
      border-radius: 0.75rem;
      padding: 1.5rem;
      margin-top: -3rem;
      @media (max-width: 768px) {
      }
    }
  }

  .part-situations {
    background-color: #e3eefc;
  }

  @media (min-width: 992px) {
    .part-situations #swiper-tips .swiper-wrapper {
      gap: 1.875rem;
      flex-wrap: wrap;
      justify-content: center;
    }

    .part-situations #swiper-tips .swiper-wrapper .swiper-slide {
      flex: 0 1 calc(33% - 1.875rem);
    }
  }

  .part-situations .tip-item {
    border-radius: 1rem;
    height: 100%;
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 2rem;
    color: #000;
    z-index: 3;
    transition: all 0.2s;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: #fff;
    border: 2px solid #fff;
    text-align: center;
  }

  .part-situations .tip-item:hover {
    border: 2px solid #3ca2ff;
  }

  .part-situations .tip-item .tip-icon {
    height: 6rem;
    width: 6rem;
  }

  .part-system {
    background-color: #f6faff;

    .system-item {
      border-radius: 1rem;
      height: 100%;
      position: relative;
      overflow: hidden;
      background-color: #e6f2ff;
      height: 100%;
      display: flex;
      flex-direction: column;
      .system-item-content {
        flex: 1;
        padding: 1.5rem 2rem 2rem;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        .system-item-illustrate {
          padding: 0.75rem 1rem;
          background-color: #dbecff;
          border-radius: 0.5rem;
          border: 1px dotted #007aff;
          font-size: 12px;
          opacity: 0.6;
          margin-top: auto;
        }
        .mobile-slide {
          display: flex;
          flex-direction: column;
          height: 100%;
        }
        @media (max-width: 768px) {
          .mobile-slide {
            display: none;
          }
          .more-btn {
            display: block;
            width: 100%;
            border-radius: 6px;
            min-height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #9dccff;
            &.active {
              svg {
                transform: rotate(180deg);
              }
            }
          }
        }
      }
    }
  }

  .part-how {
    background-color: #f6faff;
    .how-box {
      background-color: #e6f2ff;
      border-radius: 1rem;
      padding: 1.875rem;
      display: flex;
      gap: 1.875rem;
      @media (max-width: 992px) {
        flex-direction: column;
        gap: 1rem;
      }
      .video-wrapper {
        border-radius: 12px;
        overflow: hidden;
        flex: 1 1 50%;
        @media (max-width: 992px) {
          svg {
            width: 3rem;
          }
        }
      }
      .methods-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        flex: 1 1 50%;
        .method-item {
          flex: 1 1 calc(33% - 8px * 2);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-radius: 0.75rem;
          border: 1px solid #c5e1ff;
          gap: 8px;
          text-align: center;
          padding: 0.875rem;
          font-size: 0.875rem;
          @media (max-width: 576px) {
            flex: 1 1 calc(50% - 8px);
          }
          img {
            width: 4.5rem;
          }
        }
      }
    }
  }

  .part-tech {
    background-color: #f6faff;
    .tech-box {
      border-radius: 1rem;
      background-color: #e6f2ff;
      overflow: hidden;
      padding: 2rem;
      @media (max-width: 576px) {
        padding: 1rem;
      }
      .tech-box-content {
        max-width: 83.33333%;
        margin: 0 auto;
        display: flex;
        gap: 3.75rem;
        @media (max-width: 1600px) {
          max-width: initial;
        }
        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1.5rem;
        }
        .left-content {
          flex: 1 1 50%;
          .tech-specs-list {
            .tech-specs-item {
              display: flex;
              img {
                flex-shrink: 0;
              }
            }
          }
        }

        .dividing-line {
          width: 1px;
          background-color: rgba($color: #0081f6, $alpha: 0.1);
          @media (max-width: 768px) {
            display: none;
          }
        }
        .right-content {
          flex: 1 1 50%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .tech-specs-list2 {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            .tech-specs-item2 {
              flex: 1 1 calc(50% - 1rem);
              padding: 0.75rem 1.5rem;
              border-radius: 12px;
              border: 1px solid #c5e1ff;
            }
          }
        }
      }
    }
  }

  .part-steps {
    background-color: #e3eefc;
    @media (min-width: 992px) {
      #swiper-steps .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: wrap;
        justify-content: center;
      }

      #swiper-steps .swiper-wrapper .swiper-slide {
        flex: 0 1 calc(33% - 1.875rem);
      }
    }
    ul {
      li {
        // list-style: none;
        color: rgba($color: #000000, $alpha: 0.7);
        margin-left: 1rem;
        font-size: 0.875rem;
      }
    }
  }
  .part-keys {
    background-color: #f6faff;
    #swiper-keys {
      @media (min-width: 1280px) {
        margin-right: -23%;
      }
    }
    .key-item {
      border-radius: 1rem;
      overflow: hidden;
      border: 2px solid transparent;
      background-color: #e6f2ff;
      height: 100%;
      &:hover {
        border: 2px solid #36c5ff;
      }
      .key-content {
        padding: 1rem 2rem 2rem;
      }
    }
    .left-btn,
    .right-btn {
      width: 3rem;
      height: 3rem;
      border-radius: 50%;
      background-color: #ebebeb;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      &:hover {
        background-color: #1a8dff;
      }
    }
  }
  .part-faq {
    background-color: #f6faff;
    .accordion-item {
      padding: 2rem 0;
      border-bottom: 1px solid #e2e2e2;
      @media (max-width: 576px) {
        padding: 1.25rem 0;
      }
    }

    .accordion-item [aria-expanded="true"] svg {
      transform: rotate(180deg);
      color: #00b0f5;
    }

    .accordion-item .faq-title {
      display: flex;
      align-items: center;
      justify-content: left;
      gap: 8px;
      flex-shrink: 0;
      max-width: 90%;
      font-weight: 700;
      font-size: 1.5rem;
      @media (max-width: 576px) {
        font-size: 1.25rem;
      }
    }
  }
  .part-links {
    background-color: #f6faff;
    .part-links-line {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }

    .line-border {
      border-right: 1px solid rgba(0, 0, 0, 0.3);
      border-left: 1px solid rgba(0, 0, 0, 0.3);
    }

    @media (max-width: 1280px) {
      .line-border {
        border-right: unset;
      }
    }

    @media (max-width: 768px) {
      .line-border {
        border-left: unset;
      }
    }

    .text-link {
      font-size: 0.875rem;
      color: #000;
      margin-top: 1rem;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding-left: 1rem;
      position: relative;
      transition: unset;
      &::before {
        content: "•";
        position: absolute;
        color: inherit;
        top: -1px;
        left: 4px;
      }
    }

    .text-link:hover {
      color: #0055fb;
    }
  }
  .part-footer {
    background-color: #f6faff;
    .footer-box {
      border-radius: 1rem;
      background: url(https://images.wondershare.com/recoverit/images2025/recycle-bin/footer-bg.jpg) no-repeat center center / cover;
      padding: 16px;
      @media (max-width: 768px) {
        background: linear-gradient(180deg, #e1edfd 0%, #b4d7f6 53.29%);
        text-align: center;
      }
    }
  }
}
