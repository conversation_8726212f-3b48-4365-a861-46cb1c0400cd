// 通用样式设置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #fff;
  color: #000;
  font-family: "Messina Sans", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
    font-family: "Messina Sans", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  h1,
  h2 {
    text-align: center;
  }

  h2 {
    font-weight: 700;
    font-size: 2.5rem;
    @media (max-width: 576px) {
      font-size: 24px;
    }
  }

  .display-3 {
    @media (max-width: 576px) {
      font-size: 2.5rem;
    }
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .text-blue {
    color: #006dff;
  }
  .text-blue2 {
    color: #008fff;
  }
  .text-gray {
    color: #616168;
  }

  .text-gradient {
    background: linear-gradient(95.44deg, #0055fb -28.33%, #00c1ff 96.36%), linear-gradient(0deg, #48deff, #48deff);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
    }
  }
  .btn {
    margin: 0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
    gap: 0.5rem;
    svg {
      max-width: 100%;
      height: 100%;
    }
  }
  .btn-download {
    border: 1px solid #ffffff;
    background: linear-gradient(89.57deg, #00c8ff -10.59%, #0084ff 15.01%, #006fff 83.38%, #00c8ff 107.75%);
    box-shadow: 0px 4.5px 6.97px 0px #ffffff6e inset, 0px -6.75px 16.65px 0px #00e5ffd6 inset, 0px 4.5px 13.84px 0px #0059ff40;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-transform: capitalize;
    border-radius: 0.875rem;
    color: #fff;
    min-width: 230px;
    overflow: hidden;
    position: relative;
    min-height: 48px;

    &:focus,
    &:active {
      color: #fff;
    }
    @media (max-width: 992px) {
      height: 4rem;
      min-width: unset;
    }

    .btn-text-wrap {
      position: relative;
      overflow: hidden;
      color: inherit;
      .btn-hover-text-wrap {
        transition: transform 0.4s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: inherit;
        &.rel {
          position: relative;
          transform: translateY(0);
        }
        &.abs {
          position: absolute;
          top: 120%;
          transform: translateY(0);
          transition-duration: 0.45s;
        }
      }
    }

    @media (any-hover: hover) {
      &:hover {
        color: #fff;
      }

      &:hover .btn-hover-text-wrap.rel {
        color: inherit;
        transform: translateY(-100%);
      }

      &:hover .btn-hover-text-wrap.abs {
        color: inherit;
        transform: translateY(-120%);
      }
    }
  }
  .btn-white {
    .btn-text-wrap {
      position: relative;
      overflow: hidden;
      color: inherit;
      .btn-hover-text-wrap {
        transition: transform 0.4s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: inherit;
        &.rel {
          position: relative;
          transform: translateY(0);
          white-space: nowrap;
        }
        &.abs {
          position: absolute;
          top: 120%;
          transform: translateY(0);
          transition-duration: 0.45s;
          white-space: nowrap;
        }
      }
    }

    @media (any-hover: hover) {
      &:hover {
        color: #fff;
      }

      &:hover .btn-hover-text-wrap.rel {
        color: inherit;
        transform: translateY(-100%);
      }

      &:hover .btn-hover-text-wrap.abs {
        color: inherit;
        transform: translateY(-120%);
      }
    }
  }
  .swiper-pagination {
    top: 10px;
    height: 20px;
    // pointer-events: none;
  }

  .swiper-pagination-bullet {
    width: 14px;
    height: 8px;
    background-color: #0080ff;
    opacity: 0.3;
    border-radius: 999px;
  }

  .swiper-pagination-bullet-active {
    width: 20px;
    height: 8px;
    background-color: #006dff;
    opacity: 0.7;
    border-radius: 999px;
  }

  .part-banner {
    position: relative;
    @media (max-width: 768px) {
      background-color: #f4f7ff;
    }
    .btn-outline-black {
      .white-icon {
        display: none;
      }
      &:hover,
      &:focus,
      &:active {
        .white-icon {
          display: inline-block;
        }
        .black-icon {
          display: none;
        }
      }
    }

    @keyframes blinkCursor {
      from,
      to {
        border-color: transparent;
      }

      50% {
        border-color: currentColor;
      }
    }

    .content-text .typewrite {
      color: #006dff;
      display: inline-block;
      font-weight: 700;
    }

    .content-text .typewrite .wrap {
      border-right: 0.08em solid;
      animation: blinkCursor 0.75s step-end infinite;
    }

    .trustpilot-list {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0.5rem;
      }
    }

    .system-list {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;

      a {
        color: #b5adad;
        text-decoration: none;
        position: relative;
        @media (any-hover: hover) {
          &:hover {
            .qrcode-box {
              opacity: 1;
            }
          }
        }
      }
      .qrcode-box {
        width: max-content;
        position: absolute;
        left: 50%;
        top: 200%;
        transform: translate(-50%);
        z-index: 2;
        pointer-events: none;
        opacity: 0;
        transition: all 0.2s ease-in-out;
      }
      .qrcode-box::before {
        content: "";
        width: 52px;
        height: 20px;
        background-image: url(https://images.wondershare.com/edrawmax/images2024/article-ad-pics/qrcode-arrow-top.svg);
        background-size: 100% auto;
        background-repeat: no-repeat;
        position: absolute;
        top: 2px;
        left: 50%;
        transform: translate(-48%, -100%);
        z-index: 1;
      }
      .qrcode-box .qrcode-container {
        background: #ffffff;
        border-radius: 1rem;
        border: 1px solid #c0c0c0;
        color: #000;
        position: relative;
      }

      .qrcode-box .wsc-icon-sm svg {
        width: auto;
        height: 100%;
        display: inline-block;
      }
    }

    @keyframes banner-diffuse1 {
      0% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0.1;
      }

      60% {
        transform: translate(-50%, -50%) scale(0.7);
        opacity: 0.5;
      }

      100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
      }
    }

    @keyframes banner-diffuse2 {
      0% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0.1;
      }

      60% {
        transform: translate(-50%, -50%) scale(0.9);
        opacity: 0.5;
      }

      100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
      }
    }
    .part-wave-icon-box {
      position: absolute;
      right: 10%;
      top: -5%;
      width: 12%;
      container-type: inline-size; /* 将此元素设置为容器 */
      @media (max-width: 576px) {
        width: 15%;
      }
      .download-text {
        font-weight: 700;
        color: #006dff;
        font-weight: 700;
        color: #006dff;
        position: absolute;
        left: 22%;
        top: 50%;
        font-size: 1rem;
        font-size: 13.2cqw;
      }
    }

    .part-wave-icon-box .wave1 {
      width: 111%;
      aspect-ratio: 106 / 123;
      border-radius: 20%;
      border: 3px solid rgba(63, 130, 255, 0.5);

      z-index: 1;
      opacity: 0.8;
      backdrop-filter: blur(6px);
      position: absolute;
      left: 54%;
      top: 37%;
      transform: translate(-50%, -50%) scale(0);
      animation: banner-diffuse1 2s linear infinite;
    }

    .part-wave-icon-box .wave2 {
      width: 130%;
      aspect-ratio: 106 / 123;
      border-radius: 20%;
      border: 3px solid rgba(63, 130, 255, 0.5);
      z-index: 1;
      opacity: 0.8;
      backdrop-filter: blur(6px);
      position: absolute;
      left: 54%;
      top: 37%;
      transform: translate(-50%, -50%) scale(0);
      animation: banner-diffuse1 2s linear infinite;
    }

    @keyframes marquee1 {
      0% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(-50%);
      }
    }
    @keyframes marquee2 {
      0% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(50%);
      }
    }
    .marks-list {
      display: flex;
      flex-wrap: nowrap;
      width: fit-content;
      position: absolute;
      bottom: 30%;
      left: 0;
      @media (max-width: 768px) {
        position: relative;
        background-color: #fff;
        padding: 12px 0;
      }
      img {
        height: 2.5rem;
        max-width: fit-content;
        margin: 0 2.5rem;
        @media (max-width: 768px) {
          height: 24px;
          margin: 0 24px;
        }
      }
    }
    .marks-list {
      animation: marquee1 30s linear infinite;
    }
  }
  .part-solutions {
    background-color: #f1f3fb;
    //  pc
    @media (min-width: 1280px) {
      .unlock-wrapper {
        border-radius: 1.5rem;
        position: relative;
        background-color: #fff;
        justify-content: center;
        .screen-unlock-left-phone {
          position: absolute;
          width: calc(100% - 20px);
          bottom: 0;
          left: 0;
          @media (max-width: 1600px) {
            width: calc(100% - 56px);
            left: 28px;
          }
        }
        .screen-unlock-right-phone {
          position: absolute;
          width: calc(100% - 20px);
          bottom: 0;
          right: 0;
          @media (max-width: 1600px) {
            width: calc(100% - 56px);
            right: 28px;
          }
        }
        .screen-unlock-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 390px;
          padding: 1rem;
          max-width: 344px;
          margin: 0 auto;
          text-align: center;
          .link-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 3rem;
            a {
              text-decoration: underline;
              color: #000;
              &:hover {
                color: #006dff;
              }
            }
          }
        }
      }
      .sys-change {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 31.25rem;
        margin: 0 auto;
        padding-top: 6rem;
        .ios-change,
        .android-change {
          flex: 1;
          text-align: center;
          padding: 0 1rem 0.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.625rem;
          font-weight: 700;
          font-size: 2rem;
          line-height: 1.625rem;
          color: #adadad;
          border-bottom: 1px solid rgba($color: #000000, $alpha: 0.2);
          cursor: pointer;
          position: relative;
          &.active {
            color: #0080ff;
            &::after {
              content: "";
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 2px;
              background-color: #0080ff;
            }
          }
        }
      }
      .system-change-wrapper {
        border-radius: 1.5rem;
        background-color: #fff;
        display: flex;
        gap: 0;

        .system-repair,
        .data-recovery {
          flex: 25%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
          padding: 2.5rem 3rem;
          @media (max-width: 1600px) {
            padding: 1rem 1.5rem;
          }
          .link {
            text-decoration: underline;
            color: #616168;
            &:hover {
              color: #0080ff;
            }
          }
          &.active {
            .title {
              color: #0080ff;
            }
          }
        }
        .system-img-wrapper {
          flex: 50%;
          position: relative;
          .computer-img-wrapper {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            z-index: 3;
            img {
              opacity: 0;
              transition: all 0.6s ease-in-out;
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              &.show {
                opacity: 1;
              }
            }
          }
          .phone-img-wrapper-inner {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 21%;
            z-index: 3;
            aspect-ratio: 148 / 231;
            overflow: hidden;
            &.right {
              right: 0 !important;
              left: initial;
            }

            img {
              opacity: 0;
              transition: all 0.4s ease-in-out;
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              transform: translateY(40%);

              &.show {
                opacity: 1;
                transform: translateY(0);
              }
            }
          }
        }
      }
      .tools-card-list {
        display: flex;
        justify-content: center;
        border-radius: 1.5rem;
        .tool-card {
          flex: 1;
          position: relative;
          text-align: center;
          .tool-box-background {
            background-color: #fff;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            transform-origin: 50% 100%;
            box-shadow: none;
            transform: scale(1);
            transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
            transition-property: transform, box-shadow;
            overflow: hidden;
          }
          &:nth-child(odd) {
            .tool-box-background {
              background-color: #fff;
            }
          }
          &:nth-child(even) {
            .tool-box-background {
              background-color: #e8f2ff;
            }
            &:hover {
              .tool-box-background-hover {
                opacity: 1;
                background-color: #fff;
              }
            }
          }
          .tool-box-background-hover {
            display: block;
            width: 230%;
            height: 230%;
            border-radius: 50%;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translate(-50%, 50%) scale(0);
            opacity: 1;
            transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.895, 0.03, 0.685, 0.22);
          }
          .tool-box-content {
            display: block;
            width: 100%;
            height: 100%;
            position: relative;
            transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
          }
          &:hover {
            z-index: 2;
            .tool-box-background {
              transform: scale(1.05);
              box-shadow: 6px 6px 32px 0px #c4daef;
            }
            .tool-box-background-hover {
              opacity: 0;
              background-color: #f3f9ff;
              transform: translate(-50%, 50%) scale(1);
            }
            .tool-box-content {
              transform: translateY(-10px);
            }
          }
          &:first-child {
            .tool-box-background {
              border-top-left-radius: 1.5rem;
              border-bottom-left-radius: 1.5rem;
            }
          }
          &:last-child {
            .tool-box-background {
              border-top-right-radius: 1.5rem;
              border-bottom-right-radius: 1.5rem;
            }
          }
          // &:nth-child(odd) {
          //   background-color: #fff;
          // }
          // &:nth-child(even) {
          //   background-color: #e8f2ff;
          // }
          padding-bottom: 1.5rem;
          position: relative;
          .right-icon {
            position: absolute;
            right: 1.125rem;
            top: 1.125rem;
          }
          .card-content {
            position: relative;
            padding: 2rem 1rem 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .more-info {
              position: absolute;
              width: 100%;
              height: 100%;
              top: 0;
              left: 0;
              z-index: 3;
              cursor: pointer;
            }
          }
          .btn-outline-action {
            border: 1px solid #0080ff;
          }
        }
      }
    }
    // mobile code
    @media (max-width: 1279.9px) {
      .sys-change-mobile {
        display: flex;
        max-width: 265px;
        border: 1px solid #0080ff;
        border-radius: 8px;
        background-color: #fff;
        font-size: 14px;
        font-weight: 700;
        color: #0080ff;
        margin: 32px auto;
        text-align: center;
        cursor: pointer;
        overflow: hidden;
        .ios-change {
          padding: 12px;
          flex: 1 1 50%;
          font-size: 14px;
          &.active {
            background-color: #0080ff;
            color: #fff;
            border-radius: 0 8px 8px 0;
          }
        }
        .android-change {
          padding: 12px;
          flex: 1 1 50%;
          font-size: 14px;
          &.active {
            background-color: #006dff;
            color: #fff;
            border-radius: 8px 0 0 8px;
          }
        }
      }
      #all-tools-swiper {
        .swiper-slide {
          opacity: 0.5;
        }

        .swiper-slide-active {
          opacity: 1;
        }
      }
      .tool-box {
        border: 1px solid rgba(0, 128, 255, 0.2);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0px 6.61px 9.53px 0px rgba(17, 73, 255, 0.21);
        padding: 24px;
        text-align: left;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #fff;
      }

      .tool-box .tool-box-title {
        font-weight: 700;
        font-size: 16px;
        line-height: 100%;
        color: #000;
        margin-bottom: 15px;
        margin-top: 18px;
      }

      .tool-box .tool-box-content {
        font-size: 12px;
        line-height: 100%;
        color: #616168;
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .tool-box .tool-box-content .btn-outline-action {
        font-size: 14px;
        line-height: 100%;
        color: #0080ff;
        border: 0.55px solid #0080ff;
        border-radius: 7px;
        min-height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: auto;
        display: block;
        min-width: unset;
        &:hover,
        &:active,
        &:focus {
          background-color: #0080ff;
          color: #fff;
          border-color: #0080ff;
        }
      }

      .phone-box {
        background-color: #fff;
        padding: 32px 0 40px;
        border-radius: 18px;
        box-shadow: 0px 8px 7.6px 0px #0080ff0d;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        height: 100%;
        .title {
          font-size: 24px;
        }
        .link {
          font-weight: 500;
          text-decoration: underline;
          color: #000;
          &:hover {
            color: #006dff;
          }
        }
      }
    }
  }

  .part-problems {
    background-color: #2f2f2f;
    color: #fff;
    .swiper-pagination-bullet {
      background-color: #fff;
    }
    .tab-list {
      display: flex;
      justify-content: center;
      max-width: 1216px;
      margin: 0 auto;
      gap: 1.5rem;
      .tab-item {
        flex: 1;
        height: 4.5rem;
        padding: 0.5rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4rem;
        font-weight: 500;
        font-size: 1.125rem;
        color: #fff;
        cursor: pointer;
        &.active {
          background-color: #008fff;
          box-shadow: 0px 4.5px 6.97px 0px #ffffff6e inset, 0px -6.75px 16.65px 0px #00e5ffd6 inset;
        }
      }
    }
    .problem-box {
      display: flex;
      justify-content: space-between;
      border-radius: 1.5rem;
      backdrop-filter: blur(20px);
      background-color: #000;
      height: 100%;
      @media (max-width: 1280px) {
        flex-direction: column;
        justify-content: flex-start;
      }
      .problem-box-left {
        flex: 0 1 45.1%;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        padding-left: 4.875rem;
        align-items: flex-start;
        justify-content: center;
        .ios-update-text {
          font-weight: 700;
          font-size: 1.125rem;
          line-height: 100%;
          margin-left: 0.5rem;
          color: #fff;
          text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
          white-space: nowrap;

          height: 2rem;
          aspect-ratio: 308 / 32;
          background: url("https://images.wondershare.com/drfone/images2025/index/ios-system.png") center center / contain no-repeat;
          display: inline-flex;
          align-items: center;
          justify-content: center;
        }
        @media (max-width: 1600px) {
          padding-left: 3rem;
        }
        @media (max-width: 1280px) {
          order: 2;
          padding: 0 24px 32px;
          flex: initial;
        }
        .title {
          font-size: 2rem;
          font-weight: 700;
          @media (max-width: 576px) {
            font-size: 18px;
          }
        }
        .desc {
          font-weight: 500;
          color: rgba($color: #ffffff, $alpha: 0.6);
        }
        .link-list {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 3rem;
          @media (max-width: 1280px) {
            gap: 1rem;
          }
          a {
            color: #fff;
            text-decoration: underline;
            &:hover {
              color: #006dff;
            }
          }
        }
      }
      .problem-box-right {
        flex: 0 1 54.9%;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        @media (max-width: 1280px) {
          flex: initial;
          margin-bottom: -9%;
        }
        .mobile-title {
          font-size: 18px;
          font-weight: 700;
          position: absolute;
          top: 1.75rem;
          left: 0;
          text-align: center;
          width: 100%;
        }
      }
    }
  }

  .part-discover {
    background-color: #2f2f2f;
    .swiper-pagination-bullet {
      background-color: #fff;
    }
    @media (min-width: 1280px) {
      .swiper {
        overflow: visible;
      }
      .swiper-wrapper {
        display: grid;
        grid-template-rows: 286fr 330fr;
        grid-template-columns: repeat(3, 1fr);
        grid-template-areas:
          "frp-bypass icloud-activation-unlocker icloud-activation-unlocker"
          "frp-bypass DocPassRemover one-click-root";
        gap: 1.875rem;
        .swiper-slide {
          .logo-icon {
            @media (max-width: 1600px) {
              height: 40px;
            }
          }
          &.frp-bypass {
            grid-area: frp-bypass;
            .discover-box {
              display: flex;
              flex-direction: column;
              height: 100%;
              justify-content: space-between;
              align-items: center;
              .discover-box-content {
                padding: 2.5rem;
                padding-bottom: 1rem;
              }
            }
          }
          &.icloud-activation-unlocker {
            grid-area: icloud-activation-unlocker;
            .discover-box {
              height: 100%;
              display: flex;
              overflow: visible;
              .discover-box-content {
                flex: 42.6%;
                padding: 1rem;
                padding-left: 2.5rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
              }
              .discover-box-img {
                flex: 57.4%;
                position: relative;

                img {
                  position: absolute;
                  width: 100%;
                  bottom: 0;
                  left: 0;
                }
              }
            }
          }
          &.DocPassRemover {
            grid-area: DocPassRemover;
            .discover-box {
              display: flex;
              flex-direction: column;
              height: 100%;
              justify-content: space-between;
              align-items: center;

              .discover-box-content {
                padding: 2rem 2.5rem 0;
                @media (max-width: 1600px) {
                  padding: 1rem 1rem 0;
                }
                .btn-white {
                  max-width: 90px;
                  white-space: nowrap;
                }
              }
            }
          }
          &.one-click-root {
            grid-area: one-click-root;
            .discover-box {
              display: flex;
              height: 100%;
              justify-content: space-between;
              .discover-box-content {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                padding-left: 2.5rem;
                flex: 50%;
                .btn-white {
                  flex: 1;
                }
              }
              .discover-box-img {
                flex: 50%;
              }
            }
          }
        }
      }
    }
    .discover-box {
      border-radius: 1.5rem;
      background-color: #008fff;
      color: #fff;
      overflow: visible;
      height: 100%;
      transition: transform 0.3s ease-in-out;
      @media (any-hover: hover) {
        &:hover {
          transform: scale(1.05);
        }
      }
      .discover-box-content {
        position: relative;
        z-index: 2;
      }
      .discover-box-img {
        position: relative;
        z-index: 2;
      }

      @media (max-width: 1280px) {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        .logo-icon {
          height: 40px;
        }
        .discover-box-content {
          padding: 32px;
          padding-bottom: 0;
        }
      }
      .btn-white {
        color: #0080ff !important;
        max-height: 2.5rem;
      }
    }
  }

  .part-right {
    background-color: #f1f3fb;
    @media (min-width: 1280px) {
      #swiper-right .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: wrap;
      }

      #swiper-right .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(33% - 1.875rem);
      }
    }
    @media (max-width: 768px) {
      .swiper-slide {
        .right-item {
          opacity: 0.5;
        }
      }
      .swiper-slide-active {
        .right-item {
          opacity: 1;
        }
      }
      .btn {
        min-width: unset;
      }
    }

    .right-item {
      height: 100%;
      border-radius: 1.5rem;
      overflow: hidden;
      box-shadow: 0px 8px 7.6px 0px rgba(0, 128, 255, 0.05);
      padding: 3rem 1.5rem;
      min-height: 398px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      background-color: #fff;
      text-align: center;
      position: relative;
      overflow: hidden;
      @media (max-width: 1280px) {
        min-height: unset;
        border: 0.73px solid #0080ff80;
        box-shadow: 0px 8px 7.6px 0px #0080ff0d;
      }
      .title {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        font-weight: 700;
        font-size: 2rem;
        line-height: 100%;
        white-space: nowrap;
        @media (max-width: 576px) {
          font-size: 18px;
        }
      }
      .change-to-ios,
      .change-to-android {
        position: absolute;
        right: 0;
        top: 0;
        background: linear-gradient(229.4deg, #ff792e -1.84%, #fc02b6 87.33%);
        border-radius: 0 1.5rem 0 1.5rem;
        font-weight: 700;
        font-size: 1.125rem;
        color: #fff;
        width: 9rem;
        height: 3.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        cursor: pointer;
        z-index: 3;
        pointer-events: auto;

        @media (max-width: 1280px) {
          height: 40px;
        }
      }
    }
  }

  .part-loved {
    background-color: #fff;

    .count-list {
      display: flex;
      gap: 1.875rem;
      justify-content: space-between;

      @media (max-width: 1600px) {
        gap: 1rem;
        flex-wrap: wrap;
      }
      @media (max-width: 576px) {
        gap: 4px;
      }
    }
    .count-item {
      flex: 1 1 calc(25% - 1.875rem * 3);
      border-radius: 1.25rem;
      background-color: #2f2f2f;
      backdrop-filter: blur(16px);
      padding: 2.25rem 2.5rem;
      color: #fff;
      @media (max-width: 1280px) {
        flex: 1 1 calc(50% - 1rem * 1);
        text-align: center;
        padding: 1rem;
        min-height: 140px;
      }

      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      .box-download {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 3;
        @media (any-hover: hover) {
          &:hover {
            & ~ .icon-list {
              .download-icon {
                opacity: 1;
              }
            }
          }
        }
      }

      .icon-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1.5rem;
        @media (max-width: 768px) {
          display: none;
        }
        .download-icon {
          opacity: 0.1;
        }
      }
      .count {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 4px;
        font-size: 3.75rem;
        line-height: 100%;
        font-weight: 1000;
        font-family: "Mulish", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
          "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

        @media (max-width: 768px) {
          font-size: 32px;
          justify-content: center;
        }

        .count-num {
          font-size: 3.75rem;
          line-height: 100%;
          font-weight: 1000;
          font-family: "Mulish", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
            "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
          @media (max-width: 768px) {
            font-size: 32px;
          }
        }
        .count-plus-wrap {
          display: flex;
          flex-direction: column;
          margin-bottom: auto;
          align-items: flex-start;
          .count-plus {
            font-size: 2.5rem;
            line-height: 72%;
            font-weight: 1000;
          }
          .count-years {
            font-size: 20px;
            color: #fff;
            line-height: initial;
            @media (max-width: 576px) {
              font-size: 14px;
            }
          }
        }
      }
      .count-desc {
        font-size: 1.75rem;
        color: rgba($color: #ffffff, $alpha: 0.5);
        @media (max-width: 768px) {
          font-size: 16px;
          text-align: center;
        }
      }
    }
  }

  .part-customer {
    background-color: #f1f3fb;
    .excellent-wrapper {
      max-width: 229px;
      margin: 0 auto;
      @media (max-width: 768px) {
        max-width: 137px;
      }
    }
    @media (min-width: 1280px) {
      #swiper-customer .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: wrap;
      }

      #swiper-customer .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(33% - 1.875rem * 2);
      }
    }
    .customer-item {
      border-radius: 1.5rem;
      overflow: hidden;
      .customer-info {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        padding: 3rem 2.5rem 2.5rem;
        .customer-name {
          font-weight: 700;
          font-size: 1.125rem;
          line-height: 100%;
          border-radius: 0.5rem;
          background: rgba(0, 0, 0, 0.5);
          color: #fff;
          display: inline-block;
          padding: 8px;
        }
        .customer-content {
          .customer-text {
            font-weight: 500;
            text-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
            color: #fff;
            margin-top: 1.25rem;
          }
        }
      }
    }
  }
  .part-bottom {
    padding: 0 3.5rem 6rem;
    background: linear-gradient(to top, #000 60%, #f1f3fb 40%);
    @media (max-width: 768px) {
      padding: 0 16px 96px;
    }

    .bottom-section-wrapper {
      border-radius: 24px;
      background: #0080ff;
      position: relative;
      text-align: center;
      overflow: hidden;
    }

    .bottom-section-video-container-wrapper::after {
      content: "";
      position: absolute;
      top: 0%;
      left: 0%;
      width: 100%;
      height: 100%;
      background-image: url(https://images.wondershare.com/drfone/images2023/home/<USER>
      background-repeat: no-repeat;
      background-size: auto 100%;
      background-position: center center;
    }

    .bottom-section-video-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      height: 1000px;
      width: 750px;
      overflow: hidden;
    }

    .bottom-section-video-container::before {
      content: "";
      padding-top: calc(750 / 1000 * 100%);
    }

    .bottom-section-video-container video {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      height: 100%;
      width: auto;
    }

    .wave-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .wave-item {
      width: 2300px;
      height: 2300px;
      border-radius: 50%;
      border: 2px solid #fff;
      z-index: 1;
      opacity: 0.2;
      position: absolute;
      left: 50%;
      top: 50%;
      margin-top: -1150px;
      margin-left: -1150px;
      transform: scale(0);
    }

    .wave-box .wave-item1 {
      animation: newNimate 24s linear infinite;
    }

    .wave-box .wave-item1-2 {
      animation: newNimate 24s 1.6s linear infinite;
    }

    .wave-box .wave-item1-3 {
      animation: newNimate 24s 3.2s linear infinite;
    }

    .wave-box .wave-item1-4 {
      animation: newNimate 24s 4.8s linear infinite;
    }

    .wave-box .wave-item1-5 {
      animation: newNimate 24s 6.4s linear infinite;
    }

    .wave-box .wave-item1-6 {
      animation: newNimate 24s 8s linear infinite;
    }

    .wave-box .wave-item1-7 {
      animation: newNimate 24s 9.6s linear infinite;
    }

    .wave-box .wave-item1-8 {
      animation: newNimate 24s 11.2s linear infinite;
    }

    .wave-box .wave-item1-9 {
      animation: newNimate 24s 12.8s linear infinite;
    }

    .wave-box .wave-item1-10 {
      animation: newNimate 24s 14.4s linear infinite;
    }

    .wave-box .wave-item1-11 {
      animation: newNimate 24s 16s linear infinite;
    }

    .wave-box .wave-item1-12 {
      animation: newNimate 24s 17.6s linear infinite;
    }

    .wave-box .wave-item1-13 {
      animation: newNimate 24s 19.2s linear infinite;
    }

    .wave-box .wave-item1-14 {
      animation: newNimate 24s 20.8s linear infinite;
    }

    .wave-box .wave-item1-15 {
      animation: newNimate 24s 22.4s linear infinite;
    }
    .btn {
      @media (max-width: 576px) {
        min-width: 260px;
        margin: 0 auto;
      }
    }
    .btn-white {
      color: #0080ff;
    }
  }

  /* display优化渲染 */
  @keyframes newNimate {
    0% {
      transform: scale(0);
      opacity: 0.1;
      display: block;
    }

    50% {
      transform: scale(0.5);
      opacity: 0.15;
    }

    100% {
      transform: scale(1);
      opacity: 0;
      display: none;
    }
  }
}
