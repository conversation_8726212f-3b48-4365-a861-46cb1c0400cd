* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #f5f8ff;
  color: #000;

  @media (max-width: 1280px) {
    background-color: #f4f7ff;
  }
  video {
    height: 100%;
    width: 100%;
    object-fit: cover;
    line-height: 0;
    font-size: 0;
    // google去黑线
    filter: grayscale(0);
    // 火狐去黑线
    clip-path: fill-box;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
  }

  h2 {
    text-align: center;
    font-weight: 800;
    font-size: 2.25rem;
  }

  h1,
  h2,
  h3 {
    text-align: center;
  }

  h2 {
    font-size: 2.25rem;
    font-weight: 800;
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .blue-text {
    color: #0055fb;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .btn-wrapper {
      flex-direction: column;
      gap: 8px;
    }
  }

  .btn-wrapper .btn {
    margin: 0;
    border-radius: 8px;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 158px;
    &.btn-lg {
      min-width: 228px;
    }
  }

  @media (max-width: 768px) {
    .btn-wrapper .btn {
      display: block;
      vertical-align: baseline;
    }
  }

  .btn-download {
    background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);
    border: none;
    color: #fff;
    background-color: #0458ff;
  }

  .btn-download:hover {
    color: #fff;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),
      linear-gradient(0deg, #0055fb, #0055fb);
    background-color: #0458ff;
  }

  .swiper-pagination {
    bottom: -4px !important;
  }

  .swiper-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background-color: #c2cee9;
    opacity: 1;
  }

  .swiper-pagination .swiper-pagination-bullet-active {
    width: 64px;
    background: linear-gradient(89.5deg, #0458ff 0%, #0499ff 100%);
    border-radius: 8px;
  }
  .part-banner {
    text-align: center;
    background: linear-gradient(180deg, #afdaff -32.94%, #d1e9fd 6.57%, rgba(245, 248, 255, 0) 30%), linear-gradient(0deg, #f5f8ff, #f5f8ff);
    .sub-title {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
      .blue-tip {
        background: linear-gradient(100.2deg, #0055fb 0%, #00c1ff 100%), linear-gradient(0deg, #d9d9d9, #d9d9d9);
        border-radius: 24px;
        padding: 4px 12px;
        font-weight: 700;
        font-size: 1.25rem;
        line-height: 100%;
        color: #fff;
      }
    }
    h1 {
      background: linear-gradient(95.44deg, #0055fb -28.33%, #00c1ff 96.36%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    .feature-list {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 4px 8px;
      border-radius: 1.5rem;
      border: 1.5px solid #0055fb;
      min-width: 602px;
      flex-wrap: wrap;
      @media (max-width: 768px) {
        min-width: unset;
      }
      .feature-item {
        flex: 1;
        font-weight: 600;
        font-size: 1.125rem;
        color: #0055fb;
        padding: 0 1rem;
        position: relative;
        &.text-gradient {
          background: linear-gradient(92.01deg, #e046ff 4.9%, #7970ff 52.55%, #0066ff 99.48%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }
        @media (max-width: 768px) {
          flex: 1 1 50%;
        }
        &:not(:last-child)::after {
          content: "";
          display: inline-block;
          width: 1px;
          height: 80%;
          background-color: #0055fb;
          top: 50%;
          transform: translateY(-50%);
          position: absolute;
          right: 0;
          @media (max-width: 768px) {
            content: unset;
          }
        }
      }
    }
    #banner-text-swiper {
      height: 22px;
      overflow: hidden;
      @media (max-width: 576px) {
        height: 44px;
      }
    }
    .detail-item {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      gap: 10px;
      font-size: 14px;
    }
    .video-wrapper {
      @media (max-width: 768px) {
        padding-top: 1.5rem;
      }
    }
  }
  .part-logos {
    .logos-wrapper {
      background-color: #fff;
      border-radius: 1rem;
      padding: 2rem 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    @media (max-width: 768px) {
      .logos-wrapper {
        padding: 1.5rem 0.5rem;
      }
    }

    .logos-wrapper .logo-item {
      flex: 1 1 33%;
      max-height: 2rem;
      text-align: center;
    }

    .logos-wrapper .logo-item:not(:last-child) {
      border-right: 1px solid rgba(0, 0, 0, 0.1);
    }

    .logos-wrapper .logo-item img {
      max-width: 100%;
      max-height: 2rem;
      object-fit: contain;
    }

    @media (max-width: 768px) {
      .logos-wrapper .logo-item img {
        max-height: 1.1rem;
      }
    }
  }
  .part-tools {
    background-color: #f5f8ff;

    .why {
      overflow: visible;
    }

    @media (min-width: 1280px) {
      .why {
        height: 300vh;
      }
    }

    .why .why-box {
      position: sticky;
      top: 90px;
      z-index: 1;
    }

    @media (min-width: 1280px) {
      .why .why-box {
        height: 80vh;
      }
    }
    .why-progress-wrapper {
      border-radius: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      max-width: 400px;
      margin: 2rem auto 0;
    }

    .why-progress-wrapper .why-progress {
      flex: 1 1 33%;
      height: 8px;
      border-radius: 30px;
      background-color: #c2cee9;
      position: relative;
      cursor: pointer;
    }

    .why-progress-wrapper .why-progress .why-progress-inner {
      position: absolute;
      display: inline-block;
      width: 0%;
      height: 100%;
      background: linear-gradient(95.7deg, #0055fb -29.57%, #00c1ff 100.6%);
      border-radius: 30px;
    }

    @media (min-width: 1280px) {
      #swiper-why .swiper-slide {
        height: auto;
      }
    }

    .tools-box {
      border-radius: 1rem;
      overflow: hidden;
      display: flex;
      justify-content: center;
      background-color: #fff;
      @media (max-width: 1280px) {
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
      }
      .tools-box-content {
        flex: 1 1 46.8%;
        padding: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        max-width: 480px;
        margin: 0 auto;
        @media (max-width: 1280px) {
          flex: initial;
          max-width: unset;
          margin: 0;
          padding: 1.5rem;
        }
        .tip {
          color: #fff;
          font-weight: 800;
          padding: 0.25rem 0.75rem;
          background: linear-gradient(61.93deg, #0057ff 21.98%, #477bff 57.83%, #ffa3f6 93.93%);
          border-radius: 0.625rem;
        }
        .feature-list {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          list-style: none;
          li {
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.7);
            position: relative;
            padding-left: 1rem;
            &::before {
              content: "•";
              margin-right: 0.5rem;
              position: absolute;
              left: 0rem;
              top: -0.05rem;
            }
          }
        }
      }
      .tools-box-img {
        flex: 0 1 53.2%;
      }
    }
  }
  .part-fixer {
    background: url(https://images.wondershare.com/repairit/images2025/Photo-Repair/part-fixer-bg.jpg) no-repeat center center / cover;
    .fixer-wrapper {
      background-color: #fff;
      border-radius: 1rem;
      padding: 2rem 0 4rem;
      @media (max-width: 992px) {
        padding: 1.5rem 0 2rem;
      }
      .nav {
        display: flex;
        background-color: #9dd7ff;
        padding: 4px;
        border-radius: 999px;
        @media (max-width: 768px) {
          flex-direction: column;
          border-radius: 1rem;
        }
        .nav-item {
          flex: 50%;
          border-radius: 999px;
          font-size: 1.25rem;
          font-weight: 800;
          padding: 8px 12px;
          text-align: center;
          text-decoration: none;
          display: flex;
          align-items: center;
          justify-content: center;
          @media (max-width: 768px) {
            border-radius: 1rem;
          }
          span {
            background: linear-gradient(90deg, #4ca8e7 0%, #6cb6ea 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
          }
          &.active {
            background-color: #fff;
            span {
              background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
        }
      }
      .photo-formats-content {
        .formats-item {
          display: flex;
          justify-content: space-between;
          border-radius: 1rem;
          border: 2px solid #e6eeff;
          overflow: hidden;
          .formats-item-text {
            height: auto;
            flex: 0 1 28.2%;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            background-color: #e6eeff;
            color: #000;
            padding: 0.5rem;
            font-size: 1.25rem;
            font-weight: 700;
            @media (max-width: 768px) {
              font-size: 1rem;
            }
          }
          .formats-item-img {
            flex: 0 1 71.8%;
            overflow: hidden;
            padding: 1.5rem 0;
            position: relative;
            &::after {
              content: "";
              display: inline-block;
              width: 11%;
              height: 100%;
              background: linear-gradient(270deg, #ffffff 29.74%, rgba(255, 255, 255, 0) 100%);
              position: absolute;
              right: 0;
              top: 0;
              z-index: 2;
            }

            .formats-item-img-wrapper {
              display: flex;
              width: fit-content;
              flex-wrap: nowrap;
              @keyframes marquee1 {
                0% {
                  transform: translateX(0);
                }

                100% {
                  transform: translateX(-50%);
                }
              }
              @keyframes marquee2 {
                0% {
                  transform: translateX(-50%);
                }

                100% {
                  transform: translateX(0%);
                }
              }
              img {
                height: 4.5rem;
                max-width: fit-content;
                margin: 0 0.25rem;
                @media (max-width: 768px) {
                  height: 3rem;
                }
              }
              &.right {
                animation: marquee2 30s linear infinite;
              }
              &.left {
                animation: marquee1 30s linear infinite;
              }
            }
          }
        }
      }
      .photo-devices-content {
        @media (min-width: 992px) {
          #swiper-photo-devices .swiper-wrapper {
            gap: 1.875rem;
            flex-wrap: wrap;
          }

          #swiper-photo-devices .swiper-wrapper .swiper-slide {
            flex: 1 1 calc(33% - 1.875rem);
          }
        }
        .device-item {
          border-radius: 1rem;
          border: 1px solid #e6eeff;
          padding: 1.5rem 2rem;
          position: relative;
          overflow: hidden;
          text-align: center;
          height: 100%;
          .device-item-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .device-item-title {
              font-size: 1.25rem;
              font-weight: 700;
              color: #000;
            }
            .device-item-description {
              font-size: 0.875rem;
              opacity: 0.7;
            }
          }
          @media (any-hover: hover) {
            &:hover {
              &::after {
                content: "";
                position: absolute;
                inset: 0;
                border-radius: 1rem;
                padding: 2px;
                background: linear-gradient(90.92deg, #0055fb 0.16%, #00c1ff 100%);
                mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
                mask-composite: exclude;
              }
            }
          }
        }
      }
    }
  }
  .part-powerful {
    @media (max-width: 576px) {
      .col-6 {
        padding-right: 8px;
        padding-left: 8px;
      }

      .col-6:nth-child(odd) {
        padding-right: 4px;
      }

      .col-6:nth-child(even) {
        padding-left: 4px;
      }
    }
    .power-item {
      border-radius: 1rem;
      overflow: hidden;
      background-color: #fff;
      height: 100%;

      .power-item-content {
        padding: 1rem 2rem 2rem;
        text-align: center;
        @media (max-width: 768px) {
          padding: 8px;
        }
        .item-link {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          font-size: 1.25rem;
          font-weight: 800;
          text-decoration: none;
          color: #000;
          @media (max-width: 576px) {
            font-size: 1rem;
          }
          .arrow-icon {
            line-height: 0;
            flex-shrink: 0;
            flex-grow: 0;
            @media (max-width: 768px) {
              width: 1.25rem;
            }
            .active-arrow {
              display: none;
            }
          }
          &:hover {
            color: #0458ff;
            .active-arrow {
              display: block;
            }
            .normal-arrow {
              display: none;
            }
          }
        }
      }
    }
    @media (min-width: 992px) {
      #swiper-feature .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: nowrap;
      }

      #swiper-feature .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(20% - 1.875rem * 3 / 4);
      }
    }
    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 2rem 1.5rem;
      background-color: #eaf2ff;
      border-radius: 1.5rem;
      overflow: hidden;
      text-align: center;

      height: 100%;
      &-title {
        font-size: 1.125rem;
        font-weight: 800;
        color: #000;
      }
      &-description {
        font-size: 0.875rem;
        opacity: 0.6;
      }
    }
  }

  .part-steps {
    background: radial-gradient(82.32% 135.56% at 31.17% -26.53%, #b6e0ff 0%, #e3f3ff 50%, #e0f2ff 100%);

    .nav-item {
      padding: 1.5rem;
      border-radius: 12px;
      width: 100%;
    }

    .nav-item.active {
      background-color: #fff;
    }

    .nav-item [data-toggle="collapse"] {
      display: flex;
      align-items: flex-start;
    }
  }
}
main .part-faq .accordion-box {
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 0.5rem 4rem;
}

@media (max-width: 992px) {
  main .part-faq .accordion-box {
    padding: 0.5rem 2rem;
  }
}

@media (max-width: 768px) {
  main .part-faq .accordion-box {
    padding: 0.5rem 1rem;
  }
}

main .part-faq .accordion-box .accordion-item {
  padding: 1.5rem;
}

main .part-faq .accordion-box .accordion-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

main .part-faq .accordion-box .accordion-item svg {
  transition: all 0.2s linear;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item svg {
    width: 1rem;
  }
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item {
    padding: 1rem 0.5rem;
  }
}

main .part-faq .accordion-box .accordion-item [aria-expanded="true"] svg {
  transform: rotate(180deg);
}

main .part-faq .accordion-box .accordion-item .serial-number {
  display: inline-flex;
  width: 22px;
  height: 22px;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
  border-radius: 50%;
  margin-right: 8px;
  font-size: 1rem;
  font-weight: 800;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item .serial-number {
    width: 16px;
    height: 16px;
    color: #fff;
  }
}

main .part-faq .accordion-box .accordion-item .faq-detail {
  font-size: 14px;
  padding-top: 1rem;
  opacity: 0.7;
  padding-left: 30px;
  padding-right: 32px;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item .faq-detail {
    padding-left: 20px;
    padding-right: 16px;
  }
}

main .part-stories .swiper {
  margin: 2rem;
}

@media (max-width: 768px) {
  main .part-stories .swiper {
    margin: 0.5rem;
  }
}

@media (min-width: 768px) {
  main .part-stories .swiper-slide .user-wrapper::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(210, 223, 255, 0.3);
    z-index: 2;
  }

  main .part-stories .swiper-slide.swiper-slide-active .user-wrapper::before {
    content: unset;
  }
}

main .part-stories .user-wrapper {
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.6);
  }
}

main .part-stories .user-wrapper .user-story {
  position: absolute;
  right: 4rem;
  top: 3rem;
  max-width: 360px;
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper .user-story {
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 8px;
    color: #fff;
  }
}

main .part-stories .user-wrapper .user-story .user-occupation {
  font-size: 14px;
  margin-bottom: 16px;
}

main .part-stories .user-wrapper .user-story .user-comments {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.7);
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper .user-story .user-comments {
    color: #fff;
  }
}

main .part-stories .swiper-pagination {
  bottom: -2.5rem !important;
}

main .part-links .part-links-line {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

main .part-links .line-border {
  border-right: 1px solid rgba(0, 0, 0, 0.3);
  border-left: 1px solid rgba(0, 0, 0, 0.3);
}

@media (max-width: 1280px) {
  main .part-links .line-border {
    border-right: unset;
  }
}

@media (max-width: 768px) {
  main .part-links .line-border {
    border-left: unset;
  }
}

main .part-links .text-link {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.7);
  margin-top: 1.5rem;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

main .part-links .text-link:hover {
  color: #0055fb;
}

main .part-links .part-links-videos {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

main .part-links .part-links-videos .video-wrapper {
  border-radius: 0.75rem;
}

@media (max-width: 1280px) {
  main .part-links .part-links-videos {
    flex-direction: row;
    padding-top: 2rem;
  }
}

@media (max-width: 576px) {
  main .part-links .part-links-videos {
    display: block;
  }
}

main .part-links .text-line4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

main .part-footer {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  @media (max-width: 576px) {
    .display-2 {
      font-size: 2.5rem;
    }
  }
}

main .part-footer-logo {
  height: 4rem;
  width: 14.5rem;
  margin: 0 auto;
}

@media (max-width: 576px) {
  main .part-footer .btn-outline-action {
    background-color: #fff;
    vertical-align: text-bottom;
  }
}

main .part-advanced .advanced-item {
  border-radius: 1rem;
  background-color: #ffffff;
  overflow: hidden;
  height: 100%;
}

main .part-advanced .advanced-item .compare-before {
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0;
  top: 0;
  background-size: auto 100%;
  background-repeat: no-repeat;
  z-index: 2;
}

main .part-advanced .advanced-item .compare-before::after {
  content: "";
  width: 2px;
  height: 100%;
  background: #fff;
  position: absolute;
  right: 0;
  top: 0;
}

main .part-advanced .advanced-item .compare-before::before {
  content: "";
  background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);
  background-size: contain;
  background-position: center;
  width: 4.25rem;
  height: 2.5rem;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  z-index: 3;
}

@keyframes changeWidth {
  0% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

main .part-advanced .advanced-item .compare-before.compare-before-1 {
  background-image: url(https://images.wondershare.com/repairit/images2025/Photo-Repair/AI-Photo-Enhancer-before.png);
  animation: changeWidth 8s linear infinite;
  aspect-ratio: 328 / 192;
}

main .part-advanced .advanced-item .compare-before.compare-before-2 {
  background-image: url(https://images.wondershare.com/repairit/images2025/Photo-Repair/old-photo-restoration-before.png);
  animation: changeWidth 7s linear infinite;
}

main .part-advanced .advanced-item .compare-before.compare-before-3 {
  background-image: url(https://images.wondershare.com/repairit/images2025/Photo-Repair/AI-Photo-Colorize-before.png);
  animation: changeWidth 7s linear infinite 1s;
}

main .part-advanced .advanced-item .compare-before.compare-before-4 {
  background-image: url(https://images.wondershare.com/repairit/images2025/Photo-Repair/photo-repair-before.png);
  animation: changeWidth 6s linear infinite;
}

main .part-advanced .advanced-item .slider {
  -webkit-appearance: none;
  appearance: none;
  outline: 0;
  margin: 0;
  background: 0 0;
  z-index: 3;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

main .part-advanced .advanced-item .slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 2px;
  height: auto;
  background: transparent;
}

main .part-advanced .advanced-item .item-link {
  color: #000;
}

main .part-advanced .advanced-item .item-link .normal-arrow {
  display: inline;
}

main .part-advanced .advanced-item .item-link .active-arrow {
  display: none;
}

main .part-advanced .advanced-item .item-link .arrow-icon {
  width: 2rem;
  display: inline-block;
}

@media (max-width: 576px) {
  main .part-advanced .advanced-item .item-link .arrow-icon {
    display: block;
  }
}

main .part-advanced .advanced-item .item-link:hover {
  color: #0458ff;
}

main .part-advanced .advanced-item .item-link:hover .normal-arrow {
  display: none;
}

main .part-advanced .advanced-item .item-link:hover .active-arrow {
  display: inline;
}
