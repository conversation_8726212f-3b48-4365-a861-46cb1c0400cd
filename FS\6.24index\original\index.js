import "./index.scss";

$(() => {
  // --- Configuration & State ---
  const isDesktop = window.innerWidth >= 1280;
  let isTransitioning = false;
  let lastSlideIndex = 0;
  let stepVal = true;
  let digitalTextSwiper, digitalImgSwiper;

  // --- Utility Functions ---
  const throttle = (func, limit) => {
    let inThrottle;
    return (...args) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  };

  const wait = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  const setOpacity = (selector, opacity, duration) => {
    $(selector).css({
      opacity: opacity,
      transition: `opacity ${duration}ms ease-in-out`,
    });
    return wait(duration);
  };

  const isElementFullyInViewport = (el) => {
    if (!el) return false;
    const rect = el.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  };

  // --- Event Handlers & Initializers ---

  const handleResize = () => {
    const newIsDesktop = window.innerWidth >= 1280;
    if (isDesktop !== newIsDesktop) {
      window.location.reload();
    }
  };
  $(window).on("resize", throttle(handleResize, 200));

  // Component-specific logic
  if (isDesktop) {
    // Digital Section Swipers (Desktop)
    digitalTextSwiper = new Swiper("#digital-text-swiper", {
      slidesPerView: 1,
      speed: 300,
      allowTouchMove: false,
      direction: "horizontal",
      effect: "fade",
      fadeEffect: { crossFade: true },
    });
    digitalImgSwiper = new Swiper("#digital-img-swiper", {
      slidesPerView: 1,
      speed: 300,
      allowTouchMove: false,
      effect: "fade",
      fadeEffect: { crossFade: true },
    });

    const customSlideTransition = async (targetIndex) => {
      if (isTransitioning) return;
      isTransitioning = true;
      try {
        await setOpacity("#digital-text-swiper, #digital-img-swiper", "0", 200);
        digitalTextSwiper.slideTo(targetIndex, 100, false);
        digitalImgSwiper.slideTo(targetIndex, 100, false);
        await wait(100);
        const textPromise = setOpacity("#digital-text-swiper", "1", 300);
        await wait(200);
        const imgPromise = setOpacity("#digital-img-swiper", "1", 400);
        await Promise.all([textPromise, imgPromise]);
      } finally {
        isTransitioning = false;
      }
    };

    const handleDigitalScroll = () => {
      const apartSection = $(".part-digital")[0];
      if (apartSection) {
        const totalSlides = digitalTextSwiper.slides.length;
        const offset = apartSection.getBoundingClientRect();
        if (offset.top < 72 && offset.bottom - window.innerHeight > 0) {
          const perc = Math.round((100 * Math.abs(offset.top)) / (offset.height - $(window).height()));
          const slideIndex = Math.min(Math.floor((perc * totalSlides) / 100), totalSlides);
          if (slideIndex !== lastSlideIndex && slideIndex !== digitalTextSwiper.activeIndex) {
            lastSlideIndex = slideIndex;
            customSlideTransition(slideIndex);
          }
        }
      }
    };
    $(window).on("scroll", throttle(handleDigitalScroll, 100));

    // Assets Swiper Hover (Desktop)
    $(".assetsSwiper .swiper-slide").mouseenter(function () {
      $(this).addClass("active").siblings().removeClass("active");
      $(".assetsSwiper-box").css("--assetIndex", $(this).index());
    });
  } else {
    // Digital Section Swiper (Mobile)
    $("#grow-swiper .social-media-slide").remove();
    new Swiper("#digital-text-swiper", {
      slidesPerView: 1.2,
      spaceBetween: 15,
      loop: true,
      allowTouchMove: true,
      autoplay: { delay: 3000, disableOnInteraction: false },
      breakpoints: { 576: { slidesPerView: 2 } },
      pagination: { el: "#digital-text-swiper .swiper-pagination", clickable: true },
    });

    // Safeguard & Grow Swipers (Mobile)
    new Swiper("#safeguard-mobile-swiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      loop: true,
      // autoplay: { delay: 3000, disableOnInteraction: false },
      breakpoints: { 768: { slidesPerView: 2 } },
      pagination: { el: "#safeguard-mobile-swiper .swiper-pagination", clickable: true },
      navigation: { nextEl: "#safeguard-mobile-swiper .right-btn", prevEl: "#safeguard-mobile-swiper .left-btn" },
    });
    new Swiper("#grow-swiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      loop: true,
      autoplay: { delay: 3000, disableOnInteraction: false },
      breakpoints: { 768: { slidesPerView: 2 } },
      pagination: { el: "#grow-swiper .swiper-pagination", clickable: true },
    });

    // Assets Swiper (Mobile)
    new Swiper("#assetsSwiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      centeredSlides: true,
      autoplay: { delay: 3000, disableOnInteraction: false },
      loop: true,
      loopedSlides: 2,
      breakpoints: { 576: { slidesPerView: 1.6, spaceBetween: 20, centeredSlides: true } },
      pagination: { el: ".assetsSwiper-pagination", clickable: true },
    });
  }

  // Feature Swiper (Common)
  const featureTextSwiper = new Swiper("#feature-text-mobile-swiper", {
    slidesPerView: 1,
    spaceBetween: 15,
    effect: "fade",
    allowTouchMove: false,
    fadeEffect: { crossFade: true },
  });

  // 1. Define base Swiper options for #feature-swiper.
  const swiperOptions = {
    loop: true,
    autoplay: { delay: 3000, disableOnInteraction: false },
    pagination: { el: "#feature-swiper .swiper-pagination", clickable: true },
    on: {
      slideChange: function () {
        featureTextSwiper.slideTo(this.realIndex, 100, false);
      },
    },
    // 2. Define breakpoints for responsive behavior (desktop-first).
    breakpoints: {
      1600: { slidesPerView: 4, spaceBetween: 20 },
      992: { slidesPerView: 3, spaceBetween: 15 },
      576: { slidesPerView: 2, spaceBetween: 15 },
    },
  };

  // 3. Add mobile-specific "creative" effect for screens <= 576px.
  if (window.innerWidth <= 576) {
    Object.assign(swiperOptions, {
      effect: "creative",
      watchSlidesProgress: true,
      centeredSlides: true,
      slidesPerView: 1.8,
      spaceBetween: -20,
      creativeEffect: {
        prev: {
          shadow: false,
          translate: ["-85%", "5%", 0],
          rotate: [0, 0, -10],
          scale: 0.85,
          opacity: 1,
          origin: "bottom",
        },
        next: {
          shadow: false,
          translate: ["85%", "5%", 0],
          rotate: [0, 0, 10],
          scale: 0.85,
          opacity: 1,
          origin: "bottom",
        },
        limitProgress: 2,
      },
    });
  }

  // 4. Initialize Swiper and add desktop-only event listeners.
  const featureSwiper = new Swiper("#feature-swiper", swiperOptions);
  if (window.innerWidth > 576) {
    $("#feature-swiper").on("mouseenter", function () {
      featureSwiper.autoplay.stop();
    });
    $("#feature-swiper").on("mouseleave", function () {
      $(this).removeClass("active");
    });
  }

  // Count-up numbers (Common)
  const handleCountUpScroll = () => {
    const myElement = $(".count-box")[0];
    if (stepVal && isElementFullyInViewport(myElement)) {
      $(".count-num").countTo();
      stepVal = false;
    }
  };
  $(window).on("scroll", throttle(handleCountUpScroll, 200));
});
