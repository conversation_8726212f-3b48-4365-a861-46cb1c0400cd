import "./index.scss";

$(function () {
  // ========== 全局配置 ==========
  const CONFIG = {
    mobileBreakpoint: 992, // 移动端断点
    largeBreakpoint: 1280, // 大屏断点
    autoplayDelay: 3000, // 自动播放延迟
    transitionDuration: 300, // 过渡动画时长
    debounceDelay: 100, // 防抖延迟
  };

  // ========== AOS初始化 ==========

  /**
   * 初始化AOS动画库
   * 确保AOS库加载完成后再初始化，在移动端禁用动画以提升性能
   */
  function initAOS() {
    // 检查AOS是否已加载
    if (typeof AOS === "undefined") {
      console.warn("AOS库尚未加载完成，延迟初始化...");
      // 延迟100ms后重试
      setTimeout(initAOS, 100);
      return;
    }

    // 初始化AOS，恢复原始配置参数
    AOS.init({
      disable: window.innerWidth < CONFIG.mobileBreakpoint,
    });

    // 移动端强制禁用AOS动画样式（双重保险）
    disableAOSOnMobile();
  }

  /**
   * 在移动端强制禁用AOS动画
   * 通过CSS样式覆盖的方式确保移动端不显示动画
   */
  function disableAOSOnMobile() {
    if (window.innerWidth <= CONFIG.mobileBreakpoint) {
      const style = document.createElement("style");
      style.id = "aos-mobile-disable";
      style.textContent = `
        [data-aos] {
          transition-duration: 0s !important;
          transform: none !important;
          opacity: 1 !important;
          animation: none !important;
        }
      `;
      document.head.appendChild(style);
    }
  }

  // ========== 视频控制模块 ==========

  /**
   * 初始化视频播放控制
   * 点击视频区域切换播放/暂停状态
   */
  function initVideoControl() {
    $(".part-video .video-wrapper").on("click", function () {
      const $video = $(this).find("video");
      const video = $video[0];
      const $mask = $(this).find(".video-mask");
      const $playBtn = $(this).find(".video-play");

      if (video.paused) {
        video.play();
        $mask.hide();
        $playBtn.hide();
      } else {
        video.pause();
        $mask.show();
        $playBtn.show();
      }
    });
  }

  // ========== 轮播图模块 ==========

  /**
   * 初始化电脑端轮播图
   * 根据屏幕尺寸使用不同的配置
   */
  function initComputerSwiper() {
    const isLargeScreen = window.innerWidth > CONFIG.largeBreakpoint;

    const commonConfig = {
      loop: true,
      autoplay: {
        delay: CONFIG.autoplayDelay,
        disableOnInteraction: false,
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      on: {
        slideChange: function (swiper) {
          updateActiveTab(swiper.realIndex);
        },
      },
    };

    const desktopConfig = {
      ...commonConfig,
      effect: "fade",
      fadeEffect: {
        crossFade: true,
      },
    };

    const mobileConfig = {
      ...commonConfig,
      slidesPerView: 1,
      spaceBetween: 30,
    };

    return new Swiper("#swiper-computer", isLargeScreen ? desktopConfig : mobileConfig);
  }

  /**
   * 更新活动标签状态
   * @param {number} activeIndex - 当前活动的索引
   */
  function updateActiveTab(activeIndex) {
    // 桌面端标签
    $(".type-item-tab").each(function () {
      const $tab = $(this);
      $tab.toggleClass("active", $tab.data("index") === activeIndex);
    });

    // 移动端标签
    $(".type-tab-mobile").each(function (index) {
      $(this).toggleClass("active", index === activeIndex);
    });
  }

  /**
   * 初始化标签点击事件
   * @param {Swiper} swiperInstance - Swiper实例
   */
  function initTabControls(swiperInstance) {
    // 桌面端标签点击
    $(".type-item-tab").on("click", function () {
      const index = $(this).data("index");
      $(this).addClass("active").siblings().removeClass("active");
      swiperInstance.slideTo(index + 1, CONFIG.transitionDuration, false);
    });

    // 移动端标签点击
    $(".type-tab-mobile").on("click", function () {
      const index = $(this).index();
      $(this).addClass("active").siblings().removeClass("active");
      swiperInstance.slideTo(index + 1, CONFIG.transitionDuration, false);
    });
  }

  /**
   * 初始化GoPro轮播图
   */
  function initGoProSwiper() {
    return new Swiper("#swiper-gopro", {
      slidesPerView: 2,
      spaceBetween: 10,
      loop: true,
      autoplay: {
        delay: CONFIG.autoplayDelay,
        disableOnInteraction: false,
      },
      pagination: {
        el: "#swiper-gopro .swiper-pagination",
        clickable: true,
      },
      breakpoints: {
        1280: {
          loop: false,
          slidesPerView: 6,
          spaceBetween: 30,
        },
        992: {
          slidesPerView: 4,
          spaceBetween: 30,
        },
      },
    });
  }

  /**
   * 初始化Everyone轮播图
   * 大屏使用鼠标悬停效果，小屏使用轮播
   */
  function initEveryoneSection() {
    if (window.innerWidth > CONFIG.largeBreakpoint) {
      // 大屏幕使用鼠标悬停效果
      $("#swiper-everyone .swiper-slide").on("mouseenter", function () {
        $(this).addClass("active").siblings().removeClass("active");
      });
    } else {
      // 小屏幕使用轮播
      return new Swiper("#swiper-everyone", {
        loop: true,
        slidesPerView: 1,
        spaceBetween: 10,
        autoplay: {
          delay: CONFIG.autoplayDelay,
          disableOnInteraction: false,
        },
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        breakpoints: {
          768: {
            slidesPerView: 2,
            spaceBetween: 20,
          },
          992: {
            slidesPerView: 3,
            spaceBetween: 15,
          },
        },
      });
    }
  }

  /**
   * 初始化无人机轮播图
   */
  function initDroneSwiper() {
    const swiper = new Swiper("#drone-swiper", {
      slidesPerView: 1,
      effect: "fade",
      fadeEffect: {
        crossFade: true,
      },
      autoplay: {
        delay: CONFIG.autoplayDelay,
        disableOnInteraction: false,
      },
      loop: true,
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
    });

    // 添加鼠标悬浮和触摸控制自动播放的逻辑
    $("#drone-swiper")
      .on("mouseenter touchstart", function () {
        // 鼠标进入或触摸开始时停止自动播放
        swiper.autoplay.stop();
      })
      .on("mouseleave touchend", function () {
        // 鼠标离开或触摸结束时恢复自动播放
        swiper.autoplay.start();
      });

    return swiper;
  }

  /**
   * 初始化Best轮播图（仅移动端）
   */
  function initBestSwiper() {
    if (window.innerWidth < CONFIG.largeBreakpoint) {
      return new Swiper("#best-swiper", {
        slidesPerView: 1,
        spaceBetween: 10,
        loop: true,
        autoplay: {
          delay: CONFIG.autoplayDelay,
          disableOnInteraction: false,
        },
        breakpoints: {
          768: {
            slidesPerView: 3,
            spaceBetween: 15,
          },
          576: {
            slidesPerView: 2,
            spaceBetween: 15,
          },
        },
        pagination: {
          el: "#best-swiper .swiper-pagination",
          clickable: true,
        },
      });
    }
  }

  // ========== 滚动监听模块 ==========

  /**
   * 检查元素是否有一半在视口内
   * @param {Element} el - DOM元素
   * @returns {boolean} 是否有一半在视口内
   */
  function isElementHalfInViewport(el) {
    const rect = el.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const elementHeight = rect.height;
    // 计算元素可见部分的高度
    const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
    return visibleHeight >= elementHeight / 2;
  }

  /**
   * 检查元素是否完全在视口内
   * @param {Element} el - DOM元素
   * @returns {boolean} 是否完全在视口内
   */
  function isElementFullyInViewport(el) {
    const rect = el.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;
    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= windowHeight && rect.right <= windowWidth;
  }

  /**
   * 初始化滚动动画效果
   * 包括logo图标和类型列表的懒加载动画
   */
  function initScrollAnimations() {
    const $logosIcons = $(".logos-icon");
    const $typeLists = $(".type-list");

    // 使用防抖优化滚动事件
    let scrollTimeout;

    $(window).on("scroll", function () {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(function () {
        // Logo图标动画
        $logosIcons.each(function () {
          if (isElementHalfInViewport(this)) {
            $(this).addClass("active");
          }
        });

        // 类型列表动画
        $typeLists.each(function () {
          if (isElementHalfInViewport(this)) {
            $(this).addClass("active");
          }
        });
      }, 50); // 减少防抖延迟以提高响应速度
    });
  }

  /**
   * 初始化数字滚动效果
   * 当计数框完全进入视口时触发
   */
  function initCountAnimation() {
    let hasTriggered = false;
    let scrollTimeout;

    function checkCountBox() {
      const $countBox = $(".count-box");
      if ($countBox.length && isElementFullyInViewport($countBox[0]) && !hasTriggered) {
        $(".count-num").countTo();
        hasTriggered = true;
        // 触发后移除监听器以节省性能
        $(window).off("scroll.countAnimation");
      }
    }

    // 使用命名空间避免事件冲突
    $(window).on("scroll.countAnimation", function () {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(checkCountBox, CONFIG.debounceDelay);
    });
  }

  // ========== 初始化所有功能 ==========

  // 初始化视频控制
  initVideoControl();

  // 初始化各种轮播图
  const swiperComputer = initComputerSwiper();
  initTabControls(swiperComputer);
  initGoProSwiper();
  initEveryoneSection();
  initDroneSwiper();
  initBestSwiper();

  // 初始化滚动相关功能
  initScrollAnimations();
  initCountAnimation();
  initAOS();
});
