/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 787:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

eval("\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\nvar injectStylesIntoStyleTag = __webpack_require__(72);\nvar injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleDomAPI.js\nvar styleDomAPI = __webpack_require__(825);\nvar styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertBySelector.js\nvar insertBySelector = __webpack_require__(659);\nvar insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\nvar setAttributesWithoutAttributes = __webpack_require__(56);\nvar setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertStyleElement.js\nvar insertStyleElement = __webpack_require__(540);\nvar insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleTagTransform.js\nvar styleTagTransform = __webpack_require__(113);\nvar styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);\n// EXTERNAL MODULE: ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/index.scss\nvar cjs_js_src = __webpack_require__(2);\n;// CONCATENATED MODULE: ./src/index.scss\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (styleTagTransform_default());\noptions.setAttributes = (setAttributesWithoutAttributes_default());\noptions.insert = insertBySelector_default().bind(null, \"head\");\noptions.domAPI = (styleDomAPI_default());\noptions.insertStyleElement = (insertStyleElement_default());\n\nvar update = injectStylesIntoStyleTag_default()(cjs_js_src/* default */.A, options);\n\n\n\n\n       /* harmony default export */ const src = (cjs_js_src/* default */.A && cjs_js_src/* default */.A.locals ? cjs_js_src/* default */.A.locals : undefined);\n\n;// CONCATENATED MODULE: ./src/index.js\n\n$(() => {\n  // --- Configuration & State ---\n  const isDesktop = window.innerWidth >= 1280;\n  let isTransitioning = false;\n  let lastSlideIndex = 0;\n  let stepVal = true;\n  let digitalTextSwiper, digitalImgSwiper;\n\n  // --- Utility Functions ---\n  const throttle = (func, limit) => {\n    let inThrottle;\n    return (...args) => {\n      if (!inThrottle) {\n        func.apply(undefined, args);\n        inThrottle = true;\n        setTimeout(() => inThrottle = false, limit);\n      }\n    };\n  };\n  const wait = ms => new Promise(resolve => setTimeout(resolve, ms));\n  const setOpacity = (selector, opacity, duration) => {\n    $(selector).css({\n      opacity: opacity,\n      transition: `opacity ${duration}ms ease-in-out`\n    });\n    return wait(duration);\n  };\n  const isElementFullyInViewport = el => {\n    if (!el) return false;\n    const rect = el.getBoundingClientRect();\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth);\n  };\n\n  // --- Event Handlers & Initializers ---\n\n  const handleResize = () => {\n    const newIsDesktop = window.innerWidth >= 1280;\n    if (isDesktop !== newIsDesktop) {\n      window.location.reload();\n    }\n  };\n  $(window).on(\"resize\", throttle(handleResize, 200));\n\n  // Component-specific logic\n  if (isDesktop) {\n    // Digital Section Swipers (Desktop)\n    digitalTextSwiper = new Swiper(\"#digital-text-swiper\", {\n      slidesPerView: 1,\n      speed: 300,\n      allowTouchMove: false,\n      direction: \"horizontal\",\n      effect: \"fade\",\n      fadeEffect: {\n        crossFade: true\n      }\n    });\n    digitalImgSwiper = new Swiper(\"#digital-img-swiper\", {\n      slidesPerView: 1,\n      speed: 300,\n      allowTouchMove: false,\n      effect: \"fade\",\n      fadeEffect: {\n        crossFade: true\n      }\n    });\n    const customSlideTransition = async targetIndex => {\n      if (isTransitioning) return;\n      isTransitioning = true;\n      try {\n        await setOpacity(\"#digital-text-swiper, #digital-img-swiper\", \"0\", 200);\n        digitalTextSwiper.slideTo(targetIndex, 100, false);\n        digitalImgSwiper.slideTo(targetIndex, 100, false);\n        await wait(100);\n        const textPromise = setOpacity(\"#digital-text-swiper\", \"1\", 300);\n        await wait(200);\n        const imgPromise = setOpacity(\"#digital-img-swiper\", \"1\", 400);\n        await Promise.all([textPromise, imgPromise]);\n      } finally {\n        isTransitioning = false;\n      }\n    };\n    const handleDigitalScroll = () => {\n      const apartSection = $(\".part-digital\")[0];\n      if (apartSection) {\n        const totalSlides = digitalTextSwiper.slides.length;\n        const offset = apartSection.getBoundingClientRect();\n        if (offset.top < 72 && offset.bottom - window.innerHeight > 0) {\n          const perc = Math.round(100 * Math.abs(offset.top) / (offset.height - $(window).height()));\n          const slideIndex = Math.min(Math.floor(perc * totalSlides / 100), totalSlides);\n          if (slideIndex !== lastSlideIndex && slideIndex !== digitalTextSwiper.activeIndex) {\n            lastSlideIndex = slideIndex;\n            customSlideTransition(slideIndex);\n          }\n        }\n      }\n    };\n    $(window).on(\"scroll\", throttle(handleDigitalScroll, 100));\n\n    // Assets Swiper Hover (Desktop)\n    $(\".assetsSwiper .swiper-slide\").mouseenter(function () {\n      $(this).addClass(\"active\").siblings().removeClass(\"active\");\n      $(\".assetsSwiper-box\").css(\"--assetIndex\", $(this).index());\n    });\n  } else {\n    // Digital Section Swiper (Mobile)\n    $(\"#grow-swiper .social-media-slide\").remove();\n    new Swiper(\"#digital-text-swiper\", {\n      slidesPerView: 1.2,\n      spaceBetween: 15,\n      loop: true,\n      allowTouchMove: true,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      breakpoints: {\n        576: {\n          slidesPerView: 2\n        }\n      },\n      pagination: {\n        el: \"#digital-text-swiper .swiper-pagination\",\n        clickable: true\n      }\n    });\n\n    // Safeguard & Grow Swipers (Mobile)\n    new Swiper(\"#safeguard-mobile-swiper\", {\n      slidesPerView: 1,\n      spaceBetween: 20,\n      loop: true,\n      // autoplay: { delay: 3000, disableOnInteraction: false },\n      breakpoints: {\n        768: {\n          slidesPerView: 2\n        }\n      },\n      pagination: {\n        el: \"#safeguard-mobile-swiper .swiper-pagination\",\n        clickable: true\n      },\n      navigation: {\n        nextEl: \"#safeguard-mobile-swiper .right-btn\",\n        prevEl: \"#safeguard-mobile-swiper .left-btn\"\n      }\n    });\n    new Swiper(\"#grow-swiper\", {\n      slidesPerView: 1,\n      spaceBetween: 20,\n      loop: true,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      breakpoints: {\n        768: {\n          slidesPerView: 2\n        }\n      },\n      pagination: {\n        el: \"#grow-swiper .swiper-pagination\",\n        clickable: true\n      }\n    });\n\n    // Assets Swiper (Mobile)\n    new Swiper(\"#assetsSwiper\", {\n      slidesPerView: 1,\n      spaceBetween: 20,\n      centeredSlides: true,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      loop: true,\n      loopedSlides: 2,\n      breakpoints: {\n        576: {\n          slidesPerView: 1.6,\n          spaceBetween: 20,\n          centeredSlides: true\n        }\n      },\n      pagination: {\n        el: \".assetsSwiper-pagination\",\n        clickable: true\n      }\n    });\n  }\n\n  // Feature Swiper (Common)\n  const featureTextSwiper = new Swiper(\"#feature-text-mobile-swiper\", {\n    slidesPerView: 1,\n    spaceBetween: 15,\n    effect: \"fade\",\n    allowTouchMove: false,\n    fadeEffect: {\n      crossFade: true\n    }\n  });\n\n  // 1. Define base Swiper options for #feature-swiper.\n  const swiperOptions = {\n    loop: true,\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false\n    },\n    pagination: {\n      el: \"#feature-swiper .swiper-pagination\",\n      clickable: true\n    },\n    on: {\n      slideChange: function () {\n        featureTextSwiper.slideTo(this.realIndex, 100, false);\n      }\n    },\n    // 2. Define breakpoints for responsive behavior (desktop-first).\n    breakpoints: {\n      1600: {\n        slidesPerView: 4,\n        spaceBetween: 20\n      },\n      992: {\n        slidesPerView: 3,\n        spaceBetween: 15\n      },\n      576: {\n        slidesPerView: 2,\n        spaceBetween: 15\n      }\n    }\n  };\n\n  // 3. Add mobile-specific \"creative\" effect for screens <= 576px.\n  if (window.innerWidth <= 576) {\n    Object.assign(swiperOptions, {\n      effect: \"creative\",\n      watchSlidesProgress: true,\n      centeredSlides: true,\n      slidesPerView: 1.8,\n      spaceBetween: -20,\n      creativeEffect: {\n        prev: {\n          shadow: false,\n          translate: [\"-85%\", \"5%\", 0],\n          rotate: [0, 0, -10],\n          scale: 0.85,\n          opacity: 1,\n          origin: \"bottom\"\n        },\n        next: {\n          shadow: false,\n          translate: [\"85%\", \"5%\", 0],\n          rotate: [0, 0, 10],\n          scale: 0.85,\n          opacity: 1,\n          origin: \"bottom\"\n        },\n        limitProgress: 2\n      }\n    });\n  }\n\n  // 4. Initialize Swiper and add desktop-only event listeners.\n  const featureSwiper = new Swiper(\"#feature-swiper\", swiperOptions);\n  if (window.innerWidth > 576) {\n    $(\"#feature-swiper\").on(\"mouseenter\", function () {\n      featureSwiper.autoplay.stop();\n    });\n    $(\"#feature-swiper\").on(\"mouseleave\", function () {\n      $(this).removeClass(\"active\");\n    });\n  }\n\n  // Count-up numbers (Common)\n  const handleCountUpScroll = () => {\n    const myElement = $(\".count-box\")[0];\n    if (stepVal && isElementFullyInViewport(myElement)) {\n      $(\".count-num\").countTo();\n      stepVal = false;\n    }\n  };\n  $(window).on(\"scroll\", throttle(handleCountUpScroll, 200));\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzg3LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ0EsTUFBK0Y7QUFDL0YsTUFBcUY7QUFDckYsTUFBNEY7QUFDNUYsTUFBK0c7QUFDL0csTUFBd0c7QUFDeEcsTUFBd0c7QUFDeEcsTUFBNEk7QUFDNUk7QUFDQTs7QUFFQTs7QUFFQSw0QkFBNEIsNkJBQW1CO0FBQy9DLHdCQUF3QiwwQ0FBYTtBQUNyQyxpQkFBaUIsK0JBQWE7QUFDOUIsaUJBQWlCLHVCQUFNO0FBQ3ZCLDZCQUE2Qiw4QkFBa0I7O0FBRS9DLGFBQWEsa0NBQUcsQ0FBQyx5QkFBTzs7OztBQUlzRjtBQUM5RyxPQUFPLDBDQUFlLHlCQUFPLElBQUkseUJBQU8sVUFBVSx5QkFBTyxtQkFBbUIsRUFBQzs7O0FDeEJ2RDtBQUV0QkEsQ0FBQyxDQUFDLE1BQU07RUFDTjtFQUNBLE1BQU1DLFNBQVMsR0FBR0MsTUFBTSxDQUFDQyxVQUFVLElBQUksSUFBSTtFQUMzQyxJQUFJQyxlQUFlLEdBQUcsS0FBSztFQUMzQixJQUFJQyxjQUFjLEdBQUcsQ0FBQztFQUN0QixJQUFJQyxPQUFPLEdBQUcsSUFBSTtFQUNsQixJQUFJQyxpQkFBaUIsRUFBRUMsZ0JBQWdCOztFQUV2QztFQUNBLE1BQU1DLFFBQVEsR0FBR0EsQ0FBQ0MsSUFBSSxFQUFFQyxLQUFLLEtBQUs7SUFDaEMsSUFBSUMsVUFBVTtJQUNkLE9BQU8sQ0FBQyxHQUFHQyxJQUFJLEtBQUs7TUFDbEIsSUFBSSxDQUFDRCxVQUFVLEVBQUU7UUFDZkYsSUFBSSxDQUFDSSxLQUFLLENBQUMsU0FBSSxFQUFFRCxJQUFJLENBQUM7UUFDdEJELFVBQVUsR0FBRyxJQUFJO1FBQ2pCRyxVQUFVLENBQUMsTUFBT0gsVUFBVSxHQUFHLEtBQU0sRUFBRUQsS0FBSyxDQUFDO01BQy9DO0lBQ0YsQ0FBQztFQUNILENBQUM7RUFFRCxNQUFNSyxJQUFJLEdBQUlDLEVBQUUsSUFBSyxJQUFJQyxPQUFPLENBQUVDLE9BQU8sSUFBS0osVUFBVSxDQUFDSSxPQUFPLEVBQUVGLEVBQUUsQ0FBQyxDQUFDO0VBRXRFLE1BQU1HLFVBQVUsR0FBR0EsQ0FBQ0MsUUFBUSxFQUFFQyxPQUFPLEVBQUVDLFFBQVEsS0FBSztJQUNsRHZCLENBQUMsQ0FBQ3FCLFFBQVEsQ0FBQyxDQUFDRyxHQUFHLENBQUM7TUFDZEYsT0FBTyxFQUFFQSxPQUFPO01BQ2hCRyxVQUFVLEVBQUUsV0FBV0YsUUFBUTtJQUNqQyxDQUFDLENBQUM7SUFDRixPQUFPUCxJQUFJLENBQUNPLFFBQVEsQ0FBQztFQUN2QixDQUFDO0VBRUQsTUFBTUcsd0JBQXdCLEdBQUlDLEVBQUUsSUFBSztJQUN2QyxJQUFJLENBQUNBLEVBQUUsRUFBRSxPQUFPLEtBQUs7SUFDckIsTUFBTUMsSUFBSSxHQUFHRCxFQUFFLENBQUNFLHFCQUFxQixDQUFDLENBQUM7SUFDdkMsT0FDRUQsSUFBSSxDQUFDRSxHQUFHLElBQUksQ0FBQyxJQUNiRixJQUFJLENBQUNHLElBQUksSUFBSSxDQUFDLElBQ2RILElBQUksQ0FBQ0ksTUFBTSxLQUFLOUIsTUFBTSxDQUFDK0IsV0FBVyxJQUFJQyxRQUFRLENBQUNDLGVBQWUsQ0FBQ0MsWUFBWSxDQUFDLElBQzVFUixJQUFJLENBQUNTLEtBQUssS0FBS25DLE1BQU0sQ0FBQ0MsVUFBVSxJQUFJK0IsUUFBUSxDQUFDQyxlQUFlLENBQUNHLFdBQVcsQ0FBQztFQUU3RSxDQUFDOztFQUVEOztFQUVBLE1BQU1DLFlBQVksR0FBR0EsQ0FBQSxLQUFNO0lBQ3pCLE1BQU1DLFlBQVksR0FBR3RDLE1BQU0sQ0FBQ0MsVUFBVSxJQUFJLElBQUk7SUFDOUMsSUFBSUYsU0FBUyxLQUFLdUMsWUFBWSxFQUFFO01BQzlCdEMsTUFBTSxDQUFDdUMsUUFBUSxDQUFDQyxNQUFNLENBQUMsQ0FBQztJQUMxQjtFQUNGLENBQUM7RUFDRDFDLENBQUMsQ0FBQ0UsTUFBTSxDQUFDLENBQUN5QyxFQUFFLENBQUMsUUFBUSxFQUFFbEMsUUFBUSxDQUFDOEIsWUFBWSxFQUFFLEdBQUcsQ0FBQyxDQUFDOztFQUVuRDtFQUNBLElBQUl0QyxTQUFTLEVBQUU7SUFDYjtJQUNBTSxpQkFBaUIsR0FBRyxJQUFJcUMsTUFBTSxDQUFDLHNCQUFzQixFQUFFO01BQ3JEQyxhQUFhLEVBQUUsQ0FBQztNQUNoQkMsS0FBSyxFQUFFLEdBQUc7TUFDVkMsY0FBYyxFQUFFLEtBQUs7TUFDckJDLFNBQVMsRUFBRSxZQUFZO01BQ3ZCQyxNQUFNLEVBQUUsTUFBTTtNQUNkQyxVQUFVLEVBQUU7UUFBRUMsU0FBUyxFQUFFO01BQUs7SUFDaEMsQ0FBQyxDQUFDO0lBQ0YzQyxnQkFBZ0IsR0FBRyxJQUFJb0MsTUFBTSxDQUFDLHFCQUFxQixFQUFFO01BQ25EQyxhQUFhLEVBQUUsQ0FBQztNQUNoQkMsS0FBSyxFQUFFLEdBQUc7TUFDVkMsY0FBYyxFQUFFLEtBQUs7TUFDckJFLE1BQU0sRUFBRSxNQUFNO01BQ2RDLFVBQVUsRUFBRTtRQUFFQyxTQUFTLEVBQUU7TUFBSztJQUNoQyxDQUFDLENBQUM7SUFFRixNQUFNQyxxQkFBcUIsR0FBRyxNQUFPQyxXQUFXLElBQUs7TUFDbkQsSUFBSWpELGVBQWUsRUFBRTtNQUNyQkEsZUFBZSxHQUFHLElBQUk7TUFDdEIsSUFBSTtRQUNGLE1BQU1nQixVQUFVLENBQUMsMkNBQTJDLEVBQUUsR0FBRyxFQUFFLEdBQUcsQ0FBQztRQUN2RWIsaUJBQWlCLENBQUMrQyxPQUFPLENBQUNELFdBQVcsRUFBRSxHQUFHLEVBQUUsS0FBSyxDQUFDO1FBQ2xEN0MsZ0JBQWdCLENBQUM4QyxPQUFPLENBQUNELFdBQVcsRUFBRSxHQUFHLEVBQUUsS0FBSyxDQUFDO1FBQ2pELE1BQU1yQyxJQUFJLENBQUMsR0FBRyxDQUFDO1FBQ2YsTUFBTXVDLFdBQVcsR0FBR25DLFVBQVUsQ0FBQyxzQkFBc0IsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDO1FBQ2hFLE1BQU1KLElBQUksQ0FBQyxHQUFHLENBQUM7UUFDZixNQUFNd0MsVUFBVSxHQUFHcEMsVUFBVSxDQUFDLHFCQUFxQixFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUM7UUFDOUQsTUFBTUYsT0FBTyxDQUFDdUMsR0FBRyxDQUFDLENBQUNGLFdBQVcsRUFBRUMsVUFBVSxDQUFDLENBQUM7TUFDOUMsQ0FBQyxTQUFTO1FBQ1JwRCxlQUFlLEdBQUcsS0FBSztNQUN6QjtJQUNGLENBQUM7SUFFRCxNQUFNc0QsbUJBQW1CLEdBQUdBLENBQUEsS0FBTTtNQUNoQyxNQUFNQyxZQUFZLEdBQUczRCxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDO01BQzFDLElBQUkyRCxZQUFZLEVBQUU7UUFDaEIsTUFBTUMsV0FBVyxHQUFHckQsaUJBQWlCLENBQUNzRCxNQUFNLENBQUNDLE1BQU07UUFDbkQsTUFBTUMsTUFBTSxHQUFHSixZQUFZLENBQUM5QixxQkFBcUIsQ0FBQyxDQUFDO1FBQ25ELElBQUlrQyxNQUFNLENBQUNqQyxHQUFHLEdBQUcsRUFBRSxJQUFJaUMsTUFBTSxDQUFDL0IsTUFBTSxHQUFHOUIsTUFBTSxDQUFDK0IsV0FBVyxHQUFHLENBQUMsRUFBRTtVQUM3RCxNQUFNK0IsSUFBSSxHQUFHQyxJQUFJLENBQUNDLEtBQUssQ0FBRSxHQUFHLEdBQUdELElBQUksQ0FBQ0UsR0FBRyxDQUFDSixNQUFNLENBQUNqQyxHQUFHLENBQUMsSUFBS2lDLE1BQU0sQ0FBQ0ssTUFBTSxHQUFHcEUsQ0FBQyxDQUFDRSxNQUFNLENBQUMsQ0FBQ2tFLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQztVQUM1RixNQUFNQyxVQUFVLEdBQUdKLElBQUksQ0FBQ0ssR0FBRyxDQUFDTCxJQUFJLENBQUNNLEtBQUssQ0FBRVAsSUFBSSxHQUFHSixXQUFXLEdBQUksR0FBRyxDQUFDLEVBQUVBLFdBQVcsQ0FBQztVQUNoRixJQUFJUyxVQUFVLEtBQUtoRSxjQUFjLElBQUlnRSxVQUFVLEtBQUs5RCxpQkFBaUIsQ0FBQ2lFLFdBQVcsRUFBRTtZQUNqRm5FLGNBQWMsR0FBR2dFLFVBQVU7WUFDM0JqQixxQkFBcUIsQ0FBQ2lCLFVBQVUsQ0FBQztVQUNuQztRQUNGO01BQ0Y7SUFDRixDQUFDO0lBQ0RyRSxDQUFDLENBQUNFLE1BQU0sQ0FBQyxDQUFDeUMsRUFBRSxDQUFDLFFBQVEsRUFBRWxDLFFBQVEsQ0FBQ2lELG1CQUFtQixFQUFFLEdBQUcsQ0FBQyxDQUFDOztJQUUxRDtJQUNBMUQsQ0FBQyxDQUFDLDZCQUE2QixDQUFDLENBQUN5RSxVQUFVLENBQUMsWUFBWTtNQUN0RHpFLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQzBFLFFBQVEsQ0FBQyxRQUFRLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLFFBQVEsQ0FBQztNQUMzRDVFLENBQUMsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDd0IsR0FBRyxDQUFDLGNBQWMsRUFBRXhCLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQzZFLEtBQUssQ0FBQyxDQUFDLENBQUM7SUFDN0QsQ0FBQyxDQUFDO0VBQ0osQ0FBQyxNQUFNO0lBQ0w7SUFDQTdFLENBQUMsQ0FBQyxrQ0FBa0MsQ0FBQyxDQUFDOEUsTUFBTSxDQUFDLENBQUM7SUFDOUMsSUFBSWxDLE1BQU0sQ0FBQyxzQkFBc0IsRUFBRTtNQUNqQ0MsYUFBYSxFQUFFLEdBQUc7TUFDbEJrQyxZQUFZLEVBQUUsRUFBRTtNQUNoQkMsSUFBSSxFQUFFLElBQUk7TUFDVmpDLGNBQWMsRUFBRSxJQUFJO01BQ3BCa0MsUUFBUSxFQUFFO1FBQUVDLEtBQUssRUFBRSxJQUFJO1FBQUVDLG9CQUFvQixFQUFFO01BQU0sQ0FBQztNQUN0REMsV0FBVyxFQUFFO1FBQUUsR0FBRyxFQUFFO1VBQUV2QyxhQUFhLEVBQUU7UUFBRTtNQUFFLENBQUM7TUFDMUN3QyxVQUFVLEVBQUU7UUFBRTFELEVBQUUsRUFBRSx5Q0FBeUM7UUFBRTJELFNBQVMsRUFBRTtNQUFLO0lBQy9FLENBQUMsQ0FBQzs7SUFFRjtJQUNBLElBQUkxQyxNQUFNLENBQUMsMEJBQTBCLEVBQUU7TUFDckNDLGFBQWEsRUFBRSxDQUFDO01BQ2hCa0MsWUFBWSxFQUFFLEVBQUU7TUFDaEJDLElBQUksRUFBRSxJQUFJO01BQ1Y7TUFDQUksV0FBVyxFQUFFO1FBQUUsR0FBRyxFQUFFO1VBQUV2QyxhQUFhLEVBQUU7UUFBRTtNQUFFLENBQUM7TUFDMUN3QyxVQUFVLEVBQUU7UUFBRTFELEVBQUUsRUFBRSw2Q0FBNkM7UUFBRTJELFNBQVMsRUFBRTtNQUFLLENBQUM7TUFDbEZDLFVBQVUsRUFBRTtRQUFFQyxNQUFNLEVBQUUscUNBQXFDO1FBQUVDLE1BQU0sRUFBRTtNQUFxQztJQUM1RyxDQUFDLENBQUM7SUFDRixJQUFJN0MsTUFBTSxDQUFDLGNBQWMsRUFBRTtNQUN6QkMsYUFBYSxFQUFFLENBQUM7TUFDaEJrQyxZQUFZLEVBQUUsRUFBRTtNQUNoQkMsSUFBSSxFQUFFLElBQUk7TUFDVkMsUUFBUSxFQUFFO1FBQUVDLEtBQUssRUFBRSxJQUFJO1FBQUVDLG9CQUFvQixFQUFFO01BQU0sQ0FBQztNQUN0REMsV0FBVyxFQUFFO1FBQUUsR0FBRyxFQUFFO1VBQUV2QyxhQUFhLEVBQUU7UUFBRTtNQUFFLENBQUM7TUFDMUN3QyxVQUFVLEVBQUU7UUFBRTFELEVBQUUsRUFBRSxpQ0FBaUM7UUFBRTJELFNBQVMsRUFBRTtNQUFLO0lBQ3ZFLENBQUMsQ0FBQzs7SUFFRjtJQUNBLElBQUkxQyxNQUFNLENBQUMsZUFBZSxFQUFFO01BQzFCQyxhQUFhLEVBQUUsQ0FBQztNQUNoQmtDLFlBQVksRUFBRSxFQUFFO01BQ2hCVyxjQUFjLEVBQUUsSUFBSTtNQUNwQlQsUUFBUSxFQUFFO1FBQUVDLEtBQUssRUFBRSxJQUFJO1FBQUVDLG9CQUFvQixFQUFFO01BQU0sQ0FBQztNQUN0REgsSUFBSSxFQUFFLElBQUk7TUFDVlcsWUFBWSxFQUFFLENBQUM7TUFDZlAsV0FBVyxFQUFFO1FBQUUsR0FBRyxFQUFFO1VBQUV2QyxhQUFhLEVBQUUsR0FBRztVQUFFa0MsWUFBWSxFQUFFLEVBQUU7VUFBRVcsY0FBYyxFQUFFO1FBQUs7TUFBRSxDQUFDO01BQ3BGTCxVQUFVLEVBQUU7UUFBRTFELEVBQUUsRUFBRSwwQkFBMEI7UUFBRTJELFNBQVMsRUFBRTtNQUFLO0lBQ2hFLENBQUMsQ0FBQztFQUNKOztFQUVBO0VBQ0EsTUFBTU0saUJBQWlCLEdBQUcsSUFBSWhELE1BQU0sQ0FBQyw2QkFBNkIsRUFBRTtJQUNsRUMsYUFBYSxFQUFFLENBQUM7SUFDaEJrQyxZQUFZLEVBQUUsRUFBRTtJQUNoQjlCLE1BQU0sRUFBRSxNQUFNO0lBQ2RGLGNBQWMsRUFBRSxLQUFLO0lBQ3JCRyxVQUFVLEVBQUU7TUFBRUMsU0FBUyxFQUFFO0lBQUs7RUFDaEMsQ0FBQyxDQUFDOztFQUVGO0VBQ0EsTUFBTTBDLGFBQWEsR0FBRztJQUNwQmIsSUFBSSxFQUFFLElBQUk7SUFDVkMsUUFBUSxFQUFFO01BQUVDLEtBQUssRUFBRSxJQUFJO01BQUVDLG9CQUFvQixFQUFFO0lBQU0sQ0FBQztJQUN0REUsVUFBVSxFQUFFO01BQUUxRCxFQUFFLEVBQUUsb0NBQW9DO01BQUUyRCxTQUFTLEVBQUU7SUFBSyxDQUFDO0lBQ3pFM0MsRUFBRSxFQUFFO01BQ0ZtRCxXQUFXLEVBQUUsU0FBQUEsQ0FBQSxFQUFZO1FBQ3ZCRixpQkFBaUIsQ0FBQ3RDLE9BQU8sQ0FBQyxJQUFJLENBQUN5QyxTQUFTLEVBQUUsR0FBRyxFQUFFLEtBQUssQ0FBQztNQUN2RDtJQUNGLENBQUM7SUFDRDtJQUNBWCxXQUFXLEVBQUU7TUFDWCxJQUFJLEVBQUU7UUFBRXZDLGFBQWEsRUFBRSxDQUFDO1FBQUVrQyxZQUFZLEVBQUU7TUFBRyxDQUFDO01BQzVDLEdBQUcsRUFBRTtRQUFFbEMsYUFBYSxFQUFFLENBQUM7UUFBRWtDLFlBQVksRUFBRTtNQUFHLENBQUM7TUFDM0MsR0FBRyxFQUFFO1FBQUVsQyxhQUFhLEVBQUUsQ0FBQztRQUFFa0MsWUFBWSxFQUFFO01BQUc7SUFDNUM7RUFDRixDQUFDOztFQUVEO0VBQ0EsSUFBSTdFLE1BQU0sQ0FBQ0MsVUFBVSxJQUFJLEdBQUcsRUFBRTtJQUM1QjZGLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDSixhQUFhLEVBQUU7TUFDM0I1QyxNQUFNLEVBQUUsVUFBVTtNQUNsQmlELG1CQUFtQixFQUFFLElBQUk7TUFDekJSLGNBQWMsRUFBRSxJQUFJO01BQ3BCN0MsYUFBYSxFQUFFLEdBQUc7TUFDbEJrQyxZQUFZLEVBQUUsQ0FBQyxFQUFFO01BQ2pCb0IsY0FBYyxFQUFFO1FBQ2RDLElBQUksRUFBRTtVQUNKQyxNQUFNLEVBQUUsS0FBSztVQUNiQyxTQUFTLEVBQUUsQ0FBQyxNQUFNLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQztVQUM1QkMsTUFBTSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQztVQUNuQkMsS0FBSyxFQUFFLElBQUk7VUFDWGxGLE9BQU8sRUFBRSxDQUFDO1VBQ1ZtRixNQUFNLEVBQUU7UUFDVixDQUFDO1FBQ0RDLElBQUksRUFBRTtVQUNKTCxNQUFNLEVBQUUsS0FBSztVQUNiQyxTQUFTLEVBQUUsQ0FBQyxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQztVQUMzQkMsTUFBTSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUM7VUFDbEJDLEtBQUssRUFBRSxJQUFJO1VBQ1hsRixPQUFPLEVBQUUsQ0FBQztVQUNWbUYsTUFBTSxFQUFFO1FBQ1YsQ0FBQztRQUNERSxhQUFhLEVBQUU7TUFDakI7SUFDRixDQUFDLENBQUM7RUFDSjs7RUFFQTtFQUNBLE1BQU1DLGFBQWEsR0FBRyxJQUFJaEUsTUFBTSxDQUFDLGlCQUFpQixFQUFFaUQsYUFBYSxDQUFDO0VBQ2xFLElBQUkzRixNQUFNLENBQUNDLFVBQVUsR0FBRyxHQUFHLEVBQUU7SUFDM0JILENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDMkMsRUFBRSxDQUFDLFlBQVksRUFBRSxZQUFZO01BQ2hEaUUsYUFBYSxDQUFDM0IsUUFBUSxDQUFDNEIsSUFBSSxDQUFDLENBQUM7SUFDL0IsQ0FBQyxDQUFDO0lBQ0Y3RyxDQUFDLENBQUMsaUJBQWlCLENBQUMsQ0FBQzJDLEVBQUUsQ0FBQyxZQUFZLEVBQUUsWUFBWTtNQUNoRDNDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQzRFLFdBQVcsQ0FBQyxRQUFRLENBQUM7SUFDL0IsQ0FBQyxDQUFDO0VBQ0o7O0VBRUE7RUFDQSxNQUFNa0MsbUJBQW1CLEdBQUdBLENBQUEsS0FBTTtJQUNoQyxNQUFNQyxTQUFTLEdBQUcvRyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQ3BDLElBQUlNLE9BQU8sSUFBSW9CLHdCQUF3QixDQUFDcUYsU0FBUyxDQUFDLEVBQUU7TUFDbEQvRyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUNnSCxPQUFPLENBQUMsQ0FBQztNQUN6QjFHLE9BQU8sR0FBRyxLQUFLO0lBQ2pCO0VBQ0YsQ0FBQztFQUNETixDQUFDLENBQUNFLE1BQU0sQ0FBQyxDQUFDeUMsRUFBRSxDQUFDLFFBQVEsRUFBRWxDLFFBQVEsQ0FBQ3FHLG1CQUFtQixFQUFFLEdBQUcsQ0FBQyxDQUFDO0FBQzVELENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL3NyYy9pbmRleC5zY3NzPzcyMjMiLCJ3ZWJwYWNrOi8vLy4vc3JjL2luZGV4LmpzP2I2MzUiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgICBpbXBvcnQgQVBJIGZyb20gXCIhLi4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvaW5qZWN0U3R5bGVzSW50b1N0eWxlVGFnLmpzXCI7XG4gICAgICBpbXBvcnQgZG9tQVBJIGZyb20gXCIhLi4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc3R5bGVEb21BUEkuanNcIjtcbiAgICAgIGltcG9ydCBpbnNlcnRGbiBmcm9tIFwiIS4uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanNcIjtcbiAgICAgIGltcG9ydCBzZXRBdHRyaWJ1dGVzIGZyb20gXCIhLi4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc2V0QXR0cmlidXRlc1dpdGhvdXRBdHRyaWJ1dGVzLmpzXCI7XG4gICAgICBpbXBvcnQgaW5zZXJ0U3R5bGVFbGVtZW50IGZyb20gXCIhLi4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvaW5zZXJ0U3R5bGVFbGVtZW50LmpzXCI7XG4gICAgICBpbXBvcnQgc3R5bGVUYWdUcmFuc2Zvcm1GbiBmcm9tIFwiIS4uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL3N0eWxlVGFnVHJhbnNmb3JtLmpzXCI7XG4gICAgICBpbXBvcnQgY29udGVudCwgKiBhcyBuYW1lZEV4cG9ydCBmcm9tIFwiISEuLi9ub2RlX21vZHVsZXMvY3NzLWxvYWRlci9kaXN0L2Nqcy5qcyEuLi9ub2RlX21vZHVsZXMvc2Fzcy1sb2FkZXIvZGlzdC9janMuanMhLi9pbmRleC5zY3NzXCI7XG4gICAgICBcbiAgICAgIFxuXG52YXIgb3B0aW9ucyA9IHt9O1xuXG5vcHRpb25zLnN0eWxlVGFnVHJhbnNmb3JtID0gc3R5bGVUYWdUcmFuc2Zvcm1Gbjtcbm9wdGlvbnMuc2V0QXR0cmlidXRlcyA9IHNldEF0dHJpYnV0ZXM7XG5vcHRpb25zLmluc2VydCA9IGluc2VydEZuLmJpbmQobnVsbCwgXCJoZWFkXCIpO1xub3B0aW9ucy5kb21BUEkgPSBkb21BUEk7XG5vcHRpb25zLmluc2VydFN0eWxlRWxlbWVudCA9IGluc2VydFN0eWxlRWxlbWVudDtcblxudmFyIHVwZGF0ZSA9IEFQSShjb250ZW50LCBvcHRpb25zKTtcblxuXG5cbmV4cG9ydCAqIGZyb20gXCIhIS4uL25vZGVfbW9kdWxlcy9jc3MtbG9hZGVyL2Rpc3QvY2pzLmpzIS4uL25vZGVfbW9kdWxlcy9zYXNzLWxvYWRlci9kaXN0L2Nqcy5qcyEuL2luZGV4LnNjc3NcIjtcbiAgICAgICBleHBvcnQgZGVmYXVsdCBjb250ZW50ICYmIGNvbnRlbnQubG9jYWxzID8gY29udGVudC5sb2NhbHMgOiB1bmRlZmluZWQ7XG4iLCJpbXBvcnQgXCIuL2luZGV4LnNjc3NcIjtcblxuJCgoKSA9PiB7XG4gIC8vIC0tLSBDb25maWd1cmF0aW9uICYgU3RhdGUgLS0tXG4gIGNvbnN0IGlzRGVza3RvcCA9IHdpbmRvdy5pbm5lcldpZHRoID49IDEyODA7XG4gIGxldCBpc1RyYW5zaXRpb25pbmcgPSBmYWxzZTtcbiAgbGV0IGxhc3RTbGlkZUluZGV4ID0gMDtcbiAgbGV0IHN0ZXBWYWwgPSB0cnVlO1xuICBsZXQgZGlnaXRhbFRleHRTd2lwZXIsIGRpZ2l0YWxJbWdTd2lwZXI7XG5cbiAgLy8gLS0tIFV0aWxpdHkgRnVuY3Rpb25zIC0tLVxuICBjb25zdCB0aHJvdHRsZSA9IChmdW5jLCBsaW1pdCkgPT4ge1xuICAgIGxldCBpblRocm90dGxlO1xuICAgIHJldHVybiAoLi4uYXJncykgPT4ge1xuICAgICAgaWYgKCFpblRocm90dGxlKSB7XG4gICAgICAgIGZ1bmMuYXBwbHkodGhpcywgYXJncyk7XG4gICAgICAgIGluVGhyb3R0bGUgPSB0cnVlO1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IChpblRocm90dGxlID0gZmFsc2UpLCBsaW1pdCk7XG4gICAgICB9XG4gICAgfTtcbiAgfTtcblxuICBjb25zdCB3YWl0ID0gKG1zKSA9PiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCBtcykpO1xuXG4gIGNvbnN0IHNldE9wYWNpdHkgPSAoc2VsZWN0b3IsIG9wYWNpdHksIGR1cmF0aW9uKSA9PiB7XG4gICAgJChzZWxlY3RvcikuY3NzKHtcbiAgICAgIG9wYWNpdHk6IG9wYWNpdHksXG4gICAgICB0cmFuc2l0aW9uOiBgb3BhY2l0eSAke2R1cmF0aW9ufW1zIGVhc2UtaW4tb3V0YCxcbiAgICB9KTtcbiAgICByZXR1cm4gd2FpdChkdXJhdGlvbik7XG4gIH07XG5cbiAgY29uc3QgaXNFbGVtZW50RnVsbHlJblZpZXdwb3J0ID0gKGVsKSA9PiB7XG4gICAgaWYgKCFlbCkgcmV0dXJuIGZhbHNlO1xuICAgIGNvbnN0IHJlY3QgPSBlbC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICByZXR1cm4gKFxuICAgICAgcmVjdC50b3AgPj0gMCAmJlxuICAgICAgcmVjdC5sZWZ0ID49IDAgJiZcbiAgICAgIHJlY3QuYm90dG9tIDw9ICh3aW5kb3cuaW5uZXJIZWlnaHQgfHwgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsaWVudEhlaWdodCkgJiZcbiAgICAgIHJlY3QucmlnaHQgPD0gKHdpbmRvdy5pbm5lcldpZHRoIHx8IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRXaWR0aClcbiAgICApO1xuICB9O1xuXG4gIC8vIC0tLSBFdmVudCBIYW5kbGVycyAmIEluaXRpYWxpemVycyAtLS1cblxuICBjb25zdCBoYW5kbGVSZXNpemUgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3SXNEZXNrdG9wID0gd2luZG93LmlubmVyV2lkdGggPj0gMTI4MDtcbiAgICBpZiAoaXNEZXNrdG9wICE9PSBuZXdJc0Rlc2t0b3ApIHtcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKTtcbiAgICB9XG4gIH07XG4gICQod2luZG93KS5vbihcInJlc2l6ZVwiLCB0aHJvdHRsZShoYW5kbGVSZXNpemUsIDIwMCkpO1xuXG4gIC8vIENvbXBvbmVudC1zcGVjaWZpYyBsb2dpY1xuICBpZiAoaXNEZXNrdG9wKSB7XG4gICAgLy8gRGlnaXRhbCBTZWN0aW9uIFN3aXBlcnMgKERlc2t0b3ApXG4gICAgZGlnaXRhbFRleHRTd2lwZXIgPSBuZXcgU3dpcGVyKFwiI2RpZ2l0YWwtdGV4dC1zd2lwZXJcIiwge1xuICAgICAgc2xpZGVzUGVyVmlldzogMSxcbiAgICAgIHNwZWVkOiAzMDAsXG4gICAgICBhbGxvd1RvdWNoTW92ZTogZmFsc2UsXG4gICAgICBkaXJlY3Rpb246IFwiaG9yaXpvbnRhbFwiLFxuICAgICAgZWZmZWN0OiBcImZhZGVcIixcbiAgICAgIGZhZGVFZmZlY3Q6IHsgY3Jvc3NGYWRlOiB0cnVlIH0sXG4gICAgfSk7XG4gICAgZGlnaXRhbEltZ1N3aXBlciA9IG5ldyBTd2lwZXIoXCIjZGlnaXRhbC1pbWctc3dpcGVyXCIsIHtcbiAgICAgIHNsaWRlc1BlclZpZXc6IDEsXG4gICAgICBzcGVlZDogMzAwLFxuICAgICAgYWxsb3dUb3VjaE1vdmU6IGZhbHNlLFxuICAgICAgZWZmZWN0OiBcImZhZGVcIixcbiAgICAgIGZhZGVFZmZlY3Q6IHsgY3Jvc3NGYWRlOiB0cnVlIH0sXG4gICAgfSk7XG5cbiAgICBjb25zdCBjdXN0b21TbGlkZVRyYW5zaXRpb24gPSBhc3luYyAodGFyZ2V0SW5kZXgpID0+IHtcbiAgICAgIGlmIChpc1RyYW5zaXRpb25pbmcpIHJldHVybjtcbiAgICAgIGlzVHJhbnNpdGlvbmluZyA9IHRydWU7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCBzZXRPcGFjaXR5KFwiI2RpZ2l0YWwtdGV4dC1zd2lwZXIsICNkaWdpdGFsLWltZy1zd2lwZXJcIiwgXCIwXCIsIDIwMCk7XG4gICAgICAgIGRpZ2l0YWxUZXh0U3dpcGVyLnNsaWRlVG8odGFyZ2V0SW5kZXgsIDEwMCwgZmFsc2UpO1xuICAgICAgICBkaWdpdGFsSW1nU3dpcGVyLnNsaWRlVG8odGFyZ2V0SW5kZXgsIDEwMCwgZmFsc2UpO1xuICAgICAgICBhd2FpdCB3YWl0KDEwMCk7XG4gICAgICAgIGNvbnN0IHRleHRQcm9taXNlID0gc2V0T3BhY2l0eShcIiNkaWdpdGFsLXRleHQtc3dpcGVyXCIsIFwiMVwiLCAzMDApO1xuICAgICAgICBhd2FpdCB3YWl0KDIwMCk7XG4gICAgICAgIGNvbnN0IGltZ1Byb21pc2UgPSBzZXRPcGFjaXR5KFwiI2RpZ2l0YWwtaW1nLXN3aXBlclwiLCBcIjFcIiwgNDAwKTtcbiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoW3RleHRQcm9taXNlLCBpbWdQcm9taXNlXSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBpc1RyYW5zaXRpb25pbmcgPSBmYWxzZTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgY29uc3QgaGFuZGxlRGlnaXRhbFNjcm9sbCA9ICgpID0+IHtcbiAgICAgIGNvbnN0IGFwYXJ0U2VjdGlvbiA9ICQoXCIucGFydC1kaWdpdGFsXCIpWzBdO1xuICAgICAgaWYgKGFwYXJ0U2VjdGlvbikge1xuICAgICAgICBjb25zdCB0b3RhbFNsaWRlcyA9IGRpZ2l0YWxUZXh0U3dpcGVyLnNsaWRlcy5sZW5ndGg7XG4gICAgICAgIGNvbnN0IG9mZnNldCA9IGFwYXJ0U2VjdGlvbi5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICAgICAgaWYgKG9mZnNldC50b3AgPCA3MiAmJiBvZmZzZXQuYm90dG9tIC0gd2luZG93LmlubmVySGVpZ2h0ID4gMCkge1xuICAgICAgICAgIGNvbnN0IHBlcmMgPSBNYXRoLnJvdW5kKCgxMDAgKiBNYXRoLmFicyhvZmZzZXQudG9wKSkgLyAob2Zmc2V0LmhlaWdodCAtICQod2luZG93KS5oZWlnaHQoKSkpO1xuICAgICAgICAgIGNvbnN0IHNsaWRlSW5kZXggPSBNYXRoLm1pbihNYXRoLmZsb29yKChwZXJjICogdG90YWxTbGlkZXMpIC8gMTAwKSwgdG90YWxTbGlkZXMpO1xuICAgICAgICAgIGlmIChzbGlkZUluZGV4ICE9PSBsYXN0U2xpZGVJbmRleCAmJiBzbGlkZUluZGV4ICE9PSBkaWdpdGFsVGV4dFN3aXBlci5hY3RpdmVJbmRleCkge1xuICAgICAgICAgICAgbGFzdFNsaWRlSW5kZXggPSBzbGlkZUluZGV4O1xuICAgICAgICAgICAgY3VzdG9tU2xpZGVUcmFuc2l0aW9uKHNsaWRlSW5kZXgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG4gICAgJCh3aW5kb3cpLm9uKFwic2Nyb2xsXCIsIHRocm90dGxlKGhhbmRsZURpZ2l0YWxTY3JvbGwsIDEwMCkpO1xuXG4gICAgLy8gQXNzZXRzIFN3aXBlciBIb3ZlciAoRGVza3RvcClcbiAgICAkKFwiLmFzc2V0c1N3aXBlciAuc3dpcGVyLXNsaWRlXCIpLm1vdXNlZW50ZXIoZnVuY3Rpb24gKCkge1xuICAgICAgJCh0aGlzKS5hZGRDbGFzcyhcImFjdGl2ZVwiKS5zaWJsaW5ncygpLnJlbW92ZUNsYXNzKFwiYWN0aXZlXCIpO1xuICAgICAgJChcIi5hc3NldHNTd2lwZXItYm94XCIpLmNzcyhcIi0tYXNzZXRJbmRleFwiLCAkKHRoaXMpLmluZGV4KCkpO1xuICAgIH0pO1xuICB9IGVsc2Uge1xuICAgIC8vIERpZ2l0YWwgU2VjdGlvbiBTd2lwZXIgKE1vYmlsZSlcbiAgICAkKFwiI2dyb3ctc3dpcGVyIC5zb2NpYWwtbWVkaWEtc2xpZGVcIikucmVtb3ZlKCk7XG4gICAgbmV3IFN3aXBlcihcIiNkaWdpdGFsLXRleHQtc3dpcGVyXCIsIHtcbiAgICAgIHNsaWRlc1BlclZpZXc6IDEuMixcbiAgICAgIHNwYWNlQmV0d2VlbjogMTUsXG4gICAgICBsb29wOiB0cnVlLFxuICAgICAgYWxsb3dUb3VjaE1vdmU6IHRydWUsXG4gICAgICBhdXRvcGxheTogeyBkZWxheTogMzAwMCwgZGlzYWJsZU9uSW50ZXJhY3Rpb246IGZhbHNlIH0sXG4gICAgICBicmVha3BvaW50czogeyA1NzY6IHsgc2xpZGVzUGVyVmlldzogMiB9IH0sXG4gICAgICBwYWdpbmF0aW9uOiB7IGVsOiBcIiNkaWdpdGFsLXRleHQtc3dpcGVyIC5zd2lwZXItcGFnaW5hdGlvblwiLCBjbGlja2FibGU6IHRydWUgfSxcbiAgICB9KTtcblxuICAgIC8vIFNhZmVndWFyZCAmIEdyb3cgU3dpcGVycyAoTW9iaWxlKVxuICAgIG5ldyBTd2lwZXIoXCIjc2FmZWd1YXJkLW1vYmlsZS1zd2lwZXJcIiwge1xuICAgICAgc2xpZGVzUGVyVmlldzogMSxcbiAgICAgIHNwYWNlQmV0d2VlbjogMjAsXG4gICAgICBsb29wOiB0cnVlLFxuICAgICAgLy8gYXV0b3BsYXk6IHsgZGVsYXk6IDMwMDAsIGRpc2FibGVPbkludGVyYWN0aW9uOiBmYWxzZSB9LFxuICAgICAgYnJlYWtwb2ludHM6IHsgNzY4OiB7IHNsaWRlc1BlclZpZXc6IDIgfSB9LFxuICAgICAgcGFnaW5hdGlvbjogeyBlbDogXCIjc2FmZWd1YXJkLW1vYmlsZS1zd2lwZXIgLnN3aXBlci1wYWdpbmF0aW9uXCIsIGNsaWNrYWJsZTogdHJ1ZSB9LFxuICAgICAgbmF2aWdhdGlvbjogeyBuZXh0RWw6IFwiI3NhZmVndWFyZC1tb2JpbGUtc3dpcGVyIC5yaWdodC1idG5cIiwgcHJldkVsOiBcIiNzYWZlZ3VhcmQtbW9iaWxlLXN3aXBlciAubGVmdC1idG5cIiB9LFxuICAgIH0pO1xuICAgIG5ldyBTd2lwZXIoXCIjZ3Jvdy1zd2lwZXJcIiwge1xuICAgICAgc2xpZGVzUGVyVmlldzogMSxcbiAgICAgIHNwYWNlQmV0d2VlbjogMjAsXG4gICAgICBsb29wOiB0cnVlLFxuICAgICAgYXV0b3BsYXk6IHsgZGVsYXk6IDMwMDAsIGRpc2FibGVPbkludGVyYWN0aW9uOiBmYWxzZSB9LFxuICAgICAgYnJlYWtwb2ludHM6IHsgNzY4OiB7IHNsaWRlc1BlclZpZXc6IDIgfSB9LFxuICAgICAgcGFnaW5hdGlvbjogeyBlbDogXCIjZ3Jvdy1zd2lwZXIgLnN3aXBlci1wYWdpbmF0aW9uXCIsIGNsaWNrYWJsZTogdHJ1ZSB9LFxuICAgIH0pO1xuXG4gICAgLy8gQXNzZXRzIFN3aXBlciAoTW9iaWxlKVxuICAgIG5ldyBTd2lwZXIoXCIjYXNzZXRzU3dpcGVyXCIsIHtcbiAgICAgIHNsaWRlc1BlclZpZXc6IDEsXG4gICAgICBzcGFjZUJldHdlZW46IDIwLFxuICAgICAgY2VudGVyZWRTbGlkZXM6IHRydWUsXG4gICAgICBhdXRvcGxheTogeyBkZWxheTogMzAwMCwgZGlzYWJsZU9uSW50ZXJhY3Rpb246IGZhbHNlIH0sXG4gICAgICBsb29wOiB0cnVlLFxuICAgICAgbG9vcGVkU2xpZGVzOiAyLFxuICAgICAgYnJlYWtwb2ludHM6IHsgNTc2OiB7IHNsaWRlc1BlclZpZXc6IDEuNiwgc3BhY2VCZXR3ZWVuOiAyMCwgY2VudGVyZWRTbGlkZXM6IHRydWUgfSB9LFxuICAgICAgcGFnaW5hdGlvbjogeyBlbDogXCIuYXNzZXRzU3dpcGVyLXBhZ2luYXRpb25cIiwgY2xpY2thYmxlOiB0cnVlIH0sXG4gICAgfSk7XG4gIH1cblxuICAvLyBGZWF0dXJlIFN3aXBlciAoQ29tbW9uKVxuICBjb25zdCBmZWF0dXJlVGV4dFN3aXBlciA9IG5ldyBTd2lwZXIoXCIjZmVhdHVyZS10ZXh0LW1vYmlsZS1zd2lwZXJcIiwge1xuICAgIHNsaWRlc1BlclZpZXc6IDEsXG4gICAgc3BhY2VCZXR3ZWVuOiAxNSxcbiAgICBlZmZlY3Q6IFwiZmFkZVwiLFxuICAgIGFsbG93VG91Y2hNb3ZlOiBmYWxzZSxcbiAgICBmYWRlRWZmZWN0OiB7IGNyb3NzRmFkZTogdHJ1ZSB9LFxuICB9KTtcblxuICAvLyAxLiBEZWZpbmUgYmFzZSBTd2lwZXIgb3B0aW9ucyBmb3IgI2ZlYXR1cmUtc3dpcGVyLlxuICBjb25zdCBzd2lwZXJPcHRpb25zID0ge1xuICAgIGxvb3A6IHRydWUsXG4gICAgYXV0b3BsYXk6IHsgZGVsYXk6IDMwMDAsIGRpc2FibGVPbkludGVyYWN0aW9uOiBmYWxzZSB9LFxuICAgIHBhZ2luYXRpb246IHsgZWw6IFwiI2ZlYXR1cmUtc3dpcGVyIC5zd2lwZXItcGFnaW5hdGlvblwiLCBjbGlja2FibGU6IHRydWUgfSxcbiAgICBvbjoge1xuICAgICAgc2xpZGVDaGFuZ2U6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgZmVhdHVyZVRleHRTd2lwZXIuc2xpZGVUbyh0aGlzLnJlYWxJbmRleCwgMTAwLCBmYWxzZSk7XG4gICAgICB9LFxuICAgIH0sXG4gICAgLy8gMi4gRGVmaW5lIGJyZWFrcG9pbnRzIGZvciByZXNwb25zaXZlIGJlaGF2aW9yIChkZXNrdG9wLWZpcnN0KS5cbiAgICBicmVha3BvaW50czoge1xuICAgICAgMTYwMDogeyBzbGlkZXNQZXJWaWV3OiA0LCBzcGFjZUJldHdlZW46IDIwIH0sXG4gICAgICA5OTI6IHsgc2xpZGVzUGVyVmlldzogMywgc3BhY2VCZXR3ZWVuOiAxNSB9LFxuICAgICAgNTc2OiB7IHNsaWRlc1BlclZpZXc6IDIsIHNwYWNlQmV0d2VlbjogMTUgfSxcbiAgICB9LFxuICB9O1xuXG4gIC8vIDMuIEFkZCBtb2JpbGUtc3BlY2lmaWMgXCJjcmVhdGl2ZVwiIGVmZmVjdCBmb3Igc2NyZWVucyA8PSA1NzZweC5cbiAgaWYgKHdpbmRvdy5pbm5lcldpZHRoIDw9IDU3Nikge1xuICAgIE9iamVjdC5hc3NpZ24oc3dpcGVyT3B0aW9ucywge1xuICAgICAgZWZmZWN0OiBcImNyZWF0aXZlXCIsXG4gICAgICB3YXRjaFNsaWRlc1Byb2dyZXNzOiB0cnVlLFxuICAgICAgY2VudGVyZWRTbGlkZXM6IHRydWUsXG4gICAgICBzbGlkZXNQZXJWaWV3OiAxLjgsXG4gICAgICBzcGFjZUJldHdlZW46IC0yMCxcbiAgICAgIGNyZWF0aXZlRWZmZWN0OiB7XG4gICAgICAgIHByZXY6IHtcbiAgICAgICAgICBzaGFkb3c6IGZhbHNlLFxuICAgICAgICAgIHRyYW5zbGF0ZTogW1wiLTg1JVwiLCBcIjUlXCIsIDBdLFxuICAgICAgICAgIHJvdGF0ZTogWzAsIDAsIC0xMF0sXG4gICAgICAgICAgc2NhbGU6IDAuODUsXG4gICAgICAgICAgb3BhY2l0eTogMSxcbiAgICAgICAgICBvcmlnaW46IFwiYm90dG9tXCIsXG4gICAgICAgIH0sXG4gICAgICAgIG5leHQ6IHtcbiAgICAgICAgICBzaGFkb3c6IGZhbHNlLFxuICAgICAgICAgIHRyYW5zbGF0ZTogW1wiODUlXCIsIFwiNSVcIiwgMF0sXG4gICAgICAgICAgcm90YXRlOiBbMCwgMCwgMTBdLFxuICAgICAgICAgIHNjYWxlOiAwLjg1LFxuICAgICAgICAgIG9wYWNpdHk6IDEsXG4gICAgICAgICAgb3JpZ2luOiBcImJvdHRvbVwiLFxuICAgICAgICB9LFxuICAgICAgICBsaW1pdFByb2dyZXNzOiAyLFxuICAgICAgfSxcbiAgICB9KTtcbiAgfVxuXG4gIC8vIDQuIEluaXRpYWxpemUgU3dpcGVyIGFuZCBhZGQgZGVza3RvcC1vbmx5IGV2ZW50IGxpc3RlbmVycy5cbiAgY29uc3QgZmVhdHVyZVN3aXBlciA9IG5ldyBTd2lwZXIoXCIjZmVhdHVyZS1zd2lwZXJcIiwgc3dpcGVyT3B0aW9ucyk7XG4gIGlmICh3aW5kb3cuaW5uZXJXaWR0aCA+IDU3Nikge1xuICAgICQoXCIjZmVhdHVyZS1zd2lwZXJcIikub24oXCJtb3VzZWVudGVyXCIsIGZ1bmN0aW9uICgpIHtcbiAgICAgIGZlYXR1cmVTd2lwZXIuYXV0b3BsYXkuc3RvcCgpO1xuICAgIH0pO1xuICAgICQoXCIjZmVhdHVyZS1zd2lwZXJcIikub24oXCJtb3VzZWxlYXZlXCIsIGZ1bmN0aW9uICgpIHtcbiAgICAgICQodGhpcykucmVtb3ZlQ2xhc3MoXCJhY3RpdmVcIik7XG4gICAgfSk7XG4gIH1cblxuICAvLyBDb3VudC11cCBudW1iZXJzIChDb21tb24pXG4gIGNvbnN0IGhhbmRsZUNvdW50VXBTY3JvbGwgPSAoKSA9PiB7XG4gICAgY29uc3QgbXlFbGVtZW50ID0gJChcIi5jb3VudC1ib3hcIilbMF07XG4gICAgaWYgKHN0ZXBWYWwgJiYgaXNFbGVtZW50RnVsbHlJblZpZXdwb3J0KG15RWxlbWVudCkpIHtcbiAgICAgICQoXCIuY291bnQtbnVtXCIpLmNvdW50VG8oKTtcbiAgICAgIHN0ZXBWYWwgPSBmYWxzZTtcbiAgICB9XG4gIH07XG4gICQod2luZG93KS5vbihcInNjcm9sbFwiLCB0aHJvdHRsZShoYW5kbGVDb3VudFVwU2Nyb2xsLCAyMDApKTtcbn0pO1xuIl0sIm5hbWVzIjpbIiQiLCJpc0Rlc2t0b3AiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiaXNUcmFuc2l0aW9uaW5nIiwibGFzdFNsaWRlSW5kZXgiLCJzdGVwVmFsIiwiZGlnaXRhbFRleHRTd2lwZXIiLCJkaWdpdGFsSW1nU3dpcGVyIiwidGhyb3R0bGUiLCJmdW5jIiwibGltaXQiLCJpblRocm90dGxlIiwiYXJncyIsImFwcGx5Iiwic2V0VGltZW91dCIsIndhaXQiLCJtcyIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0T3BhY2l0eSIsInNlbGVjdG9yIiwib3BhY2l0eSIsImR1cmF0aW9uIiwiY3NzIiwidHJhbnNpdGlvbiIsImlzRWxlbWVudEZ1bGx5SW5WaWV3cG9ydCIsImVsIiwicmVjdCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsInRvcCIsImxlZnQiLCJib3R0b20iLCJpbm5lckhlaWdodCIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiY2xpZW50SGVpZ2h0IiwicmlnaHQiLCJjbGllbnRXaWR0aCIsImhhbmRsZVJlc2l6ZSIsIm5ld0lzRGVza3RvcCIsImxvY2F0aW9uIiwicmVsb2FkIiwib24iLCJTd2lwZXIiLCJzbGlkZXNQZXJWaWV3Iiwic3BlZWQiLCJhbGxvd1RvdWNoTW92ZSIsImRpcmVjdGlvbiIsImVmZmVjdCIsImZhZGVFZmZlY3QiLCJjcm9zc0ZhZGUiLCJjdXN0b21TbGlkZVRyYW5zaXRpb24iLCJ0YXJnZXRJbmRleCIsInNsaWRlVG8iLCJ0ZXh0UHJvbWlzZSIsImltZ1Byb21pc2UiLCJhbGwiLCJoYW5kbGVEaWdpdGFsU2Nyb2xsIiwiYXBhcnRTZWN0aW9uIiwidG90YWxTbGlkZXMiLCJzbGlkZXMiLCJsZW5ndGgiLCJvZmZzZXQiLCJwZXJjIiwiTWF0aCIsInJvdW5kIiwiYWJzIiwiaGVpZ2h0Iiwic2xpZGVJbmRleCIsIm1pbiIsImZsb29yIiwiYWN0aXZlSW5kZXgiLCJtb3VzZWVudGVyIiwiYWRkQ2xhc3MiLCJzaWJsaW5ncyIsInJlbW92ZUNsYXNzIiwiaW5kZXgiLCJyZW1vdmUiLCJzcGFjZUJldHdlZW4iLCJsb29wIiwiYXV0b3BsYXkiLCJkZWxheSIsImRpc2FibGVPbkludGVyYWN0aW9uIiwiYnJlYWtwb2ludHMiLCJwYWdpbmF0aW9uIiwiY2xpY2thYmxlIiwibmF2aWdhdGlvbiIsIm5leHRFbCIsInByZXZFbCIsImNlbnRlcmVkU2xpZGVzIiwibG9vcGVkU2xpZGVzIiwiZmVhdHVyZVRleHRTd2lwZXIiLCJzd2lwZXJPcHRpb25zIiwic2xpZGVDaGFuZ2UiLCJyZWFsSW5kZXgiLCJPYmplY3QiLCJhc3NpZ24iLCJ3YXRjaFNsaWRlc1Byb2dyZXNzIiwiY3JlYXRpdmVFZmZlY3QiLCJwcmV2Iiwic2hhZG93IiwidHJhbnNsYXRlIiwicm90YXRlIiwic2NhbGUiLCJvcmlnaW4iLCJuZXh0IiwibGltaXRQcm9ncmVzcyIsImZlYXR1cmVTd2lwZXIiLCJzdG9wIiwiaGFuZGxlQ291bnRVcFNjcm9sbCIsIm15RWxlbWVudCIsImNvdW50VG8iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///787\n");

/***/ }),

/***/ 2:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(354);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(314);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `*{margin:0;padding:0;box-sizing:border-box}main{overflow:visible !important;background-color:#fff;color:#000;font-family:\"Messina Sans\",-apple-system,blinkmacsystemfont,\"Segoe UI\",roboto,\"Helvetica Neue\",arial,\"Noto Sans\",sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\"}@media(max-width: 992px){main{overflow:hidden !important}}main h1,main h2,main h3,main h4,main h5,main h6,main p,main div,main span,main ul,main li{margin-bottom:0;font-family:\"Messina Sans\",-apple-system,blinkmacsystemfont,\"Segoe UI\",roboto,\"Helvetica Neue\",arial,\"Noto Sans\",sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\"}main h2{text-align:center;font-weight:700;font-size:3.5rem}@media(max-width: 768px){main h2{font-size:24px}}main .opacity-7{opacity:.7}main .text-purple{color:#7a57ee}main .btn-wrapper{display:flex;justify-content:center;gap:1rem}@media(max-width: 768px){main .btn-wrapper{flex-direction:column;align-items:center;gap:8px}}main .btn-wrapper .btn{margin:0;border-radius:8px;text-transform:capitalize;display:flex;align-items:center;justify-content:center;min-width:158px}@media(min-width: 1280px){main .btn-wrapper .btn.btn-lg{min-width:224px}}@media(max-width: 768px){main .btn-wrapper .btn{width:280px;height:48px}}@media(max-width: 768px){main .btn-wrapper .btn{display:block;vertical-align:baseline}}@keyframes gradientAnimation{0%{background-position:0% 50%}50%{background-position:100% 50%}100%{background-position:0% 50%}}main .btn-white{color:#7a57ee}main .btn-white:hover{color:#7a57ee}main .btn-colorful{background:linear-gradient(94.2deg, #7a57ee 41.38%, #39a0fa 73.42%, #24e3c8 96.58%);background-size:200% 200%;animation:gradientAnimation 3s infinite linear;transition:transform .2s ease-in-out;color:#fff}main .btn-colorful:hover{transform:scale(1.05);color:#fff}main .btn-purple-bg{border:none;color:#fff;background:url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg.png) no-repeat center center/contain;aspect-ratio:232/64;width:232px;transition:transform .3s ease-in-out}@media(max-width: 768px){main .btn-purple-bg{background:url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg-mobile.png) no-repeat center center/contain;aspect-ratio:280/48;margin:0 auto}}main .btn-purple-bg:hover{transform:translateY(-8px);color:#fff}main .btn-purple-bg2{border:none;color:#fff;background:url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg2.png) no-repeat center center/contain;aspect-ratio:280/64;width:280px;transition:transform .3s ease-in-out;display:flex;gap:.5rem;box-shadow:0px 14px 19.8px 0px rgba(120,88,255,.2588235294)}main .btn-purple-bg2:hover{color:#fff}main .swiper-pagination{bottom:-4px !important}main .swiper-pagination .swiper-pagination-bullet{width:8px;height:8px;background:rgba(0,0,0,.14);opacity:1}main .swiper-pagination .swiper-pagination-bullet-active{width:54px;background:linear-gradient(90deg, #ad8fff 0%, #7c65fe 100%);border-radius:8px}main .part-banner{background:url(https://famisafe.wondershare.com/images/images-2025/index/banner.jpg) no-repeat center center/cover}@media(max-width: 768px){main .part-banner{background:url(https://famisafe.wondershare.com/images/images-2025/index/banner-mobile.png) no-repeat center center/cover;text-align:center}}main .part-banner .sub-title{display:flex;gap:12px;align-items:center;justify-content:flex-start}@media(max-width: 768px){main .part-banner .sub-title{justify-content:center}}main .part-banner .sub-title .colorful-tip{background:linear-gradient(96.75deg, #7a57ee 36.5%, #0dc1ed 72.94%, #00d2ab 96.47%);border-radius:24px;padding:4px 12px;font-weight:700;font-size:1.25rem;line-height:100%;color:#fff}main .part-banner h1{font-weight:700;font-size:4.125rem;line-height:100%}@media(max-width: 768px){main .part-banner h1{font-size:32px}}main .part-banner .system-list{display:flex;gap:1rem}main .part-banner .system-list a{text-decoration:none}main .part-banner .system-list a:hover{color:#7a57ee}main .part-honour{background-color:#fbf8ff}@keyframes ToRight{0%{transform:translate3d(0, 0, 0)}100%{transform:translate3d(-50%, 0, 0);-webkit-transform:translate3d(-50%, 0, 0);-moz-transform:translate3d(-50%, 0, 0);-ms-transform:translate3d(-50%, 0, 0);-o-transform:translate3d(-50%, 0, 0)}}main .part-honour .honour-list{display:flex;flex-wrap:nowrap;animation:ToRight 18s linear infinite;width:fit-content}main .part-honour .honour-list .honour-item{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:auto;margin:0 3rem}@media(max-width: 768px){main .part-honour .honour-list .honour-item{margin:0 15px}}main .part-honour .honour-list .honour-item .honour-logo{height:64px;width:64px;display:flex;align-items:center;justify-content:center;overflow:hidden}main .part-honour .honour-list .honour-item .honour-intro{white-space:nowrap;text-align:center}@media(max-width: 768px){main .part-honour .honour-list .honour-item .honour-intro{display:none}}main .part-safeguard .nav{display:flex;justify-content:center;gap:2.875rem;flex-wrap:nowrap}main .part-safeguard .nav .nav-item{text-decoration:none;border-radius:1rem;background-color:#f8f7ff;color:#000;font-size:1.5rem;flex:1 1 33.3333333333%;display:flex;align-items:center;justify-content:center;padding:1.25rem;cursor:pointer}main .part-safeguard .nav .nav-item.active{background-color:#7a57ee;color:#fff;font-weight:700}main .part-safeguard .safeguard-box{position:relative;border-radius:.5rem;overflow:hidden}main .part-safeguard .safeguard-box .feature-card{position:absolute;width:100%;padding:1.5rem;border-radius:8px;background:linear-gradient(111.89deg, #ffffff -0.85%, rgba(255, 255, 255, 0.39) 78.51%);backdrop-filter:blur(16.5px);max-width:250px;transition:transform .3s ease-in-out}main .part-safeguard .safeguard-box .feature-card::after{content:\"\";position:absolute;inset:0;border-radius:8px;padding:5px;background:linear-gradient(135.73deg, rgba(255, 255, 255, 0) 41.9%, rgba(255, 255, 255, 0.45) 118.83%);mask:linear-gradient(#000 0 0) content-box,linear-gradient(#000 0 0);mask-composite:exclude;pointer-events:none}main .part-safeguard .safeguard-box .feature-card:hover{transform:translateY(-8px)}main .part-safeguard .safeguard-box .feature-card .feature-card-icon{width:40%;position:absolute;top:0;left:0;transform:translate(-41%, -25%)}main .part-safeguard .safeguard-box .feature-card .feature-card-title{font-weight:700;color:var(--color-title);text-align:center}main .part-safeguard .safeguard-box .feature-card .feature-card-description{text-align:center;font-size:.875rem}main .part-safeguard .safeguard-box-mobile{border-radius:1rem;overflow:hidden;position:relative}main .part-safeguard .safeguard-box-mobile .title{font-weight:700;font-size:20px;color:#000;text-align:center;position:absolute;top:0;left:0;width:100%;padding:16px}main .part-safeguard .safeguard-box-mobile .feature-list{position:absolute;bottom:0;left:0;margin:16px;display:flex;flex-direction:column;justify-content:center;align-items:center;border-radius:8px;width:calc(100% - 32px);bottom:0;left:0;background:rgba(255,255,255,.7);backdrop-filter:blur(9px);padding:12px}main .part-safeguard .safeguard-box-mobile .feature-list .feature-list-wrapper{display:flex;flex-direction:column;gap:8px;width:auto}main .part-safeguard .safeguard-box-mobile .feature-list .feature-list-wrapper .feature-item{display:flex;justify-content:flex-start;align-items:center;gap:8px;font-size:16px}main .part-safeguard .left-btn,main .part-safeguard .right-btn{position:absolute;z-index:2;top:34px;width:32px;height:32px;display:flex;align-items:center;justify-content:center;color:#000;border-radius:50%;color:#fff;border:1.5px solid #fff}main .part-safeguard .left-btn:hover,main .part-safeguard .right-btn:hover{background-color:#fff;color:#9281ff}main .part-safeguard .left-btn{left:16px}main .part-safeguard .right-btn{right:16px}main .part-digital{--digital-item-height: 87vh;height:calc(var(--digital-item-height)*5);position:relative;background:rgba(244,243,255,.64)}@media(max-width: 1280px){main .part-digital{height:auto}}main .part-digital .digital-wrapper{height:var(--digital-item-height);position:sticky;top:72px;display:flex;flex-direction:column}@media(min-width: 2000px){main .part-digital .digital-wrapper{top:10vh}}@media(max-width: 1280px){main .part-digital .digital-wrapper{height:auto;position:relative;top:unset;text-align:center}}@media(max-width: 576px){main .part-digital .digital-wrapper .container{padding-right:0}}main .part-digital h2{margin-bottom:6.875rem;margin-top:4rem}@media(max-width: 1280px){main .part-digital h2{margin-top:0;margin-bottom:3rem}}main .part-digital .family-title{font-weight:700;font-size:18px;color:#000;display:inline-block;padding:4px 8px;background-color:#d7cdfa;border-radius:999px;margin-bottom:8px}main .part-digital .digital-box{display:flex;background:linear-gradient(237.63deg, rgba(248, 247, 255, 0) 35.13%, rgba(248, 247, 255, 0.8) 88.35%),linear-gradient(211.11deg, #e2deff 14.16%, #e3dfff 42.27%);border-radius:1rem;overflow-y:visible;justify-content:space-between;min-height:418px;position:relative}@media(max-width: 1280px){main .part-digital .digital-box{background:unset;overflow:hidden;display:block;border-radius:16px}}@media(max-width: 576px){main .part-digital .digital-box{margin:0 auto;border-radius:0}}main .part-digital .digital-box .text-content{width:45.84%;padding:1.5rem 1.5rem 1.5rem 6.25rem;overflow:hidden}@media(max-width: 1600px){main .part-digital .digital-box .text-content{padding:1.5rem 1.5rem 1.5rem 2.25rem}}@media(max-width: 1280px){main .part-digital .digital-box .text-content{width:100%;padding:unset;border-radius:0 0 16px 16px;overflow:hidden}main .part-digital .digital-box .text-content .mobile-img-wrapper{border-radius:16px;overflow:hidden}}main .part-digital .digital-box .text-content .digital-item{height:100%;display:flex;flex-direction:column;justify-content:center}@media(max-width: 1280px){main .part-digital .digital-box .text-content .digital-item{position:absolute;bottom:0;left:0;width:100%;height:auto;z-index:3;background:rgba(151,136,255,.7882352941);backdrop-filter:blur(10px);padding:10px 22px;display:block;text-align:center;border-radius:0 0 16px 16px;overflow:hidden}}main .part-digital .digital-box .text-content .digital-item .digital-item-title{font-weight:700;font-size:2.5rem;color:#7a57ee;margin-bottom:2rem}@media(max-width: 1280px){main .part-digital .digital-box .text-content .digital-item .digital-item-title{font-size:24px;line-height:32px;font-weight:700;color:#fff;margin-bottom:0}}main .part-digital .digital-box .text-content .digital-item .digital-item-description{font-size:1.125rem;line-height:2rem;padding-bottom:1rem}@media(max-width: 1280px){main .part-digital .digital-box .text-content .digital-item .digital-item-description{font-size:16px;line-height:20px;padding-bottom:0;color:#fff}}main .part-digital .digital-box .img-content{width:54.16%;overflow:visible}main .part-digital .digital-box .img-content #digital-img-swiper{overflow:visible}main .part-digital .digital-box .img-content .img-item-wrapper{position:relative;height:100%}main .part-digital .digital-box .img-content .img-item-wrapper img{position:absolute;bottom:0;left:0;width:100%}main .part-feature{background:rgba(244,243,255,.64)}main .part-feature .feature-item{position:relative;border-radius:1rem;overflow:hidden}@media(any-hover: hover){main .part-feature .feature-item:hover{box-shadow:0px 7px 14px 0px #d4cff7}main .part-feature .feature-item:hover .feature-detail-card{opacity:1}}main .part-feature .feature-item-title{font-weight:700;font-size:1.25rem;padding:1.5rem;position:absolute;left:0;top:0}@media(min-width: 576px){main .part-feature .feature-item .feature-detail-card{position:absolute;left:0;bottom:0;width:100%;height:100%;z-index:5;text-decoration:none;opacity:0;transition:opacity .3s ease-in-out;background:linear-gradient(180deg, rgba(122, 87, 238, 0.6) 0%, #7a57ee 64.68%);backdrop-filter:blur(20px);display:flex;flex-direction:column;justify-content:center;align-items:center;gap:1.5rem;padding:1.5rem 3rem;margin:0 auto;color:#fff;text-align:center}main .part-feature .feature-item .feature-detail-card-title{font-weight:700;font-size:1.5rem}main .part-feature .feature-item .feature-detail-card-description{font-size:1.125rem}main .part-feature .feature-item .feature-detail-card-arrow{flex-shrink:0;color:#fff;width:2.5rem;height:2.5rem;display:flex;align-items:center;justify-content:center;border-radius:50%;border:2px solid rgba(255,255,255,.6)}main .part-feature .feature-item .feature-detail-card-arrow:hover{border-color:#fff;background-color:#fff;color:#7a57ee}}@media(max-width: 576px){main .part-feature #feature-swiper{margin-left:-15px;margin-right:-15px;position:relative}main .part-feature #feature-swiper::before{content:\"\";position:absolute;left:0;bottom:0;z-index:2;width:11.2%;height:100%;background:linear-gradient(90deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);pointer-events:none}main .part-feature #feature-swiper::after{content:\"\";position:absolute;right:0;bottom:0;z-index:2;width:11.2%;height:100%;background:linear-gradient(270deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);pointer-events:none}main .part-feature #feature-swiper .swiper-slide{overflow:visible}main .part-feature #feature-swiper .swiper-slide.swiper-slide-active{z-index:5}main .part-feature #feature-swiper .swiper-slide.swiper-slide-active .feature-item{overflow:visible}main .part-feature #feature-swiper .swiper-slide.swiper-slide-active .feature-item img{border:4.28px solid #fff;box-shadow:0px 10.69px 19.36px 0px rgba(40,3,236,.2392156863);border-radius:1rem}main .part-feature #feature-swiper .feature-detail-card{display:none}main .part-feature .feature-item-mobile{height:100%}main .part-feature #feature-text-mobile-swiper .feature-detail-card{height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;text-decoration:none}main .part-feature .mobile-feature-text{margin-top:2.125rem;border-radius:1rem;background:linear-gradient(360deg, #7d58f9 15.62%, #a78dff 100.05%);box-shadow:0px 6px 12.9px 0px #e3dffe;padding:1rem;text-align:center;color:#fff;position:relative}main .part-feature .mobile-feature-text::before{content:\"\";position:absolute;top:0;left:50%;transform:translate(-50%, -100%);width:18px;height:6px;background:url(https://famisafe.wondershare.com/images/images-2025/index/purple-top.png) no-repeat center center/contain}main .part-feature .mobile-feature-text .feature-detail-card-title{font-weight:700;font-size:18px;line-height:32px;margin-bottom:8px;color:#fff}main .part-feature .mobile-feature-text .feature-detail-card-description{font-size:16px;line-height:20px;color:#fff;margin-bottom:8px}main .part-feature .mobile-feature-text .feature-detail-card-arrow{width:32px;height:32px;color:#7a57ee;background-color:#fff;border-radius:50%;display:flex;justify-content:center;align-items:center;margin:0 auto}}main .part-geonection .geonection-wrapper{background:url(https://famisafe.wondershare.com/images/images-2025/index/geonection-part-bg.png) no-repeat center center/cover;position:relative;border-radius:1.5rem;overflow:hidden;display:flex;align-items:center;justify-content:space-between;padding:5.375rem 0 3.625rem}@media(max-width: 992px){main .part-geonection .geonection-wrapper{flex-direction:column;padding:24px 0;justify-content:center;align-items:center;text-align:center}}main .part-geonection .geonection-wrapper .geonection-logo{position:absolute;top:1.75rem;left:1.5rem;z-index:1}@media(max-width: 992px){main .part-geonection .geonection-wrapper .geonection-logo{position:relative;width:150px;top:unset;left:unset}}main .part-geonection .geonection-wrapper .geonection-content{flex:0 0 40.4%;position:relative;z-index:1;padding-left:5rem}@media(max-width: 1280px){main .part-geonection .geonection-wrapper .geonection-content{padding-left:3rem}}@media(max-width: 992px){main .part-geonection .geonection-wrapper .geonection-content{flex:auto;padding-left:unset;padding:0 16px}}main .part-geonection .geonection-wrapper .geonection-content .geonection-item-title{font-size:2.5rem;font-weight:700}@media(max-width: 992px){main .part-geonection .geonection-wrapper .geonection-content .geonection-item-title{font-size:24px;line-height:36px;margin-top:16px;margin-bottom:12px}}main .part-geonection .geonection-wrapper .geonection-content .btn-green{background:linear-gradient(90.52deg, #92d45d 36.28%, #6ad018 75.06%);box-shadow:0px 4px 4px 0px rgba(255,255,255,.2509803922) inset,0px -3px 4px 0px rgba(255,255,255,.2509803922) inset;color:#000;font-size:18px}main .part-geonection .geonection-wrapper .geonection-img{flex:0 0 59.6%;position:relative;z-index:1}@media(max-width: 992px){main .part-geonection .geonection-wrapper .geonection-img{flex:auto;margin-top:36px}}@media(max-width: 768px){main .part-customer .mobile-container{max-width:540px;margin:0 auto}}main .part-customer .img-container{position:relative;line-height:0;overflow:hidden;height:100%}main .part-customer .img-container img{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);width:100%;height:100%;object-fit:cover}@media(max-width: 1280px){main .part-customer .assetsSwiper .box-style .img-container img{all:unset;max-width:100%}}main .part-customer .assetsSwiper .box-style .assets-title{position:absolute;width:100%;text-align:center;font-size:1.25rem;font-weight:700;color:#fff;margin-bottom:22px;padding:0 16px;bottom:0;left:0;z-index:2}main .part-customer .assetsSwiper .swiper-slide.active .box-style .assets-title{display:none}@media(max-width: 1280px){main .part-customer .assetsSwiper .box-style .assets-title{display:none}}@media(min-width: 1280px){main .part-customer .assetsSwiper-box{position:relative}main .part-customer .assetsSwiper .swiper-wrapper{aspect-ratio:1920/456;gap:14px;justify-content:space-between}main .part-customer .assetsSwiper .swiper-slide{width:12.2%;display:block;overflow:hidden;border-radius:1rem;position:relative;transition:.8s cubic-bezier(0.05, 0.61, 0.41, 0.95)}main .part-customer .assetsSwiper .swiper-slide.active{width:48.13%;opacity:1}main .part-customer .assetsSwiper .swiper-slide .box-style{height:100%;position:relative;border-radius:1rem;overflow:hidden}@keyframes fadeIn{from{visibility:hidden}to{visibility:visible}}main .part-customer .assetsSwiper .swiper-slide .box-style .customer-info-box{visibility:hidden}main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box{animation:fadeIn .01s ease-in-out;animation-delay:.8s;animation-fill-mode:forwards;position:absolute;margin:24px;margin-bottom:2.25rem;bottom:0;left:0;width:calc(100% - 48px);background:rgba(0,0,0,.33);backdrop-filter:blur(10px);border-radius:1rem;padding:.5rem 1rem;display:flex;align-items:center;justify-content:space-between;gap:3rem}main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box .customer-info{font-size:1rem;font-weight:400;color:#fff;line-height:100%}main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box2{position:absolute;left:60px;top:24px}main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 .customer-info .customer-name{font-weight:700;font-size:1.25rem;line-height:100%;color:#fff}main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 .customer-info .customer-desc{font-weight:400;font-size:1rem;line-height:100%;color:#fff}main .part-customer .assetsSwiper .swiper-slide:not(.active) .box-style::after{content:\"\";width:100%;height:100%;background:rgba(0,0,0,.5);position:absolute;top:0;left:0;z-index:1}}@media(max-width: 1280px){main .part-customer .assetsSwiper{overflow:initial;padding-top:24px}}@media(max-width: 1280px)and (max-width: 768px){main .part-customer .assetsSwiper{padding-top:12px}}@media(max-width: 1280px){main .part-customer .assetsSwiper-box{padding:0 15px}main .part-customer .assetsSwiper .swiper-slide{opacity:.5}main .part-customer .assetsSwiper .swiper-slide-active{opacity:1}main .part-customer .assetsSwiper .rounded-16{border-radius:8px}main .part-customer .customer-info-box{position:absolute;margin:16px;bottom:0;left:0;width:calc(100% - 32px);background-color:rgba(0,0,0,.6);backdrop-filter:blur(4px);border-radius:8px;padding:16px 24px;display:flex;align-items:center;justify-content:space-between;color:#fff}main .part-customer .customer-info-box .btn-wrapper{display:none}main .part-customer .customer-info-box2{position:absolute;top:16px;right:16px;display:flex;align-items:stretch;justify-content:center;color:#fff}main .part-customer .customer-info-box2 .customer-name{font-size:18px;font-weight:700}main .part-customer .customer-info-box2 .customer-desc{font-size:16px;font-weight:400}}main .part-saying .saying-box{display:flex;justify-content:space-between;background:linear-gradient(180deg, rgba(254, 232, 226, 0) 23.47%, rgba(254, 232, 226, 0.86) 100%),linear-gradient(0deg, #7a57ee, #7a57ee);border-radius:1.5rem;overflow:hidden;color:#fff;gap:8.75rem;max-height:732px}@media(max-width: 1600px){main .part-saying .saying-box{gap:3.75rem}}@media(max-width: 1280px){main .part-saying .saying-box{flex-direction:column;gap:unset;max-height:unset}}main .part-saying .saying-box .left-box{flex:0 1 48%;padding:2rem 4rem;padding-right:0;display:flex;flex-direction:column;justify-content:center}@media(max-width: 1280px){main .part-saying .saying-box .left-box{flex:initial;padding:30px 24px;text-align:center;align-items:center}}main .part-saying .saying-box .left-box .saying-title{text-align:left;font-size:3.5rem;line-height:1.2}@media(max-width: 1280px){main .part-saying .saying-box .left-box .saying-title{text-align:center;font-size:24px;font-weight:700}}main .part-saying .saying-box .left-box .white-divider{width:100%;height:1px;background-color:#fff;opacity:.7;margin:3rem 0;max-width:395px}@media(max-width: 1280px){main .part-saying .saying-box .left-box .white-divider{display:none}}main .part-saying .saying-box .left-box .count-list{display:flex;align-items:center;justify-content:space-between;color:#fff;flex-wrap:wrap;gap:3.5rem 4rem;max-width:344px}@media(max-width: 1280px){main .part-saying .saying-box .left-box .count-list{max-width:unset;gap:24px 50px;justify-content:space-around;margin-top:24px}}main .part-saying .saying-box .left-box .count-list .count-box{flex:0 0 auto;display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative;gap:4px;color:#fff;max-width:170px}@media(max-width: 576px){main .part-saying .saying-box .left-box .count-list .count-box{flex:0 0 40%}}main .part-saying .saying-box .left-box .count-list .count-box .count{font-size:2.5rem;font-weight:700}main .part-saying .saying-box .left-box .count-list .count-box .count-desc{font-size:1.25rem}main .part-saying .saying-box .right-box{flex:0 0 52%;padding-right:3rem;display:flex;gap:1.5rem;position:relative}@media(max-width: 1280px){main .part-saying .saying-box .right-box{flex:initial;padding-right:0;gap:0;width:fit-content}}main .part-saying .saying-box .right-box::after{position:absolute;z-index:1;bottom:0;right:0;content:\"\";width:100%;height:22.4%;background:linear-gradient(0deg, #ebd3e4 0%, rgba(211, 185, 230, 0) 93.33%);pointer-events:none}@media(max-width: 1280px){main .part-saying .saying-box .right-box::after{display:none}}main .part-saying .saying-box .right-box::before{position:absolute;z-index:1;top:0;right:0;content:\"\";width:100%;height:22.4%;background:linear-gradient(180deg, #7a57ee 0%, rgba(122, 87, 238, 0) 93.33%);pointer-events:none}@media(max-width: 1280px){main .part-saying .saying-box .right-box::before{display:none}}main .part-saying .saying-box .right-box .user-card-list{display:flex;flex-direction:column;height:fit-content;flex-wrap:nowrap}@media(max-width: 1280px){main .part-saying .saying-box .right-box .user-card-list{flex-direction:row}}@keyframes marquee1{0%{transform:translateY(0)}100%{transform:translateY(-50%)}}@keyframes marquee2{0%{transform:translateY(-50%)}100%{transform:translateY(0)}}@keyframes marquee1-mobile{0%{transform:translateX(0)}100%{transform:translateX(-50%)}}main .part-saying .saying-box .right-box .user-card-list.list1{animation:marquee1 40s linear infinite}@media(max-width: 1280px){main .part-saying .saying-box .right-box .user-card-list.list1{animation:marquee1-mobile 40s linear infinite}}main .part-saying .saying-box .right-box .user-card-list.list2{animation:marquee2 40s linear infinite}@media(max-width: 1280px){main .part-saying .saying-box .right-box .user-card-list.list2{animation:marquee2-mobile 40s linear infinite}}main .part-saying .saying-box .right-box .user-card-list.list1-mobile{animation:marquee1-mobile 60s linear infinite;margin:30px 0}main .part-saying .saying-box .right-box .user-card-list .user-card{display:flex;flex-direction:column;gap:.5rem;margin:1rem 0;background-color:#fff;border-radius:1rem;overflow:hidden;padding:1.5rem;color:#000}@media(max-width: 1280px){main .part-saying .saying-box .right-box .user-card-list .user-card{margin:0 5px;width:276px;padding:20px}}main .part-saying .saying-box .right-box .user-card-list .user-card .useer-info{display:flex;gap:.875rem;align-items:center}main .part-saying .saying-box .right-box .user-card-list .user-card .useer-info .user-avatar{width:3.625rem}main .part-saying .saying-box .right-box .user-card-list .user-card .useer-info .user-name{font-size:1.125rem;font-weight:700;margin-bottom:6px;line-height:100%}main .part-saying .saying-box .right-box .user-card-list .user-card .user-desc{font-size:.875rem;color:#000}@media(min-width: 1280px){main .part-grow .swiper-wrapper{flex-wrap:wrap;gap:8px;display:flex}main .part-grow .swiper-wrapper .swiper-slide.what-new-slide{flex:0 1 calc(61.2% - 8px)}main .part-grow .swiper-wrapper .swiper-slide.tutorials-slide{flex:0 1 calc(38.8% - 8px)}main .part-grow .swiper-wrapper .swiper-slide.social-media-slide{flex:0 1 calc(44.1% - 8px)}main .part-grow .swiper-wrapper .swiper-slide.blogs-slide{flex:0 1 calc(55.9% - 8px)}}main .part-grow .social-media-mobile-list{display:flex;gap:16px;justify-content:center;align-items:center;margin-top:24px}main .part-grow .grow-box{border-radius:1rem;display:flex;align-items:center;position:relative;overflow:hidden;height:100%;justify-content:space-between}@media(max-width: 1280px){main .part-grow .grow-box{flex-direction:column}main .part-grow .grow-box .content-wrapper{flex:auto !important;padding:16px !important;text-align:center}main .part-grow .grow-box .img-wrapper{width:100% !important;box-sizing:border-box}}@keyframes jump{0%{transform:translateX(5px)}50%{transform:translateX(-3px)}100%{transform:translateX(5px)}}@media(any-hover: hover){main .part-grow .grow-box:hover .img-wrapper{transform:scale(1.1)}main .part-grow .grow-box:hover .black-arrow{animation:jump 1s infinite}}main .part-grow .grow-box .card-link{position:absolute;left:0;top:0;width:100%;height:100%;z-index:2}main .part-grow .grow-box .title{font-size:2rem;font-weight:700}main .part-grow .what-new{background:linear-gradient(300.68deg, #b7a3ff 21.43%, #f3ddff 55.08%, #ffe9dd 83.49%)}main .part-grow .what-new .content-wrapper{flex:0 1 46.52%;padding:1.5rem 0;padding-left:3.75rem}main .part-grow .what-new .img-wrapper{width:53.48%;transition:transform .3s linear;align-self:flex-end}main .part-grow .tutorials{background:linear-gradient(180deg, #d2d2ff 0%, #ece7fe 100%)}main .part-grow .tutorials .content-wrapper{flex:0 1 51.4%;padding:1rem;padding-left:2.25rem}main .part-grow .tutorials .img-wrapper{width:48.6%;transition:transform .3s linear;align-self:flex-end}main .part-grow .social-media .social-media-list{display:flex;gap:1.75rem;flex-wrap:nowrap;position:absolute;bottom:40%;left:17%;width:65%}main .part-grow .social-media .social-media-list .social-media-item{flex:1;transition:transform .3s linear}main .part-grow .social-media .social-media-list .social-media-item:hover{transform:scale(1.4)}main .part-grow .blogs{background:linear-gradient(313.48deg, #d3faf9 42.66%, #cbcbff 95.75%)}main .part-grow .blogs .content-wrapper{flex:0 1 41.03%;padding:1rem;padding-left:3.75rem}main .part-grow .blogs .img-wrapper{width:58.97%;transition:transform .3s linear;align-self:flex-end}main .part-protection .protection-box{border-radius:1.5rem;background:linear-gradient(299.31deg, rgba(215, 199, 255, 0.67) 13.16%, rgba(178, 165, 255, 0.67) 67.6%);padding-left:80px;display:flex;align-items:center;justify-content:space-between;gap:1.875rem;position:relative}@media(max-width: 1280px){main .part-protection .protection-box{flex-direction:column;padding-left:0;gap:0px;justify-content:space-between;background:#e2dfff;padding:30px}}main .part-protection .protection-box .trusted-box,main .part-protection .protection-box .privacy-box{flex:1;border-radius:8px;overflow:hidden;background:linear-gradient(141.94deg, #ffffff 21.96%, rgba(255, 255, 255, 0.7) 93.72%);padding:1.5rem 2rem;text-align:center}@media(max-width: 1280px){main .part-protection .protection-box .trusted-box,main .part-protection .protection-box .privacy-box{background:unset;padding-bottom:0}}main .part-protection .protection-box .trusted-box .title,main .part-protection .protection-box .privacy-box .title{font-size:1.5rem;font-weight:700;position:relative;text-align:center;display:inline-flex;margin-bottom:1.25rem}@media(max-width: 1280px){main .part-protection .protection-box .trusted-box .title,main .part-protection .protection-box .privacy-box .title{color:#7a57ee;margin-bottom:16px}}main .part-protection .protection-box .trusted-box .title::before,main .part-protection .protection-box .privacy-box .title::before{content:\"\";position:absolute;bottom:0;left:0;transform:translate(-110%, -5%);aspect-ratio:47/42;background:url(https://famisafe.wondershare.com/images/images-2025/index/feather-left.svg) no-repeat center center/contain;width:2.625rem}main .part-protection .protection-box .trusted-box .title::after,main .part-protection .protection-box .privacy-box .title::after{content:\"\";position:absolute;bottom:0;right:0;transform:translate(110%, -5%);aspect-ratio:47/42;background:url(https://famisafe.wondershare.com/images/images-2025/index/feather-right.svg) no-repeat center center/contain;width:2.625rem}main .part-protection .protection-box .purple-divider{width:100%;height:1px;background-color:#bbadfe;margin:24px 0}main .part-protection .protection-box .purple-lock{width:33.3%}@media(max-width: 1280px){main .part-protection .protection-box .purple-lock{width:122px;position:absolute;top:0;left:50%;transform:translate(-50%, -50%)}}main .part-footer .footer-box{border-radius:1rem;overflow:hidden;background-color:#e2dfff;background:url(https://famisafe.wondershare.com/images/images-2025/index/footer-bg.jpg) no-repeat center center/cover;margin:0 2.625rem;padding:6rem 3rem;text-align:center}@media(max-width: 768px){main .part-footer .footer-box{margin:0 15px;padding:30px 15px}}#modal-youtube .btn-action{background-color:#8c5bde;border-color:#8c5bde}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/index.scss\"],\"names\":[],\"mappings\":\"AAAA,EACE,QAAA,CACA,SAAA,CACA,qBAAA,CAGF,KACE,2BAAA,CACA,qBAAA,CACA,UAAA,CACA,qMAAA,CAEA,yBANF,KAOI,0BAAA,CAAA,CAGF,0FAWE,eAAA,CACA,qMAAA,CAIF,QACE,iBAAA,CACA,eAAA,CACA,gBAAA,CACA,yBAJF,QAKI,cAAA,CAAA,CAIJ,gBACE,UAAA,CAGF,kBACE,aAAA,CAGF,kBACE,YAAA,CACA,sBAAA,CACA,QAAA,CAGF,yBACE,kBACE,qBAAA,CACA,kBAAA,CACA,OAAA,CAAA,CAIJ,uBACE,QAAA,CACA,iBAAA,CACA,yBAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,eAAA,CACA,0BACE,8BACE,eAAA,CAAA,CAGJ,yBAbF,uBAcI,WAAA,CACA,WAAA,CAAA,CAIJ,yBACE,uBACE,aAAA,CACA,uBAAA,CAAA,CAGJ,6BACE,GACE,0BAAA,CAEF,IACE,4BAAA,CAEF,KACE,0BAAA,CAAA,CAGJ,gBACE,aAAA,CACA,sBACE,aAAA,CAIJ,mBACE,mFAAA,CACA,yBAAA,CACA,8CAAA,CACA,oCAAA,CACA,UAAA,CACA,yBACE,qBAAA,CACA,UAAA,CAIJ,oBACE,WAAA,CACA,UAAA,CACA,2HAAA,CACA,mBAAA,CACA,WAAA,CACA,oCAAA,CACA,yBAPF,oBAQI,kIAAA,CACA,mBAAA,CAEA,aAAA,CAAA,CAGF,0BACE,0BAAA,CACA,UAAA,CAIJ,qBACE,WAAA,CACA,UAAA,CACA,4HAAA,CACA,mBAAA,CACA,WAAA,CACA,oCAAA,CACA,YAAA,CACA,SAAA,CACA,2DAAA,CACA,2BACE,UAAA,CAIJ,wBACE,sBAAA,CAGF,kDACE,SAAA,CACA,UAAA,CACA,0BAAA,CACA,SAAA,CAGF,yDACE,UAAA,CACA,2DAAA,CACA,iBAAA,CAEF,kBACE,kHAAA,CACA,yBAFF,kBAGI,yHAAA,CACA,iBAAA,CAAA,CAEF,6BACE,YAAA,CACA,QAAA,CACA,kBAAA,CACA,0BAAA,CACA,yBALF,6BAMI,sBAAA,CAAA,CAEF,2CACE,mFAAA,CACA,kBAAA,CACA,gBAAA,CACA,eAAA,CACA,iBAAA,CACA,gBAAA,CACA,UAAA,CAGJ,qBACE,eAAA,CACA,kBAAA,CACA,gBAAA,CACA,yBAJF,qBAKI,cAAA,CAAA,CAGJ,+BACE,YAAA,CACA,QAAA,CAEA,iCACE,oBAAA,CACA,uCACE,aAAA,CAKR,kBACE,wBAAA,CACA,mBACE,GACE,8BAAA,CAEF,KACE,iCAAA,CACA,yCAAA,CACA,sCAAA,CACA,qCAAA,CACA,oCAAA,CAAA,CAGJ,+BACE,YAAA,CACA,gBAAA,CACA,qCAAA,CACA,iBAAA,CACA,4CACE,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,sBAAA,CACA,SAAA,CACA,aAAA,CACA,yBAPF,4CAQI,aAAA,CAAA,CAEF,yDACE,WAAA,CACA,UAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,eAAA,CAEF,0DACE,kBAAA,CACA,iBAAA,CACA,yBAHF,0DAII,YAAA,CAAA,CAOR,0BACE,YAAA,CACA,sBAAA,CACA,YAAA,CACA,gBAAA,CACA,oCACE,oBAAA,CACA,kBAAA,CACA,wBAAA,CACA,UAAA,CACA,gBAAA,CACA,uBAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,eAAA,CACA,cAAA,CACA,2CACE,wBAAA,CACA,UAAA,CACA,eAAA,CAIN,oCACE,iBAAA,CACA,mBAAA,CACA,eAAA,CACA,kDACE,iBAAA,CACA,UAAA,CACA,cAAA,CACA,iBAAA,CACA,uFAAA,CACA,4BAAA,CACA,eAAA,CACA,oCAAA,CACA,yDACE,UAAA,CACA,iBAAA,CACA,OAAA,CACA,iBAAA,CACA,WAAA,CACA,sGAAA,CACA,oEAAA,CACA,sBAAA,CACA,mBAAA,CAEF,wDACE,0BAAA,CAGF,qEACE,SAAA,CACA,iBAAA,CACA,KAAA,CACA,MAAA,CACA,+BAAA,CAEF,sEACE,eAAA,CACA,wBAAA,CACA,iBAAA,CAEF,4EACE,iBAAA,CACA,iBAAA,CAIN,2CACE,kBAAA,CACA,eAAA,CACA,iBAAA,CAEA,kDACE,eAAA,CACA,cAAA,CACA,UAAA,CACA,iBAAA,CACA,iBAAA,CACA,KAAA,CACA,MAAA,CACA,UAAA,CACA,YAAA,CAEF,yDACE,iBAAA,CACA,QAAA,CACA,MAAA,CACA,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,kBAAA,CAEA,iBAAA,CACA,uBAAA,CACA,QAAA,CACA,MAAA,CACA,+BAAA,CACA,yBAAA,CACA,YAAA,CACA,+EACE,YAAA,CACA,qBAAA,CACA,OAAA,CACA,UAAA,CACA,6FACE,YAAA,CACA,0BAAA,CACA,kBAAA,CACA,OAAA,CACA,cAAA,CAKR,+DAEE,iBAAA,CACA,SAAA,CACA,QAAA,CAEA,UAAA,CACA,WAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,UAAA,CACA,iBAAA,CACA,UAAA,CAEA,uBAAA,CACA,2EACE,qBAAA,CAEA,aAAA,CAGJ,+BACE,SAAA,CAEF,gCACE,UAAA,CAIJ,mBACE,2BAAA,CACA,yCAAA,CACA,iBAAA,CACA,gCAAA,CACA,0BALF,mBAMI,WAAA,CAAA,CAGF,oCACE,iCAAA,CACA,eAAA,CACA,QAAA,CACA,YAAA,CACA,qBAAA,CACA,0BANF,oCAOI,QAAA,CAAA,CAEF,0BATF,oCAUI,WAAA,CACA,iBAAA,CACA,SAAA,CACA,iBAAA,CAAA,CAEF,yBACE,+CACE,eAAA,CAAA,CAIN,sBACE,sBAAA,CACA,eAAA,CACA,0BAHF,sBAII,YAAA,CACA,kBAAA,CAAA,CAGJ,iCACE,eAAA,CACA,cAAA,CACA,UAAA,CACA,oBAAA,CACA,eAAA,CACA,wBAAA,CACA,mBAAA,CACA,iBAAA,CAEF,gCACE,YAAA,CACA,gKAAA,CAEA,kBAAA,CACA,kBAAA,CACA,6BAAA,CACA,gBAAA,CACA,iBAAA,CACA,0BATF,gCAUI,gBAAA,CACA,eAAA,CACA,aAAA,CACA,kBAAA,CAAA,CAEF,yBAfF,gCAgBI,aAAA,CACA,eAAA,CAAA,CAEF,8CACE,YAAA,CACA,oCAAA,CACA,eAAA,CACA,0BAJF,8CAKI,oCAAA,CAAA,CAEF,0BAPF,8CAQI,UAAA,CACA,aAAA,CACA,2BAAA,CACA,eAAA,CACA,kEACE,kBAAA,CACA,eAAA,CAAA,CAIJ,4DACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,0BALF,4DAMI,iBAAA,CACA,QAAA,CACA,MAAA,CACA,UAAA,CACA,WAAA,CACA,SAAA,CACA,wCAAA,CACA,0BAAA,CACA,iBAAA,CACA,aAAA,CACA,iBAAA,CACA,2BAAA,CACA,eAAA,CAAA,CAEF,gFACE,eAAA,CACA,gBAAA,CACA,aAAA,CACA,kBAAA,CACA,0BALF,gFAMI,cAAA,CACA,gBAAA,CACA,eAAA,CACA,UAAA,CACA,eAAA,CAAA,CAGJ,sFACE,kBAAA,CACA,gBAAA,CACA,mBAAA,CACA,0BAJF,sFAKI,cAAA,CACA,gBAAA,CACA,gBAAA,CACA,UAAA,CAAA,CAKR,6CACE,YAAA,CACA,gBAAA,CAEA,iEACE,gBAAA,CAEF,+DACE,iBAAA,CACA,WAAA,CACA,mEACE,iBAAA,CACA,QAAA,CACA,MAAA,CACA,UAAA,CAOV,mBACE,gCAAA,CACA,iCACE,iBAAA,CACA,kBAAA,CACA,eAAA,CACA,yBACE,uCACE,mCAAA,CACA,4DACE,SAAA,CAAA,CAKN,uCACE,eAAA,CACA,iBAAA,CACA,cAAA,CACA,iBAAA,CACA,MAAA,CACA,KAAA,CAEF,yBACE,sDACE,iBAAA,CACA,MAAA,CACA,QAAA,CACA,UAAA,CACA,WAAA,CACA,SAAA,CACA,oBAAA,CACA,SAAA,CACA,kCAAA,CACA,8EAAA,CAEA,0BAAA,CAEA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,kBAAA,CACA,UAAA,CACA,mBAAA,CAEA,aAAA,CACA,UAAA,CACA,iBAAA,CAGF,4DACE,eAAA,CACA,gBAAA,CAEF,kEACE,kBAAA,CAEF,4DACE,aAAA,CACA,UAAA,CACA,YAAA,CACA,aAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,iBAAA,CACA,qCAAA,CACA,kEACE,iBAAA,CACA,qBAAA,CACA,aAAA,CAAA,CAMR,yBACE,mCACE,iBAAA,CACA,kBAAA,CACA,iBAAA,CACA,2CACE,UAAA,CACA,iBAAA,CACA,MAAA,CACA,QAAA,CACA,SAAA,CACA,WAAA,CACA,WAAA,CACA,+FAAA,CACA,mBAAA,CAEF,0CACE,UAAA,CACA,iBAAA,CACA,OAAA,CACA,QAAA,CACA,SAAA,CACA,WAAA,CACA,WAAA,CACA,gGAAA,CACA,mBAAA,CAGJ,iDACE,gBAAA,CAGF,qEACE,SAAA,CACA,mFACE,gBAAA,CACA,uFACE,wBAAA,CACA,6DAAA,CACA,kBAAA,CAKN,wDACE,YAAA,CAEF,wCACE,WAAA,CAEF,oEACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,kBAAA,CACA,oBAAA,CAGF,wCACE,mBAAA,CACA,kBAAA,CACA,mEAAA,CACA,qCAAA,CACA,YAAA,CACA,iBAAA,CACA,UAAA,CACA,iBAAA,CACA,gDACE,UAAA,CACA,iBAAA,CACA,KAAA,CACA,QAAA,CACA,gCAAA,CACA,UAAA,CACA,UAAA,CACA,wHAAA,CAEF,mEACE,eAAA,CACA,cAAA,CACA,gBAAA,CACA,iBAAA,CACA,UAAA,CAEF,yEACE,cAAA,CACA,gBAAA,CACA,UAAA,CACA,iBAAA,CAEF,mEACE,UAAA,CACA,WAAA,CACA,aAAA,CACA,qBAAA,CACA,iBAAA,CACA,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,aAAA,CAAA,CAON,0CACE,8HAAA,CACA,iBAAA,CACA,oBAAA,CACA,eAAA,CACA,YAAA,CACA,kBAAA,CACA,6BAAA,CACA,2BAAA,CACA,yBATF,0CAUI,qBAAA,CACA,cAAA,CACA,sBAAA,CACA,kBAAA,CACA,iBAAA,CAAA,CAEF,2DACE,iBAAA,CACA,WAAA,CACA,WAAA,CACA,SAAA,CACA,yBALF,2DAMI,iBAAA,CACA,WAAA,CACA,SAAA,CACA,UAAA,CAAA,CAGJ,8DACE,cAAA,CACA,iBAAA,CACA,SAAA,CACA,iBAAA,CACA,0BALF,8DAMI,iBAAA,CAAA,CAEF,yBARF,8DASI,SAAA,CACA,kBAAA,CACA,cAAA,CAAA,CAEF,qFACE,gBAAA,CACA,eAAA,CACA,yBAHF,qFAII,cAAA,CACA,gBAAA,CACA,eAAA,CACA,kBAAA,CAAA,CAGJ,yEACE,oEAAA,CACA,mHAAA,CACA,UAAA,CACA,cAAA,CAGJ,0DACE,cAAA,CACA,iBAAA,CACA,SAAA,CACA,yBAJF,0DAKI,SAAA,CACA,eAAA,CAAA,CAON,yBACE,sCACE,eAAA,CACA,aAAA,CAAA,CAIJ,mCACE,iBAAA,CACA,aAAA,CACA,eAAA,CACA,WAAA,CAGF,uCACE,iBAAA,CACA,OAAA,CACA,QAAA,CACA,+BAAA,CACA,UAAA,CACA,WAAA,CACA,gBAAA,CAGF,0BACE,gEACE,SAAA,CACA,cAAA,CAAA,CAIJ,2DACE,iBAAA,CACA,UAAA,CACA,iBAAA,CACA,iBAAA,CACA,eAAA,CACA,UAAA,CACA,kBAAA,CACA,cAAA,CACA,QAAA,CACA,MAAA,CACA,SAAA,CAGF,gFACE,YAAA,CAGF,0BACE,2DACE,YAAA,CAAA,CAIJ,0BACE,sCACE,iBAAA,CAGF,kDACE,qBAAA,CACA,QAAA,CACA,6BAAA,CAGF,gDACE,WAAA,CACA,aAAA,CACA,eAAA,CACA,kBAAA,CACA,iBAAA,CACA,mDAAA,CAGF,uDACE,YAAA,CACA,SAAA,CAGF,2DACE,WAAA,CACA,iBAAA,CACA,kBAAA,CACA,eAAA,CAGF,kBACE,KACE,iBAAA,CAGF,GACE,kBAAA,CAAA,CAIJ,8EACE,iBAAA,CAGF,qFACE,iCAAA,CACA,mBAAA,CACA,4BAAA,CACA,iBAAA,CACA,WAAA,CACA,qBAAA,CACA,QAAA,CACA,MAAA,CACA,uBAAA,CACA,0BAAA,CACA,0BAAA,CACA,kBAAA,CACA,kBAAA,CACA,YAAA,CACA,kBAAA,CACA,6BAAA,CACA,QAAA,CAGF,oGACE,cAAA,CACA,eAAA,CACA,UAAA,CACA,gBAAA,CAMF,sFACE,iBAAA,CACA,SAAA,CACA,QAAA,CAGF,oHACE,eAAA,CACA,iBAAA,CACA,gBAAA,CACA,UAAA,CAGF,oHACE,eAAA,CACA,cAAA,CACA,gBAAA,CACA,UAAA,CAGF,+EACE,UAAA,CACA,UAAA,CACA,WAAA,CACA,yBAAA,CACA,iBAAA,CACA,KAAA,CACA,MAAA,CACA,SAAA,CAAA,CAIJ,0BACE,kCACE,gBAAA,CACA,gBAAA,CAAA,CAIJ,gDACE,kCACE,gBAAA,CAAA,CAIJ,0BACE,sCACE,cAAA,CAGF,gDACE,UAAA,CAGF,uDACE,SAAA,CAGF,8CACE,iBAAA,CAEF,uCACE,iBAAA,CACA,WAAA,CACA,QAAA,CACA,MAAA,CACA,uBAAA,CACA,+BAAA,CACA,yBAAA,CACA,iBAAA,CACA,iBAAA,CACA,YAAA,CACA,kBAAA,CACA,6BAAA,CACA,UAAA,CACA,oDACE,YAAA,CAGJ,wCACE,iBAAA,CACA,QAAA,CACA,UAAA,CACA,YAAA,CACA,mBAAA,CACA,sBAAA,CACA,UAAA,CACA,uDACE,cAAA,CACA,eAAA,CAEF,uDACE,cAAA,CACA,eAAA,CAAA,CAON,8BACE,YAAA,CACA,6BAAA,CACA,yIAAA,CACA,oBAAA,CACA,eAAA,CACA,UAAA,CACA,WAAA,CACA,gBAAA,CACA,0BATF,8BAUI,WAAA,CAAA,CAEF,0BAZF,8BAaI,qBAAA,CACA,SAAA,CACA,gBAAA,CAAA,CAEF,wCACE,YAAA,CACA,iBAAA,CACA,eAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,0BAPF,wCAQI,YAAA,CACA,iBAAA,CACA,iBAAA,CACA,kBAAA,CAAA,CAEF,sDACE,eAAA,CACA,gBAAA,CACA,eAAA,CACA,0BAJF,sDAKI,iBAAA,CACA,cAAA,CACA,eAAA,CAAA,CAGJ,uDACE,UAAA,CACA,UAAA,CACA,qBAAA,CACA,UAAA,CACA,aAAA,CACA,eAAA,CACA,0BAPF,uDAQI,YAAA,CAAA,CAGJ,oDACE,YAAA,CACA,kBAAA,CACA,6BAAA,CACA,UAAA,CACA,cAAA,CACA,eAAA,CACA,eAAA,CACA,0BARF,oDASI,eAAA,CACA,aAAA,CACA,4BAAA,CACA,eAAA,CAAA,CAEF,+DACE,aAAA,CACA,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,sBAAA,CACA,iBAAA,CACA,OAAA,CACA,UAAA,CACA,eAAA,CACA,yBAVF,+DAWI,YAAA,CAAA,CAEF,sEACE,gBAAA,CACA,eAAA,CAEF,2EACE,iBAAA,CAKR,yCACE,YAAA,CACA,kBAAA,CACA,YAAA,CACA,UAAA,CACA,iBAAA,CACA,0BANF,yCAOI,YAAA,CACA,eAAA,CACA,KAAA,CACA,iBAAA,CAAA,CAEF,gDACE,iBAAA,CACA,SAAA,CACA,QAAA,CACA,OAAA,CACA,UAAA,CACA,UAAA,CACA,YAAA,CACA,2EAAA,CACA,mBAAA,CACA,0BAVF,gDAWI,YAAA,CAAA,CAGJ,iDACE,iBAAA,CACA,SAAA,CACA,KAAA,CACA,OAAA,CACA,UAAA,CACA,UAAA,CACA,YAAA,CACA,4EAAA,CACA,mBAAA,CACA,0BAVF,iDAWI,YAAA,CAAA,CAIJ,yDACE,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,gBAAA,CACA,0BALF,yDAMI,kBAAA,CAAA,CAGF,oBACE,GACE,uBAAA,CAEF,KACE,0BAAA,CAAA,CAGJ,oBACE,GACE,0BAAA,CAEF,KACE,uBAAA,CAAA,CAGJ,2BACE,GACE,uBAAA,CAEF,KACE,0BAAA,CAAA,CAIJ,+DACE,sCAAA,CACA,0BAFF,+DAGI,6CAAA,CAAA,CAGJ,+DACE,sCAAA,CACA,0BAFF,+DAGI,6CAAA,CAAA,CAGJ,sEACE,6CAAA,CACA,aAAA,CAEF,oEACE,YAAA,CACA,qBAAA,CACA,SAAA,CACA,aAAA,CACA,qBAAA,CACA,kBAAA,CACA,eAAA,CACA,cAAA,CACA,UAAA,CAEA,0BAXF,oEAYI,YAAA,CACA,WAAA,CACA,YAAA,CAAA,CAGF,gFACE,YAAA,CACA,WAAA,CACA,kBAAA,CACA,6FACE,cAAA,CAEF,2FACE,kBAAA,CACA,eAAA,CACA,iBAAA,CACA,gBAAA,CAGJ,+EACE,iBAAA,CACA,UAAA,CAQV,0BACE,gCACE,cAAA,CACA,OAAA,CACA,YAAA,CAEE,6DACE,0BAAA,CAEF,8DACE,0BAAA,CAEF,iEACE,0BAAA,CAEF,0DACE,0BAAA,CAAA,CAKR,0CACE,YAAA,CACA,QAAA,CACA,sBAAA,CACA,kBAAA,CACA,eAAA,CAEF,0BACE,kBAAA,CACA,YAAA,CACA,kBAAA,CACA,iBAAA,CACA,eAAA,CACA,WAAA,CACA,6BAAA,CACA,0BARF,0BASI,qBAAA,CACA,2CACE,oBAAA,CACA,uBAAA,CACA,iBAAA,CAEF,uCACE,qBAAA,CACA,qBAAA,CAAA,CAIJ,gBACE,GACE,yBAAA,CAEF,IACE,0BAAA,CAEF,KACE,yBAAA,CAAA,CAGJ,yBAEI,6CACE,oBAAA,CAEF,6CACE,0BAAA,CAAA,CAIN,qCACE,iBAAA,CACA,MAAA,CACA,KAAA,CACA,UAAA,CACA,WAAA,CACA,SAAA,CAEF,iCACE,cAAA,CACA,eAAA,CAGJ,0BACE,qFAAA,CACA,2CACE,eAAA,CACA,gBAAA,CACA,oBAAA,CAEF,uCACE,YAAA,CACA,+BAAA,CACA,mBAAA,CAGJ,2BACE,4DAAA,CAEA,4CACE,cAAA,CACA,YAAA,CACA,oBAAA,CAEF,wCACE,WAAA,CACA,+BAAA,CACA,mBAAA,CAIF,iDACE,YAAA,CACA,WAAA,CACA,gBAAA,CACA,iBAAA,CACA,UAAA,CACA,QAAA,CACA,SAAA,CACA,oEACE,MAAA,CACA,+BAAA,CACA,0EACE,oBAAA,CAKR,uBACE,qEAAA,CAEA,wCACE,eAAA,CACA,YAAA,CACA,oBAAA,CAEF,oCACE,YAAA,CACA,+BAAA,CACA,mBAAA,CAKJ,sCACE,oBAAA,CACA,wGAAA,CACA,iBAAA,CACA,YAAA,CACA,kBAAA,CACA,6BAAA,CACA,YAAA,CACA,iBAAA,CACA,0BATF,sCAUI,qBAAA,CACA,cAAA,CACA,OAAA,CACA,6BAAA,CACA,kBAAA,CACA,YAAA,CAAA,CAEF,sGAEE,MAAA,CACA,iBAAA,CACA,eAAA,CACA,sFAAA,CACA,mBAAA,CACA,iBAAA,CAEA,0BATF,sGAUI,gBAAA,CACA,gBAAA,CAAA,CAEF,oHACE,gBAAA,CACA,eAAA,CACA,iBAAA,CACA,iBAAA,CACA,mBAAA,CACA,qBAAA,CACA,0BAPF,oHAQI,aAAA,CACA,kBAAA,CAAA,CAEF,oIACE,UAAA,CACA,iBAAA,CACA,QAAA,CACA,MAAA,CACA,+BAAA,CACA,kBAAA,CACA,0HAAA,CACA,cAAA,CAEF,kIACE,UAAA,CACA,iBAAA,CACA,QAAA,CACA,OAAA,CACA,8BAAA,CACA,kBAAA,CACA,2HAAA,CACA,cAAA,CAIN,sDACE,UAAA,CACA,UAAA,CACA,wBAAA,CACA,aAAA,CAEF,mDACE,WAAA,CACA,0BAFF,mDAGI,WAAA,CACA,iBAAA,CACA,KAAA,CACA,QAAA,CACA,+BAAA,CAAA,CAMN,8BACE,kBAAA,CACA,eAAA,CACA,wBAAA,CACA,qHAAA,CACA,iBAAA,CACA,iBAAA,CACA,iBAAA,CACA,yBARF,8BASI,aAAA,CACA,iBAAA,CAAA,CAOR,2BACE,wBAAA,CACA,oBAAA\",\"sourcesContent\":[\"* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nmain {\\n  overflow: visible !important;\\n  background-color: #fff;\\n  color: #000;\\n  font-family: \\\"Messina Sans\\\", -apple-system, blinkmacsystemfont, \\\"Segoe UI\\\", roboto, \\\"Helvetica Neue\\\", arial, \\\"Noto Sans\\\", sans-serif, \\\"Apple Color Emoji\\\",\\n    \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n  @media (max-width: 992px) {\\n    overflow: hidden !important;\\n  }\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6,\\n  p,\\n  div,\\n  span,\\n  ul,\\n  li {\\n    margin-bottom: 0;\\n    font-family: \\\"Messina Sans\\\", -apple-system, blinkmacsystemfont, \\\"Segoe UI\\\", roboto, \\\"Helvetica Neue\\\", arial, \\\"Noto Sans\\\", sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n  }\\n\\n  h2 {\\n    text-align: center;\\n    font-weight: 700;\\n    font-size: 3.5rem;\\n    @media (max-width: 768px) {\\n      font-size: 24px;\\n    }\\n  }\\n\\n  .opacity-7 {\\n    opacity: 0.7;\\n  }\\n\\n  .text-purple {\\n    color: #7a57ee;\\n  }\\n\\n  .btn-wrapper {\\n    display: flex;\\n    justify-content: center;\\n    gap: 1rem;\\n  }\\n\\n  @media (max-width: 768px) {\\n    .btn-wrapper {\\n      flex-direction: column;\\n      align-items: center;\\n      gap: 8px;\\n    }\\n  }\\n\\n  .btn-wrapper .btn {\\n    margin: 0;\\n    border-radius: 8px;\\n    text-transform: capitalize;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    min-width: 158px;\\n    @media (min-width: 1280px) {\\n      &.btn-lg {\\n        min-width: 224px;\\n      }\\n    }\\n    @media (max-width: 768px) {\\n      width: 280px;\\n      height: 48px;\\n    }\\n  }\\n\\n  @media (max-width: 768px) {\\n    .btn-wrapper .btn {\\n      display: block;\\n      vertical-align: baseline;\\n    }\\n  }\\n  @keyframes gradientAnimation {\\n    0% {\\n      background-position: 0% 50%;\\n    }\\n    50% {\\n      background-position: 100% 50%;\\n    }\\n    100% {\\n      background-position: 0% 50%;\\n    }\\n  }\\n  .btn-white {\\n    color: #7a57ee;\\n    &:hover {\\n      color: #7a57ee;\\n    }\\n  }\\n\\n  .btn-colorful {\\n    background: linear-gradient(94.2deg, #7a57ee 41.38%, #39a0fa 73.42%, #24e3c8 96.58%);\\n    background-size: 200% 200%;\\n    animation: gradientAnimation 3s infinite linear;\\n    transition: transform 0.2s ease-in-out;\\n    color: #fff;\\n    &:hover {\\n      transform: scale(1.05);\\n      color: #fff;\\n    }\\n  }\\n\\n  .btn-purple-bg {\\n    border: none;\\n    color: #fff;\\n    background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg.png) no-repeat center center / contain;\\n    aspect-ratio: 232 / 64;\\n    width: 232px;\\n    transition: transform 0.3s ease-in-out;\\n    @media (max-width: 768px) {\\n      background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg-mobile.png) no-repeat center center / contain;\\n      aspect-ratio: 280 / 48;\\n\\n      margin: 0 auto;\\n    }\\n\\n    &:hover {\\n      transform: translateY(-8px);\\n      color: #fff;\\n    }\\n  }\\n\\n  .btn-purple-bg2 {\\n    border: none;\\n    color: #fff;\\n    background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg2.png) no-repeat center center / contain;\\n    aspect-ratio: 280 / 64;\\n    width: 280px;\\n    transition: transform 0.3s ease-in-out;\\n    display: flex;\\n    gap: 0.5rem;\\n    box-shadow: 0px 14px 19.8px 0px #7858ff42;\\n    &:hover {\\n      color: #fff;\\n    }\\n  }\\n\\n  .swiper-pagination {\\n    bottom: -4px !important;\\n  }\\n\\n  .swiper-pagination .swiper-pagination-bullet {\\n    width: 8px;\\n    height: 8px;\\n    background: rgba(0, 0, 0, 0.14);\\n    opacity: 1;\\n  }\\n\\n  .swiper-pagination .swiper-pagination-bullet-active {\\n    width: 54px;\\n    background: linear-gradient(90deg, #ad8fff 0%, #7c65fe 100%);\\n    border-radius: 8px;\\n  }\\n  .part-banner {\\n    background: url(https://famisafe.wondershare.com/images/images-2025/index/banner.jpg) no-repeat center center / cover;\\n    @media (max-width: 768px) {\\n      background: url(https://famisafe.wondershare.com/images/images-2025/index/banner-mobile.png) no-repeat center center / cover;\\n      text-align: center;\\n    }\\n    .sub-title {\\n      display: flex;\\n      gap: 12px;\\n      align-items: center;\\n      justify-content: flex-start;\\n      @media (max-width: 768px) {\\n        justify-content: center;\\n      }\\n      .colorful-tip {\\n        background: linear-gradient(96.75deg, #7a57ee 36.5%, #0dc1ed 72.94%, #00d2ab 96.47%);\\n        border-radius: 24px;\\n        padding: 4px 12px;\\n        font-weight: 700;\\n        font-size: 1.25rem;\\n        line-height: 100%;\\n        color: #fff;\\n      }\\n    }\\n    h1 {\\n      font-weight: 700;\\n      font-size: 4.125rem;\\n      line-height: 100%;\\n      @media (max-width: 768px) {\\n        font-size: 32px;\\n      }\\n    }\\n    .system-list {\\n      display: flex;\\n      gap: 1rem;\\n\\n      a {\\n        text-decoration: none;\\n        &:hover {\\n          color: #7a57ee;\\n        }\\n      }\\n    }\\n  }\\n  .part-honour {\\n    background-color: #fbf8ff;\\n    @keyframes ToRight {\\n      0% {\\n        transform: translate3d(0, 0, 0);\\n      }\\n      100% {\\n        transform: translate3d(-50%, 0, 0);\\n        -webkit-transform: translate3d(-50%, 0, 0);\\n        -moz-transform: translate3d(-50%, 0, 0);\\n        -ms-transform: translate3d(-50%, 0, 0);\\n        -o-transform: translate3d(-50%, 0, 0);\\n      }\\n    }\\n    .honour-list {\\n      display: flex;\\n      flex-wrap: nowrap;\\n      animation: ToRight 18s linear infinite;\\n      width: fit-content;\\n      .honour-item {\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        flex: auto;\\n        margin: 0 3rem;\\n        @media (max-width: 768px) {\\n          margin: 0 15px;\\n        }\\n        .honour-logo {\\n          height: 64px;\\n          width: 64px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          overflow: hidden;\\n        }\\n        .honour-intro {\\n          white-space: nowrap;\\n          text-align: center;\\n          @media (max-width: 768px) {\\n            display: none;\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .part-safeguard {\\n    .nav {\\n      display: flex;\\n      justify-content: center;\\n      gap: 2.875rem;\\n      flex-wrap: nowrap;\\n      .nav-item {\\n        text-decoration: none;\\n        border-radius: 1rem;\\n        background-color: #f8f7ff;\\n        color: #000;\\n        font-size: 1.5rem;\\n        flex: 1 1 calc(100% / 3);\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 1.25rem;\\n        cursor: pointer;\\n        &.active {\\n          background-color: #7a57ee;\\n          color: #fff;\\n          font-weight: 700;\\n        }\\n      }\\n    }\\n    .safeguard-box {\\n      position: relative;\\n      border-radius: 0.5rem;\\n      overflow: hidden;\\n      .feature-card {\\n        position: absolute;\\n        width: 100%;\\n        padding: 1.5rem;\\n        border-radius: 8px;\\n        background: linear-gradient(111.89deg, #ffffff -0.85%, rgba(255, 255, 255, 0.39) 78.51%);\\n        backdrop-filter: blur(16.5px);\\n        max-width: 250px;\\n        transition: transform 0.3s ease-in-out;\\n        &::after {\\n          content: \\\"\\\";\\n          position: absolute;\\n          inset: 0;\\n          border-radius: 8px;\\n          padding: 5px;\\n          background: linear-gradient(135.73deg, rgba(255, 255, 255, 0) 41.9%, rgba(255, 255, 255, 0.45) 118.83%);\\n          mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);\\n          mask-composite: exclude;\\n          pointer-events: none;\\n        }\\n        &:hover {\\n          transform: translateY(-8px);\\n        }\\n\\n        .feature-card-icon {\\n          width: 40%;\\n          position: absolute;\\n          top: 0;\\n          left: 0;\\n          transform: translate(-41%, -25%);\\n        }\\n        .feature-card-title {\\n          font-weight: 700;\\n          color: var(--color-title);\\n          text-align: center;\\n        }\\n        .feature-card-description {\\n          text-align: center;\\n          font-size: 0.875rem;\\n        }\\n      }\\n    }\\n    .safeguard-box-mobile {\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      position: relative;\\n\\n      .title {\\n        font-weight: 700;\\n        font-size: 20px;\\n        color: #000;\\n        text-align: center;\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        padding: 16px;\\n      }\\n      .feature-list {\\n        position: absolute;\\n        bottom: 0;\\n        left: 0;\\n        margin: 16px;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        align-items: center;\\n\\n        border-radius: 8px;\\n        width: calc(100% - 32px);\\n        bottom: 0;\\n        left: 0;\\n        background: rgba(255, 255, 255, 0.7);\\n        backdrop-filter: blur(9px);\\n        padding: 12px;\\n        .feature-list-wrapper {\\n          display: flex;\\n          flex-direction: column;\\n          gap: 8px;\\n          width: auto;\\n          .feature-item {\\n            display: flex;\\n            justify-content: flex-start;\\n            align-items: center;\\n            gap: 8px;\\n            font-size: 16px;\\n          }\\n        }\\n      }\\n    }\\n    .left-btn,\\n    .right-btn {\\n      position: absolute;\\n      z-index: 2;\\n      top: 34px;\\n\\n      width: 32px;\\n      height: 32px;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      color: #000;\\n      border-radius: 50%;\\n      color: #ffff;\\n\\n      border: 1.5px solid rgba(255, 255, 255, 1);\\n      &:hover {\\n        background-color: #fff;\\n\\n        color: #9281ff;\\n      }\\n    }\\n    .left-btn {\\n      left: 16px;\\n    }\\n    .right-btn {\\n      right: 16px;\\n    }\\n  }\\n\\n  .part-digital {\\n    --digital-item-height: 87vh;\\n    height: calc(var(--digital-item-height) * 5);\\n    position: relative;\\n    background: rgba(244, 243, 255, 0.64);\\n    @media (max-width: 1280px) {\\n      height: auto;\\n    }\\n\\n    .digital-wrapper {\\n      height: var(--digital-item-height);\\n      position: sticky;\\n      top: 72px;\\n      display: flex;\\n      flex-direction: column;\\n      @media (min-width: 2000px) {\\n        top: 10vh;\\n      }\\n      @media (max-width: 1280px) {\\n        height: auto;\\n        position: relative;\\n        top: unset;\\n        text-align: center;\\n      }\\n      @media (max-width: 576px) {\\n        .container {\\n          padding-right: 0;\\n        }\\n      }\\n    }\\n    h2 {\\n      margin-bottom: 6.875rem;\\n      margin-top: 4rem;\\n      @media (max-width: 1280px) {\\n        margin-top: 0;\\n        margin-bottom: 3rem;\\n      }\\n    }\\n    .family-title {\\n      font-weight: 700;\\n      font-size: 18px;\\n      color: #000;\\n      display: inline-block;\\n      padding: 4px 8px;\\n      background-color: #d7cdfa;\\n      border-radius: 999px;\\n      margin-bottom: 8px;\\n    }\\n    .digital-box {\\n      display: flex;\\n      background: linear-gradient(237.63deg, rgba(248, 247, 255, 0) 35.13%, rgba(248, 247, 255, 0.8) 88.35%),\\n        linear-gradient(211.11deg, #e2deff 14.16%, #e3dfff 42.27%);\\n      border-radius: 1rem;\\n      overflow-y: visible;\\n      justify-content: space-between;\\n      min-height: 418px;\\n      position: relative;\\n      @media (max-width: 1280px) {\\n        background: unset;\\n        overflow: hidden;\\n        display: block;\\n        border-radius: 16px;\\n      }\\n      @media (max-width: 576px) {\\n        margin: 0 auto;\\n        border-radius: 0;\\n      }\\n      .text-content {\\n        width: 45.84%;\\n        padding: 1.5rem 1.5rem 1.5rem 6.25rem;\\n        overflow: hidden;\\n        @media (max-width: 1600px) {\\n          padding: 1.5rem 1.5rem 1.5rem 2.25rem;\\n        }\\n        @media (max-width: 1280px) {\\n          width: 100%;\\n          padding: unset;\\n          border-radius: 0 0 16px 16px;\\n          overflow: hidden;\\n          .mobile-img-wrapper {\\n            border-radius: 16px;\\n            overflow: hidden;\\n          }\\n        }\\n\\n        .digital-item {\\n          height: 100%;\\n          display: flex;\\n          flex-direction: column;\\n          justify-content: center;\\n          @media (max-width: 1280px) {\\n            position: absolute;\\n            bottom: 0;\\n            left: 0;\\n            width: 100%;\\n            height: auto;\\n            z-index: 3;\\n            background: #9788ffc9;\\n            backdrop-filter: blur(10px);\\n            padding: 10px 22px;\\n            display: block;\\n            text-align: center;\\n            border-radius: 0 0 16px 16px;\\n            overflow: hidden;\\n          }\\n          .digital-item-title {\\n            font-weight: 700;\\n            font-size: 2.5rem;\\n            color: #7a57ee;\\n            margin-bottom: 2rem;\\n            @media (max-width: 1280px) {\\n              font-size: 24px;\\n              line-height: 32px;\\n              font-weight: 700;\\n              color: #fff;\\n              margin-bottom: 0;\\n            }\\n          }\\n          .digital-item-description {\\n            font-size: 1.125rem;\\n            line-height: 2rem;\\n            padding-bottom: 1rem;\\n            @media (max-width: 1280px) {\\n              font-size: 16px;\\n              line-height: 20px;\\n              padding-bottom: 0;\\n              color: #fff;\\n            }\\n          }\\n        }\\n      }\\n      .img-content {\\n        width: 54.16%;\\n        overflow: visible;\\n\\n        #digital-img-swiper {\\n          overflow: visible;\\n        }\\n        .img-item-wrapper {\\n          position: relative;\\n          height: 100%;\\n          img {\\n            position: absolute;\\n            bottom: 0;\\n            left: 0;\\n            width: 100%;\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-feature {\\n    background: rgba(244, 243, 255, 0.64);\\n    .feature-item {\\n      position: relative;\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      @media (any-hover: hover) {\\n        &:hover {\\n          box-shadow: 0px 7px 14px 0px rgba(212, 207, 247, 1);\\n          .feature-detail-card {\\n            opacity: 1;\\n          }\\n        }\\n      }\\n\\n      &-title {\\n        font-weight: 700;\\n        font-size: 1.25rem;\\n        padding: 1.5rem;\\n        position: absolute;\\n        left: 0;\\n        top: 0;\\n      }\\n      @media (min-width: 576px) {\\n        .feature-detail-card {\\n          position: absolute;\\n          left: 0;\\n          bottom: 0;\\n          width: 100%;\\n          height: 100%;\\n          z-index: 5;\\n          text-decoration: none;\\n          opacity: 0;\\n          transition: opacity 0.3s ease-in-out;\\n          background: linear-gradient(180deg, rgba(122, 87, 238, 0.6) 0%, #7a57ee 64.68%);\\n\\n          backdrop-filter: blur(20px);\\n\\n          display: flex;\\n          flex-direction: column;\\n          justify-content: center;\\n          align-items: center;\\n          gap: 1.5rem;\\n          padding: 1.5rem 3rem;\\n\\n          margin: 0 auto;\\n          color: #fff;\\n          text-align: center;\\n        }\\n\\n        .feature-detail-card-title {\\n          font-weight: 700;\\n          font-size: 1.5rem;\\n        }\\n        .feature-detail-card-description {\\n          font-size: 1.125rem;\\n        }\\n        .feature-detail-card-arrow {\\n          flex-shrink: 0;\\n          color: #fff;\\n          width: 2.5rem;\\n          height: 2.5rem;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          border-radius: 50%;\\n          border: 2px solid rgba(255, 255, 255, 0.6);\\n          &:hover {\\n            border-color: #fff;\\n            background-color: #fff;\\n            color: #7a57ee;\\n          }\\n        }\\n      }\\n    }\\n\\n    @media (max-width: 576px) {\\n      #feature-swiper {\\n        margin-left: -15px;\\n        margin-right: -15px;\\n        position: relative;\\n        &::before {\\n          content: \\\"\\\";\\n          position: absolute;\\n          left: 0;\\n          bottom: 0;\\n          z-index: 2;\\n          width: 11.2%;\\n          height: 100%;\\n          background: linear-gradient(90deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);\\n          pointer-events: none;\\n        }\\n        &::after {\\n          content: \\\"\\\";\\n          position: absolute;\\n          right: 0;\\n          bottom: 0;\\n          z-index: 2;\\n          width: 11.2%;\\n          height: 100%;\\n          background: linear-gradient(270deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);\\n          pointer-events: none;\\n        }\\n      }\\n      #feature-swiper .swiper-slide {\\n        overflow: visible;\\n      }\\n\\n      #feature-swiper .swiper-slide.swiper-slide-active {\\n        z-index: 5;\\n        .feature-item {\\n          overflow: visible;\\n          img {\\n            border: 4.28px solid #ffffff;\\n            box-shadow: 0px 10.69px 19.36px 0px #2803ec3d;\\n            border-radius: 1rem;\\n          }\\n        }\\n      }\\n\\n      #feature-swiper .feature-detail-card {\\n        display: none;\\n      }\\n      .feature-item-mobile {\\n        height: 100%;\\n      }\\n      #feature-text-mobile-swiper .feature-detail-card {\\n        height: 100%;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        align-items: center;\\n        text-decoration: none;\\n      }\\n\\n      .mobile-feature-text {\\n        margin-top: 2.125rem;\\n        border-radius: 1rem;\\n        background: linear-gradient(360deg, #7d58f9 15.62%, #a78dff 100.05%);\\n        box-shadow: 0px 6px 12.9px 0px #e3dffe;\\n        padding: 1rem;\\n        text-align: center;\\n        color: #fff;\\n        position: relative;\\n        &::before {\\n          content: \\\"\\\";\\n          position: absolute;\\n          top: 0;\\n          left: 50%;\\n          transform: translate(-50%, -100%);\\n          width: 18px;\\n          height: 6px;\\n          background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-top.png) no-repeat center center / contain;\\n        }\\n        .feature-detail-card-title {\\n          font-weight: 700;\\n          font-size: 18px;\\n          line-height: 32px;\\n          margin-bottom: 8px;\\n          color: #fff;\\n        }\\n        .feature-detail-card-description {\\n          font-size: 16px;\\n          line-height: 20px;\\n          color: #fff;\\n          margin-bottom: 8px;\\n        }\\n        .feature-detail-card-arrow {\\n          width: 32px;\\n          height: 32px;\\n          color: #7a57ee;\\n          background-color: #fff;\\n          border-radius: 50%;\\n          display: flex;\\n          justify-content: center;\\n          align-items: center;\\n          margin: 0 auto;\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-geonection {\\n    .geonection-wrapper {\\n      background: url(https://famisafe.wondershare.com/images/images-2025/index/geonection-part-bg.png) no-repeat center center / cover;\\n      position: relative;\\n      border-radius: 1.5rem;\\n      overflow: hidden;\\n      display: flex;\\n      align-items: center;\\n      justify-content: space-between;\\n      padding: 5.375rem 0 3.625rem;\\n      @media (max-width: 992px) {\\n        flex-direction: column;\\n        padding: 24px 0;\\n        justify-content: center;\\n        align-items: center;\\n        text-align: center;\\n      }\\n      .geonection-logo {\\n        position: absolute;\\n        top: 1.75rem;\\n        left: 1.5rem;\\n        z-index: 1;\\n        @media (max-width: 992px) {\\n          position: relative;\\n          width: 150px;\\n          top: unset;\\n          left: unset;\\n        }\\n      }\\n      .geonection-content {\\n        flex: 0 0 40.4%;\\n        position: relative;\\n        z-index: 1;\\n        padding-left: 5rem;\\n        @media (max-width: 1280px) {\\n          padding-left: 3rem;\\n        }\\n        @media (max-width: 992px) {\\n          flex: auto;\\n          padding-left: unset;\\n          padding: 0 16px;\\n        }\\n        .geonection-item-title {\\n          font-size: 2.5rem;\\n          font-weight: 700;\\n          @media (max-width: 992px) {\\n            font-size: 24px;\\n            line-height: 36px;\\n            margin-top: 16px;\\n            margin-bottom: 12px;\\n          }\\n        }\\n        .btn-green {\\n          background: linear-gradient(90.52deg, #92d45d 36.28%, #6ad018 75.06%);\\n          box-shadow: 0px 4px 4px 0px #ffffff40 inset, 0px -3px 4px 0px #ffffff40 inset;\\n          color: #000;\\n          font-size: 18px;\\n        }\\n      }\\n      .geonection-img {\\n        flex: 0 0 59.6%;\\n        position: relative;\\n        z-index: 1;\\n        @media (max-width: 992px) {\\n          flex: auto;\\n          margin-top: 36px;\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-customer {\\n    @media (max-width: 768px) {\\n      .mobile-container {\\n        max-width: 540px;\\n        margin: 0 auto;\\n      }\\n    }\\n\\n    .img-container {\\n      position: relative;\\n      line-height: 0;\\n      overflow: hidden;\\n      height: 100%;\\n    }\\n\\n    .img-container img {\\n      position: absolute;\\n      top: 50%;\\n      left: 50%;\\n      transform: translate(-50%, -50%);\\n      width: 100%;\\n      height: 100%;\\n      object-fit: cover;\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .assetsSwiper .box-style .img-container img {\\n        all: unset;\\n        max-width: 100%;\\n      }\\n    }\\n\\n    .assetsSwiper .box-style .assets-title {\\n      position: absolute;\\n      width: 100%;\\n      text-align: center;\\n      font-size: 1.25rem;\\n      font-weight: 700;\\n      color: #fff;\\n      margin-bottom: 22px;\\n      padding: 0 16px;\\n      bottom: 0;\\n      left: 0;\\n      z-index: 2;\\n    }\\n\\n    .assetsSwiper .swiper-slide.active .box-style .assets-title {\\n      display: none;\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .assetsSwiper .box-style .assets-title {\\n        display: none;\\n      }\\n    }\\n\\n    @media (min-width: 1280px) {\\n      .assetsSwiper-box {\\n        position: relative;\\n      }\\n\\n      .assetsSwiper .swiper-wrapper {\\n        aspect-ratio: 1920 / 456;\\n        gap: 14px;\\n        justify-content: space-between;\\n      }\\n\\n      .assetsSwiper .swiper-slide {\\n        width: 12.2%;\\n        display: block;\\n        overflow: hidden;\\n        border-radius: 1rem;\\n        position: relative;\\n        transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);\\n      }\\n\\n      .assetsSwiper .swiper-slide.active {\\n        width: 48.13%;\\n        opacity: 1;\\n      }\\n\\n      .assetsSwiper .swiper-slide .box-style {\\n        height: 100%;\\n        position: relative;\\n        border-radius: 1rem;\\n        overflow: hidden;\\n      }\\n\\n      @keyframes fadeIn {\\n        from {\\n          visibility: hidden;\\n        }\\n\\n        to {\\n          visibility: visible;\\n        }\\n      }\\n\\n      .assetsSwiper .swiper-slide .box-style .customer-info-box {\\n        visibility: hidden;\\n      }\\n\\n      .assetsSwiper .swiper-slide.active .box-style .customer-info-box {\\n        animation: fadeIn 0.01s ease-in-out;\\n        animation-delay: 0.8s;\\n        animation-fill-mode: forwards;\\n        position: absolute;\\n        margin: 24px;\\n        margin-bottom: 2.25rem;\\n        bottom: 0;\\n        left: 0;\\n        width: calc(100% - 24px * 2);\\n        background: rgba(0, 0, 0, 0.33);\\n        backdrop-filter: blur(10px);\\n        border-radius: 1rem;\\n        padding: 0.5rem 1rem;\\n        display: flex;\\n        align-items: center;\\n        justify-content: space-between;\\n        gap: 3rem;\\n      }\\n\\n      .assetsSwiper .swiper-slide.active .box-style .customer-info-box .customer-info {\\n        font-size: 1rem;\\n        font-weight: 400;\\n        color: #fff;\\n        line-height: 100%;\\n      }\\n\\n      .assetsSwiper .swiper-slide.active .box-style .customer-info-box {\\n      }\\n\\n      .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 {\\n        position: absolute;\\n        left: 60px;\\n        top: 24px;\\n      }\\n\\n      .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 .customer-info .customer-name {\\n        font-weight: 700;\\n        font-size: 1.25rem;\\n        line-height: 100%;\\n        color: #fff;\\n      }\\n\\n      .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 .customer-info .customer-desc {\\n        font-weight: 400;\\n        font-size: 1rem;\\n        line-height: 100%;\\n        color: #fff;\\n      }\\n\\n      .assetsSwiper .swiper-slide:not(.active) .box-style::after {\\n        content: \\\"\\\";\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0, 0, 0, 0.5);\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        z-index: 1;\\n      }\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .assetsSwiper {\\n        overflow: initial;\\n        padding-top: 24px;\\n      }\\n    }\\n\\n    @media (max-width: 1280px) and (max-width: 768px) {\\n      .assetsSwiper {\\n        padding-top: 12px;\\n      }\\n    }\\n\\n    @media (max-width: 1280px) {\\n      .assetsSwiper-box {\\n        padding: 0 15px;\\n      }\\n\\n      .assetsSwiper .swiper-slide {\\n        opacity: 0.5;\\n      }\\n\\n      .assetsSwiper .swiper-slide-active {\\n        opacity: 1;\\n      }\\n\\n      .assetsSwiper .rounded-16 {\\n        border-radius: 8px;\\n      }\\n      .customer-info-box {\\n        position: absolute;\\n        margin: 16px;\\n        bottom: 0;\\n        left: 0;\\n        width: calc(100% - 16px * 2);\\n        background-color: rgba(0, 0, 0, 0.6);\\n        backdrop-filter: blur(4px);\\n        border-radius: 8px;\\n        padding: 16px 24px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: space-between;\\n        color: #fff;\\n        .btn-wrapper {\\n          display: none;\\n        }\\n      }\\n      .customer-info-box2 {\\n        position: absolute;\\n        top: 16px;\\n        right: 16px;\\n        display: flex;\\n        align-items: stretch;\\n        justify-content: center;\\n        color: #fff;\\n        .customer-name {\\n          font-size: 18px;\\n          font-weight: 700;\\n        }\\n        .customer-desc {\\n          font-size: 16px;\\n          font-weight: 400;\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-saying {\\n    .saying-box {\\n      display: flex;\\n      justify-content: space-between;\\n      background: linear-gradient(180deg, rgba(254, 232, 226, 0) 23.47%, rgba(254, 232, 226, 0.86) 100%), linear-gradient(0deg, #7a57ee, #7a57ee);\\n      border-radius: 1.5rem;\\n      overflow: hidden;\\n      color: #fff;\\n      gap: 8.75rem;\\n      max-height: 732px;\\n      @media (max-width: 1600px) {\\n        gap: 3.75rem;\\n      }\\n      @media (max-width: 1280px) {\\n        flex-direction: column;\\n        gap: unset;\\n        max-height: unset;\\n      }\\n      .left-box {\\n        flex: 0 1 48%;\\n        padding: 2rem 4rem;\\n        padding-right: 0;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        @media (max-width: 1280px) {\\n          flex: initial;\\n          padding: 30px 24px;\\n          text-align: center;\\n          align-items: center;\\n        }\\n        .saying-title {\\n          text-align: left;\\n          font-size: 3.5rem;\\n          line-height: 1.2;\\n          @media (max-width: 1280px) {\\n            text-align: center;\\n            font-size: 24px;\\n            font-weight: 700;\\n          }\\n        }\\n        .white-divider {\\n          width: 100%;\\n          height: 1px;\\n          background-color: #fff;\\n          opacity: 0.7;\\n          margin: 3rem 0;\\n          max-width: 395px;\\n          @media (max-width: 1280px) {\\n            display: none;\\n          }\\n        }\\n        .count-list {\\n          display: flex;\\n          align-items: center;\\n          justify-content: space-between;\\n          color: #fff;\\n          flex-wrap: wrap;\\n          gap: 3.5rem 4rem;\\n          max-width: 344px;\\n          @media (max-width: 1280px) {\\n            max-width: unset;\\n            gap: 24px 50px;\\n            justify-content: space-around;\\n            margin-top: 24px;\\n          }\\n          .count-box {\\n            flex: 0 0 auto;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: center;\\n            justify-content: center;\\n            position: relative;\\n            gap: 4px;\\n            color: #fff;\\n            max-width: 170px;\\n            @media (max-width: 576px) {\\n              flex: 0 0 40%;\\n            }\\n            .count {\\n              font-size: 2.5rem;\\n              font-weight: 700;\\n            }\\n            .count-desc {\\n              font-size: 1.25rem;\\n            }\\n          }\\n        }\\n      }\\n      .right-box {\\n        flex: 0 0 52%;\\n        padding-right: 3rem;\\n        display: flex;\\n        gap: 1.5rem;\\n        position: relative;\\n        @media (max-width: 1280px) {\\n          flex: initial;\\n          padding-right: 0;\\n          gap: 0;\\n          width: fit-content;\\n        }\\n        &::after {\\n          position: absolute;\\n          z-index: 1;\\n          bottom: 0;\\n          right: 0;\\n          content: \\\"\\\";\\n          width: 100%;\\n          height: 22.4%;\\n          background: linear-gradient(0deg, #ebd3e4 0%, rgba(211, 185, 230, 0) 93.33%);\\n          pointer-events: none;\\n          @media (max-width: 1280px) {\\n            display: none;\\n          }\\n        }\\n        &::before {\\n          position: absolute;\\n          z-index: 1;\\n          top: 0;\\n          right: 0;\\n          content: \\\"\\\";\\n          width: 100%;\\n          height: 22.4%;\\n          background: linear-gradient(180deg, #7a57ee 0%, rgba(122, 87, 238, 0) 93.33%);\\n          pointer-events: none;\\n          @media (max-width: 1280px) {\\n            display: none;\\n          }\\n        }\\n\\n        .user-card-list {\\n          display: flex;\\n          flex-direction: column;\\n          height: fit-content;\\n          flex-wrap: nowrap;\\n          @media (max-width: 1280px) {\\n            flex-direction: row;\\n          }\\n\\n          @keyframes marquee1 {\\n            0% {\\n              transform: translateY(0);\\n            }\\n            100% {\\n              transform: translateY(-50%);\\n            }\\n          }\\n          @keyframes marquee2 {\\n            0% {\\n              transform: translateY(-50%);\\n            }\\n            100% {\\n              transform: translateY(0);\\n            }\\n          }\\n          @keyframes marquee1-mobile {\\n            0% {\\n              transform: translateX(0);\\n            }\\n            100% {\\n              transform: translateX(-50%);\\n            }\\n          }\\n\\n          &.list1 {\\n            animation: marquee1 40s linear infinite;\\n            @media (max-width: 1280px) {\\n              animation: marquee1-mobile 40s linear infinite;\\n            }\\n          }\\n          &.list2 {\\n            animation: marquee2 40s linear infinite;\\n            @media (max-width: 1280px) {\\n              animation: marquee2-mobile 40s linear infinite;\\n            }\\n          }\\n          &.list1-mobile {\\n            animation: marquee1-mobile 60s linear infinite;\\n            margin: 30px 0;\\n          }\\n          .user-card {\\n            display: flex;\\n            flex-direction: column;\\n            gap: 0.5rem;\\n            margin: 1rem 0;\\n            background-color: #fff;\\n            border-radius: 1rem;\\n            overflow: hidden;\\n            padding: 1.5rem;\\n            color: #000;\\n\\n            @media (max-width: 1280px) {\\n              margin: 0 5px;\\n              width: 276px;\\n              padding: 20px;\\n            }\\n\\n            .useer-info {\\n              display: flex;\\n              gap: 0.875rem;\\n              align-items: center;\\n              .user-avatar {\\n                width: 3.625rem;\\n              }\\n              .user-name {\\n                font-size: 1.125rem;\\n                font-weight: 700;\\n                margin-bottom: 6px;\\n                line-height: 100%;\\n              }\\n            }\\n            .user-desc {\\n              font-size: 0.875rem;\\n              color: #000;\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .part-grow {\\n    @media (min-width: 1280px) {\\n      .swiper-wrapper {\\n        flex-wrap: wrap;\\n        gap: 8px;\\n        display: flex;\\n        .swiper-slide {\\n          &.what-new-slide {\\n            flex: 0 1 calc(61.2% - 8px);\\n          }\\n          &.tutorials-slide {\\n            flex: 0 1 calc(38.8% - 8px);\\n          }\\n          &.social-media-slide {\\n            flex: 0 1 calc(44.1% - 8px);\\n          }\\n          &.blogs-slide {\\n            flex: 0 1 calc(55.9% - 8px);\\n          }\\n        }\\n      }\\n    }\\n    .social-media-mobile-list {\\n      display: flex;\\n      gap: 16px;\\n      justify-content: center;\\n      align-items: center;\\n      margin-top: 24px;\\n    }\\n    .grow-box {\\n      border-radius: 1rem;\\n      display: flex;\\n      align-items: center;\\n      position: relative;\\n      overflow: hidden;\\n      height: 100%;\\n      justify-content: space-between;\\n      @media (max-width: 1280px) {\\n        flex-direction: column;\\n        .content-wrapper {\\n          flex: auto !important;\\n          padding: 16px !important;\\n          text-align: center;\\n        }\\n        .img-wrapper {\\n          width: 100% !important;\\n          box-sizing: border-box;\\n        }\\n      }\\n\\n      @keyframes jump {\\n        0% {\\n          transform: translateX(5px);\\n        }\\n        50% {\\n          transform: translateX(-3px);\\n        }\\n        100% {\\n          transform: translateX(5px);\\n        }\\n      }\\n      @media (any-hover: hover) {\\n        &:hover {\\n          .img-wrapper {\\n            transform: scale(1.1);\\n          }\\n          .black-arrow {\\n            animation: jump 1s infinite;\\n          }\\n        }\\n      }\\n      .card-link {\\n        position: absolute;\\n        left: 0;\\n        top: 0;\\n        width: 100%;\\n        height: 100%;\\n        z-index: 2;\\n      }\\n      .title {\\n        font-size: 2rem;\\n        font-weight: 700;\\n      }\\n    }\\n    .what-new {\\n      background: linear-gradient(300.68deg, #b7a3ff 21.43%, #f3ddff 55.08%, #ffe9dd 83.49%);\\n      .content-wrapper {\\n        flex: 0 1 46.52%;\\n        padding: 1.5rem 0;\\n        padding-left: 3.75rem;\\n      }\\n      .img-wrapper {\\n        width: 53.48%;\\n        transition: transform 0.3s linear;\\n        align-self: flex-end;\\n      }\\n    }\\n    .tutorials {\\n      background: linear-gradient(180deg, #d2d2ff 0%, #ece7fe 100%);\\n\\n      .content-wrapper {\\n        flex: 0 1 51.4%;\\n        padding: 1rem;\\n        padding-left: 2.25rem;\\n      }\\n      .img-wrapper {\\n        width: 48.6%;\\n        transition: transform 0.3s linear;\\n        align-self: flex-end;\\n      }\\n    }\\n    .social-media {\\n      .social-media-list {\\n        display: flex;\\n        gap: 1.75rem;\\n        flex-wrap: nowrap;\\n        position: absolute;\\n        bottom: 40%;\\n        left: 17%;\\n        width: 65%;\\n        .social-media-item {\\n          flex: 1;\\n          transition: transform 0.3s linear;\\n          &:hover {\\n            transform: scale(1.4);\\n          }\\n        }\\n      }\\n    }\\n    .blogs {\\n      background: linear-gradient(313.48deg, #d3faf9 42.66%, #cbcbff 95.75%);\\n\\n      .content-wrapper {\\n        flex: 0 1 41.03%;\\n        padding: 1rem;\\n        padding-left: 3.75rem;\\n      }\\n      .img-wrapper {\\n        width: 58.97%;\\n        transition: transform 0.3s linear;\\n        align-self: flex-end;\\n      }\\n    }\\n  }\\n  .part-protection {\\n    .protection-box {\\n      border-radius: 1.5rem;\\n      background: linear-gradient(299.31deg, rgba(215, 199, 255, 0.67) 13.16%, rgba(178, 165, 255, 0.67) 67.6%);\\n      padding-left: 80px;\\n      display: flex;\\n      align-items: center;\\n      justify-content: space-between;\\n      gap: 1.875rem;\\n      position: relative;\\n      @media (max-width: 1280px) {\\n        flex-direction: column;\\n        padding-left: 0;\\n        gap: 0px;\\n        justify-content: space-between;\\n        background: #e2dfff;\\n        padding: 30px;\\n      }\\n      .trusted-box,\\n      .privacy-box {\\n        flex: 1;\\n        border-radius: 8px;\\n        overflow: hidden;\\n        background: linear-gradient(141.94deg, #ffffff 21.96%, rgba(255, 255, 255, 0.7) 93.72%);\\n        padding: 1.5rem 2rem;\\n        text-align: center;\\n\\n        @media (max-width: 1280px) {\\n          background: unset;\\n          padding-bottom: 0;\\n        }\\n        .title {\\n          font-size: 1.5rem;\\n          font-weight: 700;\\n          position: relative;\\n          text-align: center;\\n          display: inline-flex;\\n          margin-bottom: 1.25rem;\\n          @media (max-width: 1280px) {\\n            color: #7a57ee;\\n            margin-bottom: 16px;\\n          }\\n          &::before {\\n            content: \\\"\\\";\\n            position: absolute;\\n            bottom: 0;\\n            left: 0;\\n            transform: translate(-110%, -5%);\\n            aspect-ratio: 47 / 42;\\n            background: url(https://famisafe.wondershare.com/images/images-2025/index/feather-left.svg) no-repeat center center / contain;\\n            width: 2.625rem;\\n          }\\n          &::after {\\n            content: \\\"\\\";\\n            position: absolute;\\n            bottom: 0;\\n            right: 0;\\n            transform: translate(110%, -5%);\\n            aspect-ratio: 47 / 42;\\n            background: url(https://famisafe.wondershare.com/images/images-2025/index/feather-right.svg) no-repeat center center / contain;\\n            width: 2.625rem;\\n          }\\n        }\\n      }\\n      .purple-divider {\\n        width: 100%;\\n        height: 1px;\\n        background-color: #bbadfe;\\n        margin: 24px 0;\\n      }\\n      .purple-lock {\\n        width: 33.3%;\\n        @media (max-width: 1280px) {\\n          width: 122px;\\n          position: absolute;\\n          top: 0;\\n          left: 50%;\\n          transform: translate(-50%, -50%);\\n        }\\n      }\\n    }\\n  }\\n  .part-footer {\\n    .footer-box {\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      background-color: #e2dfff;\\n      background: url(https://famisafe.wondershare.com/images/images-2025/index/footer-bg.jpg) no-repeat center center / cover;\\n      margin: 0 2.625rem;\\n      padding: 6rem 3rem;\\n      text-align: center;\\n      @media (max-width: 768px) {\\n        margin: 0 15px;\\n        padding: 30px 15px;\\n      }\\n      .footer-logo {\\n      }\\n    }\\n  }\\n}\\n#modal-youtube .btn-action {\\n  background-color: #8c5bde;\\n  border-color: #8c5bde;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2\n");

/***/ }),

/***/ 314:
/***/ ((module) => {

eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///314\n");

/***/ }),

/***/ 354:
/***/ ((module) => {

eval("\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzU0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsY0FBYztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2Nzcy1sb2FkZXIvZGlzdC9ydW50aW1lL3NvdXJjZU1hcHMuanM/YWYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoaXRlbSkge1xuICB2YXIgY29udGVudCA9IGl0ZW1bMV07XG4gIHZhciBjc3NNYXBwaW5nID0gaXRlbVszXTtcbiAgaWYgKCFjc3NNYXBwaW5nKSB7XG4gICAgcmV0dXJuIGNvbnRlbnQ7XG4gIH1cbiAgaWYgKHR5cGVvZiBidG9hID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB2YXIgYmFzZTY0ID0gYnRvYSh1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoY3NzTWFwcGluZykpKSk7XG4gICAgdmFyIGRhdGEgPSBcInNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LFwiLmNvbmNhdChiYXNlNjQpO1xuICAgIHZhciBzb3VyY2VNYXBwaW5nID0gXCIvKiMgXCIuY29uY2F0KGRhdGEsIFwiICovXCIpO1xuICAgIHJldHVybiBbY29udGVudF0uY29uY2F0KFtzb3VyY2VNYXBwaW5nXSkuam9pbihcIlxcblwiKTtcbiAgfVxuICByZXR1cm4gW2NvbnRlbnRdLmpvaW4oXCJcXG5cIik7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///354\n");

/***/ }),

/***/ 72:
/***/ ((module) => {

eval("\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///72\n");

/***/ }),

/***/ 659:
/***/ ((module) => {

eval("\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU5LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanM/YjIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIG1lbW8gPSB7fTtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBnZXRUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0eXBlb2YgbWVtb1t0YXJnZXRdID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIHN0eWxlVGFyZ2V0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcih0YXJnZXQpO1xuXG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIHJldHVybiBoZWFkIG9mIGlmcmFtZSBpbnN0ZWFkIG9mIGlmcmFtZSBpdHNlbGZcbiAgICBpZiAod2luZG93LkhUTUxJRnJhbWVFbGVtZW50ICYmIHN0eWxlVGFyZ2V0IGluc3RhbmNlb2Ygd2luZG93LkhUTUxJRnJhbWVFbGVtZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUaGlzIHdpbGwgdGhyb3cgYW4gZXhjZXB0aW9uIGlmIGFjY2VzcyB0byBpZnJhbWUgaXMgYmxvY2tlZFxuICAgICAgICAvLyBkdWUgdG8gY3Jvc3Mtb3JpZ2luIHJlc3RyaWN0aW9uc1xuICAgICAgICBzdHlsZVRhcmdldCA9IHN0eWxlVGFyZ2V0LmNvbnRlbnREb2N1bWVudC5oZWFkO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgICAgICBzdHlsZVRhcmdldCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG1lbW9bdGFyZ2V0XSA9IHN0eWxlVGFyZ2V0O1xuICB9XG4gIHJldHVybiBtZW1vW3RhcmdldF07XG59XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gaW5zZXJ0QnlTZWxlY3RvcihpbnNlcnQsIHN0eWxlKSB7XG4gIHZhciB0YXJnZXQgPSBnZXRUYXJnZXQoaW5zZXJ0KTtcbiAgaWYgKCF0YXJnZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBmaW5kIGEgc3R5bGUgdGFyZ2V0LiBUaGlzIHByb2JhYmx5IG1lYW5zIHRoYXQgdGhlIHZhbHVlIGZvciB0aGUgJ2luc2VydCcgcGFyYW1ldGVyIGlzIGludmFsaWQuXCIpO1xuICB9XG4gIHRhcmdldC5hcHBlbmRDaGlsZChzdHlsZSk7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydEJ5U2VsZWN0b3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///659\n");

/***/ }),

/***/ 540:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTQwLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRTdHlsZUVsZW1lbnQuanM/ZGU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBpbnNlcnRTdHlsZUVsZW1lbnQob3B0aW9ucykge1xuICB2YXIgZWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgb3B0aW9ucy5zZXRBdHRyaWJ1dGVzKGVsZW1lbnQsIG9wdGlvbnMuYXR0cmlidXRlcyk7XG4gIG9wdGlvbnMuaW5zZXJ0KGVsZW1lbnQsIG9wdGlvbnMub3B0aW9ucyk7XG4gIHJldHVybiBlbGVtZW50O1xufVxubW9kdWxlLmV4cG9ydHMgPSBpbnNlcnRTdHlsZUVsZW1lbnQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///540\n");

/***/ }),

/***/ 56:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLGNBQWMsS0FBd0MsR0FBRyxzQkFBaUIsR0FBRyxDQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanM/ZGRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMoc3R5bGVFbGVtZW50KSB7XG4gIHZhciBub25jZSA9IHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gXCJ1bmRlZmluZWRcIiA/IF9fd2VicGFja19ub25jZV9fIDogbnVsbDtcbiAgaWYgKG5vbmNlKSB7XG4gICAgc3R5bGVFbGVtZW50LnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///56\n");

/***/ }),

/***/ 825:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n  var needLayer = typeof obj.layer !== \"undefined\";\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += \"}\";\n  }\n  if (obj.media) {\n    css += \"}\";\n  }\n  if (obj.supports) {\n    css += \"}\";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === \"undefined\") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///825\n");

/***/ }),

/***/ 113:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTEzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc3R5bGVUYWdUcmFuc2Zvcm0uanM/MWRkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzdHlsZVRhZ1RyYW5zZm9ybShjc3MsIHN0eWxlRWxlbWVudCkge1xuICBpZiAoc3R5bGVFbGVtZW50LnN0eWxlU2hlZXQpIHtcbiAgICBzdHlsZUVsZW1lbnQuc3R5bGVTaGVldC5jc3NUZXh0ID0gY3NzO1xuICB9IGVsc2Uge1xuICAgIHdoaWxlIChzdHlsZUVsZW1lbnQuZmlyc3RDaGlsZCkge1xuICAgICAgc3R5bGVFbGVtZW50LnJlbW92ZUNoaWxkKHN0eWxlRWxlbWVudC5maXJzdENoaWxkKTtcbiAgICB9XG4gICAgc3R5bGVFbGVtZW50LmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZVRleHROb2RlKGNzcykpO1xuICB9XG59XG5tb2R1bGUuZXhwb3J0cyA9IHN0eWxlVGFnVHJhbnNmb3JtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///113\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__(787);
/******/ 	
/******/ })()
;