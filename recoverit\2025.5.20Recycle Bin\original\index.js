import "./index.scss";

$(() => {
  if (window.innerWidth < 992) {
    const devicesSwiper = new Swiper("#swiper-tips", {
      slidesPerView: 1.01,
      centeredSlides: true,
      spaceBetween: 15,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 2500,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
      },
      pagination: {
        el: "#swiper-tips .swiper-pagination",
        clickable: true,
      },
    });
    const stepsSwiper = new Swiper("#swiper-steps", {
      slidesPerView: 1,
      centeredSlides: true,
      spaceBetween: 30,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },

      pagination: {
        el: "#swiper-steps .swiper-pagination",
        clickable: true,
      },
    });
  }
  const keysSwiper = new Swiper("#swiper-keys", {
    slidesPerView: 1.3,
    slidesPerGroup: 1,
    spaceBetween: 15,
    loop: true,
    breakpoints: {
      1280: {
        spaceBetween: 30,
        slidesPerView: 5,
      },
      768: {
        slidesPerView: 3,
      },
    },
    pagination: {
      el: "#swiper-keys .swiper-pagination",
      clickable: true,
    },
    navigation: {
      nextEl: ".part-keys .right-btn",
      prevEl: ".part-keys .left-btn",
    },
  });

  $(".part-system .more-btn").on("click", function () {
    $(this).parents(".system-item").find(".mobile-slide").slideToggle();
    $(this).toggleClass("active");
  });
});
