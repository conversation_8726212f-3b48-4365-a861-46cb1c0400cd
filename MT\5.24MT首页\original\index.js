import "./index.scss";

$(() => {
  $(".left-hand").addClass("loaded");
  $(".right-hand").addClass("loaded");
  setTimeout(function () {
    // 获取要添加active类的元素并添加active类
    $(".icon-list-wrapper").addClass("loaded");
  }, 1000);

  if (window.innerWidth > 767.9) {
    const videoSwiper = new Swiper("#video-swiper", {
      slidesPerView: 1,
      spaceBetween: 10,
      loop: true,
      autoplay: {
        delay: 1300,
        disableOnInteraction: false,
      },
      effect: "fade",
      fadeEffect: true,
      // 禁用所有手动切换方式
      allowTouchMove: false, // 禁止触摸滑动
      noSwiping: true, // 禁止滑动
      keyboard: {
        enabled: false, // 禁止键盘控制
      },
      simulateTouch: false, // 禁止模拟触摸
      on: {
        slideChange: function () {
          const currentVideo = this.slides[this.activeIndex].querySelector("video");
          if (currentVideo) {
            $(".part-banner video").each(function () {
              this.pause();
            });
            // 重置当前视频到开头并播放
            currentVideo.currentTime = 0;
            // 使用延迟来确保DOM更新后再播放
            setTimeout(function () {
              currentVideo.play().catch(function (error) {
                console.log("视频自动播放失败:", error);
              });
            }, 50);
          }
        },
      },
    });
  }
  $("main .switch-tab").on("click", "div", function () {
    const $currentTransferBox = $(this).closest(".transfer-box");
    $(this).addClass("active").siblings().removeClass("active");
    if ($(this).hasClass("phone-box")) {
      $currentTransferBox.find(".computer-box-change").hide();
      $currentTransferBox.find(".phone-box-change").fadeIn();
    } else {
      $currentTransferBox.find(".phone-box-change").hide();
      $currentTransferBox.find(".computer-box-change").fadeIn();
    }
  });
  if (window.innerWidth < 1280) {
    const customerSwiperMobile = new Swiper("#customer-swiper-mobile", {
      slidesPerView: 1.0,
      centeredSlides: true,
      spaceBetween: 30,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 1.7,
          spaceBetween: 15,
        },
      },
      pagination: {
        el: "#customer-swiper-mobile .swiper-pagination",
        clickable: true,
      },
    });
  }

  if (window.innerWidth < 992) {
    const numberSwiper = new Swiper("#swiper-number", {
      slidesPerView: 1.0,
      centeredSlides: true,
      spaceBetween: 30,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
      },
      pagination: {
        el: "#swiper-number .swiper-pagination",
        clickable: true,
      },
    });
    const featuresSwiper = new Swiper("#swiper-features", {
      slidesPerView: 1.0,
      centeredSlides: true,
      spaceBetween: 30,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        576: {
          slidesPerView: 2,
          spaceBetween: 15,
        },
      },
      pagination: {
        el: "#swiper-features .swiper-pagination",
        clickable: true,
      },
    });
  }

  // fade形式swiper
  const customerSwiper = new Swiper("#customer-swiper", {
    slidesPerView: 1,
    spaceBetween: 10,
    loop: true,
    autoplay: {
      delay: 4000,
      disableOnInteraction: false,
    },
    fadeEffect: true,
    effect: "fade",
    // 设置非活动项目不可见
    fadeEffect: {
      crossFade: true,
    },
    on: {
      slideChange: function () {
        const currentSlide = this.realIndex;
        $(".customer-avatar-list .customer-avatar-item").removeClass("active");
        $(".customer-avatar-list .customer-avatar-item").eq(currentSlide).addClass("active");
      },
    },
  });
  // 视频懒加载
  let lazy_videos = [];
  $(".lazy-video").each(function () {
    lazy_videos.push($(this));
  });
  $(window).on("scroll", function () {
    if (lazy_videos) {
      lazy_videos.forEach(function (el, index) {
        if (isElementInViewport(el[0])) {
          el.attr("src", el.data("src"));

          if (el.data("poster")) {
            el.attr("poster", el.data("poster"));
          }

          el.attr("webkit-playsinline", "").attr("playsinline", "true");

          lazy_videos.splice(index, 1);
          lazy_videos = lazy_videos.length === 0 ? null : lazy_videos;
        }
      });
    }
  });

  function isElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    return rect.top >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight);
  }
  $(".customer-avatar-list .customer-avatar-item").on("click", function () {
    const currentSlide = $(this).index();
    $(this).addClass("active").siblings().removeClass("active");
    customerSwiper.slideToLoop(currentSlide);
  });
});
