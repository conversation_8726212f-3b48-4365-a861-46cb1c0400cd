/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 787:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

eval("\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\nvar injectStylesIntoStyleTag = __webpack_require__(72);\nvar injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleDomAPI.js\nvar styleDomAPI = __webpack_require__(825);\nvar styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertBySelector.js\nvar insertBySelector = __webpack_require__(659);\nvar insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\nvar setAttributesWithoutAttributes = __webpack_require__(56);\nvar setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertStyleElement.js\nvar insertStyleElement = __webpack_require__(540);\nvar insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleTagTransform.js\nvar styleTagTransform = __webpack_require__(113);\nvar styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);\n// EXTERNAL MODULE: ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/index.scss\nvar cjs_js_src = __webpack_require__(2);\n;// CONCATENATED MODULE: ./src/index.scss\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (styleTagTransform_default());\noptions.setAttributes = (setAttributesWithoutAttributes_default());\noptions.insert = insertBySelector_default().bind(null, \"head\");\noptions.domAPI = (styleDomAPI_default());\noptions.insertStyleElement = (insertStyleElement_default());\n\nvar update = injectStylesIntoStyleTag_default()(cjs_js_src/* default */.A, options);\n\n\n\n\n       /* harmony default export */ const src = (cjs_js_src/* default */.A && cjs_js_src/* default */.A.locals ? cjs_js_src/* default */.A.locals : undefined);\n\n;// CONCATENATED MODULE: ./src/index.js\n\n$(() => {\n  // ================= 配置常量 =================\n  const BREAKPOINTS = {\n    mobile: 576,\n    tablet: 768,\n    laptop: 992,\n    desktop: 1280,\n    largeDesktop: 1600\n  };\n\n  // ================= 状态变量 =================\n  const screenWidth = window.innerWidth;\n  const isDesktop = screenWidth >= BREAKPOINTS.desktop;\n  const isMobile = screenWidth <= BREAKPOINTS.mobile;\n  let countUpTriggered = false; // 替代原来的 stepVal\n\n  // ================= 工具函数 =================\n  // 节流函数\n  // 节流函数：限制函数在指定时间间隔内只能执行一次\n  const throttle = (func, delay) => {\n    let lastExecTime = 0; // 记录上次执行时间\n\n    return function (...args) {\n      const currentTime = Date.now(); // 获取当前时间\n\n      // 如果距离上次执行的时间超过了延迟时间，则执行函数\n      if (currentTime - lastExecTime >= delay) {\n        func.apply(this, args);\n        lastExecTime = currentTime; // 更新上次执行时间\n      }\n    };\n  };\n\n  // 检查元素是否完全在视口内\n  const isElementFullyInViewport = element => {\n    if (!element) return false;\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth);\n  };\n\n  // ================= 数字动画初始化 =================\n  const initCountUp = () => {\n    const handleCountUpScroll = () => {\n      const countBox = $(\".count-box\")[0];\n      if (!countUpTriggered && isElementFullyInViewport(countBox)) {\n        $(\".count-num\").countTo();\n        countUpTriggered = true;\n      }\n    };\n    $(window).on(\"scroll\", throttle(handleCountUpScroll, 200));\n  };\n\n  // ================= Swiper 初始化 =================\n  // 解决方案轮播\n  const swiperSolutions = new Swiper(\"#swiper-solutions\", {\n    slidesPerView: 1.2,\n    spaceBetween: 24,\n    loop: true,\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false\n    },\n    pagination: {\n      el: \"#swiper-solutions .swiper-pagination\",\n      clickable: true\n    },\n    breakpoints: {\n      [BREAKPOINTS.mobile]: {\n        slidesPerView: 2,\n        spaceBetween: 24\n      },\n      [BREAKPOINTS.laptop]: {\n        slidesPerView: 3,\n        spaceBetween: 24\n      },\n      [BREAKPOINTS.largeDesktop]: {\n        slidesPerView: 4,\n        spaceBetween: 24,\n        autoplay: false,\n        loop: false\n      }\n    }\n  });\n\n  // 步骤轮播\n  const swiperStep = new Swiper(\"#swiper-step\", {\n    slidesPerView: 1,\n    spaceBetween: 24,\n    loop: true,\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false\n    },\n    pagination: {\n      el: \"#swiper-step .swiper-pagination\",\n      clickable: true\n    },\n    breakpoints: {\n      [BREAKPOINTS.tablet]: {\n        slidesPerView: 2,\n        spaceBetween: 24\n      },\n      [BREAKPOINTS.desktop]: {\n        slidesPerView: 3,\n        spaceBetween: 24,\n        autoplay: false,\n        loop: false\n      }\n    }\n  });\n\n  // 家长评价轮播\n  const swiperParents = new Swiper(\"#swiper-parents\", {\n    slidesPerView: 1,\n    spaceBetween: 24,\n    centeredSlides: true,\n    loop: true,\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false\n    },\n    pagination: {\n      el: \".swiper-pagination\",\n      clickable: true\n    },\n    navigation: {\n      nextEl: \".swiper-button-next\",\n      prevEl: \".swiper-button-prev\"\n    },\n    breakpoints: {\n      [BREAKPOINTS.mobile]: {\n        slidesPerView: 1.5,\n        spaceBetween: 24\n      },\n      [BREAKPOINTS.tablet]: {\n        slidesPerView: 2,\n        spaceBetween: 24\n      },\n      [BREAKPOINTS.laptop]: {\n        slidesPerView: 1,\n        spaceBetween: 24\n      },\n      [BREAKPOINTS.largeDesktop]: {\n        slidesPerView: 1.2,\n        spaceBetween: 24\n      }\n    }\n  });\n\n  // ================= 特性轮播（特殊处理）=================\n  // 基础配置\n  const featureSwiperBaseConfig = {\n    loop: true,\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false\n    },\n    pagination: {\n      el: \"#feature-swiper .swiper-pagination\",\n      clickable: true\n    },\n    breakpoints: {\n      [BREAKPOINTS.mobile]: {\n        slidesPerView: 2,\n        spaceBetween: 15\n      },\n      [BREAKPOINTS.laptop]: {\n        slidesPerView: 3,\n        spaceBetween: 15\n      },\n      [BREAKPOINTS.largeDesktop]: {\n        slidesPerView: 4,\n        spaceBetween: 20\n      }\n    }\n  };\n\n  // 移动端特殊处理\n  let featureTextSwiper; // 声明在外部，便于后续访问\n  let featureSwiperConfig; // 最终配置\n\n  if (isMobile) {\n    // 文字同步轮播\n    featureTextSwiper = new Swiper(\"#feature-text-mobile-swiper\", {\n      slidesPerView: 1,\n      spaceBetween: 15,\n      effect: \"fade\",\n      allowTouchMove: false,\n      fadeEffect: {\n        crossFade: true\n      }\n    });\n\n    // 创建移动端配置（不修改原对象）\n    featureSwiperConfig = {\n      ...featureSwiperBaseConfig,\n      effect: \"creative\",\n      watchSlidesProgress: true,\n      centeredSlides: true,\n      slidesPerView: 1.8,\n      spaceBetween: -20,\n      creativeEffect: {\n        prev: {\n          shadow: false,\n          translate: [\"-85%\", \"5%\", 0],\n          rotate: [0, 0, -10],\n          scale: 0.85,\n          opacity: 1,\n          origin: \"bottom\"\n        },\n        next: {\n          shadow: false,\n          translate: [\"85%\", \"5%\", 0],\n          rotate: [0, 0, 10],\n          scale: 0.85,\n          opacity: 1,\n          origin: \"bottom\"\n        },\n        limitProgress: 2\n      },\n      on: {\n        slideChange: function () {\n          if (featureTextSwiper) {\n            featureTextSwiper.slideTo(this.realIndex, 100, false);\n          }\n        }\n      }\n    };\n  } else {\n    // 桌面端直接使用基础配置\n    featureSwiperConfig = featureSwiperBaseConfig;\n  }\n\n  // 初始化特性轮播\n  const featureSwiper = new Swiper(\"#feature-swiper\", featureSwiperConfig);\n\n  // 桌面端悬停控制\n  if (!isMobile) {\n    $(\"#feature-swiper\").on(\"mouseenter\", function () {\n      featureSwiper.autoplay.stop();\n    }).on(\"mouseleave\", function () {\n      featureSwiper.autoplay.start();\n    });\n  }\n\n  // ================= 初始化执行 =================\n  initCountUp();\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///787\n");

/***/ }),

/***/ 2:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(354);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(314);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `*{margin:0;padding:0;box-sizing:border-box}main{overflow:visible !important;background-color:#fff;color:#000;font-family:\"Messina Sans\",-apple-system,blinkmacsystemfont,\"Segoe UI\",roboto,\"Helvetica Neue\",arial,\"Noto Sans\",sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\"}@media(max-width: 992px){main{overflow:hidden !important}}main h1,main h2,main h3,main h4,main h5,main h6,main p,main div,main span,main ul,main li{margin-bottom:0;font-family:\"Messina Sans\",-apple-system,blinkmacsystemfont,\"Segoe UI\",roboto,\"Helvetica Neue\",arial,\"Noto Sans\",sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\"}main h2{text-align:center;font-weight:700;font-size:3.5rem}@media(max-width: 768px){main h2{font-size:24px}}main .opacity-7{opacity:.7}main .text-purple{color:#7a57ee}main .btn-wrapper{display:flex;justify-content:center;gap:1rem}@media(max-width: 768px){main .btn-wrapper{flex-direction:column;align-items:center;gap:8px}}main .btn-wrapper .btn{margin:0;border-radius:8px;text-transform:capitalize;display:flex;align-items:center;justify-content:center;min-width:158px}@media(min-width: 1280px){main .btn-wrapper .btn.btn-lg{min-width:224px}}@media(max-width: 768px){main .btn-wrapper .btn{width:280px;height:48px}}@media(max-width: 768px){main .btn-wrapper .btn{display:block;vertical-align:baseline}}@keyframes gradientAnimation{0%{background-position:0% 50%}50%{background-position:100% 50%}100%{background-position:0% 50%}}main .btn-white{color:#7a57ee}main .btn-white:hover{color:#7a57ee}main .btn-colorful{background:linear-gradient(94.2deg, #7a57ee 41.38%, #39a0fa 73.42%, #24e3c8 96.58%);background-size:200% 200%;animation:gradientAnimation 3s infinite linear;transition:transform .2s ease-in-out;color:#fff}main .btn-colorful:hover{transform:scale(1.05);color:#fff}main .btn-purple-bg{border:none;color:#fff;background:url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg.png) no-repeat center center/contain;aspect-ratio:232/64;width:232px;transition:transform .3s ease-in-out}@media(max-width: 768px){main .btn-purple-bg{background:url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg-mobile.png) no-repeat center center/contain;aspect-ratio:280/48;margin:0 auto}}@media(any-hover: hover){main .btn-purple-bg:hover{transform:translateY(-8px);color:#fff}}main .btn-purple-bg2{border:none;color:#fff;background:url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg2.png) no-repeat center center/contain;aspect-ratio:280/64;width:280px;transition:transform .3s ease-in-out;display:flex;gap:.5rem;box-shadow:0px 14px 19.8px 0px rgba(120,88,255,.2588235294)}main .btn-purple-bg2:hover{color:#fff}main .swiper-pagination .swiper-pagination-bullet{width:8px;height:8px;background:rgba(123,88,238,.3);opacity:1}main .swiper-pagination .swiper-pagination-bullet-active{width:54px;background:linear-gradient(90deg, #ad8fff 0%, #7c65fe 100%);border-radius:8px}main .part-banner{background:linear-gradient(177.83deg, rgba(204, 195, 255, 0.38) 39.74%, rgba(132, 113, 255, 0.38) 89.8%)}@media(max-width: 992px){main .part-banner{background:linear-gradient(185.96deg, rgba(186, 176, 255, 0.38) 14.77%, rgba(146, 129, 255, 0.38) 50.84%, rgba(34, 0, 255, 0.38) 82.21%)}}main .part-banner h1{font-weight:700;font-size:3.75rem;line-height:100%}@media(max-width: 768px){main .part-banner h1{font-size:32px}}main .part-banner .system-list{display:flex;gap:1rem}@media(max-width: 992px){main .part-banner .system-list{justify-content:center}}main .part-banner .system-list a{text-decoration:none}main .part-banner .system-list a:hover{color:#7a57ee}main .part-honour{background-color:#fbf8ff}@keyframes ToRight{0%{transform:translate3d(0, 0, 0)}100%{transform:translate3d(-50%, 0, 0);-webkit-transform:translate3d(-50%, 0, 0);-moz-transform:translate3d(-50%, 0, 0);-ms-transform:translate3d(-50%, 0, 0);-o-transform:translate3d(-50%, 0, 0)}}main .part-honour .honour-list{display:flex;flex-wrap:nowrap;animation:ToRight 18s linear infinite;width:fit-content}main .part-honour .honour-list .honour-item{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:auto;margin:0 3rem}@media(max-width: 768px){main .part-honour .honour-list .honour-item{margin:0 15px}}main .part-honour .honour-list .honour-item .honour-logo{height:64px;width:64px;display:flex;align-items:center;justify-content:center;overflow:hidden}main .part-honour .honour-list .honour-item .honour-intro{white-space:nowrap;text-align:center}@media(max-width: 768px){main .part-honour .honour-list .honour-item .honour-intro{display:none}}main .part-supervise .num-wrapper{background:linear-gradient(282.22deg, #cfffff 20.03%, #efe7ff 57.69%);border-radius:1rem;overflow:hidden;display:flex;justify-content:center;padding:1.5rem}main .part-supervise .num-wrapper .num-list{display:flex;width:100%;max-width:764px;justify-content:space-between}main .part-supervise .num-wrapper .num-list .num-box{display:flex;flex-direction:column;align-items:center;justify-content:center}main .part-supervise .num-wrapper .num-list .num-box .count-box{font-size:3rem;font-weight:700;color:#7a57ee;margin-bottom:.25rem;line-height:1}@media(max-width: 576px){main .part-supervise .num-wrapper .num-list .num-box .count-box{font-size:32px}}main .part-supervise .num-wrapper .num-list .num-box .num-desc{font-size:1.25rem}@media(max-width: 576px){main .part-supervise .num-wrapper .num-list .num-box .num-desc{font-size:12px}}main .part-ability{background:rgba(244,243,255,.64)}main .part-ability .ability-content{display:flex;justify-content:center;flex-direction:column;height:100%;max-width:565px;margin:0 auto}@media(max-width: 992px){main .part-ability .ability-content{max-width:unset;align-items:center;text-align:center}}main .part-ability .ability-content .ability-content-title{font-weight:700;font-size:2.5rem;color:#7a57ee}main .part-ability .ability-content .ability-content-list{margin-top:1.875rem;display:flex;justify-content:center;flex-direction:column;text-align:left}@media(max-width: 992px){main .part-ability .ability-content .ability-content-list{margin-top:1rem}}main .part-ability .ability-content .ability-content-list .ability-content-item{display:flex;align-items:flex-start;gap:4px}main .part-ability .ability-content .ability-content-list .ability-content-item .ability-icon{flex-shrink:0;margin-top:6px}@media(max-width: 576px){main .part-ability .ability-content .ability-content-list .ability-content-item .ability-icon{margin-top:3px}}main .part-ability .ability-content .ability-content-list .ability-content-item .ability-content-text{color:rgba(0,0,0,.6);line-height:150%}@media(max-width: 576px){main .part-solutions #swiper-solutions{margin-right:-15px}}main .part-solutions .solution-item{position:relative}main .part-solutions .solution-item .solution-content{position:absolute;top:0;left:0;width:100%;padding:1.5rem;text-align:center;z-index:3}main .part-solutions .solution-item .solution-content .solution-title{font-weight:700;font-size:1.125rem;color:#000;margin-bottom:4px}main .part-solutions .solution-item .solution-content .solution-description{font-size:.875rem}main .part-step{background:rgba(244,243,255,.64)}main .part-step .step-box{border-radius:1rem;padding:1.125rem;background-color:#fff;overflow:hidden;display:flex;flex-direction:column;align-items:center;height:100%;transition:all .3s ease-in-out}@media(any-hover: hover){main .part-step .step-box:hover{box-shadow:2px 13px 22.8px 0px #e9e7f5;transform:translateY(-5px)}}main .part-step .step-box .step-box-title{display:flex;align-items:center;gap:.5rem;font-size:1.25rem;font-weight:700;display:flex;gap:.5rem;margin-top:1.125rem;margin-bottom:.75rem}main .part-step .step-box .step-box-content{color:#444;padding:0 1rem 1rem;text-align:center}main .part-step .step-box .step-box-img{margin-top:auto}main .part-parents .testimonial-card{border-radius:1rem;overflow:hidden;position:relative}@media(max-width: 992px){main .part-parents .testimonial-card{height:100%;display:flex;flex-direction:column;border:1px solid rgba(0,0,0,.2)}}main .part-parents .testimonial-card-content{position:absolute;left:12.05%;top:50%;transform:translateY(-50%);border-radius:1.25rem;background:rgba(122,87,238,.85);padding:1.75rem;width:40%;max-width:392px;z-index:3;color:#fff}@media(max-width: 992px){main .part-parents .testimonial-card-content{position:relative;left:unset;top:unset;transform:unset;width:100%;max-width:unset;padding:1.5rem;border-radius:0;flex:1;background-color:#fff;color:#000}}main .part-parents .testimonial-card-content::before{content:\"\";position:absolute;top:50%;right:0;border-radius:4px;width:16px;aspect-ratio:16/38;transform:translate(98%, -50%);background-color:rgba(0,0,0,0);background:url(https://famisafe.wondershare.com/images/images-2025/screen-time/right-triangle.svg) no-repeat center center/contain}@media(max-width: 992px){main .part-parents .testimonial-card-content::before{content:unset}}main .part-parents .testimonial-card-content .testimonial-card-quote-left{position:absolute;top:0;right:11.23%;width:11.23%;transform:translateY(-50%)}@media(max-width: 992px){main .part-parents .testimonial-card-content .testimonial-card-quote-left{display:none}}main .part-parents .testimonial-card-content .testimonial-card-author{font-weight:700;font-size:1.125rem;margin-bottom:.5rem}main .part-parents .testimonial-card-content .testimonial-card-role{font-weight:700;color:#09d7be}@media(max-width: 992px){main .part-parents .testimonial-card-content .testimonial-card-role{font-size:12px;color:#787878;font-weight:400}}main .part-parents .testimonial-card-content .testimonial-card-text{font-size:1rem;color:rgba(255,255,255,.7)}@media(max-width: 992px){main .part-parents .testimonial-card-content .testimonial-card-text{font-size:14px;color:#444}}main .part-parents .testimonial-card-content .testimonial-card-quote-right{position:absolute;bottom:0;left:11.23%;width:11.23%;transform:translateY(50%)}@media(max-width: 992px){main .part-parents .testimonial-card-content .testimonial-card-quote-right{display:none}}main .part-feature .feature-item{position:relative;border-radius:1rem;overflow:hidden}@media(any-hover: hover){main .part-feature .feature-item:hover{box-shadow:0px 7px 14px 0px #d4cff7}main .part-feature .feature-item:hover .feature-detail-card{opacity:1}}main .part-feature .feature-item-title{font-weight:700;font-size:1.25rem;padding:1.5rem;position:absolute;left:0;top:0}@media(min-width: 576px){main .part-feature .feature-item .feature-detail-card{position:absolute;left:0;bottom:0;width:100%;height:100%;z-index:5;text-decoration:none;opacity:0;transition:opacity .3s ease-in-out;background:linear-gradient(180deg, rgba(122, 87, 238, 0.6) 0%, #7a57ee 64.68%);backdrop-filter:blur(20px);display:flex;flex-direction:column;justify-content:center;align-items:center;gap:1.5rem;padding:1.5rem 3rem;margin:0 auto;color:#fff;text-align:center}main .part-feature .feature-item .feature-detail-card-title{font-weight:700;font-size:1.5rem}main .part-feature .feature-item .feature-detail-card-description{font-size:1.125rem}main .part-feature .feature-item .feature-detail-card-arrow{flex-shrink:0;color:#fff;width:2.5rem;height:2.5rem;display:flex;align-items:center;justify-content:center;border-radius:50%;border:2px solid rgba(255,255,255,.6)}main .part-feature .feature-item .feature-detail-card-arrow:hover{border-color:#fff;background-color:#fff;color:#7a57ee}}@media(max-width: 576px){main .part-feature #feature-swiper{margin-left:-15px;margin-right:-15px;position:relative}main .part-feature #feature-swiper::before{content:\"\";position:absolute;left:0;bottom:0;z-index:2;width:11.2%;height:100%;background:linear-gradient(90deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);pointer-events:none}main .part-feature #feature-swiper::after{content:\"\";position:absolute;right:0;bottom:0;z-index:2;width:11.2%;height:100%;background:linear-gradient(270deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);pointer-events:none}main .part-feature #feature-swiper .swiper-slide{overflow:visible}main .part-feature #feature-swiper .swiper-slide.swiper-slide-active{z-index:5}main .part-feature #feature-swiper .swiper-slide.swiper-slide-active .feature-item{overflow:visible}main .part-feature #feature-swiper .swiper-slide.swiper-slide-active .feature-item img{border:4.28px solid #fff;box-shadow:0px 10.69px 19.36px 0px rgba(40,3,236,.2392156863);border-radius:1rem}main .part-feature #feature-swiper .feature-detail-card{display:none}main .part-feature .feature-item-mobile{height:100%}main .part-feature #feature-text-mobile-swiper .feature-detail-card{height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;text-decoration:none}main .part-feature .mobile-feature-text{margin-top:2.125rem;border-radius:1rem;background:linear-gradient(360deg, #7d58f9 15.62%, #a78dff 100.05%);box-shadow:0px 6px 12.9px 0px #e3dffe;padding:1rem;text-align:center;color:#fff;position:relative}main .part-feature .mobile-feature-text::before{content:\"\";position:absolute;top:0;left:50%;transform:translate(-50%, -100%);width:18px;height:6px;background:url(https://famisafe.wondershare.com/images/images-2025/index/purple-top.png) no-repeat center center/contain}main .part-feature .mobile-feature-text .feature-detail-card-title{font-weight:700;font-size:18px;line-height:32px;margin-bottom:8px;color:#fff}main .part-feature .mobile-feature-text .feature-detail-card-description{font-size:16px;line-height:20px;color:#fff;margin-bottom:8px}main .part-feature .mobile-feature-text .feature-detail-card-arrow{width:32px;height:32px;color:#7a57ee;background-color:#fff;border-radius:50%;display:flex;justify-content:center;align-items:center;margin:0 auto}}main .part-tips .tip-wrapper{border-radius:1rem;background:linear-gradient(325.88deg, #cfffff -6.18%, #efe7ff 66.9%);padding:4rem 1.5rem}main .part-tips .tip-wrapper .tips-list{max-width:1200px;display:flex;gap:1.5rem;flex-wrap:wrap;margin:4rem auto 0}@media(max-width: 768px){main .part-tips .tip-wrapper .tips-list{gap:8px;margin-top:24px}}main .part-tips .tip-wrapper .tips-list .tips-item{flex:0 0 calc(50% - .75rem);display:flex;align-items:center;background-color:#fff;padding:.75rem 1.25rem;border-radius:.5rem;gap:.5rem;color:rgba(0,0,0,.72)}@media(max-width: 768px){main .part-tips .tip-wrapper .tips-list .tips-item{flex:0 0 100%}}main .part-tips .tip-wrapper .tips-list .tips-item:hover{color:#7a57ee}main .part-tips .tip-wrapper .tips-list .tips-item .tips-title{font-weight:700;font-size:1.125rem;color:inherit}@media(max-width: 768px){main .part-tips .tip-wrapper .tips-list .tips-item .tips-item-circle{display:none}}main .part-tips .tip-wrapper .tips-list .tips-item .tips-item-arrow{display:none}@media(max-width: 768px){main .part-tips .tip-wrapper .tips-list .tips-item .tips-item-arrow{display:block;flex-shrink:0;margin-left:auto}}main .part-faq{background-color:rgba(244,243,255,.64)}main .part-faq .accordion-item{padding:1.5rem 0;border-bottom:1px solid #ddd}@media(max-width: 768px){main .part-faq .accordion-item{padding:16px 0}}main .part-faq .accordion-item .faq-icon{color:#b3afc1;flex-shrink:0;transition:transform .2s ease-in-out}@media(max-width: 768px){main .part-faq .accordion-item .faq-icon svg{width:24px;height:24px}}main .part-faq .accordion-item [aria-expanded=true] .faq-icon{color:#7a57ee;transform:rotate(45deg)}main .part-faq .accordion-item [aria-expanded=true] .faq-title{color:#7a57ee}main .part-faq .accordion-item .faq-title{flex-shrink:0;font-weight:700;font-size:1.5rem;color:#07273d}@media(max-width: 768px){main .part-faq .accordion-item .faq-title{flex-shrink:initial;margin-right:1rem;font-size:14px}}main .part-faq .faq-content{color:#07273d}main .part-footer .footer-box{border-radius:1rem;overflow:hidden;background-color:#e2dfff;background:url(https://famisafe.wondershare.com/images/images-2025/index/footer-bg.jpg) no-repeat center center/cover;margin:0 2.625rem;padding:6rem 3rem;text-align:center}@media(max-width: 768px){main .part-footer .footer-box{margin:0 15px;padding:30px 15px}}.section-footer{display:none !important}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/index.scss\"],\"names\":[],\"mappings\":\"AAAA,EACE,QAAA,CACA,SAAA,CACA,qBAAA,CAGF,KACE,2BAAA,CACA,qBAAA,CACA,UAAA,CACA,qMAAA,CAEA,yBANF,KAOI,0BAAA,CAAA,CAGF,0FAWE,eAAA,CACA,qMAAA,CAIF,QACE,iBAAA,CACA,eAAA,CACA,gBAAA,CACA,yBAJF,QAKI,cAAA,CAAA,CAIJ,gBACE,UAAA,CAGF,kBACE,aAAA,CAGF,kBACE,YAAA,CACA,sBAAA,CACA,QAAA,CAGF,yBACE,kBACE,qBAAA,CACA,kBAAA,CACA,OAAA,CAAA,CAIJ,uBACE,QAAA,CACA,iBAAA,CACA,yBAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,eAAA,CACA,0BACE,8BACE,eAAA,CAAA,CAGJ,yBAbF,uBAcI,WAAA,CACA,WAAA,CAAA,CAIJ,yBACE,uBACE,aAAA,CACA,uBAAA,CAAA,CAGJ,6BACE,GACE,0BAAA,CAEF,IACE,4BAAA,CAEF,KACE,0BAAA,CAAA,CAGJ,gBACE,aAAA,CACA,sBACE,aAAA,CAIJ,mBACE,mFAAA,CACA,yBAAA,CACA,8CAAA,CACA,oCAAA,CACA,UAAA,CACA,yBACE,qBAAA,CACA,UAAA,CAIJ,oBACE,WAAA,CACA,UAAA,CACA,2HAAA,CACA,mBAAA,CACA,WAAA,CACA,oCAAA,CACA,yBAPF,oBAQI,kIAAA,CACA,mBAAA,CAEA,aAAA,CAAA,CAGF,yBACE,0BACE,0BAAA,CACA,UAAA,CAAA,CAKN,qBACE,WAAA,CACA,UAAA,CACA,4HAAA,CACA,mBAAA,CACA,WAAA,CACA,oCAAA,CACA,YAAA,CACA,SAAA,CACA,2DAAA,CACA,2BACE,UAAA,CAIJ,kDACE,SAAA,CACA,UAAA,CACA,8BAAA,CAEA,SAAA,CAGF,yDACE,UAAA,CACA,2DAAA,CACA,iBAAA,CAEF,kBACE,wGAAA,CACA,yBAFF,kBAGI,wIAAA,CAAA,CAGF,qBACE,eAAA,CACA,iBAAA,CACA,gBAAA,CACA,yBAJF,qBAKI,cAAA,CAAA,CAGJ,+BACE,YAAA,CACA,QAAA,CACA,yBAHF,+BAII,sBAAA,CAAA,CAGF,iCACE,oBAAA,CACA,uCACE,aAAA,CAKR,kBACE,wBAAA,CACA,mBACE,GACE,8BAAA,CAEF,KACE,iCAAA,CACA,yCAAA,CACA,sCAAA,CACA,qCAAA,CACA,oCAAA,CAAA,CAGJ,+BACE,YAAA,CACA,gBAAA,CACA,qCAAA,CACA,iBAAA,CACA,4CACE,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,sBAAA,CACA,SAAA,CACA,aAAA,CACA,yBAPF,4CAQI,aAAA,CAAA,CAEF,yDACE,WAAA,CACA,UAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,eAAA,CAEF,0DACE,kBAAA,CACA,iBAAA,CACA,yBAHF,0DAII,YAAA,CAAA,CAQR,kCACE,qEAAA,CACA,kBAAA,CACA,eAAA,CACA,YAAA,CACA,sBAAA,CACA,cAAA,CACA,4CACE,YAAA,CACA,UAAA,CACA,eAAA,CACA,6BAAA,CACA,qDACE,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,sBAAA,CACA,gEACE,cAAA,CACA,eAAA,CACA,aAAA,CACA,oBAAA,CACA,aAAA,CACA,yBANF,gEAOI,cAAA,CAAA,CAGJ,+DACE,iBAAA,CACA,yBAFF,+DAGI,cAAA,CAAA,CAQZ,mBACE,gCAAA,CACA,oCACE,YAAA,CACA,sBAAA,CACA,qBAAA,CACA,WAAA,CACA,eAAA,CACA,aAAA,CACA,yBAPF,oCAQI,eAAA,CACA,kBAAA,CACA,iBAAA,CAAA,CAEF,2DACE,eAAA,CACA,gBAAA,CACA,aAAA,CAEF,0DACE,mBAAA,CACA,YAAA,CACA,sBAAA,CACA,qBAAA,CACA,eAAA,CACA,yBANF,0DAOI,eAAA,CAAA,CAEF,gFACE,YAAA,CACA,sBAAA,CACA,OAAA,CACA,8FACE,aAAA,CACA,cAAA,CACA,yBAHF,8FAII,cAAA,CAAA,CAGJ,sGACE,oBAAA,CACA,gBAAA,CAQR,yBACE,uCACE,kBAAA,CAAA,CAGJ,oCACE,iBAAA,CACA,sDACE,iBAAA,CACA,KAAA,CACA,MAAA,CACA,UAAA,CACA,cAAA,CACA,iBAAA,CACA,SAAA,CACA,sEACE,eAAA,CACA,kBAAA,CACA,UAAA,CACA,iBAAA,CAEF,4EACE,iBAAA,CAMR,gBACE,gCAAA,CACA,0BACE,kBAAA,CACA,gBAAA,CACA,qBAAA,CACA,eAAA,CACA,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,WAAA,CACA,8BAAA,CACA,yBACE,gCACE,sCAAA,CACA,0BAAA,CAAA,CAGJ,0CACE,YAAA,CACA,kBAAA,CACA,SAAA,CACA,iBAAA,CACA,eAAA,CACA,YAAA,CACA,SAAA,CACA,mBAAA,CACA,oBAAA,CAEF,4CACE,UAAA,CACA,mBAAA,CACA,iBAAA,CAEF,wCACE,eAAA,CAKJ,qCACE,kBAAA,CACA,eAAA,CACA,iBAAA,CACA,yBAJF,qCAKI,WAAA,CACA,YAAA,CACA,qBAAA,CACA,+BAAA,CAAA,CAEF,6CACE,iBAAA,CACA,WAAA,CACA,OAAA,CACA,0BAAA,CACA,qBAAA,CACA,+BAAA,CACA,eAAA,CACA,SAAA,CACA,eAAA,CACA,SAAA,CACA,UAAA,CACA,yBAZF,6CAaI,iBAAA,CACA,UAAA,CACA,SAAA,CACA,eAAA,CACA,UAAA,CACA,eAAA,CACA,cAAA,CACA,eAAA,CACA,MAAA,CACA,qBAAA,CACA,UAAA,CAAA,CAEF,qDACE,UAAA,CACA,iBAAA,CACA,OAAA,CACA,OAAA,CACA,iBAAA,CACA,UAAA,CACA,kBAAA,CACA,8BAAA,CACA,8BAAA,CACA,kIAAA,CACA,yBAXF,qDAYI,aAAA,CAAA,CAIJ,0EACE,iBAAA,CACA,KAAA,CACA,YAAA,CACA,YAAA,CACA,0BAAA,CACA,yBANF,0EAOI,YAAA,CAAA,CAGJ,sEACE,eAAA,CACA,kBAAA,CACA,mBAAA,CAEF,oEACE,eAAA,CACA,aAAA,CACA,yBAHF,oEAII,cAAA,CACA,aAAA,CACA,eAAA,CAAA,CAGJ,oEACE,cAAA,CACA,0BAAA,CACA,yBAHF,oEAII,cAAA,CACA,UAAA,CAAA,CAGJ,2EACE,iBAAA,CACA,QAAA,CACA,WAAA,CACA,YAAA,CACA,yBAAA,CACA,yBANF,2EAOI,YAAA,CAAA,CAQR,iCACE,iBAAA,CACA,kBAAA,CACA,eAAA,CACA,yBACE,uCACE,mCAAA,CACA,4DACE,SAAA,CAAA,CAKN,uCACE,eAAA,CACA,iBAAA,CACA,cAAA,CACA,iBAAA,CACA,MAAA,CACA,KAAA,CAEF,yBACE,sDACE,iBAAA,CACA,MAAA,CACA,QAAA,CACA,UAAA,CACA,WAAA,CACA,SAAA,CACA,oBAAA,CACA,SAAA,CACA,kCAAA,CACA,8EAAA,CAEA,0BAAA,CAEA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,kBAAA,CACA,UAAA,CACA,mBAAA,CAEA,aAAA,CACA,UAAA,CACA,iBAAA,CAGF,4DACE,eAAA,CACA,gBAAA,CAEF,kEACE,kBAAA,CAEF,4DACE,aAAA,CACA,UAAA,CACA,YAAA,CACA,aAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,iBAAA,CACA,qCAAA,CACA,kEACE,iBAAA,CACA,qBAAA,CACA,aAAA,CAAA,CAMR,yBACE,mCACE,iBAAA,CACA,kBAAA,CACA,iBAAA,CACA,2CACE,UAAA,CACA,iBAAA,CACA,MAAA,CACA,QAAA,CACA,SAAA,CACA,WAAA,CACA,WAAA,CACA,+FAAA,CACA,mBAAA,CAEF,0CACE,UAAA,CACA,iBAAA,CACA,OAAA,CACA,QAAA,CACA,SAAA,CACA,WAAA,CACA,WAAA,CACA,gGAAA,CACA,mBAAA,CAGJ,iDACE,gBAAA,CAGF,qEACE,SAAA,CACA,mFACE,gBAAA,CACA,uFACE,wBAAA,CACA,6DAAA,CACA,kBAAA,CAKN,wDACE,YAAA,CAEF,wCACE,WAAA,CAEF,oEACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,kBAAA,CACA,oBAAA,CAGF,wCACE,mBAAA,CACA,kBAAA,CACA,mEAAA,CACA,qCAAA,CACA,YAAA,CACA,iBAAA,CACA,UAAA,CACA,iBAAA,CACA,gDACE,UAAA,CACA,iBAAA,CACA,KAAA,CACA,QAAA,CACA,gCAAA,CACA,UAAA,CACA,UAAA,CACA,wHAAA,CAEF,mEACE,eAAA,CACA,cAAA,CACA,gBAAA,CACA,iBAAA,CACA,UAAA,CAEF,yEACE,cAAA,CACA,gBAAA,CACA,UAAA,CACA,iBAAA,CAEF,mEACE,UAAA,CACA,WAAA,CACA,aAAA,CACA,qBAAA,CACA,iBAAA,CACA,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,aAAA,CAAA,CAON,6BACE,kBAAA,CACA,oEAAA,CACA,mBAAA,CACA,wCACE,gBAAA,CACA,YAAA,CACA,UAAA,CACA,cAAA,CACA,kBAAA,CACA,yBANF,wCAOI,OAAA,CACA,eAAA,CAAA,CAEF,mDACE,2BAAA,CACA,YAAA,CACA,kBAAA,CACA,qBAAA,CACA,sBAAA,CACA,mBAAA,CACA,SAAA,CACA,qBAAA,CACA,yBATF,mDAUI,aAAA,CAAA,CAGF,yDACE,aAAA,CAGF,+DACE,eAAA,CACA,kBAAA,CACA,aAAA,CAEF,yBACE,qEACE,YAAA,CAAA,CAGJ,oEACE,YAAA,CACA,yBAFF,oEAGI,aAAA,CACA,aAAA,CACA,gBAAA,CAAA,CAQZ,eACE,sCAAA,CACA,+BACE,gBAAA,CACA,4BAAA,CACA,yBAHF,+BAII,cAAA,CAAA,CAEF,yCACE,aAAA,CACA,aAAA,CACA,oCAAA,CACA,yBACE,6CACE,UAAA,CACA,WAAA,CAAA,CAON,8DACE,aAAA,CACA,uBAAA,CAEF,+DACE,aAAA,CAIJ,0CACE,aAAA,CACA,eAAA,CACA,gBAAA,CACA,aAAA,CACA,yBALF,0CAMI,mBAAA,CACA,iBAAA,CACA,cAAA,CAAA,CAGJ,4BACE,aAAA,CAKF,8BACE,kBAAA,CACA,eAAA,CACA,wBAAA,CACA,qHAAA,CACA,iBAAA,CACA,iBAAA,CACA,iBAAA,CACA,yBARF,8BASI,aAAA,CACA,iBAAA,CAAA,CAOR,gBACE,uBAAA\",\"sourcesContent\":[\"* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nmain {\\n  overflow: visible !important;\\n  background-color: #fff;\\n  color: #000;\\n  font-family: \\\"Messina Sans\\\", -apple-system, blinkmacsystemfont, \\\"Segoe UI\\\", roboto, \\\"Helvetica Neue\\\", arial, \\\"Noto Sans\\\", sans-serif, \\\"Apple Color Emoji\\\",\\n    \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n  @media (max-width: 992px) {\\n    overflow: hidden !important;\\n  }\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6,\\n  p,\\n  div,\\n  span,\\n  ul,\\n  li {\\n    margin-bottom: 0;\\n    font-family: \\\"Messina Sans\\\", -apple-system, blinkmacsystemfont, \\\"Segoe UI\\\", roboto, \\\"Helvetica Neue\\\", arial, \\\"Noto Sans\\\", sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n  }\\n\\n  h2 {\\n    text-align: center;\\n    font-weight: 700;\\n    font-size: 3.5rem;\\n    @media (max-width: 768px) {\\n      font-size: 24px;\\n    }\\n  }\\n\\n  .opacity-7 {\\n    opacity: 0.7;\\n  }\\n\\n  .text-purple {\\n    color: #7a57ee;\\n  }\\n\\n  .btn-wrapper {\\n    display: flex;\\n    justify-content: center;\\n    gap: 1rem;\\n  }\\n\\n  @media (max-width: 768px) {\\n    .btn-wrapper {\\n      flex-direction: column;\\n      align-items: center;\\n      gap: 8px;\\n    }\\n  }\\n\\n  .btn-wrapper .btn {\\n    margin: 0;\\n    border-radius: 8px;\\n    text-transform: capitalize;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    min-width: 158px;\\n    @media (min-width: 1280px) {\\n      &.btn-lg {\\n        min-width: 224px;\\n      }\\n    }\\n    @media (max-width: 768px) {\\n      width: 280px;\\n      height: 48px;\\n    }\\n  }\\n\\n  @media (max-width: 768px) {\\n    .btn-wrapper .btn {\\n      display: block;\\n      vertical-align: baseline;\\n    }\\n  }\\n  @keyframes gradientAnimation {\\n    0% {\\n      background-position: 0% 50%;\\n    }\\n    50% {\\n      background-position: 100% 50%;\\n    }\\n    100% {\\n      background-position: 0% 50%;\\n    }\\n  }\\n  .btn-white {\\n    color: #7a57ee;\\n    &:hover {\\n      color: #7a57ee;\\n    }\\n  }\\n\\n  .btn-colorful {\\n    background: linear-gradient(94.2deg, #7a57ee 41.38%, #39a0fa 73.42%, #24e3c8 96.58%);\\n    background-size: 200% 200%;\\n    animation: gradientAnimation 3s infinite linear;\\n    transition: transform 0.2s ease-in-out;\\n    color: #fff;\\n    &:hover {\\n      transform: scale(1.05);\\n      color: #fff;\\n    }\\n  }\\n\\n  .btn-purple-bg {\\n    border: none;\\n    color: #fff;\\n    background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg.png) no-repeat center center / contain;\\n    aspect-ratio: 232 / 64;\\n    width: 232px;\\n    transition: transform 0.3s ease-in-out;\\n    @media (max-width: 768px) {\\n      background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg-mobile.png) no-repeat center center / contain;\\n      aspect-ratio: 280 / 48;\\n\\n      margin: 0 auto;\\n    }\\n\\n    @media (any-hover: hover) {\\n      &:hover {\\n        transform: translateY(-8px);\\n        color: #fff;\\n      }\\n    }\\n  }\\n\\n  .btn-purple-bg2 {\\n    border: none;\\n    color: #fff;\\n    background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg2.png) no-repeat center center / contain;\\n    aspect-ratio: 280 / 64;\\n    width: 280px;\\n    transition: transform 0.3s ease-in-out;\\n    display: flex;\\n    gap: 0.5rem;\\n    box-shadow: 0px 14px 19.8px 0px #7858ff42;\\n    &:hover {\\n      color: #fff;\\n    }\\n  }\\n\\n  .swiper-pagination .swiper-pagination-bullet {\\n    width: 8px;\\n    height: 8px;\\n    background: hsla(254, 82%, 64%, 0.3);\\n\\n    opacity: 1;\\n  }\\n\\n  .swiper-pagination .swiper-pagination-bullet-active {\\n    width: 54px;\\n    background: linear-gradient(90deg, #ad8fff 0%, #7c65fe 100%);\\n    border-radius: 8px;\\n  }\\n  .part-banner {\\n    background: linear-gradient(177.83deg, rgba(204, 195, 255, 0.38) 39.74%, rgba(132, 113, 255, 0.38) 89.8%);\\n    @media (max-width: 992px) {\\n      background: linear-gradient(185.96deg, rgba(186, 176, 255, 0.38) 14.77%, rgba(146, 129, 255, 0.38) 50.84%, rgba(34, 0, 255, 0.38) 82.21%);\\n    }\\n\\n    h1 {\\n      font-weight: 700;\\n      font-size: 3.75rem;\\n      line-height: 100%;\\n      @media (max-width: 768px) {\\n        font-size: 32px;\\n      }\\n    }\\n    .system-list {\\n      display: flex;\\n      gap: 1rem;\\n      @media (max-width: 992px) {\\n        justify-content: center;\\n      }\\n\\n      a {\\n        text-decoration: none;\\n        &:hover {\\n          color: #7a57ee;\\n        }\\n      }\\n    }\\n  }\\n  .part-honour {\\n    background-color: #fbf8ff;\\n    @keyframes ToRight {\\n      0% {\\n        transform: translate3d(0, 0, 0);\\n      }\\n      100% {\\n        transform: translate3d(-50%, 0, 0);\\n        -webkit-transform: translate3d(-50%, 0, 0);\\n        -moz-transform: translate3d(-50%, 0, 0);\\n        -ms-transform: translate3d(-50%, 0, 0);\\n        -o-transform: translate3d(-50%, 0, 0);\\n      }\\n    }\\n    .honour-list {\\n      display: flex;\\n      flex-wrap: nowrap;\\n      animation: ToRight 18s linear infinite;\\n      width: fit-content;\\n      .honour-item {\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        flex: auto;\\n        margin: 0 3rem;\\n        @media (max-width: 768px) {\\n          margin: 0 15px;\\n        }\\n        .honour-logo {\\n          height: 64px;\\n          width: 64px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          overflow: hidden;\\n        }\\n        .honour-intro {\\n          white-space: nowrap;\\n          text-align: center;\\n          @media (max-width: 768px) {\\n            display: none;\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-supervise {\\n    .num-wrapper {\\n      background: linear-gradient(282.22deg, #cfffff 20.03%, #efe7ff 57.69%);\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      display: flex;\\n      justify-content: center;\\n      padding: 1.5rem;\\n      .num-list {\\n        display: flex;\\n        width: 100%;\\n        max-width: 764px;\\n        justify-content: space-between;\\n        .num-box {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          justify-content: center;\\n          .count-box {\\n            font-size: 3rem;\\n            font-weight: 700;\\n            color: #7a57ee;\\n            margin-bottom: 0.25rem;\\n            line-height: 1;\\n            @media (max-width: 576px) {\\n              font-size: 32px;\\n            }\\n          }\\n          .num-desc {\\n            font-size: 1.25rem;\\n            @media (max-width: 576px) {\\n              font-size: 12px;\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-ability {\\n    background: rgba(244, 243, 255, 0.64);\\n    .ability-content {\\n      display: flex;\\n      justify-content: center;\\n      flex-direction: column;\\n      height: 100%;\\n      max-width: 565px;\\n      margin: 0 auto;\\n      @media (max-width: 992px) {\\n        max-width: unset;\\n        align-items: center;\\n        text-align: center;\\n      }\\n      .ability-content-title {\\n        font-weight: 700;\\n        font-size: 2.5rem;\\n        color: #7a57ee;\\n      }\\n      .ability-content-list {\\n        margin-top: 1.875rem;\\n        display: flex;\\n        justify-content: center;\\n        flex-direction: column;\\n        text-align: left;\\n        @media (max-width: 992px) {\\n          margin-top: 1rem;\\n        }\\n        .ability-content-item {\\n          display: flex;\\n          align-items: flex-start;\\n          gap: 4px;\\n          .ability-icon {\\n            flex-shrink: 0;\\n            margin-top: 6px;\\n            @media (max-width: 576px) {\\n              margin-top: 3px;\\n            }\\n          }\\n          .ability-content-text {\\n            color: rgba(0, 0, 0, 0.6);\\n            line-height: 150%;\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-solutions {\\n    @media (max-width: 576px) {\\n      #swiper-solutions {\\n        margin-right: -15px;\\n      }\\n    }\\n    .solution-item {\\n      position: relative;\\n      .solution-content {\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        padding: 1.5rem;\\n        text-align: center;\\n        z-index: 3;\\n        .solution-title {\\n          font-weight: 700;\\n          font-size: 1.125rem;\\n          color: #000;\\n          margin-bottom: 4px;\\n        }\\n        .solution-description {\\n          font-size: 0.875rem;\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-step {\\n    background: rgba(244, 243, 255, 0.64);\\n    .step-box {\\n      border-radius: 1rem;\\n      padding: 1.125rem;\\n      background-color: #fff;\\n      overflow: hidden;\\n      display: flex;\\n      flex-direction: column;\\n      align-items: center;\\n      height: 100%;\\n      transition: all 0.3s ease-in-out;\\n      @media (any-hover: hover) {\\n        &:hover {\\n          box-shadow: 2px 13px 22.8px 0px rgba(233, 231, 245, 1);\\n          transform: translateY(-5px);\\n        }\\n      }\\n      .step-box-title {\\n        display: flex;\\n        align-items: center;\\n        gap: 0.5rem;\\n        font-size: 1.25rem;\\n        font-weight: 700;\\n        display: flex;\\n        gap: 0.5rem;\\n        margin-top: 1.125rem;\\n        margin-bottom: 0.75rem;\\n      }\\n      .step-box-content {\\n        color: #444444;\\n        padding: 0 1rem 1rem;\\n        text-align: center;\\n      }\\n      .step-box-img {\\n        margin-top: auto;\\n      }\\n    }\\n  }\\n  .part-parents {\\n    .testimonial-card {\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      position: relative;\\n      @media (max-width: 992px) {\\n        height: 100%;\\n        display: flex;\\n        flex-direction: column;\\n        border: 1px solid hsla(0, 0%, 0%, 0.2);\\n      }\\n      &-content {\\n        position: absolute;\\n        left: 12.05%;\\n        top: 50%;\\n        transform: translateY(-50%);\\n        border-radius: 1.25rem;\\n        background: rgba(122, 87, 238, 0.85);\\n        padding: 1.75rem;\\n        width: 40%;\\n        max-width: 392px;\\n        z-index: 3;\\n        color: #fff;\\n        @media (max-width: 992px) {\\n          position: relative;\\n          left: unset;\\n          top: unset;\\n          transform: unset;\\n          width: 100%;\\n          max-width: unset;\\n          padding: 1.5rem;\\n          border-radius: 0;\\n          flex: 1;\\n          background-color: #fff;\\n          color: #000;\\n        }\\n        &::before {\\n          content: \\\"\\\";\\n          position: absolute;\\n          top: 50%;\\n          right: 0;\\n          border-radius: 4px;\\n          width: 16px;\\n          aspect-ratio: 16 / 38;\\n          transform: translate(98%, -50%);\\n          background-color: transparent;\\n          background: url(https://famisafe.wondershare.com/images/images-2025/screen-time/right-triangle.svg) no-repeat center center / contain;\\n          @media (max-width: 992px) {\\n            content: unset;\\n          }\\n        }\\n\\n        .testimonial-card-quote-left {\\n          position: absolute;\\n          top: 0;\\n          right: 11.23%;\\n          width: 11.23%;\\n          transform: translateY(-50%);\\n          @media (max-width: 992px) {\\n            display: none;\\n          }\\n        }\\n        .testimonial-card-author {\\n          font-weight: 700;\\n          font-size: 1.125rem;\\n          margin-bottom: 0.5rem;\\n        }\\n        .testimonial-card-role {\\n          font-weight: 700;\\n          color: #09d7be;\\n          @media (max-width: 992px) {\\n            font-size: 12px;\\n            color: #787878;\\n            font-weight: 400;\\n          }\\n        }\\n        .testimonial-card-text {\\n          font-size: 1rem;\\n          color: rgba(255, 255, 255, 0.7);\\n          @media (max-width: 992px) {\\n            font-size: 14px;\\n            color: #444444;\\n          }\\n        }\\n        .testimonial-card-quote-right {\\n          position: absolute;\\n          bottom: 0;\\n          left: 11.23%;\\n          width: 11.23%;\\n          transform: translateY(50%);\\n          @media (max-width: 992px) {\\n            display: none;\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-feature {\\n    .feature-item {\\n      position: relative;\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      @media (any-hover: hover) {\\n        &:hover {\\n          box-shadow: 0px 7px 14px 0px rgba(212, 207, 247, 1);\\n          .feature-detail-card {\\n            opacity: 1;\\n          }\\n        }\\n      }\\n\\n      &-title {\\n        font-weight: 700;\\n        font-size: 1.25rem;\\n        padding: 1.5rem;\\n        position: absolute;\\n        left: 0;\\n        top: 0;\\n      }\\n      @media (min-width: 576px) {\\n        .feature-detail-card {\\n          position: absolute;\\n          left: 0;\\n          bottom: 0;\\n          width: 100%;\\n          height: 100%;\\n          z-index: 5;\\n          text-decoration: none;\\n          opacity: 0;\\n          transition: opacity 0.3s ease-in-out;\\n          background: linear-gradient(180deg, rgba(122, 87, 238, 0.6) 0%, #7a57ee 64.68%);\\n\\n          backdrop-filter: blur(20px);\\n\\n          display: flex;\\n          flex-direction: column;\\n          justify-content: center;\\n          align-items: center;\\n          gap: 1.5rem;\\n          padding: 1.5rem 3rem;\\n\\n          margin: 0 auto;\\n          color: #fff;\\n          text-align: center;\\n        }\\n\\n        .feature-detail-card-title {\\n          font-weight: 700;\\n          font-size: 1.5rem;\\n        }\\n        .feature-detail-card-description {\\n          font-size: 1.125rem;\\n        }\\n        .feature-detail-card-arrow {\\n          flex-shrink: 0;\\n          color: #fff;\\n          width: 2.5rem;\\n          height: 2.5rem;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          border-radius: 50%;\\n          border: 2px solid rgba(255, 255, 255, 0.6);\\n          &:hover {\\n            border-color: #fff;\\n            background-color: #fff;\\n            color: #7a57ee;\\n          }\\n        }\\n      }\\n    }\\n\\n    @media (max-width: 576px) {\\n      #feature-swiper {\\n        margin-left: -15px;\\n        margin-right: -15px;\\n        position: relative;\\n        &::before {\\n          content: \\\"\\\";\\n          position: absolute;\\n          left: 0;\\n          bottom: 0;\\n          z-index: 2;\\n          width: 11.2%;\\n          height: 100%;\\n          background: linear-gradient(90deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);\\n          pointer-events: none;\\n        }\\n        &::after {\\n          content: \\\"\\\";\\n          position: absolute;\\n          right: 0;\\n          bottom: 0;\\n          z-index: 2;\\n          width: 11.2%;\\n          height: 100%;\\n          background: linear-gradient(270deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);\\n          pointer-events: none;\\n        }\\n      }\\n      #feature-swiper .swiper-slide {\\n        overflow: visible;\\n      }\\n\\n      #feature-swiper .swiper-slide.swiper-slide-active {\\n        z-index: 5;\\n        .feature-item {\\n          overflow: visible;\\n          img {\\n            border: 4.28px solid #ffffff;\\n            box-shadow: 0px 10.69px 19.36px 0px #2803ec3d;\\n            border-radius: 1rem;\\n          }\\n        }\\n      }\\n\\n      #feature-swiper .feature-detail-card {\\n        display: none;\\n      }\\n      .feature-item-mobile {\\n        height: 100%;\\n      }\\n      #feature-text-mobile-swiper .feature-detail-card {\\n        height: 100%;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        align-items: center;\\n        text-decoration: none;\\n      }\\n\\n      .mobile-feature-text {\\n        margin-top: 2.125rem;\\n        border-radius: 1rem;\\n        background: linear-gradient(360deg, #7d58f9 15.62%, #a78dff 100.05%);\\n        box-shadow: 0px 6px 12.9px 0px #e3dffe;\\n        padding: 1rem;\\n        text-align: center;\\n        color: #fff;\\n        position: relative;\\n        &::before {\\n          content: \\\"\\\";\\n          position: absolute;\\n          top: 0;\\n          left: 50%;\\n          transform: translate(-50%, -100%);\\n          width: 18px;\\n          height: 6px;\\n          background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-top.png) no-repeat center center / contain;\\n        }\\n        .feature-detail-card-title {\\n          font-weight: 700;\\n          font-size: 18px;\\n          line-height: 32px;\\n          margin-bottom: 8px;\\n          color: #fff;\\n        }\\n        .feature-detail-card-description {\\n          font-size: 16px;\\n          line-height: 20px;\\n          color: #fff;\\n          margin-bottom: 8px;\\n        }\\n        .feature-detail-card-arrow {\\n          width: 32px;\\n          height: 32px;\\n          color: #7a57ee;\\n          background-color: #fff;\\n          border-radius: 50%;\\n          display: flex;\\n          justify-content: center;\\n          align-items: center;\\n          margin: 0 auto;\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-tips {\\n    .tip-wrapper {\\n      border-radius: 1rem;\\n      background: linear-gradient(325.88deg, #cfffff -6.18%, #efe7ff 66.9%);\\n      padding: 4rem 1.5rem;\\n      .tips-list {\\n        max-width: 1200px;\\n        display: flex;\\n        gap: 1.5rem;\\n        flex-wrap: wrap;\\n        margin: 4rem auto 0;\\n        @media (max-width: 768px) {\\n          gap: 8px;\\n          margin-top: 24px;\\n        }\\n        .tips-item {\\n          flex: 0 0 calc(50% - 1.5rem / 2);\\n          display: flex;\\n          align-items: center;\\n          background-color: #fff;\\n          padding: 0.75rem 1.25rem;\\n          border-radius: 0.5rem;\\n          gap: 0.5rem;\\n          color: rgba(0, 0, 0, 0.72);\\n          @media (max-width: 768px) {\\n            flex: 0 0 100%;\\n          }\\n\\n          &:hover {\\n            color: #7a57ee;\\n          }\\n\\n          .tips-title {\\n            font-weight: 700;\\n            font-size: 1.125rem;\\n            color: inherit;\\n          }\\n          @media (max-width: 768px) {\\n            .tips-item-circle {\\n              display: none;\\n            }\\n          }\\n          .tips-item-arrow {\\n            display: none;\\n            @media (max-width: 768px) {\\n              display: block;\\n              flex-shrink: 0;\\n              margin-left: auto;\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-faq {\\n    background-color: rgba(244, 243, 255, 0.64);\\n    .accordion-item {\\n      padding: 1.5rem 0;\\n      border-bottom: 1px solid #dddddd;\\n      @media (max-width: 768px) {\\n        padding: 16px 0;\\n      }\\n      .faq-icon {\\n        color: #b3afc1;\\n        flex-shrink: 0;\\n        transition: transform 0.2s ease-in-out;\\n        @media (max-width: 768px) {\\n          svg {\\n            width: 24px;\\n            height: 24px;\\n          }\\n        }\\n      }\\n    }\\n\\n    .accordion-item [aria-expanded=\\\"true\\\"] {\\n      .faq-icon {\\n        color: #7a57ee;\\n        transform: rotate(45deg);\\n      }\\n      .faq-title {\\n        color: #7a57ee;\\n      }\\n    }\\n\\n    .accordion-item .faq-title {\\n      flex-shrink: 0;\\n      font-weight: 700;\\n      font-size: 1.5rem;\\n      color: #07273d;\\n      @media (max-width: 768px) {\\n        flex-shrink: initial;\\n        margin-right: 1rem;\\n        font-size: 14px;\\n      }\\n    }\\n    .faq-content {\\n      color: #07273d;\\n    }\\n  }\\n\\n  .part-footer {\\n    .footer-box {\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      background-color: #e2dfff;\\n      background: url(https://famisafe.wondershare.com/images/images-2025/index/footer-bg.jpg) no-repeat center center / cover;\\n      margin: 0 2.625rem;\\n      padding: 6rem 3rem;\\n      text-align: center;\\n      @media (max-width: 768px) {\\n        margin: 0 15px;\\n        padding: 30px 15px;\\n      }\\n      .footer-logo {\\n      }\\n    }\\n  }\\n}\\n.section-footer {\\n  display: none !important;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2\n");

/***/ }),

/***/ 314:
/***/ ((module) => {

eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///314\n");

/***/ }),

/***/ 354:
/***/ ((module) => {

eval("\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzU0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsY0FBYztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2Nzcy1sb2FkZXIvZGlzdC9ydW50aW1lL3NvdXJjZU1hcHMuanM/YWYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoaXRlbSkge1xuICB2YXIgY29udGVudCA9IGl0ZW1bMV07XG4gIHZhciBjc3NNYXBwaW5nID0gaXRlbVszXTtcbiAgaWYgKCFjc3NNYXBwaW5nKSB7XG4gICAgcmV0dXJuIGNvbnRlbnQ7XG4gIH1cbiAgaWYgKHR5cGVvZiBidG9hID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB2YXIgYmFzZTY0ID0gYnRvYSh1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoY3NzTWFwcGluZykpKSk7XG4gICAgdmFyIGRhdGEgPSBcInNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LFwiLmNvbmNhdChiYXNlNjQpO1xuICAgIHZhciBzb3VyY2VNYXBwaW5nID0gXCIvKiMgXCIuY29uY2F0KGRhdGEsIFwiICovXCIpO1xuICAgIHJldHVybiBbY29udGVudF0uY29uY2F0KFtzb3VyY2VNYXBwaW5nXSkuam9pbihcIlxcblwiKTtcbiAgfVxuICByZXR1cm4gW2NvbnRlbnRdLmpvaW4oXCJcXG5cIik7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///354\n");

/***/ }),

/***/ 72:
/***/ ((module) => {

eval("\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///72\n");

/***/ }),

/***/ 659:
/***/ ((module) => {

eval("\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU5LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanM/YjIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIG1lbW8gPSB7fTtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBnZXRUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0eXBlb2YgbWVtb1t0YXJnZXRdID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIHN0eWxlVGFyZ2V0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcih0YXJnZXQpO1xuXG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIHJldHVybiBoZWFkIG9mIGlmcmFtZSBpbnN0ZWFkIG9mIGlmcmFtZSBpdHNlbGZcbiAgICBpZiAod2luZG93LkhUTUxJRnJhbWVFbGVtZW50ICYmIHN0eWxlVGFyZ2V0IGluc3RhbmNlb2Ygd2luZG93LkhUTUxJRnJhbWVFbGVtZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUaGlzIHdpbGwgdGhyb3cgYW4gZXhjZXB0aW9uIGlmIGFjY2VzcyB0byBpZnJhbWUgaXMgYmxvY2tlZFxuICAgICAgICAvLyBkdWUgdG8gY3Jvc3Mtb3JpZ2luIHJlc3RyaWN0aW9uc1xuICAgICAgICBzdHlsZVRhcmdldCA9IHN0eWxlVGFyZ2V0LmNvbnRlbnREb2N1bWVudC5oZWFkO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgICAgICBzdHlsZVRhcmdldCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG1lbW9bdGFyZ2V0XSA9IHN0eWxlVGFyZ2V0O1xuICB9XG4gIHJldHVybiBtZW1vW3RhcmdldF07XG59XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gaW5zZXJ0QnlTZWxlY3RvcihpbnNlcnQsIHN0eWxlKSB7XG4gIHZhciB0YXJnZXQgPSBnZXRUYXJnZXQoaW5zZXJ0KTtcbiAgaWYgKCF0YXJnZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBmaW5kIGEgc3R5bGUgdGFyZ2V0LiBUaGlzIHByb2JhYmx5IG1lYW5zIHRoYXQgdGhlIHZhbHVlIGZvciB0aGUgJ2luc2VydCcgcGFyYW1ldGVyIGlzIGludmFsaWQuXCIpO1xuICB9XG4gIHRhcmdldC5hcHBlbmRDaGlsZChzdHlsZSk7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydEJ5U2VsZWN0b3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///659\n");

/***/ }),

/***/ 540:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTQwLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRTdHlsZUVsZW1lbnQuanM/ZGU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBpbnNlcnRTdHlsZUVsZW1lbnQob3B0aW9ucykge1xuICB2YXIgZWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgb3B0aW9ucy5zZXRBdHRyaWJ1dGVzKGVsZW1lbnQsIG9wdGlvbnMuYXR0cmlidXRlcyk7XG4gIG9wdGlvbnMuaW5zZXJ0KGVsZW1lbnQsIG9wdGlvbnMub3B0aW9ucyk7XG4gIHJldHVybiBlbGVtZW50O1xufVxubW9kdWxlLmV4cG9ydHMgPSBpbnNlcnRTdHlsZUVsZW1lbnQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///540\n");

/***/ }),

/***/ 56:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLGNBQWMsS0FBd0MsR0FBRyxzQkFBaUIsR0FBRyxDQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanM/ZGRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMoc3R5bGVFbGVtZW50KSB7XG4gIHZhciBub25jZSA9IHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gXCJ1bmRlZmluZWRcIiA/IF9fd2VicGFja19ub25jZV9fIDogbnVsbDtcbiAgaWYgKG5vbmNlKSB7XG4gICAgc3R5bGVFbGVtZW50LnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///56\n");

/***/ }),

/***/ 825:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n  var needLayer = typeof obj.layer !== \"undefined\";\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += \"}\";\n  }\n  if (obj.media) {\n    css += \"}\";\n  }\n  if (obj.supports) {\n    css += \"}\";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === \"undefined\") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///825\n");

/***/ }),

/***/ 113:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTEzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc3R5bGVUYWdUcmFuc2Zvcm0uanM/MWRkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzdHlsZVRhZ1RyYW5zZm9ybShjc3MsIHN0eWxlRWxlbWVudCkge1xuICBpZiAoc3R5bGVFbGVtZW50LnN0eWxlU2hlZXQpIHtcbiAgICBzdHlsZUVsZW1lbnQuc3R5bGVTaGVldC5jc3NUZXh0ID0gY3NzO1xuICB9IGVsc2Uge1xuICAgIHdoaWxlIChzdHlsZUVsZW1lbnQuZmlyc3RDaGlsZCkge1xuICAgICAgc3R5bGVFbGVtZW50LnJlbW92ZUNoaWxkKHN0eWxlRWxlbWVudC5maXJzdENoaWxkKTtcbiAgICB9XG4gICAgc3R5bGVFbGVtZW50LmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZVRleHROb2RlKGNzcykpO1xuICB9XG59XG5tb2R1bGUuZXhwb3J0cyA9IHN0eWxlVGFnVHJhbnNmb3JtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///113\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__(787);
/******/ 	
/******/ })()
;