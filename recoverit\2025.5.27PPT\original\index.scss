* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #f5f8ff;
  color: #000;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
    font-family: "Mulish", sans-serif;
  }

  h1,
  h2 {
    text-align: center;
  }

  h2 {
    font-size: 2.25rem;
    font-weight: 800;
  }

  .display-3 {
    @media (max-width: 576px) {
      font-size: 2.5rem;
    }
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .text-blue {
    color: #0085ff;
  }

  .text-gradient {
    color: transparent;
    background: linear-gradient(24.6deg, #59b0ff 40.59%, #0085ff 90.84%), linear-gradient(170.79deg, #34425b -1.84%, #768598 100.27%);
    -webkit-background-clip: text;
    background-clip: text;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1rem;
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
      align-items: center;
    }
  }
  .btn {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
    svg {
      max-width: 100%;
      height: 100%;
    }
    &.btn-action {
      background-color: #0085ff;
      border-color: #0085ff;
      min-width: 397;

      &:hover,
      &:focus,
      &:active {
        color: #fff;
        background-color: #005dd9;
        border-color: #0057cc;
      }
    }
    @media (max-width: 768px) {
      display: block;
      min-width: unset !important;
    }
  }

  .part-banner {
    position: relative;
    .small-title {
      color: #13171a;
      font-size: 1.875rem;
      margin-bottom: 1rem;
      font-weight: 700;
      line-height: 90%;
      @media (max-width: 576px) {
        font-size: 1.5rem;
      }
    }
  }

  @media (max-width: 768px) {
    .part-banner {
      background: linear-gradient(180deg, #c0eeff 0%, #f8fbff 96.46%);
    }
  }

  .part-banner .banner-left-download {
    z-index: 10;
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    width: 33%;
    @media (max-width: 768px) {
      display: none;
    }
  }
  .part-banner .banner-right-download {
    z-index: 10;
    position: absolute;
    height: 100%;
    top: 0;
    right: 0;
    width: 33%;
    @media (max-width: 768px) {
      display: none;
    }
  }

  .part-banner .video-wrapper {
    line-height: 0;
    font-size: 0;
  }

  .part-banner .video-wrapper video {
    height: 100%;
    width: 100%;
    object-fit: cover;
    min-height: 533px;
  }

  @media (max-width: 768px) {
    .part-banner .video-wrapper {
      display: none;
    }
  }

  .part-banner .part-banner-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
  }

  @media (max-width: 768px) {
    .part-banner .part-banner-content {
      position: relative;
      padding: 3rem 0;
      text-align: center;
    }
  }

  .part-banner .part-banner-content h1 {
    color: #13171a;
    line-height: 110%;
  }

  @media (max-width: 576px) {
    .part-banner .part-banner-content h1 {
      font-size: 28px;
    }
  }

  .part-banner .part-banner-content h1 span {
    color: #05a5ff;
  }

  .part-banner .part-banner-content h2 {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 100%;
    color: #13171a;
  }

  @media (max-width: 576px) {
    .part-banner .part-banner-content h2 {
      font-size: 1.25rem;
      margin-bottom: 1rem;
    }
  }

  @media (max-width: 768px) {
    .part-banner .part-banner-content .btn {
      min-width: unset;
    }
  }

  .part-banner .part-banner-content .logo-list {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 14.4px;

    .split-line {
      position: relative;
      height: 16px;
      width: 2px;
      top: 70%;
      border-radius: 1.5px;
      background-color: rgba($color: #000000, $alpha: 0.7);
    }
  }

  @media (max-width: 768px) {
    .part-banner .part-banner-content .logo-list {
      flex-wrap: wrap;
    }
  }

  @media (max-width: 768px) {
    .part-banner .part-banner-content .logo-list .logo-img {
      flex: 1;
      max-height: 24px;
      object-fit: contain;
    }
  }

  .part-format {
    .video-wrapper {
      line-height: 0;
      font-size: 0;
      border-radius: 16px;
      overflow: hidden;

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        // google去黑线
        filter: grayscale(0);
        // 火狐去黑线
        clip-path: fill-box;
      }
    }
  }

  .part-files {
    .file-box {
      height: 100%;
      display: flex;
      flex-direction: column;
      border-radius: 1rem;
      overflow: hidden;
    }

    .file-box .file-box-content {
      background-color: #fff;
      padding: 1.5rem 1.5rem 2rem;
      flex: 1;
      display: flex;
      flex-direction: column;

      p {
        color: #787878;
      }
    }

    @media (max-width: 576px) {
      .file-box .file-box-content {
        padding: 8px;
      }
    }

    .file-box .file-box-content .box-title {
      font-weight: 700;
      font-size: 1.125rem;
      color: #000;
      text-decoration: none;
      display: inline-block;
      margin-bottom: 1rem;
    }

    @media (max-width: 576px) {
      .col-6 {
        padding-right: 8px;
        padding-left: 8px;
      }

      .col-6:nth-child(odd) {
        padding-right: 4px;
      }

      .col-6:nth-child(even) {
        padding-left: 4px;
      }
    }
  }

  .part-devices {
    .device-list {
      max-width: 1310px;
      margin: 0 auto;
      display: flex;
      justify-content: space-around;
      gap: 1rem;
      @media (max-width: 768px) {
        flex-wrap: wrap;
      }
      .device-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
        max-width: 7.5rem;
        text-align: center;
        @media (max-width: 768px) {
          flex: 1 1 20%;
        }
        img {
          transition: transform 0.2s linear;
          @media (any-hover: hover) {
            &:hover {
              transform: scale(1.3);
            }
          }
        }
      }
    }
  }

  .part-tech {
    .tech-wrapper {
      border-radius: 2.5rem;
      overflow: hidden;
      background: url(./images/tech-bg.jpg) no-repeat center center/cover;
      padding: 4.375rem 2rem;
    }

    .tech-wrapper .tech-wrapper-inner {
      max-width: 786px;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .tech-wrapper .tech-wrapper-inner .tech-item {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 80px;
      color: #fff;
    }

    @media (max-width: 768px) {
      .tech-wrapper .tech-wrapper-inner .tech-item {
        flex-direction: column;
        gap: 1rem;
      }
    }

    .tech-wrapper .tech-wrapper-inner .tech-item .left-content {
      width: 202px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 12px;
      color: #fff;
    }

    .tech-wrapper .tech-wrapper-inner .tech-item .right-content {
      flex: 1;
      text-align: left;
      font-weight: 500;
      color: #fff;
    }

    .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
    }

    @media (max-width: 768px) {
      .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail {
        justify-content: center;
        text-align: center;
      }
    }

    .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail .sys-title {
      font-weight: 700;
      font-size: 1.125rem;
    }

    .tech-wrapper .tech-wrapper-inner .tech-item-dividing {
      width: 100%;
      border-bottom: 1px dashed rgba(255, 255, 255, 0.5);
    }
  }

  .part-how {
    .nav {
      display: flex;
      flex-wrap: nowrap;

      gap: 12px;
      padding-top: 3rem;
      padding-bottom: 1.875rem;
    }

    @media (max-width: 768px) {
      .nav {
        padding-top: 1.5rem;
      }
    }

    .nav .nav-item {
      flex: 1 1 50%;
      text-align: center;
      padding: 1rem;
      border-radius: 1rem;
      background-color: #fff;
      border: 1px solid #b5dae8;
      font-weight: 600;
      font-size: 1.125rem;
      color: #000;
      text-decoration: none;
      transition: unset;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav .nav-item.active {
      background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(0deg, #0055fb, #0055fb);
      color: #fff;
    }

    .how-box {
      display: flex;
      border-radius: 16px;
      overflow: hidden;
      background-color: #fff;
      padding: 2.5rem 3.5rem;
      gap: 2.5rem;
      justify-content: center;
      align-items: center;
    }

    @media (max-width: 992px) {
      .how-box {
        flex-direction: column;
        padding: 1.5rem 1.5rem;
        gap: 1.5rem;
      }
    }

    .how-box .content-wrapper {
      flex: 1 1 46%;
      width: 100%;
    }

    .how-box .content-wrapper .advantages-box {
      padding: 1.5rem 1rem;
      background-color: #f6fff7;
      position: relative;
      border-radius: 12px;
      overflow: hidden;
    }

    .how-box .content-wrapper .advantages-box::before {
      content: "Pros";
      position: absolute;
      top: 0;
      right: 0;
      font-size: 1.125rem;
      font-weight: 500;
      line-height: 100%;
      color: #fff;
      background-color: #0cad73;
      border-radius: 0 12px 0 12px;
      padding: 4px 0.75rem;
    }

    .how-box .content-wrapper .advantages-box .advantages-list {
      display: flex;
      gap: 1.25rem;
      flex-direction: column;
    }

    .how-box .content-wrapper .advantages-box .advantages-list .advantage-item {
      gap: 12px;
      display: flex;
      align-items: center;
      line-height: 100%;
      font-size: 1.125rem;
      font-weight: 500;
    }

    .how-box .content-wrapper .disadvantages-box {
      padding: 1.5rem 1rem;
      background-color: #fff9fc;
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      min-height: 105px;
    }

    .how-box .content-wrapper .disadvantages-box::before {
      content: "Cons";
      position: absolute;
      top: 0;
      right: 0;
      font-size: 1.125rem;
      font-weight: 500;
      line-height: 100%;
      color: #fff;
      background-color: #ff4a75;
      border-radius: 0 12px 0 12px;
      padding: 4px 0.75rem;
    }

    .how-box .content-wrapper .disadvantages-box .disadvantages-list {
      display: flex;
      gap: 1.25rem;
      flex-direction: column;
    }

    .how-box .content-wrapper .disadvantages-box .disadvantages-list .disadvantage-item {
      gap: 12px;
      display: flex;
      align-items: center;
      line-height: 100%;
      font-size: 1.125rem;
      font-weight: 500;
    }

    .how-box .video-wrapper {
      line-height: 0;
      font-size: 0;
      border-radius: 1.25rem;
      overflow: hidden;
      flex: 0 0 54%;
      height: 100%;
      width: 100%;
    }
  }

  .part-customer {
    .customer-wrapper {
      border-radius: 2rem;
      overflow: hidden;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      height: 100%;
      position: relative;
      .customer-info-wrapper {
        position: absolute;
        top: 50%;
        left: 8.7%;
        min-height: 80%;
        padding: 1.25rem 1.875rem;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(35px);
        border-radius: 1.5rem;
        max-width: 360px;

        @media (max-width: 1600px) {
          max-width: 460px;
        }
        @media (max-width: 992px) {
          position: relative;
          transform: unset;
          top: initial;
          left: initial;
          right: initial !important;
          min-height: unset;
          padding: 1.25rem 1.875rem;
          max-width: 100%;
          border-radius: 0;
        }

        &.right {
          left: unset;
          right: 8.7%;
        }
        .customer-info-list {
          display: flex;
          align-items: center;
          gap: 24px;
          padding-left: 14px;
        }
        .customer-detail {
          .detail-title {
            color: #0080ff;
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            margin-top: 1.25rem;
          }
        }
      }
    }

    .customer-wrapper .customer-info-list .customer-title,
    .customer-wrapper .customer-info-list .customer-profession,
    .customer-wrapper .customer-info-list .customer-age {
      position: relative;
      background-color: #bcd3e9;
      border-radius: 0 6px 6px 0;
      padding-right: 8px;
      padding-left: 5px;
      font-size: 14px;
      color: #000;
      font-weight: 600;
      height: 31px;
      display: flex;
      align-items: center;
    }

    .customer-wrapper .customer-info-list .customer-title::before,
    .customer-wrapper .customer-info-list .customer-profession::before,
    .customer-wrapper .customer-info-list .customer-age::before {
      content: "";
      position: absolute;
      height: 100%;
      aspect-ratio: 28 / 62;
      left: 0;
      top: 0;
      transform: translateX(-100%);
      background: url(./images/left-tip.png) no-repeat center center/contain;
    }

    .left-btn,
    .right-btn {
      background-color: #c0c0c0;
      width: 2.25rem;
      aspect-ratio: 1 / 1;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      cursor: pointer;
      position: absolute;
      top: 36%;
    }

    @media (max-width: 576px) {
      .left-btn,
      .right-btn {
        display: none;
      }
    }

    .left-btn:hover,
    .right-btn:hover {
      background-color: #006dff;
    }

    .right-btn {
      right: -3.25rem;
    }

    @media (max-width: 768px) {
      .right-btn {
        right: 1.55rem;
      }
    }

    .left-btn {
      left: -3.25rem;
    }

    @media (max-width: 768px) {
      .left-btn {
        left: 1.55rem;
      }
    }
  }

  .part-count {
    .count-list {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
    }

    @media (max-width: 768px) {
      .count-list {
        flex-wrap: wrap;
        gap: 4px;
      }
    }

    .count-list .count-box {
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    @media (max-width: 768px) {
      .count-list .count-box {
        flex: 1 1 45%;
      }
    }

    .count-list .count-box .count {
      display: flex;
      align-items: top;
      justify-content: center;
      font-weight: 800;
      font-size: 4rem;
      line-height: 130%;
      letter-spacing: 0%;
      text-align: center;
      color: #3e90ff;
    }

    @media (max-width: 1280px) {
      .count-list .count-box .count {
        font-size: 3.5rem;
      }
    }

    .count-list .count-box .count .count-num {
      font-weight: 800;
      font-size: 4rem;
      line-height: 130%;
      letter-spacing: 0%;
      text-align: center;
      color: #3e90ff;
    }

    @media (max-width: 1280px) {
      .count-list .count-box .count .count-num {
        font-size: 3.5rem;
      }
    }

    .count-list .count-box .count .count-plus {
      font-weight: 700;
      font-size: 2.5rem;
      line-height: 130%;
      letter-spacing: 0%;
      text-align: center;
      color: #3e90ff;
    }

    @media (max-width: 1280px) {
      .count-list .count-box .count .count-plus {
        font-size: 2rem;
      }
    }

    .count-list .count-box .count-desc {
      font-weight: 400;
      font-size: 1.5rem;
      line-height: 130%;
      letter-spacing: 0%;
      text-align: center;
      color: #616161;
    }
  }

  .part-bestSwiper {
    @media (min-width: 992px) {
      .swiper-wrapper {
        gap: 1rem;
      }

      .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(25% - 1rem * 3);
      }
    }
    .best-box {
      height: 100%;
      border-radius: 1rem;
      background-color: #fff;
      padding: 2.5rem 2rem;
      overflow: hidden;
      position: relative;
      color: #000;
      .box-download {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 5;
      }
      .download-icon {
        position: absolute;
        right: 1rem;
        top: 1rem;
        width: 1.5rem;
        .active-img {
          display: none;
        }
      }
      .best-icon {
        width: 3.5rem;
        .active-img {
          display: none;
        }
      }
      .best-item-title {
        font-weight: 700;
        font-size: 1.125rem;
        line-height: 100%;
        color: inherit;
        margin-bottom: 6px;
      }
      .best-item-desc {
        font-size: 0.875rem;
        line-height: 100%;
        color: inherit;
        opacity: 0.8;
      }

      @media (any-hover: hover) {
        &:has(.box-download:hover) {
          background-color: #3e90ff;
          color: #fff;
          .best-icon .active-img {
            display: inline-block;
          }
          .best-icon .default-img {
            display: none;
          }
          .download-icon .active-img {
            display: inline-block;
          }
          .download-icon .default-img {
            display: none;
          }
        }
      }
    }
  }

  .part-links {
    .part-links-line {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .line-border {
      border-right: 1px solid rgba(0, 0, 0, 0.3);
      border-left: 1px solid rgba(0, 0, 0, 0.3);
    }

    @media (max-width: 1280px) {
      .line-border {
        border-right: unset;
      }
    }

    @media (max-width: 768px) {
      .line-border {
        border-left: unset;
      }
    }

    .text-link {
      font-size: 0.875rem;
      color: rgba(0, 0, 0, 0.7);
      margin-top: 1.5rem;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .text-link:hover {
      color: #0055fb;
    }

    .part-links-videos {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .part-links-videos .video-wrapper {
      border-radius: 0.75rem;
    }

    @media (max-width: 1280px) {
      .part-links-videos {
        flex-direction: row;
        padding-top: 2rem;
      }
    }

    @media (max-width: 576px) {
      .part-links-videos {
        display: block;
      }
    }

    .text-line4 {
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
  .part-footer .footer-box {
    border-radius: 1rem;
    background: url(https://images.wondershare.com/recoverit/images2025/drfone/footer.jpg) no-repeat center center/cover;
    margin: 0 -2.5rem;
    padding: 3.5rem 1.5rem;
    text-align: center;
  }

  @media (max-width: 768px) {
    .part-footer .footer-box {
      margin: 0;
    }
  }

  @media (max-width: 768px) {
    .part-footer .logo-icon img {
      height: 3rem;
    }
  }

  .part-footer .btn-wrapper .btn-white {
    color: #0196ff !important;
  }
}
