* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  color: #fff;
  background-color: #000;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span {
    margin-bottom: 0;
  }

  h1,
  h2,
  h3 {
    text-align: center;
  }
  h2 {
    font-size: 2.25rem;
    font-weight: 800;
  }

  .opacity-6 {
    opacity: 0.6;
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .blue-text {
    color: #006dff;
  }

  .btn-wrapper {
    display: flex;

    justify-content: center;
    gap: 1rem;
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
    }
    .btn {
      margin: 0;
      border-radius: 4px;
      text-transform: capitalize;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 160px;
      @media (max-width: 768px) {
        display: block;
        vertical-align: baseline;
      }
    }
  }

  .btn-outline-secondary {
    color: #349eff;
    border-color: #349eff;
  }

  .btn-outline-secondary:hover {
    color: #fff;
    background-color: #349eff;
    border-color: #349eff;
  }

  .btn-download {
    background: linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%);
    border: none;
    color: #fff;
    transition: unset;
    text-transform: capitalize;
    gap: 0.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #fff;
      background: #0085ff;
    }
  }

  .btn-white {
    color: #000;
    &:hover,
    &:focus,
    &:active {
      background: #006dff;
      color: #fff;
      border-color: #006dff;
    }
  }

  .part-banner {
    position: relative;
    .banner-top-img {
      @media (max-width: 768px) {
        height: 212px;
        margin-bottom: 1.5rem;
        object-fit: cover;
        width: auto;
      }
    }
  }

  @media (min-width: 992px) {
    .part-banner .btn {
      height: 48px;
    }
  }

  .part-banner .wsc-icon {
    height: 1.125rem;
  }
  .part-banner .content-wrapper {
    text-align: center;
    margin-top: -2.5rem;
  }

  .part-banner .content-wrapper h1 {
    text-align: center;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 20.83%, #ffffff 84.03%), linear-gradient(90.99deg, #3fa8ff 1.8%, #4bf3ff 99.78%);
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    font-weight: 900;
    font-size: 4rem;
    @media (max-width: 768px) {
      font-size: 3rem;
    }
  }

  .part-banner .content-wrapper .btn-wrapper .btn-white {
    min-width: 11.5rem;
  }

  .part-banner .content-wrapper .btn-wrapper .btn-download {
    min-width: 11.5rem;
  }

  .part-banner .content-wrapper .download-list {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #787878;
  }

  .part-banner .content-wrapper .download-list a {
    color: inherit;
    text-decoration: none;
  }

  .part-banner .content-wrapper .download-list a:hover {
    color: #fff;
  }

  .part-company {
    text-align: center;
  }

  @keyframes marquee {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-50%);
    }
  }

  .part-company .logo-list {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
    justify-content: center;
  }

  @media (max-width: 992px) {
    .part-company .logo-list {
      width: fit-content;
      animation: marquee 30s linear infinite;
    }
  }

  .part-company .logo-list .logo-item {
    flex: 1 1 auto;
    height: auto;
  }

  @media (max-width: 992px) {
    .part-company .logo-list .logo-item {
      height: 32px;
      flex: 0 0 auto;
    }
  }

  .part-company .logo-list .logo-item img {
    width: auto;
    max-width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .part-video {
    background: url(https://images.wondershare.com/recoverit/images2025/GoPro/mountain.jpg) no-repeat center bottom / cover;
    min-height: 818px;
    @media (max-width: 1280px) {
      min-height: unset;
      padding-bottom: 3rem;
    }
    .video-wrapper {
      position: relative;
      overflow: hidden;
      border-radius: 1rem;
      line-height: 0;
      font-size: 0;
      cursor: pointer;
    }

    .video-wrapper video {
      width: 100%;
      height: auto;
      max-width: 100%;
    }

    .video-wrapper .video-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      top: 0;
      left: 0;
      pointer-events: none;
    }

    .video-wrapper .video-play {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .part-scenarios {
    .scenarios-box {
      position: relative;
      background: url(https://images.wondershare.com/recoverit/images2025/GoPro/scenarios-bg.jpg) no-repeat center center/cover;
      overflow: hidden;
      border-radius: 1rem;
      overflow: hidden;
    }

    .scenarios-box .type-list-mobile {
      display: flex;
      gap: 4px;
      align-items: center;
      width: fit-content;
      overflow-x: scroll;
      padding: 0 16px;
    }

    .scenarios-box .type-list-mobile .type-tab-mobile {
      padding: 10px 16px;
      border-radius: 8px;
      background-color: #fff;
      border: 1px solid #fff;
      color: #8babc8;
      font-weight: 800;
      flex-shrink: 0;
      white-space: nowrap;
      cursor: pointer;
    }

    .scenarios-box .type-list-mobile .type-tab-mobile.active {
      border: 1px solid #3ca2ff;
      color: #0085ff;
    }

    .scenarios-box .type-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      height: 100%;
      position: absolute;
      height: 120%;
      top: -10%;
      transition: all 0.5s linear;
    }

    @media (max-width: 1280px) {
      .scenarios-box .type-list {
        display: none;
      }
    }

    .scenarios-box .type-list.left {
      left: 40%;
    }

    .scenarios-box .type-list.left.active {
      left: 20%;
    }

    .scenarios-box .type-list.left.active .type-item-tab {
      top: unset !important;
      bottom: unset !important;
    }

    .scenarios-box .type-list.right {
      right: 40%;
    }

    .scenarios-box .type-list.right.active {
      right: 20%;
    }

    .scenarios-box .type-list.right.active .type-item-tab {
      top: unset !important;
      bottom: unset !important;
    }

    @media (max-width: 992px) {
      .scenarios-box .type-list {
        flex-direction: row;
      }
    }

    .scenarios-box .type-list .type-item-tab {
      border-radius: 10000px;
      aspect-ratio: 1/1;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      cursor: pointer;
      font-weight: 800;
      overflow: hidden;
      color: #8babc8;
      width: 116px;
      text-align: center;
      position: relative;
      padding: 10px;
      transition: bottom 0.5s linear, top 0.5s linear;
    }

    .scenarios-box .type-list .type-item-tab[data-index="0"] {
      right: -75%;
      top: 33%;
    }

    .scenarios-box .type-list .type-item-tab[data-index="2"] {
      right: -75%;
      bottom: 33%;
    }

    .scenarios-box .type-list .type-item-tab[data-index="3"] {
      left: -75%;
      top: 33%;
    }

    .scenarios-box .type-list .type-item-tab[data-index="5"] {
      left: -75%;
      bottom: 33%;
    }

    .scenarios-box .type-list .type-item-tab.active {
      border: 2px solid #3ca2ff;
      color: #0085ff;
    }

    .scenarios-box .type-list .type-item-tab img {
      width: 40%;
    }

    .scenarios-box .blue-cricle {
      position: relative;
      transform: scale(1.8);
      z-index: 2;
      pointer-events: none;
    }

    @keyframes wave {
      0% {
        transform: scale(0.8);
        opacity: 1;
      }

      100% {
        transform: scale(1.5);
        opacity: 0;
      }
    }

    .scenarios-box .wave {
      position: absolute;
      left: 0%;
      top: 0%;
      width: 100%;
      padding-bottom: 100%;
      border-radius: 50%;
      border: 3px solid rgba(255, 255, 255, 0.4);
      animation: wave 4s linear infinite;
    }

    .scenarios-box .wave.wave2 {
      animation-delay: 1s;
    }

    .scenarios-box .wave.wave3 {
      animation-delay: 2s;
    }

    .scenarios-box .wave.wave4 {
      animation-delay: 3s;
    }

    .scenarios-box .computer-content {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      padding: 1rem;
      z-index: 5;
      transform: translate(-50%, -50%);
      color: #fff;
      text-align: center;
    }

    .scenarios-box .computer-content .email-icon {
      width: 6rem;
    }

    @media (max-width: 1280px) {
      .scenarios-box .computer-content {
        all: initial;
      }
    }

    .scenarios-box .computer-content .type-item {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 81.79%;
      margin: 0 auto;
    }

    @media (max-width: 1280px) {
      .scenarios-box .computer-content .type-item {
        all: inherit;
        height: 100%;
        border-radius: 1rem;
        background: linear-gradient(146.6deg, #0070ff 24.12%, #98d8ff 85.64%);
        overflow: hidden;
        padding: 32px 16px;
        color: #fff;
        text-align: center;
      }
    }
  }

  .part-tool {
    @keyframes banner-diffuse1 {
      0% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0.1;
      }

      60% {
        transform: translate(-50%, -50%) scale(0.7);
        opacity: 0.5;
      }

      100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
      }
    }

    @keyframes banner-diffuse2 {
      0% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0.1;
      }

      60% {
        transform: translate(-50%, -50%) scale(0.9);
        opacity: 0.5;
      }

      100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
      }
    }

    .tool-box {
      border-radius: 1rem;
      background: url(https://images.wondershare.com/recoverit/images2025/GoPro/part-tool-bg.jpg) no-repeat center center/cover;
      overflow: hidden;
      @media (max-width: 1280px) {
        padding: 0 1rem;
      }
    }

    .tool-box ul li {
      list-style: none;
      position: relative;
      padding-left: 1rem;
    }

    .tool-box ul li::before {
      content: "•";
      position: absolute;
      left: 4px;
      top: -2px;
    }

    .img-wrapper {
      position: relative;
      overflow: hidden;
    }

    .img-wrapper .email-icon-wrapper:hover .email-icon {
      transform: scale(1.2);
    }

    .img-wrapper .email-icon {
      position: absolute;
      width: 18.4%;
      transition: all 0.2s linear;
    }

    .img-wrapper .email-icon-1 {
      right: 8%;
      bottom: 60%;
    }

    .img-wrapper .email-icon-2 {
      right: 10%;
      bottom: 49%;
    }

    .img-wrapper .email-icon-3 {
      right: 12%;
      bottom: 38%;
    }

    .img-wrapper .email-icon-4 {
      right: 10%;
      bottom: 27%;
    }

    .img-wrapper .email-icon-5 {
      right: 8%;
      bottom: 17%;
    }

    .img-wrapper .email-icon-6 {
      right: 6%;
      bottom: 7%;
    }

    .part-banner-wave-icon-box {
      position: absolute;
      bottom: 8%;
      left: 9%;
      width: 11%;
    }

    @media (max-width: 1600px) {
      .part-banner-wave-icon-box {
        width: 14%;
      }
    }

    @media (max-width: 576px) {
      .part-banner-wave-icon-box {
        width: 17%;
      }
    }

    .part-banner-wave-icon-box .download-content {
      position: absolute;
      bottom: 8%;
      width: 100%;
    }

    .part-banner-wave-icon-box .download-content .num {
      color: #006dff;
      font-weight: 700;
      font-size: 14px;
      text-align: center;
      line-height: 100%;
    }

    @media (max-width: 576px) {
      .part-banner-wave-icon-box .download-content .num {
        font-size: 10px;
      }
    }

    .part-banner-wave-icon-box .download-content .text {
      text-align: center;
      color: #416fb4;
      font-size: 12px;
      line-height: 100%;
    }

    @media (max-width: 576px) {
      .part-banner-wave-icon-box .download-content .text {
        font-size: 10px;
      }
    }

    .part-banner-wave-icon-box .wave1 {
      width: 130%;
      aspect-ratio: 72 / 74;
      border-radius: 20%;
      border: 3px solid #fff;
      z-index: 1;
      opacity: 0.8;
      backdrop-filter: blur(6px);
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      animation: banner-diffuse1 2s linear infinite;
    }

    .part-banner-wave-icon-box .wave2 {
      width: 160%;
      aspect-ratio: 72 / 74;
      border-radius: 20%;
      border: 3px solid #fff;
      z-index: 1;
      opacity: 0.8;
      backdrop-filter: blur(6px);
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      animation: banner-diffuse1 2s linear infinite;
    }
  }

  .part-gopro {
    .gopro-card {
      border-radius: 0.75rem;
      background-color: #14181d;
      padding: 1rem 1.5rem 1rem;
      overflow: hidden;
      text-align: center;
      color: #fff;
      height: 100%;
      .gopro-name {
        margin-top: 0.5rem;
      }
    }
    .swiper-pagination-bullet {
      background-color: #fff;
      opacity: 0.5;
      &.swiper-pagination-bullet-active {
        background-color: #007aff;
        opacity: 1;
      }
    }
  }

  .part-everyone {
    @media (min-width: 1280px) {
      .swiper-wrapper {
        display: flex;
        gap: 0.5rem;
        flex-wrap: nowrap;
        aspect-ratio: 1410 / 640;
        justify-content: space-between;
      }
      .swiper-slide {
        width: 11.92%;
        transition: all 0.4s ease-in-out;
        height: 100%;
        overflow: hidden;
        &.active {
          width: 37.59%;
          .content-wrapper {
            padding: 2rem;
            .sport-desc {
              display: block;
            }
          }
        }
      }
    }
    .swiper-pagination-bullet {
      background-color: #fff;
      opacity: 0.5;
      &.swiper-pagination-bullet-active {
        background-color: #007aff;
        opacity: 1;
      }
    }
    .user-card {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      border-radius: 1rem;
      overflow: hidden;
      @media (max-width: 1280px) {
        aspect-ratio: 530 / 640;
        width: 100%;
      }
      &::after {
        position: absolute;
        content: "";
        left: 0;
        top: 0;
        z-index: 1;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 60%) no-repeat center bottom/ 100% 50%;
        width: 102%;
        height: 102%;
      }

      &.card-surf {
        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/surf.jpg);
      }
      &.card-snowboard {
        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/snowboard.jpg);
      }
      &.card-ski {
        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/ski.jpg);
      }
      &.card-bike {
        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/bike.jpg);
      }
      &.card-hiking {
        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/hiking.jpg);
      }
      &.card-diving {
        background-image: url(https://images.wondershare.com/recoverit/images2025/GoPro/diving.jpg);
      }

      .content-wrapper {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2;
        max-height: 400px;
        overflow: hidden;
        padding: 2rem 0.75rem;

        .sport-name {
          font-weight: 800;
          font-size: 1.5rem;
          color: #fff;
          margin-bottom: 1rem;
          @media (max-width: 1600px) {
            font-size: 1.25rem;
          }
        }
        .sport-desc {
          font-size: 0.875rem;
          opacity: 0.7;
          display: none;
          @media (max-width: 1280px) {
            display: block;
          }
        }
      }
    }
    .sd-wrapper {
      border-radius: 1rem;
      overflow: hidden;
      background: linear-gradient(0deg, #23262a, #23262a), linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
      padding-left: 1.5rem;
      padding-right: 1.5rem;
      .sd-list {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 1rem;
        gap: 1.875rem;
        max-width: 1120px;
        margin: 0 auto;
        @media (max-width: 768px) {
          flex-wrap: wrap;
        }
        .sd-item {
          flex: 1 1 calc(25% - 1.875rem);
          @media (max-width: 768px) {
            flex: 1 1 calc(50% - 1.875rem);
          }
        }
      }
    }
  }

  .part-logos {
    background-color: #f2fbff;

    @media (max-width: 1280px) {
      background: url(https://images.wondershare.com/recoverit/images2025/email/logo-bg.png) no-repeat center center/cover;
      background-color: #f2fbff;
    }

    .logo-box .logo-content {
      position: absolute;
      max-width: 570px;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      z-index: 4;
      color: #000;
    }

    @media (max-width: 1280px) {
      .logo-box .logo-content {
        position: relative;
      }
    }

    @media (max-width: 1280px) {
      .logo-box .logos-bg {
        display: none;
      }
    }

    .logo-box .logos-icon {
      transition: all 1.5s linear;
    }

    @media (max-width: 1280px) {
      .logo-box .logos-icon {
        display: none;
      }
    }

    .logo-box .top-logos {
      position: absolute;
      top: 1%;
      left: 33%;
      transform: scale(0.8);
      transform-origin: bottom center;
      pointer-events: none;
      width: 41%;
    }

    .logo-box .top-logos.active {
      bottom: 0;
      top: -8%;
      left: 33%;
      pointer-events: none;
      transform: unset;
    }

    .logo-box .left-logos {
      position: absolute;
      bottom: 0;
      left: 8%;
      pointer-events: none;
      transform: scale(0.8);
      width: 19%;
    }

    .logo-box .left-logos.active {
      bottom: 0;
      left: 6%;
      pointer-events: none;
      transform: unset;
    }

    .logo-box .right-logos {
      position: absolute;
      right: 5%;
      bottom: 0;
      pointer-events: none;
      transform: scale(0.8);
      width: 19%;
    }

    .logo-box .right-logos.active {
      position: absolute;
      right: 4%;
      bottom: 0;
      transform: unset;
      pointer-events: none;
    }

    @keyframes ToRight {
      0% {
        transform: translate3d(0, 0, 0);
      }

      100% {
        transform: translate3d(-50%, 0, 0);
        -webkit-transform: translate3d(-50%, 0, 0);
        -moz-transform: translate3d(-50%, 0, 0);
        -ms-transform: translate3d(-50%, 0, 0);
        -o-transform: translate3d(-50%, 0, 0);
      }
    }

    .logo-list-mobile {
      display: none;
    }

    @media (max-width: 1280px) {
      .logo-list-mobile {
        display: flex;
        flex-wrap: nowrap;
        width: fit-content;
        align-items: center;
        gap: 16px;
        animation: ToRight 60s linear infinite;
        padding-bottom: 2rem;
      }

      .logo-list-mobile img {
        width: 56px;
      }
    }
  }

  .part-table {
    background-color: #f2fbff;
    color: #000;
    .table-box {
      @media (max-width: 1280px) {
        overflow: auto;
        overflow-y: hidden;
      }
    }
    .table-wrapper {
      /* --- 1. 定制化变量 --- */
      --table-theme-color: #cdeeff; /* 主要主题色，用于高亮列 */
      --table-theme-color-border: #ace5ff; /* 边框和外框颜色 */
      --table-side-col-bg: #f2fbff; /* 侧边和头部非高亮背景色 */
      --table-main-bg: #f2fbff; /* 表格主体背景色 */
      --table-text-primary: #000; /* 主要文字颜色 */
      --table-text-secondary: #000; /* 次要文字颜色 (高亮列上) */
      --table-border-radius: 8px; /* 表格圆角大小 */
      --table-decorator-height: 1.5rem; /* 高亮列顶部和底部的装饰条高度 */

      /* --- 2. 基础布局和容器 --- */
      border-radius: var(--table-border-radius);
      width: 100%;
      position: relative;
      overflow: visible;
      background-color: var(--table-theme-color-border); /* 用背景色模拟外边框 */
      padding: 1.2px; /* 关键：让背景色显示为1px的边框 */
      margin-top: 3.5rem;
      margin-bottom: 6.25rem;
      min-width: 928px;
      @media (max-width: 576px) {
        min-width: 728px;
      }
    }

    .inner-table {
      border-collapse: collapse; /* 合并边框 */
      border-style: hidden; /* 隐藏表格默认边框，由wrapper接管 */
      width: 100%;
      background: var(--table-main-bg);
      border-radius: var(--table-border-radius);
      overflow: auto; /* 必须设置，以显示高亮列的装饰条 */
    }

    /* --- 3. 单元格通用样式 --- */
    .inner-table th,
    .inner-table td {
      position: relative; /* 为伪元素定位提供基准 */
      padding: 1.5rem 2rem;
      text-align: center;
      vertical-align: middle;
      width: calc(100% / 6); /* 平均分配列宽，6是总列数 */

      @media (max-width: 1600px) {
        padding: 1rem 8px;
      }
    }

    .inner-table th {
      font-weight: 700;
      font-size: 1rem;
      color: var(--table-text-primary);
      background-color: var(--table-side-col-bg);
    }

    .inner-table td {
      font-size: 1rem;
      @media (max-width: 1280px) {
        font-size: 12px;
      }
    }

    /* 行标题列 (第一列) 的特殊样式 */
    .inner-table td:first-child {
      background-color: var(--table-side-col-bg);
      font-size: 1rem;
      font-weight: 700;
      color: var(--table-text-primary);
      padding: 1.5rem;
      @media (max-width: 1280px) {
        font-size: 12px;
        padding: 1rem 8px;
      }
    }

    /* --- 4. 高亮列样式 --- */
    /* 这是核心！通过添加 .highlight-col 类来指定高亮列 */
    .inner-table .highlight-col {
      background-color: var(--table-theme-color);
      color: var(--table-text-secondary);
      @media (max-width: 1280px) {
        min-width: 10.75rem;
      }
    }

    /* 高亮列顶部的装饰条 */
    .highlight-col-top::before {
      content: "";
      position: absolute;
      top: 0;
      left: -1px;
      width: calc(100% + 2px);
      height: var(--table-decorator-height);
      background-color: var(--table-theme-color);
      border-radius: 0.5rem 0.5rem 0 0;
      transform: translateY(-98%);
    }

    /* 高亮列底部的装饰条 */
    .highlight-col-bottom {
      .download-item {
        position: absolute;
        bottom: 0;
        left: -1px;
        width: calc(100% + 2px);
        padding: 1.5rem 0;
        background-color: var(--table-theme-color);
        border-radius: 0 0 0.5rem 0.5rem;
        transform: translateY(98%);
      }
    }

    /* --- 5. 边框样式 --- */
    /* 垂直边框 */
    .inner-table th:not(:last-child),
    .inner-table td:not(:last-child) {
      border-right: 1px solid var(--table-theme-color-border);
    }

    /* 水平边框 (非高亮列) */
    .inner-table th:not(.highlight-col)::after,
    .inner-table tr:not(:last-child) td:not(.highlight-col)::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1.1px;
      background-color: var(--table-theme-color-border);
    }

    /* 水平边框 (高亮列)，颜色稍浅 */
    .inner-table th.highlight-col::after,
    .inner-table tr:not(:last-child) td.highlight-col::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 2rem;
      right: 2rem;
      height: 1.1px;
      background-color: #8ed9ff;
      @media (max-width: 768px) {
        left: 8px;
        right: 8px;
      }
    }

    /* --- 6. 内容对齐和响应式 --- */
    .inner-table td span {
      vertical-align: middle;
      margin-left: 8px;
    }

    /* 移动端响应式调整 */
    @media (max-width: 768px) {
      .inner-table th {
        font-size: 10px;
        padding: 8px 4px;
      }
      .inner-table td {
        font-size: 9px;
        padding: 8px 4px;
      }
      .inner-table td:first-child {
        font-size: 10px;
        padding: 8px;
      }
      .inner-table td img {
        width: 14px;
      }
      .mt-logo {
        width: 60%;
      }
    }

    .blue-tip {
      background-color: #4dacff;
      color: #fff;
      border-radius: 4px;
      padding: 0.1875rem 0.25rem;
      color: #fff;
      font-size: 0.75rem;
      line-height: 100%;
      margin: auto 4px;
      @media (max-width: 576px) {
        font-size: 8px;
      }
    }

    .blue-tip2 {
      background-color: #a8c4dd;
      color: #fff;
      border-radius: 4px;
      padding: 0.1875rem 0.25rem;
      color: #fff;
      font-size: 0.75rem;
      line-height: 100%;
      margin: auto 4px;
      @media (max-width: 576px) {
        font-size: 8px;
      }
    }
  }

  .part-how {
    background-color: #f2fbff;
    margin-bottom: -2px;
  }

  .part-how .nav {
    border-left: 2px solid #c1edff;
    padding-left: 3.75rem;
    height: 100%;
    justify-content: space-between;
    align-items: stretch;
    gap: 1rem;
  }

  .part-how .nav .nav-item {
    height: fit-content;
  }

  .part-how .nav .nav-item h4 {
    font-weight: 900;
    font-size: 1.5rem;
    line-height: 100%;
    color: rgba(0, 0, 0, 0.8);
    position: relative;
  }

  .part-how .nav .nav-item h4 .before-icon {
    position: absolute;
    left: -0.5rem;
    top: 50%;
    transform: translate(-100%, -50%);
    color: inherit;
  }

  .part-how .nav .nav-item h4 .before-icon img {
    width: 1.5rem;
    height: 1.5rem;
    display: inline-block;
  }

  .part-how .nav .nav-item h4 .before-icon img.active-icon {
    display: none;
  }

  .part-how .nav .nav-item .description {
    font-size: 1rem;
    color: rgba(0, 0, 0, 0.8);
  }

  .part-how .nav .nav-item.active {
    position: relative;
  }

  .part-how .nav .nav-item.active:before {
    content: "";
    height: 100%;
    border-left: 2px solid #006dff;
    position: absolute;
    left: -3.75rem;
    top: 0;
  }

  .part-how .nav .nav-item.active h4 {
    color: #006dff;
  }

  .part-how .nav .nav-item.active .description {
    color: #006dff;
  }

  .part-how .nav .nav-item.active .before-icon img.active-icon {
    display: inline-block;
  }

  .part-how .nav .nav-item.active .before-icon img.default-icon {
    display: none;
  }

  .part-faq {
    background-color: #000;
  }

  .part-faq .wsc-icon {
    position: relative;
    height: 1rem;
    line-height: 0.5;
    vertical-align: inherit;
  }

  .part-faq .accordion i {
    transition: 0.2s;
  }

  .part-faq .accordion [aria-expanded="true"] i {
    transform: rotate(180deg);
    color: #1891ff;
  }

  .part-count {
    color: #fff;
  }

  .part-count .count-list {
    display: flex;

    justify-content: center;
    color: #fff;

    gap: 1rem;
    .count-divider {
      width: 1px;
      background-color: rgba($color: #000, $alpha: 0.3);
      margin: 1rem 0;
      height: auto;
      @media (max-width: 768px) {
        display: none;
      }
    }
  }

  @media (max-width: 768px) {
    .part-count .count-list {
      flex-wrap: wrap;
      gap: 4px;
    }
  }

  .part-count .count-list .count-box {
    flex: 1 1 calc(25% - 1rem);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    color: #fff;
  }

  @media (max-width: 768px) {
    .part-count .count-list .count-box {
      flex: 1 1 45%;
      border-radius: 1.125rem;
      min-height: 10rem;
    }
  }

  .part-count .count-list .count-box:not(:last-child)::after {
    content: "";
    width: 1px;
    height: 40%;
    background-color: rgba(255, 255, 255, 0.2);
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  @media (max-width: 768px) {
    .part-count .count-list .count-box:not(:last-child)::after {
      content: unset;
    }
  }

  .part-count .count-list .count-box .count {
    display: flex;
    align-items: top;
    justify-content: center;
    font-weight: 900;
    font-size: 4.5rem;
    line-height: 110%;
    letter-spacing: 0%;
    text-align: center;
  }

  @media (max-width: 1280px) {
    .part-count .count-list .count-box .count {
      font-size: 3.5rem;
    }
  }

  .part-count .count-list .count-box .count .count-num {
    font-weight: 900;
    font-size: 4.5rem;
    line-height: 110%;
    letter-spacing: 0%;
    text-align: center;
  }

  @media (max-width: 1280px) {
    .part-count .count-list .count-box .count .count-num {
      font-size: 3.5rem;
    }
  }

  .part-count .count-list .count-box .count .count-plus {
    font-weight: 1000;
    font-size: 3rem;
    line-height: 130%;
    letter-spacing: 0%;
    text-align: center;
  }

  @media (max-width: 1280px) {
    .part-count .count-list .count-box .count .count-plus {
      font-size: 2rem;
    }
  }

  .part-count .count-list .count-box .count-desc {
    font-weight: 400;
    font-size: 1.5rem;
    line-height: 130%;
    letter-spacing: 0%;
    text-align: center;
    opacity: 0.5;
    color: rgba($color: #fff, $alpha: 0.8);
  }

  .part-best {
    color: #fff;
    @media (min-width: 1280px) {
      #best-swiper .swiper-wrapper {
        gap: 1.875rem;
      }
      #best-swiper .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(25% - 1.875rem);
      }
    }
  }

  .part-best .best-box {
    height: 100%;
    display: flex;
    align-items: flex-start;
    padding: 1.5rem;
    position: relative;
    border: 1px solid #fff;
    border-radius: 1rem;
    gap: 1.25rem;
    text-decoration: none;

    .best-box-link {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
    }
  }

  @media (max-width: 1600px) {
    .part-best .best-box {
      gap: 0.9375rem;
    }
  }

  .part-best .best-box .best-icon {
    width: 3.5rem;
    @media (max-width: 992px) {
      svg {
        width: 2.5rem;
        height: 2.5rem;
      }
    }
  }

  .part-best .best-box .best-icon .active-img {
    display: none;
  }

  .part-best .best-box .best-item-title {
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .part-best .best-box .best-item-desc {
    font-size: 0.875rem;
    color: inherit;
    text-align: left;
    opacity: 0.8;
  }

  .part-best .best-box .download-icon {
    position: absolute;
    right: 1rem;
    top: 1rem;
    width: 1.5rem;
  }

  .part-best .best-box .download-icon .active-img {
    display: none;
  }

  @media (any-hover: hover) {
    .part-best .best-box:hover {
      background-color: #3e90ff;
      color: #fff;
    }

    .part-best .best-box:hover .best-icon .active-img {
      display: inline-block;
    }

    .part-best .best-box:hover .best-icon .default-img {
      display: none;
    }

    .part-best .best-box:hover .download-icon .active-img {
      display: inline-block;
    }

    .part-best .best-box:hover .download-icon .default-img {
      display: none;
    }
  }

  @media (max-width: 992px) {
    .part-best .best-box {
      background-color: #3e90ff;
      color: #fff;
      border: unset;
    }

    .part-best .best-box .best-icon .active-img {
      display: inline-block;
      max-width: 100%;
    }

    .part-best .best-box .best-icon .default-img {
      display: none;
      max-width: 100%;
    }

    .part-best .best-box .download-icon .active-img {
      display: inline-block;
      max-width: 100%;
    }

    .part-best .best-box .download-icon .default-img {
      display: none;
      max-width: 100%;
    }
  }

  .part-drfone {
    background: url(https://images.wondershare.com/recoverit/images2025/GoPro/camera-bg.jpg) no-repeat center center/cover;
    color: #fff;
  }

  @media (max-width: 768px) {
    .part-drfone {
      background: unset;
    }
  }

  .part-drfone .swiper {
    padding: 3rem;
    padding-right: 0.5rem;
    border-radius: 1rem;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(16px);
    overflow: hidden;
    .drfone-content {
      max-height: 690px;
      overflow: auto;
      padding-right: 2.5rem;
      @media (max-width: 576px) {
        max-height: 520px;
      }
      //滚动条样式

      &::-webkit-scrollbar {
        width: 2px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 1.25rem;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 1.25rem;

        &:hover {
          background: rgba(255, 255, 255, 0.5);
        }
      }

      h4 {
        font-size: 1.25rem;
        font-weight: 700;
      }
      .method-content {
        padding-left: 1rem;
        color: #d0d0d0;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding-bottom: 1rem;
        .feature-list {
          display: flex;
          justify-content: center;
          gap: 0.875rem;
          .advantage-box {
            border: 1px solid #7bde4e;
            padding: 0.75rem 1rem;
            background-color: #7bde4e1a;
            border-radius: 0.5rem;
            color: #d0d0d0;
            flex: 1 1 50%;
          }
          .disadvantage-box {
            border: 1px solid #f44949;
            padding: 0.75rem 1rem;
            background: #f449491a;
            border-radius: 0.5rem;
            flex: 1 1 50%;
          }
        }
      }
    }
  }

  @media (max-width: 1600px) {
    .part-drfone .swiper {
      padding: 1.5rem;
      padding-bottom: 2.5rem;
    }
  }

  @media (max-width: 768px) {
    .part-drfone .swiper {
      margin-top: -85%;
      background-color: #292929;
    }
  }

  .part-drfone .swiper .swiper-slide {
    height: auto;
  }

  .part-drfone .swiper .swiper-slide h2 {
    margin-bottom: 2rem;
    text-align: left;
    font-weight: 700;
  }

  .part-drfone .swiper .swiper-slide h4 {
    font-weight: 400;
    position: relative;
  }

  .part-drfone .swiper .swiper-slide h4::before {
    content: attr(data-before);
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-120%, -50%);
  }

  .part-drfone .swiper .swiper-slide p {
    text-align: left;
    color: #d0d0d0;
    font-size: 1.125rem;
  }

  .part-drfone .swiper .drfone-content-2 p {
    color: #d0d0d0;
    position: relative;
    padding-left: 1rem;
  }

  .part-drfone .swiper .drfone-content-2 p::before {
    content: "•";
    position: absolute;
    left: 0;
    top: 15px;
    transform: translate(0, -50%);
  }

  .part-drfone .swiper .swiper-pagination {
    bottom: 1.2rem;
    left: 0;
    text-align: left;
    left: 75px;
    @media (max-width: 768px) {
      left: 8px;
      bottom: 8px;
    }
  }

  .part-drfone .swiper .swiper-pagination .swiper-pagination-bullet {
    width: 20px;
    height: 12px;
    border-radius: 100px;
    background: rgba(121, 178, 255, 0.3);
  }

  .part-drfone .swiper .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: #006dff;
    width: 48px;
  }

  .part-footer .footer-box {
    background: url(https://images.wondershare.com/recoverit/images2025/email/footer-bg.jpg) no-repeat center center/cover;
    border-radius: 1rem;
    overflow: hidden;
    padding: 0 2.25rem;
    color: #fff;
    @media (max-width: 768px) {
      padding: 2rem;
    }
  }

  .part-footer .footer-box .footer-title {
    font-weight: 700;
    font-size: 2rem;
  }

  .part-footer .btn-white {
    color: #349eff;
    &:hover,
    &:focus {
      background-color: #ececec;
      border-color: #e6e6e6;
    }
  }

  .part-footer .btn-outline-white:hover,
  .part-footer .btn-outline-white:focus {
    color: #349eff;
  }
}
