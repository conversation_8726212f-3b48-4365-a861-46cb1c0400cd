import "./index.scss";

$(() => {
  // ================= 配置常量 =================
  const BREAKPOINTS = {
    mobile: 576,
    tablet: 768,
    laptop: 992,
    desktop: 1280,
    largeDesktop: 1600,
  };

  // ================= 状态变量 =================
  const screenWidth = window.innerWidth;
  const isDesktop = screenWidth >= BREAKPOINTS.desktop;
  const isMobile = screenWidth <= BREAKPOINTS.mobile;
  let countUpTriggered = false; // 替代原来的 stepVal

  // ================= 工具函数 =================
  // 节流函数
  // 节流函数：限制函数在指定时间间隔内只能执行一次
  const throttle = (func, delay) => {
    let lastExecTime = 0; // 记录上次执行时间

    return function (...args) {
      const currentTime = Date.now(); // 获取当前时间

      // 如果距离上次执行的时间超过了延迟时间，则执行函数
      if (currentTime - lastExecTime >= delay) {
        func.apply(this, args);
        lastExecTime = currentTime; // 更新上次执行时间
      }
    };
  };

  // 检查元素是否完全在视口内
  const isElementFullyInViewport = (element) => {
    if (!element) return false;
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  };

  // ================= 数字动画初始化 =================
  const initCountUp = () => {
    const handleCountUpScroll = () => {
      const countBox = $(".count-box")[0];
      if (!countUpTriggered && isElementFullyInViewport(countBox)) {
        $(".count-num").countTo();
        countUpTriggered = true;
      }
    };

    $(window).on("scroll", throttle(handleCountUpScroll, 200));
  };

  // ================= Swiper 初始化 =================
  // 解决方案轮播
  const swiperSolutions = new Swiper("#swiper-solutions", {
    slidesPerView: 1.2,
    spaceBetween: 24,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: "#swiper-solutions .swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      [BREAKPOINTS.mobile]: {
        slidesPerView: 2,
        spaceBetween: 24,
      },
      [BREAKPOINTS.laptop]: {
        slidesPerView: 3,
        spaceBetween: 24,
      },
      [BREAKPOINTS.largeDesktop]: {
        slidesPerView: 4,
        spaceBetween: 24,
        autoplay: false,
        loop: false,
      },
    },
  });

  // 步骤轮播
  const swiperStep = new Swiper("#swiper-step", {
    slidesPerView: 1,
    spaceBetween: 24,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: "#swiper-step .swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      [BREAKPOINTS.tablet]: {
        slidesPerView: 2,
        spaceBetween: 24,
      },
      [BREAKPOINTS.desktop]: {
        slidesPerView: 3,
        spaceBetween: 24,
        autoplay: false,
        loop: false,
      },
    },
  });

  // 家长评价轮播
  const swiperParents = new Swiper("#swiper-parents", {
    slidesPerView: 1,
    spaceBetween: 24,
    centeredSlides: true,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
    breakpoints: {
      [BREAKPOINTS.mobile]: {
        slidesPerView: 1.5,
        spaceBetween: 24,
      },
      [BREAKPOINTS.tablet]: {
        slidesPerView: 2,
        spaceBetween: 24,
      },
      [BREAKPOINTS.laptop]: {
        slidesPerView: 1,
        spaceBetween: 24,
      },
      [BREAKPOINTS.largeDesktop]: {
        slidesPerView: 1.2,
        spaceBetween: 24,
      },
    },
  });

  // ================= 特性轮播（特殊处理）=================
  // 基础配置
  const featureSwiperBaseConfig = {
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: "#feature-swiper .swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      [BREAKPOINTS.mobile]: {
        slidesPerView: 2,
        spaceBetween: 15,
      },
      [BREAKPOINTS.laptop]: {
        slidesPerView: 3,
        spaceBetween: 15,
      },
      [BREAKPOINTS.largeDesktop]: {
        slidesPerView: 4,
        spaceBetween: 20,
      },
    },
  };

  // 移动端特殊处理
  let featureTextSwiper; // 声明在外部，便于后续访问
  let featureSwiperConfig; // 最终配置

  if (isMobile) {
    // 文字同步轮播
    featureTextSwiper = new Swiper("#feature-text-mobile-swiper", {
      slidesPerView: 1,
      spaceBetween: 15,
      effect: "fade",
      allowTouchMove: false,
      fadeEffect: { crossFade: true },
    });

    // 创建移动端配置（不修改原对象）
    featureSwiperConfig = {
      ...featureSwiperBaseConfig,
      effect: "creative",
      watchSlidesProgress: true,
      centeredSlides: true,
      slidesPerView: 1.8,
      spaceBetween: -20,
      creativeEffect: {
        prev: {
          shadow: false,
          translate: ["-85%", "5%", 0],
          rotate: [0, 0, -10],
          scale: 0.85,
          opacity: 1,
          origin: "bottom",
        },
        next: {
          shadow: false,
          translate: ["85%", "5%", 0],
          rotate: [0, 0, 10],
          scale: 0.85,
          opacity: 1,
          origin: "bottom",
        },
        limitProgress: 2,
      },
      on: {
        slideChange: function () {
          if (featureTextSwiper) {
            featureTextSwiper.slideTo(this.realIndex, 100, false);
          }
        },
      },
    };
  } else {
    // 桌面端直接使用基础配置
    featureSwiperConfig = featureSwiperBaseConfig;
  }

  // 初始化特性轮播
  const featureSwiper = new Swiper("#feature-swiper", featureSwiperConfig);

  // 桌面端悬停控制
  if (!isMobile) {
    $("#feature-swiper")
      .on("mouseenter", function () {
        featureSwiper.autoplay.stop();
      })
      .on("mouseleave", function () {
        featureSwiper.autoplay.start();
      });
  }

  // ================= 初始化执行 =================
  initCountUp();
});
