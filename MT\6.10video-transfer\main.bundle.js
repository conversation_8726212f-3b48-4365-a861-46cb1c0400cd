/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 787:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

eval("\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\nvar injectStylesIntoStyleTag = __webpack_require__(72);\nvar injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleDomAPI.js\nvar styleDomAPI = __webpack_require__(825);\nvar styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertBySelector.js\nvar insertBySelector = __webpack_require__(659);\nvar insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\nvar setAttributesWithoutAttributes = __webpack_require__(56);\nvar setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertStyleElement.js\nvar insertStyleElement = __webpack_require__(540);\nvar insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleTagTransform.js\nvar styleTagTransform = __webpack_require__(113);\nvar styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);\n// EXTERNAL MODULE: ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/index.scss\nvar cjs_js_src = __webpack_require__(2);\n;// CONCATENATED MODULE: ./src/index.scss\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (styleTagTransform_default());\noptions.setAttributes = (setAttributesWithoutAttributes_default());\noptions.insert = insertBySelector_default().bind(null, \"head\");\noptions.domAPI = (styleDomAPI_default());\noptions.insertStyleElement = (insertStyleElement_default());\n\nvar update = injectStylesIntoStyleTag_default()(cjs_js_src/* default */.A, options);\n\n\n\n\n       /* harmony default export */ const src = (cjs_js_src/* default */.A && cjs_js_src/* default */.A.locals ? cjs_js_src/* default */.A.locals : undefined);\n\n;// CONCATENATED MODULE: ./src/index.js\n\n$(() => {\n  const swiperScenarios = new Swiper(\"#swiper-scenarios\", {\n    slidesPerView: 1,\n    spaceBetween: 30,\n    loop: true,\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false\n    },\n    pagination: {\n      el: \".swiper-pagination\",\n      clickable: true\n    },\n    on: {\n      slideChange: function () {\n        const currentSlide = this.realIndex;\n        $(\".tab-item\").removeClass(\"active\").eq(currentSlide).addClass(\"active\");\n      }\n    },\n    navigation: {\n      nextEl: \".part-scenarios .right-btn\",\n      prevEl: \".part-scenarios .left-btn\"\n    }\n  });\n  $(\" .tab-item\").on(\"click\", function () {\n    const currentSlide = $(this).index();\n    swiperScenarios.slideToLoop(currentSlide);\n  });\n  $(\".part-solutions .arrow\").on(\"click\", function () {\n    $(\".part-solutions .step-wrapper\").slideToggle();\n    $(this).toggleClass(\"active\");\n    $(\".part-solutions .check-title\").toggleClass(\"d-none\");\n  });\n  $(\".part-solutions .grey-arrow-wrapper\").on(\"click\", function () {\n    $(\".part-solutions .step-wrapper\").slideToggle();\n    $(this).toggleClass(\"active\");\n    $(\".part-solutions .check-title\").toggleClass(\"d-none\");\n  });\n\n  // 切换标签和折叠详情的函数\n  let currentTabIndex = 0;\n  const tabs = [\"#high-speed-tab\", \"#all-major-formats-tab\", \"#no-quality-loss-tab\"];\n  const switchInterval = 3000;\n  $(tabs[0]).tab(\"show\");\n\n  // 切换标签和折叠详情的函数\n  const switchTab = () => {\n    currentTabIndex = (currentTabIndex + 1) % tabs.length;\n    $(tabs[currentTabIndex]).tab(\"show\");\n    $(tabs[currentTabIndex].replace(\"-tab\", \"-collapse\")).collapse(\"show\");\n  };\n  if (window.innerWidth > 768) {\n    // 设置自动切换定时器\n    let autoSwitchInterval = setInterval(switchTab, switchInterval);\n\n    // 用户手动点击标签时重置计时器\n    $(\".nav-item\").on(\"click\", function () {\n      clearInterval(autoSwitchInterval);\n      const clickedTabId = $(this).attr(\"id\");\n      currentTabIndex = Math.max(0, tabs.indexOf(`#${clickedTabId}`));\n      autoSwitchInterval = setInterval(switchTab, switchInterval);\n    });\n  }\n  if (window.innerWidth < 1280) {\n    const methodsSwiper = new Swiper(\"#swiper-methods\", {\n      slidesPerView: 1.0,\n      // centeredSlides: true,\n      spaceBetween: 30,\n      loop: true,\n      loopedSlides: 2,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      breakpoints: {\n        992: {\n          slidesPerView: 3,\n          spaceBetween: 15\n        },\n        576: {\n          slidesPerView: 2,\n          spaceBetween: 15\n        }\n      },\n      pagination: {\n        el: \"#swiper-methods .swiper-pagination\",\n        clickable: true\n      }\n    });\n    const featuresSwiper = new Swiper(\"#swiper-features\", {\n      slidesPerView: 1.0,\n      // centeredSlides: true,\n      spaceBetween: 30,\n      loop: true,\n      loopedSlides: 2,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      breakpoints: {\n        992: {\n          slidesPerView: 3,\n          spaceBetween: 15\n        },\n        768: {\n          slidesPerView: 2,\n          spaceBetween: 15\n        }\n      },\n      pagination: {\n        el: \"#swiper-features .swiper-pagination\",\n        clickable: true\n      }\n    });\n    const scrollCardSwiper = new Swiper(\"#swiper-scrollCard\", {\n      slidesPerView: 1.0,\n      // centeredSlides: true,\n      spaceBetween: 30,\n      loop: true,\n      loopedSlides: 2,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      breakpoints: {\n        768: {\n          slidesPerView: 2,\n          spaceBetween: 15\n        }\n      },\n      pagination: {\n        el: \"#swiper-scrollCard .swiper-pagination\",\n        clickable: true\n      }\n    });\n  }\n  $(\".part-methods .step-btn\").on(\"click\", function () {\n    $(\".part-methods .step-detail\").slideToggle();\n    $(\".part-methods .step-btn\").toggleClass(\"active\");\n  });\n\n  // 监听数字滚动部分是否可见\n  function isElementFullyInViewport(el) {\n    var rect = el.getBoundingClientRect();\n    var windowHeight = window.innerHeight || document.documentElement.clientHeight;\n    var windowWidth = window.innerWidth || document.documentElement.clientWidth;\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= windowHeight && rect.right <= windowWidth;\n  }\n  var stepVal = true;\n  function formatWithComma(value, options) {\n    return value.toLocaleString(); // 使用内建的 toLocaleString() 方法来添加逗号\n  }\n  function handleScroll() {\n    var myElement = $(\".growth-numbers-item\")[0]; // 获取DOM元素\n    if (myElement && isElementFullyInViewport(myElement) && stepVal) {\n      $(\".count-num\").countTo({\n        from: 0,\n        formatter: formatWithComma\n      });\n      stepVal = false;\n    }\n  }\n  // 使用防抖优化滚动事件\n  var scrollTimeout;\n  $(window).on(\"scroll\", function () {\n    clearTimeout(scrollTimeout);\n    scrollTimeout = setTimeout(handleScroll, 100);\n  });\n  if (window.innerWidth > 1280) {\n    gsap.registerPlugin(ScrollTrigger);\n    let cards = gsap.utils.toArray(\".scrollCard\");\n\n    // 预加载所有图片\n    const preloadImages = () => {\n      const images = document.querySelectorAll(\".scrollCard img\");\n      const imagePromises = Array.from(images).map(img => {\n        return new Promise((resolve, reject) => {\n          if (img.complete) {\n            resolve();\n          } else {\n            img.onload = resolve;\n            img.onerror = reject;\n          }\n        });\n      });\n      return Promise.all(imagePromises);\n    };\n\n    // 等待所有图片加载完成后再初始化动画\n    preloadImages().then(() => {\n      let stackHeight = window.innerHeight * 0.1;\n      cards.forEach((card, i) => {\n        if (i !== cards.length - 1) {\n          gsap.fromTo(card, {\n            opacity: 1\n          }, {\n            scale: 0.7,\n            opacity: 0,\n            ease: \"power1.out\",\n            scrollTrigger: {\n              trigger: card,\n              pin: true,\n              // markers: true,\n              scrub: 0.5,\n              start: \"top \" + stackHeight,\n              end: \"top \" + stackHeight,\n              pinSpacing: false,\n              endTrigger: \".last-card\",\n              invalidateOnRefresh: true\n            }\n          });\n        }\n      });\n    }).catch(error => {\n      console.error(\"图片加载失败:\", error);\n    });\n    window.addEventListener(\"resize\", function () {\n      ScrollTrigger.refresh(); // 重新计算动画的触发点和位置\n    });\n    // 添加ResizeObserver来监听页面高度变化\n    const resizeObserver = new ResizeObserver(entries => {\n      // 使用防抖，避免频繁触发\n      clearTimeout(window.resizeTimeout);\n      window.resizeTimeout = setTimeout(() => {\n        ScrollTrigger.refresh();\n        console.log(\"resize\");\n      }, 500);\n    });\n\n    // 监听整个文档的高度变化\n    resizeObserver.observe(document.documentElement);\n\n    // 监听body的高度变化\n    resizeObserver.observe(document.body);\n\n    // 在组件销毁时断开观察\n    $(window).on(\"unload\", () => {\n      resizeObserver.disconnect();\n    });\n  }\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///787\n");

/***/ }),

/***/ 2:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(354);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(314);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `*{margin:0;padding:0;box-sizing:border-box}main{background-color:#fff;color:#000}@media(max-width: 1280px){main{background-color:#f4f7ff}}main h1,main h2,main h3,main h4,main h5,main h6,main p,main div,main span,main ul,main li{margin-bottom:0}main h2{text-align:center;font-weight:800;font-size:2.25rem}@media(max-width: 576px){main .display-3{font-size:2.5rem}}main .opacity-7{opacity:.7}main .text-blue{color:#046fff}main .text-blue2{color:#046fff}main .text-blue3{color:#3b8eff}main .btn-wrapper{display:flex;gap:1rem}@media(max-width: 768px){main .btn-wrapper{flex-direction:column;gap:8px;align-items:center}}main .btn{margin:0;display:flex;align-items:center;justify-content:center;text-transform:capitalize;gap:8px}main .btn svg{max-width:100%;height:100%}@media(max-width: 768px){main .btn{display:block}}main .btn.dev-mobile{width:168.75px;min-width:unset !important}main .btn-action{border-radius:.5rem}main .btn-download{border:1px solid #fff;background:linear-gradient(89.57deg, #00c8ff -10.59%, #0084ff 15.01%, #006fff 83.38%, #00c8ff 107.75%);box-shadow:0px 4.5px 6.97px 0px rgba(255,255,255,.431372549) inset,0px -6.75px 16.65px 0px rgba(0,229,255,.8392156863) inset,0px 4.5px 13.84px 0px rgba(0,89,255,.2509803922);display:flex;align-items:center;justify-content:center;gap:.5rem;text-transform:capitalize;border-radius:.875rem;color:#fff;min-width:220px;overflow:hidden;position:relative}main .btn-download:focus,main .btn-download:active{color:#fff}@media(min-width: 992px){main .btn-download{height:3.5rem}main .btn-download.btn-lg{height:4rem}}main .btn-download .btn-text-wrap{position:relative;overflow:hidden;color:inherit}main .btn-download .btn-text-wrap .btn-hover-text-wrap{transition:transform .4s ease-in-out;display:flex;align-items:center;justify-content:center;gap:.5rem;color:inherit}main .btn-download .btn-text-wrap .btn-hover-text-wrap.rel{position:relative;transform:translateY(0)}main .btn-download .btn-text-wrap .btn-hover-text-wrap.abs{position:absolute;top:120%;transform:translateY(0);transition-duration:.45s}@media(any-hover: hover){main .btn-download:hover{color:#fff}main .btn-download:hover .btn-hover-text-wrap.rel{color:inherit;transform:translateY(-100%)}main .btn-download:hover .btn-hover-text-wrap.abs{color:inherit;transform:translateY(-120%)}}@keyframes marquee1{0%{transform:translateX(0)}100%{transform:translateX(-50%)}}@keyframes marquee2{0%{transform:translateX(-50%)}100%{transform:translateX(0%)}}@keyframes marquee1-vertical{0%{transform:translateY(0)}100%{transform:translateY(-50%)}}@keyframes marquee2-vertical{0%{transform:translateY(-50%)}100%{transform:translateY(0%)}}main .qr-code-icon-wrapper{position:relative;z-index:5}@media(max-width: 1280px){main .qr-code-icon-wrapper{display:none}}main .qr-code-icon-wrapper .active-icon{display:none}main .qr-code-icon-wrapper .qrcode-box{width:max-content;position:absolute;top:-8px;max-width:128px;left:50%;transform:translate(-50%, -100%);transition:opacity .2s ease-in-out;opacity:0;pointer-events:none}main .qr-code-icon-wrapper:hover .active-icon{display:inline-block}main .qr-code-icon-wrapper:hover .default-icon{display:none}main .qr-code-icon-wrapper:hover .qrcode-box{opacity:1}main .swiper-pagination .swiper-pagination-bullet{width:10px;height:10px;border-radius:100px;background-color:rgba(0,109,255,.3);opacity:1}main .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active{width:40px;border-radius:100px;opacity:1;background-color:rgba(0,109,255,.7)}main .system-list{display:flex;align-items:center;gap:12px;margin-top:1.5rem;color:#b2b2b2}@media(max-width: 1280px){main .system-list{justify-content:center}}main .system-list .qrcode-box{width:max-content;position:absolute;bottom:-8px;max-width:128px;left:50%;transform:translate(-50%, 100%) rotate(180deg);transition:opacity .2s ease-in-out;opacity:0;pointer-events:none}main .system-list a{color:#b2b2b2;text-decoration:none;position:relative}@media(any-hover: hover){main .system-list a:hover{color:#000}main .system-list a:hover .qrcode-box{opacity:1}}main .part-banner{background:linear-gradient(294.17deg, rgba(255, 255, 255, 0) 59.67%, rgba(99, 255, 204, 0.2) 99.14%),linear-gradient(50.88deg, #ffffff 56.75%, #bde2ff 88.21%)}main .part-banner .btn{border-radius:.75rem}main .part-banner .btn-action{background-color:#1da4ff;border-color:#1da4ff;color:#fff}main .part-banner .btn-action:hover{color:#fff;background-color:#005dd9;border-color:#0057cc}main .part-banner .banner-img-wrapper{position:relative}@keyframes banner-diffuse1{0%{transform:translate(-50%, -50%) scale(0.2) rotate(4.5deg);opacity:.1}60%{transform:translate(-50%, -50%) scale(0.7) rotate(4.5deg);opacity:.5}100%{transform:translate(-50%, -50%) scale(1) rotate(4.5deg);opacity:0}}@keyframes banner-diffuse2{0%{transform:translate(-50%, -50%) scale(0.2) rotate(4.5deg);opacity:.1}60%{transform:translate(-50%, -50%) scale(0.9) rotate(4.5deg);opacity:.5}100%{transform:translate(-50%, -50%) scale(1) rotate(4.5deg);opacity:0}}main .part-banner .banner-img-wrapper .part-wave-icon-box{position:absolute;right:36%;top:29%;width:16%}main .part-banner .banner-img-wrapper .part-wave-icon-box .hover-icon{display:none}@media(any-hover: hover){main .part-banner .banner-img-wrapper .part-wave-icon-box:hover .hover-icon{display:block}main .part-banner .banner-img-wrapper .part-wave-icon-box:hover .default-icon{display:none}}main .part-banner .banner-img-wrapper .part-wave-icon-box .wave1{width:115%;aspect-ratio:120/80;border-radius:43%;border:3px solid #fff;z-index:1;backdrop-filter:blur(6px);position:absolute;left:50%;top:50%;transform:translate(-50%, -50%) scale(0) rotate(-4.5deg);animation:banner-diffuse1 2s linear infinite}main .part-banner .banner-img-wrapper .part-wave-icon-box .wave2{width:130%;aspect-ratio:120/80;border-radius:43%;border:3px solid #fff;z-index:1;backdrop-filter:blur(6px);position:absolute;left:50%;top:50%;transform:translate(-50%, -50%) scale(0) rotate(-4.5deg);animation:banner-diffuse1 2s linear infinite}main .part-banner .banner-img-wrapper .part-wave-icon-box .wave3{width:150%;aspect-ratio:120/80;border-radius:43%;border:3px solid #fff;z-index:1;backdrop-filter:blur(6px);position:absolute;left:50%;top:50%;transform:translate(-50%, -50%) scale(0);animation:banner-diffuse1 2s linear infinite}main .part-scenarios{background-color:#edf7ff}main .part-scenarios .tab-list{display:flex;border-radius:.5rem;background-color:#fff}@media(max-width: 768px){main .part-scenarios .tab-list{overflow-x:auto;gap:8px;background:unset}}main .part-scenarios .tab-list .tab-item{padding:1rem;flex:1 1 25%;font-weight:700;font-size:1.375rem;color:#000;border-radius:8px;text-align:center;cursor:pointer}@media(max-width: 768px){main .part-scenarios .tab-list .tab-item{font-size:12px;padding:12px;white-space:nowrap;flex:auto;background-color:#fff}}main .part-scenarios .tab-list .tab-item.active{background-color:#046fff;color:#fff}main .part-scenarios .scenarios-item{border-radius:1rem;overflow:hidden;position:relative;color:#fff;height:100%}@media(max-width: 1280px){main .part-scenarios .scenarios-item{display:flex;flex-direction:column}}main .part-scenarios .scenarios-item .scenarios-item-content{position:absolute;top:0;right:0;height:100%;width:50%;background:rgba(0,0,0,.7);max-width:607px;padding:0 6.75rem 0 3.75rem;display:flex;flex-direction:column;justify-content:center}@media(max-width: 1280px){main .part-scenarios .scenarios-item .scenarios-item-content{flex:1;justify-content:flex-start;position:relative;width:100%;max-width:100%;padding:1.5rem;max-width:unset;background-color:#222}}main .part-scenarios .scenarios-item .scenarios-item-content .scenarios-item-title{display:flex;align-items:center;gap:1rem;color:#09ffae;font-size:2rem;font-weight:700}@media(max-width: 576px){main .part-scenarios .scenarios-item .scenarios-item-content .scenarios-item-title{font-size:22px}}main .part-scenarios .left-btn,main .part-scenarios .right-btn{position:absolute;top:50%;transform:translateY(-50%);z-index:10;cursor:pointer;width:3.5rem;height:3.5rem;background:rgba(255,255,255,.2);display:flex;align-items:center;justify-content:center;border-radius:50%}main .part-scenarios .left-btn:hover,main .part-scenarios .right-btn:hover{background:rgba(255,255,255,.7)}main .part-scenarios .left-btn{left:1.5rem}main .part-scenarios .right-btn{right:1.5rem}main .part-solutions{background:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/solutions-bg2.png) no-repeat left top;background-size:33.6%;background-color:#222;color:#fff}@media(max-width: 992px){main .part-solutions{background:#edf7ff;color:#000}}main .part-solutions .solutions-box{display:flex;justify-content:center;gap:1.875rem}@media(max-width: 1600px){main .part-solutions .solutions-box{gap:unset}}main .part-solutions .solutions-box .accordion-wrapper{flex:1 1 41.9%;max-width:455px;margin-left:auto;display:flex;flex-direction:column;justify-content:center}@media(max-width: 1600px){main .part-solutions .solutions-box .accordion-wrapper{flex:0 1 30%}}main .part-solutions .solutions-box .accordion-wrapper .nav{display:flex;flex-direction:column;gap:8px}main .part-solutions .solutions-box .accordion-wrapper .nav .divider{width:100%;height:1px;background-color:#414141}main .part-solutions .solutions-box .accordion-wrapper .nav .nav-item{border-radius:8px;text-decoration:none;color:#fff;overflow:hidden;transition:none;padding-bottom:1rem}main .part-solutions .solutions-box .accordion-wrapper .nav .nav-item .collapse-item{display:flex;align-items:center;justify-content:space-between;padding:1rem;color:#fff;padding-bottom:0;transition:none}main .part-solutions .solutions-box .accordion-wrapper .nav .nav-item .collapse-item .nav-item-title{font-size:1.25rem;font-weight:700;color:inherit}main .part-solutions .solutions-box .accordion-wrapper .nav .nav-item .collapse-detail{padding:0 1rem;font-weight:300;font-size:14px}main .part-solutions .solutions-box .accordion-wrapper .nav .nav-item.active{background-color:#fff;color:#000}main .part-solutions .solutions-box .accordion-wrapper .nav .nav-item.active .nav-item-title{color:#000}main .part-solutions .solutions-box .accordion-wrapper .nav .nav-item:has([aria-expanded=true]) svg{transform:rotate(180deg)}main .part-solutions .solutions-box .img-wrapper{flex:0 1 58.1%;min-height:648px}@media(max-width: 1600px){main .part-solutions .solutions-box .img-wrapper{flex:0 1 65%;display:flex;align-items:center}}@media(min-width: 768px){main .part-solutions .step-wrapper{display:none}main .part-solutions .step-wrapper .step-list{display:flex;justify-content:center}main .part-solutions .step-wrapper .step-list .step-item{flex:auto;height:5.25rem;padding:0 1rem;font-weight:600;font-size:1rem;color:#fff;text-align:center;display:flex;align-items:center;justify-content:center}main .part-solutions .step-wrapper .step-list .step-item.step1{background:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step1-bg.png) no-repeat center center;background-size:100% 100%}main .part-solutions .step-wrapper .step-list .step-item.step2{background:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step2-bg.png) no-repeat center center;background-size:100% 100%}main .part-solutions .step-wrapper .step-list .step-item.step3{background:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step3-bg.png) no-repeat center center;background-size:100% 100%}}@media(max-width: 768px){main .part-solutions .step-wrapper{display:none;border-radius:16px;background-color:#222;padding:2.25rem 1.875rem;color:#fff;margin-bottom:24px;margin-top:32px}main .part-solutions .step-wrapper h2{font-size:24px}main .part-solutions .step-wrapper p{font-size:14px;opacity:.5}main .part-solutions .step-wrapper .step-list{display:flex;justify-content:center;flex-direction:column;gap:46px}main .part-solutions .step-wrapper .step-list .step-item{height:75px;background-color:#4e9aff;box-shadow:0px 0px 9.9px 0px rgba(255,255,255,.4901960784) inset;border-radius:16px;text-align:center;padding:0 24px;font-weight:600;display:flex;align-items:center;justify-content:center;position:relative}main .part-solutions .step-wrapper .step-list .step-item:not(:last-child)::after{content:\"\";position:absolute;bottom:-6px;left:50%;width:10px;transform:translate(-50%, 100%);aspect-ratio:10/32;background:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/arrow-bottom2.svg) no-repeat center center/contain}main .part-solutions .step-wrapper .step-list .step-item.step1{background-color:#4e9aff}main .part-solutions .step-wrapper .step-list .step-item.step2{background-color:#2a85ff}main .part-solutions .step-wrapper .step-list .step-item.step3{background-color:#005cd7}}main .part-solutions .arrow{width:2.5rem;cursor:pointer;animation:arrow-bottom 2s linear infinite}@keyframes arrow-bottom{0%{transform:translateY(0)}50%{transform:translateY(10px)}100%{transform:translateY(0)}}main .part-solutions .arrow.active{transform:rotate(180deg);animation:unset}main .part-solutions .solution-box-list-mobile{display:flex;flex-direction:column;gap:48px}main .part-solutions .solution-box-list-mobile .solution-box-item{border-radius:1rem;background-color:#222;overflow:hidden}main .part-solutions .solution-box-list-mobile .solution-box-item .solution-box-item-title{font-weight:700;font-size:28px;text-align:center;color:#fff}main .part-solutions .solution-box-list-mobile .solution-box-item .solution-box-item-content{padding:16px 24px;font-size:14px;color:rgba(255,255,255,.5)}main .part-solutions .grey-arrow-wrapper{border-radius:8px;background:rgba(0,0,0,.1);text-align:center;cursor:pointer}main .part-solutions .grey-arrow-wrapper.active img{transform:rotate(180deg)}@media(min-width: 1280px){main .part-methods #swiper-methods .swiper-wrapper{gap:1.875rem;flex-wrap:wrap;justify-content:center}main .part-methods #swiper-methods .swiper-wrapper .swiper-slide{flex:1 1 calc(25% - 1.875rem);max-width:330px}}main .part-methods .feature-item{width:100%;border-radius:1rem;position:relative;background-color:#046fff;color:#fff;transition:unset;display:flex;justify-content:center;align-items:center;flex-direction:column;text-align:left;text-decoration:none;display:block;margin-top:32%;padding-top:33%;min-height:400px}@media(max-width: 992px){main .part-methods .feature-item{max-width:unset}}@media(any-hover: hover){main .part-methods .feature-item:hover{background-color:#222}main .part-methods .feature-item:hover .method-detail{display:none}main .part-methods .feature-item:hover .step-detail{display:block}}main .part-methods .feature-item .feature-img{position:absolute;top:0;left:0;width:100%;transform:translateY(-50%)}main .part-methods .feature-item .method-detail{padding:0 1rem 2.375rem}main .part-methods .feature-item .method-detail .divider{width:100%;height:1px;background-color:rgba(255,255,255,.3019607843)}main .part-methods .feature-item .method-detail .right-content-item,main .part-methods .feature-item .method-detail .fault-content-item{display:flex;align-items:flex-start;gap:4px;font-weight:300;font-size:14px;color:#fff}main .part-methods .feature-item .step-btn{margin:0 auto;width:204px;padding:10px;display:flex;align-items:center;justify-content:center;border-radius:8px;background:rgba(255,255,255,.36);color:#fff;font-size:18px;font-weight:800;text-align:center;margin-bottom:30px}main .part-methods .feature-item .step-btn.active svg{transform:rotate(180deg)}main .part-methods .feature-item .step-detail{display:none;padding:0 2rem 0 2.5rem}@media(max-width: 576px){main .part-methods .feature-item .step-detail{margin-bottom:24px}}main .part-methods .feature-item .step-detail ul{list-style:decimal}main .part-methods .feature-item .step-detail ul li{font-weight:300;font-size:12px;color:#fff}@keyframes marquee1{0%{transform:translateX(0)}100%{transform:translateX(-50%)}}main .part-table .table-wrapper{border-radius:24px;width:100%;position:relative;z-index:1;overflow:hidden;background-color:#0085ff;padding:1px;overflow:visible;margin-top:5rem}main .part-table .table-wrapper .inner-table,main .part-table .table-wrapper .table-blue{border-collapse:collapse;border-style:hidden;width:100%;background:#fff;border-radius:24px;overflow:hidden}main .part-table .table-wrapper .inner-table .opacity-5,main .part-table .table-wrapper .table-blue .opacity-5{opacity:.5}main .part-table .table-wrapper .inner-table tr:not(:last-child),main .part-table .table-wrapper .table-blue tr:not(:last-child){border-bottom:1px solid #0085ff}main .part-table .table-wrapper .inner-table th,main .part-table .table-wrapper .table-blue th{height:6rem;width:25%;font-weight:700;font-size:1.375rem;background-color:rgba(0,133,255,.1);vertical-align:middle;text-align:center;border-bottom:1px solid #0085ff}main .part-table .table-wrapper .inner-table th:not(:last-child),main .part-table .table-wrapper .table-blue th:not(:last-child){border-right:1px solid #0085ff}main .part-table .table-wrapper .inner-table td,main .part-table .table-wrapper .table-blue td{height:5.3125rem;vertical-align:middle;text-align:center;font-weight:600;font-size:1.125rem}main .part-table .table-wrapper .inner-table td:first-child,main .part-table .table-wrapper .table-blue td:first-child{background-color:rgba(0,133,255,.1);font-size:1.375rem;font-weight:700}main .part-table .table-wrapper .inner-table td:not(:last-child),main .part-table .table-wrapper .table-blue td:not(:last-child){border-right:1px solid #0085ff}@media(max-width: 1280px){main .part-table .table-wrapper .inner-table th,main .part-table .table-wrapper .table-blue th{font-size:1.5rem}}@media(max-width: 1280px){main .part-table .table-wrapper .inner-table td,main .part-table .table-wrapper .table-blue td{font-size:1.25rem}}main .part-table .table-wrapper .inner-table td div,main .part-table .table-wrapper .table-blue td div{display:flex;gap:15px;align-items:center;justify-content:flex-start;max-width:75%;margin:0 auto}main .part-table .table-wrapper .blue-table-wrapper{position:absolute;top:0;left:25%;width:25%;z-index:4;border-radius:1.5rem;padding:1.5rem 1rem 1.5rem;transform:translateY(-1.5rem);background-color:#046fff}main .part-table .table-wrapper .blue-table-wrapper .doc-logo{position:absolute;top:0;left:50%;transform:translate(-50%, -50%)}main .part-table .table-wrapper .blue-table-wrapper .doc-logo img{width:5rem}main .part-table .table-wrapper .blue-table-wrapper .table-blue{width:100%;border-collapse:collapse;border-style:hidden;background-color:#046fff}main .part-table .table-wrapper .blue-table-wrapper .table-blue th{color:#fff;border-bottom:1px solid #fff}main .part-table .table-wrapper .blue-table-wrapper .table-blue tr:last-child td{border-bottom:none}main .part-table .table-wrapper .blue-table-wrapper .table-blue td{color:#fff;border-bottom:1px solid #fff;font-size:1.125rem;font-weight:600}main .part-table .table-wrapper .blue-table-wrapper .table-blue td div{all:unset;display:flex;gap:15px;color:#fff}main .part-table .table-wrapper .blue-table-wrapper .table-blue td div span{text-shadow:0px 4px 4px rgba(0,0,0,.25)}main .part-table .marquee-wrapper{display:flex;flex-wrap:nowrap;width:fit-content}main .part-table .marquee-wrapper .marquee-item{font-weight:600;font-size:1.25rem;color:#000;white-space:nowrap;margin:0 1.125rem}main .part-table .marquee-wrapper{animation:marquee1 40s linear infinite}@media(min-width: 1280px){main .part-features #swiper-features .swiper-wrapper{gap:1.25rem;flex-wrap:wrap;justify-content:center}main .part-features #swiper-features .swiper-wrapper .swiper-slide{flex:1 1 calc(50% - 1.25rem)}}main .part-features .feature-box{display:flex;justify-content:center;border-radius:1rem;background-color:#edf7ff;overflow:hidden;height:100%}@media(max-width: 1280px){main .part-features .feature-box{flex-direction:column;justify-content:flex-start}}main .part-features .feature-box .img-wrapper{flex:0 0 42%}main .part-features .feature-box .img-wrapper img{width:100%;height:100%;object-fit:cover}main .part-features .feature-box .text-wrapper{display:flex;flex-direction:column;justify-content:center;padding:0 3rem;font-weight:500;font-size:1.125rem;color:#707070}@media(max-width: 1600px){main .part-features .feature-box .text-wrapper{padding:0 1rem}}@media(max-width: 1280px){main .part-features .feature-box .text-wrapper{padding:32px 24px}}@media(min-width: 1280px){main .part-helps .swiper-wrapper{display:block;transform:initial}}main .part-helps .scrollCard{overflow:hidden;position:relative;height:100%}main .part-helps .scrollCard .scrollCard-detail{display:flex;background:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/card-right-bg.jpg) no-repeat center center/cover;border-radius:1rem;overflow:hidden}@media(max-width: 1280px){main .part-helps .scrollCard .scrollCard-detail{flex-direction:column;background:#222;height:100%;justify-content:flex-start;align-items:stretch}}main .part-helps .scrollCard .scrollCard-detail.scrollCard-detail2{background-image:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/card-left-bg.jpg);justify-content:space-between}@media(max-width: 1280px){main .part-helps .scrollCard .scrollCard-detail.scrollCard-detail2{background:#222;justify-content:flex-start}}main .part-helps .scrollCard .scrollCard-detail .scrollCard-userImg{overflow:hidden;flex:0 1 46.78%}@media(max-width: 1280px){main .part-helps .scrollCard .scrollCard-detail .scrollCard-userImg{flex:unset}}main .part-helps .scrollCard .scrollCard-detail .scrollCard-userImg img{height:100%;width:100%;object-fit:cover}main .part-helps .scrollCard .scrollCard-detail .scrollCard-Intro{flex:1 1 53.22%;display:flex;flex-direction:column;color:#fff;justify-content:center;margin:0 5.9375rem;max-width:480px;text-align:left;align-items:flex-start;position:relative;padding:1.5rem 0}@media(max-width: 1280px){main .part-helps .scrollCard .scrollCard-detail .scrollCard-Intro{margin:0;padding:24px 16px;flex:1;max-width:unset;justify-content:flex-start}}@media(max-width: 768px){main .part-helps .scrollCard .scrollCard-detail .scrollCard-Intro .font-size-super{font-size:16px}main .part-helps .scrollCard .scrollCard-detail .scrollCard-Intro p.font-size-large{font-size:12px;opacity:.5}}main .part-helps .scrollCard .scrollCard-detail .scrollCard-Intro::after{content:\"\";position:absolute;left:-15%;top:10%;width:10%;background:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/tip-left.svg) no-repeat center center/contain;aspect-ratio:51/40}@media(max-width: 1280px){main .part-helps .scrollCard .scrollCard-detail .scrollCard-Intro::after{display:none}}main .part-helps .scrollCard .scrollCard-detail .scrollCard-Intro::before{content:\"\";position:absolute;right:-28%;top:10%;width:10%;background:url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/tip-right.svg) no-repeat center center/contain;aspect-ratio:51/40}@media(max-width: 1280px){main .part-helps .scrollCard .scrollCard-detail .scrollCard-Intro::before{display:none}}main .part-faq .accordion-item{padding:1.5rem 0;border-bottom:1px solid #cfd3d5}@media(max-width: 576px){main .part-faq .accordion-item{padding:14px}}main .part-faq .accordion-item [aria-expanded=true] svg{transform:rotate(180deg)}main .part-faq .accordion-item .faq-title{display:flex;align-items:center;justify-content:left;gap:8px;flex-shrink:0;max-width:90%;font-size:1.5rem;font-weight:700}@media(max-width: 576px){main .part-faq .accordion-item .faq-title{font-size:14px}}main .part-faq .topic-box{height:100%;border-radius:1rem;background-color:#edf7ff;padding:2.125rem;color:#0084ff}main .part-faq .topic-box .link-list{display:flex;flex-direction:column;gap:.75rem}main .part-faq .topic-box .link-list .link-item{font-weight:700;color:#0084ff;font-size:1rem}main .part-footer .btn-wrapper .btn-white{color:#0080ff}main .part-footer .btn-wrapper .btn-outline-white:hover{color:#0080ff}main .part-footer .footer-box{border-radius:1.25rem;background:url(https://mobiletrans.wondershare.com/images/images2025/index/footer-bg.jpg) no-repeat center center/cover;display:flex;flex-direction:column;align-items:center;justify-content:center;color:#fff;padding:4rem 0;color:#fff}@media(max-width: 768px){main .part-footer .footer-box{padding:2.5rem 1rem;margin:0 15px;text-align:center}}main .part-footer .footer-box .btn{min-width:210px;border-radius:4px}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/index.scss\"],\"names\":[],\"mappings\":\"AAAA,EACE,QAAA,CACA,SAAA,CACA,qBAAA,CAGF,KACE,qBAAA,CACA,UAAA,CAEA,0BAJF,KAKI,wBAAA,CAAA,CAGF,0FAWE,eAAA,CAGF,QACE,iBAAA,CACA,eAAA,CACA,iBAAA,CAIA,yBADF,gBAEI,gBAAA,CAAA,CAIJ,gBACE,UAAA,CAGF,gBACE,aAAA,CAEF,iBACE,aAAA,CAEF,iBACE,aAAA,CAGF,kBACE,YAAA,CACA,QAAA,CACA,yBAHF,kBAII,qBAAA,CACA,OAAA,CACA,kBAAA,CAAA,CAGJ,UACE,QAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,yBAAA,CACA,OAAA,CACA,cACE,cAAA,CACA,WAAA,CAEF,yBAXF,UAYI,aAAA,CAAA,CAEF,qBACE,cAAA,CACA,0BAAA,CAGJ,iBACE,mBAAA,CAEF,mBACE,qBAAA,CACA,sGAAA,CACA,6KAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,SAAA,CACA,yBAAA,CACA,qBAAA,CACA,UAAA,CACA,eAAA,CACA,eAAA,CACA,iBAAA,CAEA,mDAEE,UAAA,CAEF,yBAnBF,mBAoBI,aAAA,CACA,0BACE,WAAA,CAAA,CAIJ,kCACE,iBAAA,CACA,eAAA,CACA,aAAA,CACA,uDACE,oCAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,SAAA,CACA,aAAA,CACA,2DACE,iBAAA,CACA,uBAAA,CAEF,2DACE,iBAAA,CACA,QAAA,CACA,uBAAA,CACA,wBAAA,CAKN,yBACE,yBACE,UAAA,CAGF,kDACE,aAAA,CACA,2BAAA,CAGF,kDACE,aAAA,CACA,2BAAA,CAAA,CAIJ,oBACE,GACE,uBAAA,CAGF,KACE,0BAAA,CAAA,CAGJ,oBACE,GACE,0BAAA,CAGF,KACE,wBAAA,CAAA,CAGJ,6BACE,GACE,uBAAA,CAGF,KACE,0BAAA,CAAA,CAGJ,6BACE,GACE,0BAAA,CAGF,KACE,wBAAA,CAAA,CAKN,2BACE,iBAAA,CACA,SAAA,CACA,0BAHF,2BAII,YAAA,CAAA,CAEF,wCACE,YAAA,CAEF,uCACE,iBAAA,CACA,iBAAA,CACA,QAAA,CACA,eAAA,CACA,QAAA,CACA,gCAAA,CACA,kCAAA,CACA,SAAA,CACA,mBAAA,CAGA,8CACE,oBAAA,CAEF,+CACE,YAAA,CAEF,6CACE,SAAA,CAKJ,kDACE,UAAA,CACA,WAAA,CACA,mBAAA,CACA,mCAAA,CACA,SAAA,CACA,kFACE,UAAA,CACA,mBAAA,CACA,SAAA,CACA,mCAAA,CAKN,kBACE,YAAA,CACA,kBAAA,CACA,QAAA,CACA,iBAAA,CACA,aAAA,CACA,0BANF,kBAOI,sBAAA,CAAA,CAEF,8BACE,iBAAA,CACA,iBAAA,CACA,WAAA,CACA,eAAA,CACA,QAAA,CACA,8CAAA,CACA,kCAAA,CACA,SAAA,CACA,mBAAA,CAEF,oBACE,aAAA,CACA,oBAAA,CACA,iBAAA,CAEA,yBACE,0BACE,UAAA,CACA,sCACE,SAAA,CAAA,CAOV,kBACE,8JAAA,CAGA,uBACE,oBAAA,CAEF,8BACE,wBAAA,CACA,oBAAA,CACA,UAAA,CACA,oCACE,UAAA,CACA,wBAAA,CACA,oBAAA,CAGJ,sCACE,iBAAA,CACA,2BACE,GACE,yDAAA,CACA,UAAA,CAGF,IACE,yDAAA,CACA,UAAA,CAGF,KACE,uDAAA,CACA,SAAA,CAAA,CAIJ,2BACE,GACE,yDAAA,CACA,UAAA,CAGF,IACE,yDAAA,CACA,UAAA,CAGF,KACE,uDAAA,CACA,SAAA,CAAA,CAGJ,0DACE,iBAAA,CACA,SAAA,CACA,OAAA,CACA,SAAA,CACA,sEACE,YAAA,CAEF,yBAEI,4EACE,aAAA,CAEF,8EACE,YAAA,CAAA,CAMR,iEACE,UAAA,CACA,mBAAA,CACA,iBAAA,CACA,qBAAA,CACA,SAAA,CAEA,yBAAA,CACA,iBAAA,CACA,QAAA,CACA,OAAA,CACA,wDAAA,CACA,4CAAA,CAEF,iEACE,UAAA,CACA,mBAAA,CACA,iBAAA,CACA,qBAAA,CACA,SAAA,CAEA,yBAAA,CACA,iBAAA,CACA,QAAA,CACA,OAAA,CACA,wDAAA,CACA,4CAAA,CAGF,iEACE,UAAA,CACA,mBAAA,CACA,iBAAA,CACA,qBAAA,CACA,SAAA,CAEA,yBAAA,CACA,iBAAA,CACA,QAAA,CACA,OAAA,CACA,wCAAA,CACA,4CAAA,CAKN,qBACE,wBAAA,CACA,+BACE,YAAA,CACA,mBAAA,CACA,qBAAA,CACA,yBAJF,+BAKI,eAAA,CACA,OAAA,CACA,gBAAA,CAAA,CAEF,yCACE,YAAA,CACA,YAAA,CACA,eAAA,CACA,kBAAA,CACA,UAAA,CACA,iBAAA,CACA,iBAAA,CACA,cAAA,CACA,yBATF,yCAUI,cAAA,CACA,YAAA,CACA,kBAAA,CACA,SAAA,CACA,qBAAA,CAAA,CAEF,gDACE,wBAAA,CACA,UAAA,CAIN,qCACE,kBAAA,CACA,eAAA,CACA,iBAAA,CACA,UAAA,CACA,WAAA,CACA,0BANF,qCAOI,YAAA,CACA,qBAAA,CAAA,CAEF,6DACE,iBAAA,CACA,KAAA,CACA,OAAA,CACA,WAAA,CACA,SAAA,CACA,yBAAA,CACA,eAAA,CACA,2BAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,0BAZF,6DAaI,MAAA,CACA,0BAAA,CACA,iBAAA,CACA,UAAA,CACA,cAAA,CACA,cAAA,CACA,eAAA,CACA,qBAAA,CAAA,CAEF,mFACE,YAAA,CACA,kBAAA,CACA,QAAA,CACA,aAAA,CACA,cAAA,CACA,eAAA,CACA,yBAPF,mFAQI,cAAA,CAAA,CAKR,+DAEE,iBAAA,CACA,OAAA,CACA,0BAAA,CACA,UAAA,CACA,cAAA,CACA,YAAA,CACA,aAAA,CACA,+BAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,iBAAA,CACA,2EACE,+BAAA,CAGJ,+BACE,WAAA,CAEF,gCACE,YAAA,CAIJ,qBACE,yHAAA,CACA,qBAAA,CACA,qBAAA,CACA,UAAA,CACA,yBALF,qBAMI,kBAAA,CACA,UAAA,CAAA,CAEF,oCACE,YAAA,CAEA,sBAAA,CACA,YAAA,CACA,0BALF,oCAMI,SAAA,CAAA,CAEF,uDACE,cAAA,CACA,eAAA,CACA,gBAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,0BAPF,uDAQI,YAAA,CAAA,CAEF,4DACE,YAAA,CACA,qBAAA,CACA,OAAA,CACA,qEACE,UAAA,CACA,UAAA,CACA,wBAAA,CAEF,sEACE,iBAAA,CACA,oBAAA,CACA,UAAA,CACA,eAAA,CACA,eAAA,CAEA,mBAAA,CACA,qFACE,YAAA,CACA,kBAAA,CACA,6BAAA,CACA,YAAA,CACA,UAAA,CACA,gBAAA,CACA,eAAA,CACA,qGACE,iBAAA,CACA,eAAA,CACA,aAAA,CAGJ,uFACE,cAAA,CACA,eAAA,CACA,cAAA,CAEF,6EACE,qBAAA,CACA,UAAA,CAEA,6FACE,UAAA,CAOJ,oGACE,wBAAA,CAKR,iDACE,cAAA,CACA,gBAAA,CACA,0BAHF,iDAII,YAAA,CACA,YAAA,CACA,kBAAA,CAAA,CAIN,yBACE,mCACE,YAAA,CACA,8CACE,YAAA,CACA,sBAAA,CACA,yDACE,SAAA,CACA,cAAA,CACA,cAAA,CACA,eAAA,CACA,cAAA,CACA,UAAA,CACA,iBAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CAEA,+DACE,yHAAA,CACA,yBAAA,CAEF,+DACE,yHAAA,CACA,yBAAA,CAEF,+DACE,yHAAA,CACA,yBAAA,CAAA,CAMV,yBACE,mCACE,YAAA,CACA,kBAAA,CACA,qBAAA,CACA,wBAAA,CACA,UAAA,CACA,kBAAA,CACA,eAAA,CACA,sCACE,cAAA,CAEF,qCACE,cAAA,CACA,UAAA,CAEF,8CACE,YAAA,CACA,sBAAA,CACA,qBAAA,CACA,QAAA,CACA,yDACE,WAAA,CACA,wBAAA,CACA,gEAAA,CACA,kBAAA,CACA,iBAAA,CACA,cAAA,CACA,eAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,iBAAA,CACA,iFACE,UAAA,CACA,iBAAA,CACA,WAAA,CACA,QAAA,CACA,UAAA,CACA,+BAAA,CACA,kBAAA,CACA,sIAAA,CAEF,+DACE,wBAAA,CAEF,+DACE,wBAAA,CAEF,+DACE,wBAAA,CAAA,CAOV,4BACE,YAAA,CACA,cAAA,CACA,yCAAA,CACA,wBACE,GACE,uBAAA,CAEF,IACE,0BAAA,CAEF,KACE,uBAAA,CAAA,CAIJ,mCACE,wBAAA,CACA,eAAA,CAIJ,+CACE,YAAA,CACA,qBAAA,CACA,QAAA,CACA,kEACE,kBAAA,CACA,qBAAA,CACA,eAAA,CACA,2FACE,eAAA,CACA,cAAA,CACA,iBAAA,CACA,UAAA,CAEF,6FACE,iBAAA,CACA,cAAA,CACA,0BAAA,CAIN,yCACE,iBAAA,CACA,yBAAA,CACA,iBAAA,CACA,cAAA,CAEE,oDACE,wBAAA,CAON,0BACE,mDACE,YAAA,CACA,cAAA,CACA,sBAAA,CAGF,iEACE,6BAAA,CACA,eAAA,CAAA,CAGJ,iCACE,UAAA,CACA,kBAAA,CACA,iBAAA,CACA,wBAAA,CACA,UAAA,CACA,gBAAA,CACA,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,qBAAA,CACA,eAAA,CACA,oBAAA,CACA,aAAA,CACA,cAAA,CACA,eAAA,CACA,gBAAA,CACA,yBAjBF,iCAkBI,eAAA,CAAA,CAGF,yBACE,uCACE,qBAAA,CACA,sDACE,YAAA,CAEF,oDACE,aAAA,CAAA,CAKN,8CACE,iBAAA,CACA,KAAA,CACA,MAAA,CACA,UAAA,CAEA,0BAAA,CAEF,gDACE,uBAAA,CAEA,yDACE,UAAA,CACA,UAAA,CACA,8CAAA,CAEF,wIAEE,YAAA,CACA,sBAAA,CACA,OAAA,CACA,eAAA,CACA,cAAA,CACA,UAAA,CAGJ,2CACE,aAAA,CACA,WAAA,CACA,YAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,iBAAA,CACA,gCAAA,CACA,UAAA,CACA,cAAA,CACA,eAAA,CACA,iBAAA,CACA,kBAAA,CAEE,sDACE,wBAAA,CAIN,8CACE,YAAA,CACA,uBAAA,CACA,yBAHF,8CAII,kBAAA,CAAA,CAGF,iDACE,kBAAA,CACA,oDACE,eAAA,CACA,cAAA,CACA,UAAA,CAQR,oBACE,GACE,uBAAA,CAEF,KACE,0BAAA,CAAA,CAGJ,gCACE,kBAAA,CACA,UAAA,CACA,iBAAA,CACA,SAAA,CACA,eAAA,CACA,wBAAA,CACA,WAAA,CACA,gBAAA,CACA,eAAA,CAEA,yFAEE,wBAAA,CACA,mBAAA,CACA,UAAA,CACA,eAAA,CACA,kBAAA,CACA,eAAA,CAGF,+GAEE,UAAA,CAGF,iIAEE,+BAAA,CAGF,+FAEE,WAAA,CACA,SAAA,CACA,eAAA,CACA,kBAAA,CACA,mCAAA,CACA,qBAAA,CACA,iBAAA,CACA,+BAAA,CACA,iIACE,8BAAA,CAIJ,+FAEE,gBAAA,CACA,qBAAA,CACA,iBAAA,CACA,eAAA,CACA,kBAAA,CAEA,uHACE,mCAAA,CACA,kBAAA,CACA,eAAA,CAEF,iIACE,8BAAA,CAIJ,0BACE,+FAEE,gBAAA,CAAA,CAIJ,0BACE,+FAEE,iBAAA,CAAA,CAIJ,uGAEE,YAAA,CACA,QAAA,CACA,kBAAA,CACA,0BAAA,CACA,aAAA,CACA,aAAA,CAGF,oDACE,iBAAA,CACA,KAAA,CACA,QAAA,CACA,SAAA,CACA,SAAA,CACA,oBAAA,CACA,0BAAA,CACA,6BAAA,CACA,wBAAA,CAGF,8DACE,iBAAA,CACA,KAAA,CACA,QAAA,CACA,+BAAA,CAGF,kEACE,UAAA,CAGF,gEACE,UAAA,CACA,wBAAA,CACA,mBAAA,CACA,wBAAA,CAGF,mEACE,UAAA,CACA,4BAAA,CAGF,iFACE,kBAAA,CAGF,mEACE,UAAA,CACA,4BAAA,CACA,kBAAA,CACA,eAAA,CAGF,uEACE,SAAA,CACA,YAAA,CACA,QAAA,CACA,UAAA,CAGF,4EACE,uCAAA,CAGJ,kCACE,YAAA,CACA,gBAAA,CACA,iBAAA,CACA,gDACE,eAAA,CACA,iBAAA,CACA,UAAA,CACA,kBAAA,CACA,iBAAA,CAGJ,kCACE,sCAAA,CAKF,0BACE,qDACE,WAAA,CACA,cAAA,CACA,sBAAA,CAGF,mEACE,4BAAA,CAAA,CAGJ,iCACE,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,wBAAA,CACA,eAAA,CACA,WAAA,CACA,0BAPF,iCAQI,qBAAA,CACA,0BAAA,CAAA,CAEF,8CACE,YAAA,CACA,kDACE,UAAA,CACA,WAAA,CACA,gBAAA,CAGJ,+CACE,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,cAAA,CACA,eAAA,CACA,kBAAA,CACA,aAAA,CACA,0BARF,+CASI,cAAA,CAAA,CAEF,0BAXF,+CAYI,iBAAA,CAAA,CAON,0BACE,iCACE,aAAA,CACA,iBAAA,CAAA,CAGJ,6BACE,eAAA,CACA,iBAAA,CACA,WAAA,CACA,gDACE,YAAA,CACA,oIAAA,CACA,kBAAA,CACA,eAAA,CACA,0BALF,gDAMI,qBAAA,CACA,eAAA,CACA,WAAA,CACA,0BAAA,CACA,mBAAA,CAAA,CAEF,mEACE,2GAAA,CACA,6BAAA,CACA,0BAHF,mEAII,eAAA,CACA,0BAAA,CAAA,CAGJ,oEACE,eAAA,CACA,eAAA,CACA,0BAHF,oEAII,UAAA,CAAA,CAEF,wEACE,WAAA,CACA,UAAA,CACA,gBAAA,CAGJ,kEACE,eAAA,CACA,YAAA,CACA,qBAAA,CACA,UAAA,CACA,sBAAA,CACA,kBAAA,CACA,eAAA,CACA,eAAA,CACA,sBAAA,CACA,iBAAA,CACA,gBAAA,CACA,0BAZF,kEAaI,QAAA,CACA,iBAAA,CACA,MAAA,CACA,eAAA,CACA,0BAAA,CAAA,CAEF,yBACE,mFACE,cAAA,CAEF,oFACE,cAAA,CACA,UAAA,CAAA,CAGJ,yEACE,UAAA,CACA,iBAAA,CACA,SAAA,CACA,OAAA,CACA,SAAA,CACA,iIAAA,CACA,kBAAA,CACA,0BARF,yEASI,YAAA,CAAA,CAGJ,0EACE,UAAA,CACA,iBAAA,CACA,UAAA,CACA,OAAA,CACA,SAAA,CACA,kIAAA,CACA,kBAAA,CACA,0BARF,0EASI,YAAA,CAAA,CASV,+BACE,gBAAA,CACA,+BAAA,CACA,yBAHF,+BAII,YAAA,CAAA,CAGJ,wDACE,wBAAA,CAGF,0CACE,YAAA,CACA,kBAAA,CACA,oBAAA,CACA,OAAA,CACA,aAAA,CACA,aAAA,CACA,gBAAA,CACA,eAAA,CACA,yBATF,0CAUI,cAAA,CAAA,CAGJ,0BACE,WAAA,CACA,kBAAA,CACA,wBAAA,CACA,gBAAA,CACA,aAAA,CACA,qCACE,YAAA,CACA,qBAAA,CACA,UAAA,CAEA,gDACE,eAAA,CACA,aAAA,CACA,cAAA,CAON,0CACE,aAAA,CAGF,wDACE,aAAA,CAGF,8BACE,qBAAA,CACA,uHAAA,CACA,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,sBAAA,CACA,UAAA,CACA,cAAA,CACA,UAAA,CAGF,yBACE,8BACE,mBAAA,CACA,aAAA,CACA,iBAAA,CAAA,CAIJ,mCACE,eAAA,CACA,iBAAA\",\"sourcesContent\":[\"* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nmain {\\n  background-color: #fff;\\n  color: #000;\\n\\n  @media (max-width: 1280px) {\\n    background-color: #f4f7ff;\\n  }\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6,\\n  p,\\n  div,\\n  span,\\n  ul,\\n  li {\\n    margin-bottom: 0;\\n  }\\n\\n  h2 {\\n    text-align: center;\\n    font-weight: 800;\\n    font-size: 2.25rem;\\n  }\\n\\n  .display-3 {\\n    @media (max-width: 576px) {\\n      font-size: 2.5rem;\\n    }\\n  }\\n\\n  .opacity-7 {\\n    opacity: 0.7;\\n  }\\n\\n  .text-blue {\\n    color: #046fff;\\n  }\\n  .text-blue2 {\\n    color: #046fff;\\n  }\\n  .text-blue3 {\\n    color: #3b8eff;\\n  }\\n\\n  .btn-wrapper {\\n    display: flex;\\n    gap: 1rem;\\n    @media (max-width: 768px) {\\n      flex-direction: column;\\n      gap: 8px;\\n      align-items: center;\\n    }\\n  }\\n  .btn {\\n    margin: 0;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    text-transform: capitalize;\\n    gap: 8px;\\n    svg {\\n      max-width: 100%;\\n      height: 100%;\\n    }\\n    @media (max-width: 768px) {\\n      display: block;\\n    }\\n    &.dev-mobile {\\n      width: 168.75px;\\n      min-width: unset !important;\\n    }\\n  }\\n  .btn-action {\\n    border-radius: 0.5rem;\\n  }\\n  .btn-download {\\n    border: 1px solid #ffffff;\\n    background: linear-gradient(89.57deg, #00c8ff -10.59%, #0084ff 15.01%, #006fff 83.38%, #00c8ff 107.75%);\\n    box-shadow: 0px 4.5px 6.97px 0px #ffffff6e inset, 0px -6.75px 16.65px 0px #00e5ffd6 inset, 0px 4.5px 13.84px 0px #0059ff40;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    gap: 0.5rem;\\n    text-transform: capitalize;\\n    border-radius: 0.875rem;\\n    color: #fff;\\n    min-width: 220px;\\n    overflow: hidden;\\n    position: relative;\\n\\n    &:focus,\\n    &:active {\\n      color: #fff;\\n    }\\n    @media (min-width: 992px) {\\n      height: 3.5rem;\\n      &.btn-lg {\\n        height: 4rem;\\n      }\\n    }\\n\\n    .btn-text-wrap {\\n      position: relative;\\n      overflow: hidden;\\n      color: inherit;\\n      .btn-hover-text-wrap {\\n        transition: transform 0.4s ease-in-out;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        gap: 0.5rem;\\n        color: inherit;\\n        &.rel {\\n          position: relative;\\n          transform: translateY(0);\\n        }\\n        &.abs {\\n          position: absolute;\\n          top: 120%;\\n          transform: translateY(0);\\n          transition-duration: 0.45s;\\n        }\\n      }\\n    }\\n\\n    @media (any-hover: hover) {\\n      &:hover {\\n        color: #fff;\\n      }\\n\\n      &:hover .btn-hover-text-wrap.rel {\\n        color: inherit;\\n        transform: translateY(-100%);\\n      }\\n\\n      &:hover .btn-hover-text-wrap.abs {\\n        color: inherit;\\n        transform: translateY(-120%);\\n      }\\n    }\\n\\n    @keyframes marquee1 {\\n      0% {\\n        transform: translateX(0);\\n      }\\n\\n      100% {\\n        transform: translateX(-50%);\\n      }\\n    }\\n    @keyframes marquee2 {\\n      0% {\\n        transform: translateX(-50%);\\n      }\\n\\n      100% {\\n        transform: translateX(0%);\\n      }\\n    }\\n    @keyframes marquee1-vertical {\\n      0% {\\n        transform: translateY(0);\\n      }\\n\\n      100% {\\n        transform: translateY(-50%);\\n      }\\n    }\\n    @keyframes marquee2-vertical {\\n      0% {\\n        transform: translateY(-50%);\\n      }\\n\\n      100% {\\n        transform: translateY(0%);\\n      }\\n    }\\n  }\\n\\n  .qr-code-icon-wrapper {\\n    position: relative;\\n    z-index: 5;\\n    @media (max-width: 1280px) {\\n      display: none;\\n    }\\n    .active-icon {\\n      display: none;\\n    }\\n    .qrcode-box {\\n      width: max-content;\\n      position: absolute;\\n      top: -8px;\\n      max-width: 128px;\\n      left: 50%;\\n      transform: translate(-50%, -100%);\\n      transition: opacity 0.2s ease-in-out;\\n      opacity: 0;\\n      pointer-events: none;\\n    }\\n    &:hover {\\n      .active-icon {\\n        display: inline-block;\\n      }\\n      .default-icon {\\n        display: none;\\n      }\\n      .qrcode-box {\\n        opacity: 1;\\n      }\\n    }\\n  }\\n  .swiper-pagination {\\n    .swiper-pagination-bullet {\\n      width: 10px;\\n      height: 10px;\\n      border-radius: 100px;\\n      background-color: rgba($color: #006dff, $alpha: 0.3);\\n      opacity: 1;\\n      &.swiper-pagination-bullet-active {\\n        width: 40px;\\n        border-radius: 100px;\\n        opacity: 1;\\n        background-color: rgba($color: #006dff, $alpha: 0.7);\\n      }\\n    }\\n  }\\n\\n  .system-list {\\n    display: flex;\\n    align-items: center;\\n    gap: 12px;\\n    margin-top: 1.5rem;\\n    color: #b2b2b2;\\n    @media (max-width: 1280px) {\\n      justify-content: center;\\n    }\\n    .qrcode-box {\\n      width: max-content;\\n      position: absolute;\\n      bottom: -8px;\\n      max-width: 128px;\\n      left: 50%;\\n      transform: translate(-50%, 100%) rotate(180deg);\\n      transition: opacity 0.2s ease-in-out;\\n      opacity: 0;\\n      pointer-events: none;\\n    }\\n    a {\\n      color: #b2b2b2;\\n      text-decoration: none;\\n      position: relative;\\n\\n      @media (any-hover: hover) {\\n        &:hover {\\n          color: #000;\\n          .qrcode-box {\\n            opacity: 1;\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-banner {\\n    background: linear-gradient(294.17deg, rgba(255, 255, 255, 0) 59.67%, rgba(99, 255, 204, 0.2) 99.14%),\\n      linear-gradient(50.88deg, #ffffff 56.75%, #bde2ff 88.21%);\\n\\n    .btn {\\n      border-radius: 0.75rem;\\n    }\\n    .btn-action {\\n      background-color: #1da4ff;\\n      border-color: #1da4ff;\\n      color: #fff;\\n      &:hover {\\n        color: #fff;\\n        background-color: #005dd9;\\n        border-color: #0057cc;\\n      }\\n    }\\n    .banner-img-wrapper {\\n      position: relative;\\n      @keyframes banner-diffuse1 {\\n        0% {\\n          transform: translate(-50%, -50%) scale(0.2) rotate(4.5deg);\\n          opacity: 0.1;\\n        }\\n\\n        60% {\\n          transform: translate(-50%, -50%) scale(0.7) rotate(4.5deg);\\n          opacity: 0.5;\\n        }\\n\\n        100% {\\n          transform: translate(-50%, -50%) scale(1) rotate(4.5deg);\\n          opacity: 0;\\n        }\\n      }\\n\\n      @keyframes banner-diffuse2 {\\n        0% {\\n          transform: translate(-50%, -50%) scale(0.2) rotate(4.5deg);\\n          opacity: 0.1;\\n        }\\n\\n        60% {\\n          transform: translate(-50%, -50%) scale(0.9) rotate(4.5deg);\\n          opacity: 0.5;\\n        }\\n\\n        100% {\\n          transform: translate(-50%, -50%) scale(1) rotate(4.5deg);\\n          opacity: 0;\\n        }\\n      }\\n      .part-wave-icon-box {\\n        position: absolute;\\n        right: 36%;\\n        top: 29%;\\n        width: 16%;\\n        .hover-icon {\\n          display: none;\\n        }\\n        @media (any-hover: hover) {\\n          &:hover {\\n            .hover-icon {\\n              display: block;\\n            }\\n            .default-icon {\\n              display: none;\\n            }\\n          }\\n        }\\n      }\\n\\n      .part-wave-icon-box .wave1 {\\n        width: 115%;\\n        aspect-ratio: 120 / 80;\\n        border-radius: 43%;\\n        border: 3px solid rgb(255, 255, 255);\\n        z-index: 1;\\n\\n        backdrop-filter: blur(6px);\\n        position: absolute;\\n        left: 50%;\\n        top: 50%;\\n        transform: translate(-50%, -50%) scale(0) rotate(-4.5deg);\\n        animation: banner-diffuse1 2s linear infinite;\\n      }\\n      .part-wave-icon-box .wave2 {\\n        width: 130%;\\n        aspect-ratio: 120 / 80;\\n        border-radius: 43%;\\n        border: 3px solid rgb(255, 255, 255);\\n        z-index: 1;\\n\\n        backdrop-filter: blur(6px);\\n        position: absolute;\\n        left: 50%;\\n        top: 50%;\\n        transform: translate(-50%, -50%) scale(0) rotate(-4.5deg);\\n        animation: banner-diffuse1 2s linear infinite;\\n      }\\n\\n      .part-wave-icon-box .wave3 {\\n        width: 150%;\\n        aspect-ratio: 120 / 80;\\n        border-radius: 43%;\\n        border: 3px solid rgb(255, 255, 255);\\n        z-index: 1;\\n\\n        backdrop-filter: blur(6px);\\n        position: absolute;\\n        left: 50%;\\n        top: 50%;\\n        transform: translate(-50%, -50%) scale(0);\\n        animation: banner-diffuse1 2s linear infinite;\\n      }\\n    }\\n  }\\n\\n  .part-scenarios {\\n    background-color: #edf7ff;\\n    .tab-list {\\n      display: flex;\\n      border-radius: 0.5rem;\\n      background-color: #fff;\\n      @media (max-width: 768px) {\\n        overflow-x: auto;\\n        gap: 8px;\\n        background: unset;\\n      }\\n      .tab-item {\\n        padding: 1rem;\\n        flex: 1 1 25%;\\n        font-weight: 700;\\n        font-size: 1.375rem;\\n        color: #000;\\n        border-radius: 8px;\\n        text-align: center;\\n        cursor: pointer;\\n        @media (max-width: 768px) {\\n          font-size: 12px;\\n          padding: 12px;\\n          white-space: nowrap;\\n          flex: auto;\\n          background-color: #fff;\\n        }\\n        &.active {\\n          background-color: #046fff;\\n          color: #fff;\\n        }\\n      }\\n    }\\n    .scenarios-item {\\n      border-radius: 1rem;\\n      overflow: hidden;\\n      position: relative;\\n      color: #fff;\\n      height: 100%;\\n      @media (max-width: 1280px) {\\n        display: flex;\\n        flex-direction: column;\\n      }\\n      .scenarios-item-content {\\n        position: absolute;\\n        top: 0;\\n        right: 0;\\n        height: 100%;\\n        width: 50%;\\n        background: rgba(0, 0, 0, 0.7);\\n        max-width: 607px;\\n        padding: 0 6.75rem 0 3.75rem;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        @media (max-width: 1280px) {\\n          flex: 1;\\n          justify-content: flex-start;\\n          position: relative;\\n          width: 100%;\\n          max-width: 100%;\\n          padding: 1.5rem;\\n          max-width: unset;\\n          background-color: #222222;\\n        }\\n        .scenarios-item-title {\\n          display: flex;\\n          align-items: center;\\n          gap: 1rem;\\n          color: #09ffae;\\n          font-size: 2rem;\\n          font-weight: 700;\\n          @media (max-width: 576px) {\\n            font-size: 22px;\\n          }\\n        }\\n      }\\n    }\\n    .left-btn,\\n    .right-btn {\\n      position: absolute;\\n      top: 50%;\\n      transform: translateY(-50%);\\n      z-index: 10;\\n      cursor: pointer;\\n      width: 3.5rem;\\n      height: 3.5rem;\\n      background: rgba(255, 255, 255, 0.2);\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      border-radius: 50%;\\n      &:hover {\\n        background: rgba(255, 255, 255, 0.7);\\n      }\\n    }\\n    .left-btn {\\n      left: 1.5rem;\\n    }\\n    .right-btn {\\n      right: 1.5rem;\\n    }\\n  }\\n\\n  .part-solutions {\\n    background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/solutions-bg2.png) no-repeat left top;\\n    background-size: 33.6%;\\n    background-color: #222222;\\n    color: #fff;\\n    @media (max-width: 992px) {\\n      background: #edf7ff;\\n      color: #000;\\n    }\\n    .solutions-box {\\n      display: flex;\\n\\n      justify-content: center;\\n      gap: 1.875rem;\\n      @media (max-width: 1600px) {\\n        gap: unset;\\n      }\\n      .accordion-wrapper {\\n        flex: 1 1 41.9%;\\n        max-width: 455px;\\n        margin-left: auto;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        @media (max-width: 1600px) {\\n          flex: 0 1 30%;\\n        }\\n        .nav {\\n          display: flex;\\n          flex-direction: column;\\n          gap: 8px;\\n          .divider {\\n            width: 100%;\\n            height: 1px;\\n            background-color: #414141;\\n          }\\n          .nav-item {\\n            border-radius: 8px;\\n            text-decoration: none;\\n            color: #fff;\\n            overflow: hidden;\\n            transition: none;\\n\\n            padding-bottom: 1rem;\\n            .collapse-item {\\n              display: flex;\\n              align-items: center;\\n              justify-content: space-between;\\n              padding: 1rem;\\n              color: #fff;\\n              padding-bottom: 0;\\n              transition: none;\\n              .nav-item-title {\\n                font-size: 1.25rem;\\n                font-weight: 700;\\n                color: inherit;\\n              }\\n            }\\n            .collapse-detail {\\n              padding: 0 1rem;\\n              font-weight: 300;\\n              font-size: 14px;\\n            }\\n            &.active {\\n              background-color: #fff;\\n              color: #000;\\n\\n              .nav-item-title {\\n                color: #000;\\n              }\\n              // .collapse-item {\\n              //   padding-bottom: 4px;\\n              // }\\n            }\\n\\n            &:has([aria-expanded=\\\"true\\\"]) svg {\\n              transform: rotate(180deg);\\n            }\\n          }\\n        }\\n      }\\n      .img-wrapper {\\n        flex: 0 1 58.1%;\\n        min-height: 648px;\\n        @media (max-width: 1600px) {\\n          flex: 0 1 65%;\\n          display: flex;\\n          align-items: center;\\n        }\\n      }\\n    }\\n    @media (min-width: 768px) {\\n      .step-wrapper {\\n        display: none;\\n        .step-list {\\n          display: flex;\\n          justify-content: center;\\n          .step-item {\\n            flex: auto;\\n            height: 5.25rem;\\n            padding: 0 1rem;\\n            font-weight: 600;\\n            font-size: 1rem;\\n            color: #fff;\\n            text-align: center;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n\\n            &.step1 {\\n              background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step1-bg.png) no-repeat center center;\\n              background-size: 100% 100%;\\n            }\\n            &.step2 {\\n              background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step2-bg.png) no-repeat center center;\\n              background-size: 100% 100%;\\n            }\\n            &.step3 {\\n              background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/step3-bg.png) no-repeat center center;\\n              background-size: 100% 100%;\\n            }\\n          }\\n        }\\n      }\\n    }\\n    @media (max-width: 768px) {\\n      .step-wrapper {\\n        display: none;\\n        border-radius: 16px;\\n        background-color: #222222;\\n        padding: 2.25rem 1.875rem;\\n        color: #fff;\\n        margin-bottom: 24px;\\n        margin-top: 32px;\\n        h2 {\\n          font-size: 24px;\\n        }\\n        p {\\n          font-size: 14px;\\n          opacity: 0.5;\\n        }\\n        .step-list {\\n          display: flex;\\n          justify-content: center;\\n          flex-direction: column;\\n          gap: 46px;\\n          .step-item {\\n            height: 75px;\\n            background-color: #4e9aff;\\n            box-shadow: 0px 0px 9.9px 0px #ffffff7d inset;\\n            border-radius: 16px;\\n            text-align: center;\\n            padding: 0 24px;\\n            font-weight: 600;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            position: relative;\\n            &:not(:last-child)::after {\\n              content: \\\"\\\";\\n              position: absolute;\\n              bottom: -6px;\\n              left: 50%;\\n              width: 10px;\\n              transform: translate(-50%, 100%);\\n              aspect-ratio: 10 / 32;\\n              background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/arrow-bottom2.svg) no-repeat center center / contain;\\n            }\\n            &.step1 {\\n              background-color: #4e9aff;\\n            }\\n            &.step2 {\\n              background-color: #2a85ff;\\n            }\\n            &.step3 {\\n              background-color: #005cd7;\\n            }\\n          }\\n        }\\n      }\\n    }\\n\\n    .arrow {\\n      width: 2.5rem;\\n      cursor: pointer;\\n      animation: arrow-bottom 2s linear infinite;\\n      @keyframes arrow-bottom {\\n        0% {\\n          transform: translateY(0);\\n        }\\n        50% {\\n          transform: translateY(10px);\\n        }\\n        100% {\\n          transform: translateY(0);\\n        }\\n      }\\n\\n      &.active {\\n        transform: rotate(180deg);\\n        animation: unset;\\n      }\\n    }\\n\\n    .solution-box-list-mobile {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 48px;\\n      .solution-box-item {\\n        border-radius: 1rem;\\n        background-color: #222222;\\n        overflow: hidden;\\n        .solution-box-item-title {\\n          font-weight: 700;\\n          font-size: 28px;\\n          text-align: center;\\n          color: #fff;\\n        }\\n        .solution-box-item-content {\\n          padding: 16px 24px;\\n          font-size: 14px;\\n          color: rgba($color: #fff, $alpha: 0.5);\\n        }\\n      }\\n    }\\n    .grey-arrow-wrapper {\\n      border-radius: 8px;\\n      background: rgba(0, 0, 0, 0.1);\\n      text-align: center;\\n      cursor: pointer;\\n      &.active {\\n        img {\\n          transform: rotate(180deg);\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-methods {\\n    @media (min-width: 1280px) {\\n      #swiper-methods .swiper-wrapper {\\n        gap: 1.875rem;\\n        flex-wrap: wrap;\\n        justify-content: center;\\n      }\\n\\n      #swiper-methods .swiper-wrapper .swiper-slide {\\n        flex: 1 1 calc(25% - 1.875rem);\\n        max-width: 330px;\\n      }\\n    }\\n    .feature-item {\\n      width: 100%;\\n      border-radius: 1rem;\\n      position: relative;\\n      background-color: #046fff;\\n      color: #fff;\\n      transition: unset;\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      flex-direction: column;\\n      text-align: left;\\n      text-decoration: none;\\n      display: block;\\n      margin-top: 32%;\\n      padding-top: 33%;\\n      min-height: 400px;\\n      @media (max-width: 992px) {\\n        max-width: unset;\\n      }\\n\\n      @media (any-hover: hover) {\\n        &:hover {\\n          background-color: #222222;\\n          .method-detail {\\n            display: none;\\n          }\\n          .step-detail {\\n            display: block;\\n          }\\n        }\\n      }\\n\\n      .feature-img {\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n\\n        transform: translateY(-50%);\\n      }\\n      .method-detail {\\n        padding: 0 1rem 2.375rem;\\n\\n        .divider {\\n          width: 100%;\\n          height: 1px;\\n          background-color: #ffffff4d;\\n        }\\n        .right-content-item,\\n        .fault-content-item {\\n          display: flex;\\n          align-items: flex-start;\\n          gap: 4px;\\n          font-weight: 300;\\n          font-size: 14px;\\n          color: #fff;\\n        }\\n      }\\n      .step-btn {\\n        margin: 0 auto;\\n        width: 204px;\\n        padding: 10px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        border-radius: 8px;\\n        background: rgba(255, 255, 255, 0.36);\\n        color: #fff;\\n        font-size: 18px;\\n        font-weight: 800;\\n        text-align: center;\\n        margin-bottom: 30px;\\n        &.active {\\n          svg {\\n            transform: rotate(180deg);\\n          }\\n        }\\n      }\\n      .step-detail {\\n        display: none;\\n        padding: 0 2rem 0 2.5rem;\\n        @media (max-width: 576px) {\\n          margin-bottom: 24px;\\n        }\\n\\n        ul {\\n          list-style: decimal;\\n          li {\\n            font-weight: 300;\\n            font-size: 12px;\\n            color: #fff;\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-table {\\n    @keyframes marquee1 {\\n      0% {\\n        transform: translateX(0);\\n      }\\n      100% {\\n        transform: translateX(-50%);\\n      }\\n    }\\n    .table-wrapper {\\n      border-radius: 24px;\\n      width: 100%;\\n      position: relative;\\n      z-index: 1;\\n      overflow: hidden;\\n      background-color: #0085ff;\\n      padding: 1px;\\n      overflow: visible;\\n      margin-top: 5rem;\\n\\n      .inner-table,\\n      .table-blue {\\n        border-collapse: collapse;\\n        border-style: hidden;\\n        width: 100%;\\n        background: #fff;\\n        border-radius: 24px;\\n        overflow: hidden;\\n      }\\n\\n      .inner-table .opacity-5,\\n      .table-blue .opacity-5 {\\n        opacity: 0.5;\\n      }\\n\\n      .inner-table tr:not(:last-child),\\n      .table-blue tr:not(:last-child) {\\n        border-bottom: 1px solid rgba(0, 133, 255, 1);\\n      }\\n\\n      .inner-table th,\\n      .table-blue th {\\n        height: 6rem;\\n        width: calc(100% / 4);\\n        font-weight: 700;\\n        font-size: 1.375rem;\\n        background-color: rgba(0, 133, 255, 0.1);\\n        vertical-align: middle;\\n        text-align: center;\\n        border-bottom: 1px solid #0085ff;\\n        &:not(:last-child) {\\n          border-right: 1px solid #0085ff;\\n        }\\n      }\\n\\n      .inner-table td,\\n      .table-blue td {\\n        height: 5.3125rem;\\n        vertical-align: middle;\\n        text-align: center;\\n        font-weight: 600;\\n        font-size: 1.125rem;\\n\\n        &:first-child {\\n          background-color: rgba(0, 133, 255, 0.1);\\n          font-size: 1.375rem;\\n          font-weight: 700;\\n        }\\n        &:not(:last-child) {\\n          border-right: 1px solid rgba(0, 133, 255, 1);\\n        }\\n      }\\n\\n      @media (max-width: 1280px) {\\n        .inner-table th,\\n        .table-blue th {\\n          font-size: 1.5rem;\\n        }\\n      }\\n\\n      @media (max-width: 1280px) {\\n        .inner-table td,\\n        .table-blue td {\\n          font-size: 1.25rem;\\n        }\\n      }\\n\\n      .inner-table td div,\\n      .table-blue td div {\\n        display: flex;\\n        gap: 15px;\\n        align-items: center;\\n        justify-content: flex-start;\\n        max-width: 75%;\\n        margin: 0 auto;\\n      }\\n\\n      .blue-table-wrapper {\\n        position: absolute;\\n        top: 0;\\n        left: calc(100% / 4);\\n        width: calc(100% / 4);\\n        z-index: 4;\\n        border-radius: 1.5rem;\\n        padding: 1.5rem 1rem 1.5rem;\\n        transform: translateY(-1.5rem);\\n        background-color: #046fff;\\n      }\\n\\n      .blue-table-wrapper .doc-logo {\\n        position: absolute;\\n        top: 0;\\n        left: 50%;\\n        transform: translate(-50%, -50%);\\n      }\\n\\n      .blue-table-wrapper .doc-logo img {\\n        width: 5rem;\\n      }\\n\\n      .blue-table-wrapper .table-blue {\\n        width: 100%;\\n        border-collapse: collapse;\\n        border-style: hidden;\\n        background-color: #046fff;\\n      }\\n\\n      .blue-table-wrapper .table-blue th {\\n        color: #fff;\\n        border-bottom: 1px solid #ffffff;\\n      }\\n\\n      .blue-table-wrapper .table-blue tr:last-child td {\\n        border-bottom: none;\\n      }\\n\\n      .blue-table-wrapper .table-blue td {\\n        color: #fff;\\n        border-bottom: 1px solid #ffffff;\\n        font-size: 1.125rem;\\n        font-weight: 600;\\n      }\\n\\n      .blue-table-wrapper .table-blue td div {\\n        all: unset;\\n        display: flex;\\n        gap: 15px;\\n        color: #fff;\\n      }\\n\\n      .blue-table-wrapper .table-blue td div span {\\n        text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);\\n      }\\n    }\\n    .marquee-wrapper {\\n      display: flex;\\n      flex-wrap: nowrap;\\n      width: fit-content;\\n      .marquee-item {\\n        font-weight: 600;\\n        font-size: 1.25rem;\\n        color: #000;\\n        white-space: nowrap;\\n        margin: 0 1.125rem;\\n      }\\n    }\\n    .marquee-wrapper {\\n      animation: marquee1 40s linear infinite;\\n    }\\n  }\\n\\n  .part-features {\\n    @media (min-width: 1280px) {\\n      #swiper-features .swiper-wrapper {\\n        gap: 1.25rem;\\n        flex-wrap: wrap;\\n        justify-content: center;\\n      }\\n\\n      #swiper-features .swiper-wrapper .swiper-slide {\\n        flex: 1 1 calc(50% - 1.25rem);\\n      }\\n    }\\n    .feature-box {\\n      display: flex;\\n      justify-content: center;\\n      border-radius: 1rem;\\n      background-color: #edf7ff;\\n      overflow: hidden;\\n      height: 100%;\\n      @media (max-width: 1280px) {\\n        flex-direction: column;\\n        justify-content: flex-start;\\n      }\\n      .img-wrapper {\\n        flex: 0 0 42%;\\n        img {\\n          width: 100%;\\n          height: 100%;\\n          object-fit: cover;\\n        }\\n      }\\n      .text-wrapper {\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        padding: 0 3rem;\\n        font-weight: 500;\\n        font-size: 1.125rem;\\n        color: #707070;\\n        @media (max-width: 1600px) {\\n          padding: 0 1rem;\\n        }\\n        @media (max-width: 1280px) {\\n          padding: 32px 24px;\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-helps {\\n    @media (min-width: 1280px) {\\n      .swiper-wrapper {\\n        display: block;\\n        transform: initial;\\n      }\\n    }\\n    .scrollCard {\\n      overflow: hidden;\\n      position: relative;\\n      height: 100%;\\n      .scrollCard-detail {\\n        display: flex;\\n        background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/card-right-bg.jpg) no-repeat center center / cover;\\n        border-radius: 1rem;\\n        overflow: hidden;\\n        @media (max-width: 1280px) {\\n          flex-direction: column;\\n          background: #222222;\\n          height: 100%;\\n          justify-content: flex-start;\\n          align-items: stretch;\\n        }\\n        &.scrollCard-detail2 {\\n          background-image: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/card-left-bg.jpg);\\n          justify-content: space-between;\\n          @media (max-width: 1280px) {\\n            background: #222222;\\n            justify-content: flex-start;\\n          }\\n        }\\n        .scrollCard-userImg {\\n          overflow: hidden;\\n          flex: 0 1 46.78%;\\n          @media (max-width: 1280px) {\\n            flex: unset;\\n          }\\n          img {\\n            height: 100%;\\n            width: 100%;\\n            object-fit: cover;\\n          }\\n        }\\n        .scrollCard-Intro {\\n          flex: 1 1 53.22%;\\n          display: flex;\\n          flex-direction: column;\\n          color: #fff;\\n          justify-content: center;\\n          margin: 0 5.9375rem;\\n          max-width: 480px;\\n          text-align: left;\\n          align-items: flex-start;\\n          position: relative;\\n          padding: 1.5rem 0;\\n          @media (max-width: 1280px) {\\n            margin: 0;\\n            padding: 24px 16px;\\n            flex: 1;\\n            max-width: unset;\\n            justify-content: flex-start;\\n          }\\n          @media (max-width: 768px) {\\n            .font-size-super {\\n              font-size: 16px;\\n            }\\n            p.font-size-large {\\n              font-size: 12px;\\n              opacity: 0.5;\\n            }\\n          }\\n          &::after {\\n            content: \\\"\\\";\\n            position: absolute;\\n            left: -15%;\\n            top: 10%;\\n            width: 10%;\\n            background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/tip-left.svg) no-repeat center center / contain;\\n            aspect-ratio: 51 / 40;\\n            @media (max-width: 1280px) {\\n              display: none;\\n            }\\n          }\\n          &::before {\\n            content: \\\"\\\";\\n            position: absolute;\\n            right: -28%;\\n            top: 10%;\\n            width: 10%;\\n            background: url(https://mobiletrans.wondershare.com/images/images2025/video-transfer/tip-right.svg) no-repeat center center / contain;\\n            aspect-ratio: 51 / 40;\\n            @media (max-width: 1280px) {\\n              display: none;\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-faq {\\n    .accordion-item {\\n      padding: 1.5rem 0;\\n      border-bottom: 1px solid #cfd3d5;\\n      @media (max-width: 576px) {\\n        padding: 14px;\\n      }\\n    }\\n    .accordion-item [aria-expanded=\\\"true\\\"] svg {\\n      transform: rotate(180deg);\\n    }\\n\\n    .accordion-item .faq-title {\\n      display: flex;\\n      align-items: center;\\n      justify-content: left;\\n      gap: 8px;\\n      flex-shrink: 0;\\n      max-width: 90%;\\n      font-size: 1.5rem;\\n      font-weight: 700;\\n      @media (max-width: 576px) {\\n        font-size: 14px;\\n      }\\n    }\\n    .topic-box {\\n      height: 100%;\\n      border-radius: 1rem;\\n      background-color: #edf7ff;\\n      padding: 2.125rem;\\n      color: #0084ff;\\n      .link-list {\\n        display: flex;\\n        flex-direction: column;\\n        gap: 0.75rem;\\n\\n        .link-item {\\n          font-weight: 700;\\n          color: #0084ff;\\n          font-size: 1rem;\\n        }\\n      }\\n    }\\n  }\\n\\n  .part-footer {\\n    .btn-wrapper .btn-white {\\n      color: #0080ff;\\n    }\\n\\n    .btn-wrapper .btn-outline-white:hover {\\n      color: #0080ff;\\n    }\\n\\n    .footer-box {\\n      border-radius: 1.25rem;\\n      background: url(https://mobiletrans.wondershare.com/images/images2025/index/footer-bg.jpg) no-repeat center center/cover;\\n      display: flex;\\n      flex-direction: column;\\n      align-items: center;\\n      justify-content: center;\\n      color: #fff;\\n      padding: 4rem 0;\\n      color: #fff;\\n    }\\n\\n    @media (max-width: 768px) {\\n      .footer-box {\\n        padding: 2.5rem 1rem;\\n        margin: 0 15px;\\n        text-align: center;\\n      }\\n    }\\n\\n    .footer-box .btn {\\n      min-width: 210px;\\n      border-radius: 4px;\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2\n");

/***/ }),

/***/ 314:
/***/ ((module) => {

eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///314\n");

/***/ }),

/***/ 354:
/***/ ((module) => {

eval("\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzU0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsY0FBYztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2Nzcy1sb2FkZXIvZGlzdC9ydW50aW1lL3NvdXJjZU1hcHMuanM/YWYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoaXRlbSkge1xuICB2YXIgY29udGVudCA9IGl0ZW1bMV07XG4gIHZhciBjc3NNYXBwaW5nID0gaXRlbVszXTtcbiAgaWYgKCFjc3NNYXBwaW5nKSB7XG4gICAgcmV0dXJuIGNvbnRlbnQ7XG4gIH1cbiAgaWYgKHR5cGVvZiBidG9hID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB2YXIgYmFzZTY0ID0gYnRvYSh1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoY3NzTWFwcGluZykpKSk7XG4gICAgdmFyIGRhdGEgPSBcInNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LFwiLmNvbmNhdChiYXNlNjQpO1xuICAgIHZhciBzb3VyY2VNYXBwaW5nID0gXCIvKiMgXCIuY29uY2F0KGRhdGEsIFwiICovXCIpO1xuICAgIHJldHVybiBbY29udGVudF0uY29uY2F0KFtzb3VyY2VNYXBwaW5nXSkuam9pbihcIlxcblwiKTtcbiAgfVxuICByZXR1cm4gW2NvbnRlbnRdLmpvaW4oXCJcXG5cIik7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///354\n");

/***/ }),

/***/ 72:
/***/ ((module) => {

eval("\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///72\n");

/***/ }),

/***/ 659:
/***/ ((module) => {

eval("\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU5LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanM/YjIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIG1lbW8gPSB7fTtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBnZXRUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0eXBlb2YgbWVtb1t0YXJnZXRdID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIHN0eWxlVGFyZ2V0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcih0YXJnZXQpO1xuXG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIHJldHVybiBoZWFkIG9mIGlmcmFtZSBpbnN0ZWFkIG9mIGlmcmFtZSBpdHNlbGZcbiAgICBpZiAod2luZG93LkhUTUxJRnJhbWVFbGVtZW50ICYmIHN0eWxlVGFyZ2V0IGluc3RhbmNlb2Ygd2luZG93LkhUTUxJRnJhbWVFbGVtZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUaGlzIHdpbGwgdGhyb3cgYW4gZXhjZXB0aW9uIGlmIGFjY2VzcyB0byBpZnJhbWUgaXMgYmxvY2tlZFxuICAgICAgICAvLyBkdWUgdG8gY3Jvc3Mtb3JpZ2luIHJlc3RyaWN0aW9uc1xuICAgICAgICBzdHlsZVRhcmdldCA9IHN0eWxlVGFyZ2V0LmNvbnRlbnREb2N1bWVudC5oZWFkO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgICAgICBzdHlsZVRhcmdldCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG1lbW9bdGFyZ2V0XSA9IHN0eWxlVGFyZ2V0O1xuICB9XG4gIHJldHVybiBtZW1vW3RhcmdldF07XG59XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gaW5zZXJ0QnlTZWxlY3RvcihpbnNlcnQsIHN0eWxlKSB7XG4gIHZhciB0YXJnZXQgPSBnZXRUYXJnZXQoaW5zZXJ0KTtcbiAgaWYgKCF0YXJnZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBmaW5kIGEgc3R5bGUgdGFyZ2V0LiBUaGlzIHByb2JhYmx5IG1lYW5zIHRoYXQgdGhlIHZhbHVlIGZvciB0aGUgJ2luc2VydCcgcGFyYW1ldGVyIGlzIGludmFsaWQuXCIpO1xuICB9XG4gIHRhcmdldC5hcHBlbmRDaGlsZChzdHlsZSk7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydEJ5U2VsZWN0b3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///659\n");

/***/ }),

/***/ 540:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTQwLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRTdHlsZUVsZW1lbnQuanM/ZGU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBpbnNlcnRTdHlsZUVsZW1lbnQob3B0aW9ucykge1xuICB2YXIgZWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgb3B0aW9ucy5zZXRBdHRyaWJ1dGVzKGVsZW1lbnQsIG9wdGlvbnMuYXR0cmlidXRlcyk7XG4gIG9wdGlvbnMuaW5zZXJ0KGVsZW1lbnQsIG9wdGlvbnMub3B0aW9ucyk7XG4gIHJldHVybiBlbGVtZW50O1xufVxubW9kdWxlLmV4cG9ydHMgPSBpbnNlcnRTdHlsZUVsZW1lbnQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///540\n");

/***/ }),

/***/ 56:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLGNBQWMsS0FBd0MsR0FBRyxzQkFBaUIsR0FBRyxDQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanM/ZGRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMoc3R5bGVFbGVtZW50KSB7XG4gIHZhciBub25jZSA9IHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gXCJ1bmRlZmluZWRcIiA/IF9fd2VicGFja19ub25jZV9fIDogbnVsbDtcbiAgaWYgKG5vbmNlKSB7XG4gICAgc3R5bGVFbGVtZW50LnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///56\n");

/***/ }),

/***/ 825:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n  var needLayer = typeof obj.layer !== \"undefined\";\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += \"}\";\n  }\n  if (obj.media) {\n    css += \"}\";\n  }\n  if (obj.supports) {\n    css += \"}\";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === \"undefined\") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///825\n");

/***/ }),

/***/ 113:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTEzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc3R5bGVUYWdUcmFuc2Zvcm0uanM/MWRkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzdHlsZVRhZ1RyYW5zZm9ybShjc3MsIHN0eWxlRWxlbWVudCkge1xuICBpZiAoc3R5bGVFbGVtZW50LnN0eWxlU2hlZXQpIHtcbiAgICBzdHlsZUVsZW1lbnQuc3R5bGVTaGVldC5jc3NUZXh0ID0gY3NzO1xuICB9IGVsc2Uge1xuICAgIHdoaWxlIChzdHlsZUVsZW1lbnQuZmlyc3RDaGlsZCkge1xuICAgICAgc3R5bGVFbGVtZW50LnJlbW92ZUNoaWxkKHN0eWxlRWxlbWVudC5maXJzdENoaWxkKTtcbiAgICB9XG4gICAgc3R5bGVFbGVtZW50LmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZVRleHROb2RlKGNzcykpO1xuICB9XG59XG5tb2R1bGUuZXhwb3J0cyA9IHN0eWxlVGFnVHJhbnNmb3JtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///113\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__(787);
/******/ 	
/******/ })()
;