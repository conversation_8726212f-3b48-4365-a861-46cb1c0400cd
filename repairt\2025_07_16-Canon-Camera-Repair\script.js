$(() => {
  // ===================================================================
  // 1. SWIPER 轮播组件初始化
  // ===================================================================

  // 专业维修轮播 - 淡入淡出效果
  const proRepairSwiper = new Swiper("#pro-repair-swiper", {
    effect: "fade",
    fadeEffect: {
      crossFade: true, // 启用交叉淡入淡出
    },
    // 禁止所有手动交互
    allowTouchMove: false,
    noSwiping: true,
    simulateTouch: false,
    keyboard: { enabled: false },
  });

  // 专业维修照片轮播 - 淡入淡出效果
  const photoSwiper = new Swiper("#pro-repair-photo-swiper", {
    effect: "fade",
    fadeEffect: {
      crossFade: true,
    },
    allowTouchMove: false,
    noSwiping: true,
    simulateTouch: false,
    keyboard: { enabled: false },
  });

  // 工具展示轮播 - 响应式配置
  const swiperTool = new Swiper("#swiper-tool", {
    slidesPerView: 1,
    spaceBetween: 30,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      768: {
        slidesPerView: 2,
        spaceBetween: 20,
      },
      992: {
        slidesPerView: 3,
        spaceBetween: 30,
      },
      1600: {
        slidesPerView: 4,
        spaceBetween: 30,
        autoplay: false,
        loop: false,
      },
    },
  });

  // 方法展示轮播
  const methodsSwiper = new Swiper("#methods-swiper", {
    loop: true,
    allowTouchMove: false,
    on: {
      slideChange: function () {
        $(".methods-nav .col-md-6").eq(this.realIndex).addClass("active").siblings().removeClass("active");
      },
    },
    navigation: {
      prevEl: ".slidebtn-methods-prev",
      nextEl: ".slidebtn-methods-next",
    },
  });

  // 创建公共 Swiper 方法
  function createCommonTitleSwiper(swiperSelector, $parentElement) {
    return new Swiper(swiperSelector, {
      slidesPerView: 1,
      centeredSlides: true,
      loop: true,
      navigation: {
        nextEl: swiperSelector + "-next",
        prevEl: swiperSelector + "-prev",
      },
      on: {
        init: function () {
          // 初始化时激活第一个元素
          $parentElement.find(".img-wrapper .img-item").eq(0).addClass("active");
          $parentElement.find(".content-wrapper .content-item").eq(0).addClass("active");
        },
        slideChangeTransitionStart: function () {
          // 切换时更新激活状态
          const activeIndex = this.realIndex;
          $parentElement.find(".img-wrapper .img-item").eq(activeIndex).addClass("active").siblings().removeClass("active");
          $parentElement.find(".content-wrapper .content-item").eq(activeIndex).addClass("active").siblings().removeClass("active");
        },
      },
    });
  }

  // ===================================================================
  // 2. 交互功能初始化
  // ===================================================================

  // 根据屏幕尺寸初始化不同的交互方式
  if (window.innerWidth > 1280) {
    // 桌面端：鼠标悬停交互
    initDesktopInteraction();
  } else {
    // 移动端：Swiper 轮播
    const workworkspaceSwiper1 = createCommonTitleSwiper(".workworkspaceSwiper1", $(".part-reason"));
  }

  // 桌面端交互功能
  function initDesktopInteraction() {
    const $reasonItems = $(".part-reason .content-wrapper .content-item");

    $reasonItems.on("mouseenter", function () {
      const $currentItem = $(this);
      const itemIndex = $currentItem.index();

      // 激活当前项，隐藏其他项
      $currentItem.addClass("active").siblings().addClass("hideItem");
      $currentItem.find(".desc").slideDown();

      // 切换对应的图片
      $currentItem.parents(".box-style").find(".img-wrapper .img-item").eq(itemIndex).addClass("active").siblings().removeClass("active");
    });

    $reasonItems.on("mouseleave", function () {
      const $currentItem = $(this);
      const itemIndex = $currentItem.index();

      // 重置状态
      $currentItem.removeClass("active").siblings().removeClass("hideItem");
      $currentItem.find(".desc").slideUp();

      // 重置图片状态
      $currentItem.parents(".box-style").find(".img-wrapper .img-item").eq(itemIndex).removeClass("active");
    });
  }

  // 方法导航点击事件
  $(".methods-nav .col-md-6").on("click", function () {
    const targetIndex = $(this).index() + 1;
    methodsSwiper.slideTo(targetIndex);
  });

  // ===================================================================
  // 3. 手风琴与 SWIPER 联动
  // ===================================================================

  // 第一个手风琴控制专业维修轮播
  $("#pro-repair-accordion .pro-repair-accordion-header").each(function (index) {
    $(this).on("click", function () {
      proRepairSwiper.slideTo(index);
    });
  });

  // 第二个手风琴控制照片轮播
  $("#pro-repair-photo-accordion .pro-repair-accordion-header").each(function (index) {
    $(this).on("click", function () {
      photoSwiper.slideTo(index);
    });
  });

  // ===================================================================
  // 4. 图片对比滑块功能
  // ===================================================================

  // 图片对比滑块控制函数
  function initCompareSlider(sliderElement, beforeImageElement) {
    const slider = sliderElement;
    const beforeImage = beforeImageElement;
    const container = $(slider).parent();

    // 设置滑块的最大值和初始值
    slider.max = container.width();
    slider.value = container.width() / 2;

    // 移动端拖动状态管理
    let isDragging = false;

    // 更新滑块位置的通用函数（移动端触摸使用）
    function updateSliderPosition(clientX) {
      const rect = container[0].getBoundingClientRect();
      const x = clientX - rect.left;
      const clampedX = Math.max(0, Math.min(x, rect.width));

      // 更新滑块值和图片宽度
      slider.value = clampedX;
      beforeImage.style.width = clampedX + "px";
    }

    // 输入事件处理（滑块原生拖动）
    slider.addEventListener("input", function (e) {
      beforeImage.style.width = e.target.value + "px";
    });

    // 鼠标事件处理（桌面端 - hover跟随）
    let isHovering = false;

    container.on("mouseenter", function () {
      isHovering = true;
    });

    container.on("mouseleave", function () {
      isHovering = false;
    });

    container.on("mousemove", function (e) {
      if (isHovering) {
        const rect = container[0].getBoundingClientRect();
        const x = e.clientX - rect.left;
        const clampedX = Math.max(0, Math.min(x, rect.width));

        slider.value = clampedX;
        beforeImage.style.width = clampedX + "px";
      }
    });

    // 触摸事件处理（移动端）
    slider.addEventListener(
      "touchstart",
      function (e) {
        isDragging = true;
        const touch = e.touches[0];
        updateSliderPosition(touch.clientX);
        e.preventDefault();
      },
      { passive: false }
    );

    slider.addEventListener(
      "touchmove",
      function (e) {
        if (isDragging) {
          const touch = e.touches[0];
          updateSliderPosition(touch.clientX);
          e.preventDefault();
        }
      },
      { passive: false }
    );

    slider.addEventListener("touchend", function (e) {
      isDragging = false;
      e.preventDefault();
    });

    // 容器触摸事件处理
    container.on("touchstart", function (e) {
      if (e.target === slider) return; // 避免重复处理滑块自身的触摸

      const touch = e.originalEvent.touches[0];
      const rect = container[0].getBoundingClientRect();
      const x = touch.clientX - rect.left;
      const clampedX = Math.max(0, Math.min(x, rect.width));

      slider.value = clampedX;
      beforeImage.style.width = clampedX + "px";
      e.preventDefault();
    });
  }

  // 初始化两个对比滑块
  const compareSlider1 = $(".compare-img-box .compare-before-1~.slider")[0];
  const compareImage1 = $(".compare-img-box .compare-before-1")[0];
  if (compareSlider1 && compareImage1) {
    initCompareSlider(compareSlider1, compareImage1);
  }

  const compareSlider2 = $(".compare-img-box .compare-before-2~.slider")[0];
  const compareImage2 = $(".compare-img-box .compare-before-2")[0];
  if (compareSlider2 && compareImage2) {
    initCompareSlider(compareSlider2, compareImage2);
  }

  // ===================================================================
  // 5. 视频懒加载功能
  // ===================================================================

  (function initVideoLazyLoad() {
    let lazyVideos = $(".lazy-video").toArray();
    if (!lazyVideos.length) return;

    // 检查元素是否在视口中
    const isElementInViewport = (element) => {
      const rect = element.getBoundingClientRect();
      return rect.top >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight);
    };

    // 懒加载处理函数
    const handleLazyLoad = () => {
      lazyVideos = lazyVideos.filter((videoElement) => {
        if (isElementInViewport(videoElement)) {
          const $video = $(videoElement);

          // 设置视频源
          $video.attr("src", $video.data("src"));

          // 设置封面图（如果存在）
          if ($video.data("poster")) {
            $video.attr("poster", $video.data("poster"));
          }

          // 开始加载视频
          $video[0].load();

          return false; // 从数组中移除已加载的视频
        }
        return true; // 保留未加载的视频
      });

      // 所有视频加载完成后移除监听器
      if (!lazyVideos.length) {
        $(window).off("scroll.lazyVideo");
      }
    };

    // 绑定滚动事件和页面加载事件
    $(window).on("scroll.lazyVideo", handleLazyLoad);
    $(document).ready(handleLazyLoad);
  })();

  // ===================================================================
  // 6. GSAP 动画效果 (仅桌面端)
  // ===================================================================

  // 注册 GSAP 插件
  gsap.registerPlugin(ScrollTrigger);

  // 桌面端相机动画效果
  if (window.innerWidth > 1280) {
    initCameraAnimation();
  }

  function initCameraAnimation() {
    // 获取所有相机元素
    const cameraElements = gsap.utils.toArray(".part-cameras .content-img .camera-item");

    // 存储原始位置信息
    const originalPositions = cameraElements.map((element) => {
      const computedStyle = window.getComputedStyle(element);
      return {
        position: computedStyle.position,
        right: computedStyle.right,
        left: computedStyle.left,
        bottom: computedStyle.bottom,
        width: computedStyle.width,
        top: computedStyle.top,
        transform: computedStyle.transform,
      };
    });

    // 设置初始状态 - 所有元素聚集在中心
    gsap.set(cameraElements, {
      position: "absolute",
      left: "50%",
      top: "50%",
      xPercent: -50,
      yPercent: -50,
      scale: 0.7,
      opacity: 0.3,
    });

    // 创建滚动触发的散开动画
    ScrollTrigger.create({
      trigger: ".part-cameras",
      start: "top center",
      end: "bottom center",
      once: true, // 只触发一次
      onEnter: () => {
        // 元素散开到原始位置
        cameraElements.forEach((element, index) => {
          const originalPosition = originalPositions[index];

          gsap.to(element, {
            opacity: 0.3,
            scale: 1,
            duration: 1.5,
            ease: "power2.out",
            position: "absolute",
            left: originalPosition.left,
            right: originalPosition.right,
            bottom: originalPosition.bottom,
            top: originalPosition.top,
            width: originalPosition.width,
            xPercent: 0,
            yPercent: 0,
          });
        });
      },
    });
  }
});
