import "./index.scss";

// 判断用户的操作系统并设置初始状态
if (wsc.is.ios || wsc.is.mac) {
  // 如果是 iOS 或mac设备，激活 iOS 按钮
  $(".sys-change .ios-change").addClass("active").siblings().removeClass("active");

  $(".sys-ios-custom").show();
  $(".sys-android-custom").hide();
} else {
  $(".sys-change .android-change").addClass("active").siblings().removeClass("active");

  $(".sys-android-custom").show();
  $(".sys-ios-custom").hide();
}
$(() => {
  // 在DOM加载前就禁用所有AOS元素的动画效果（针对小屏幕）
  if (window.innerWidth <= 992) {
    // 创建一个样式表来立即覆盖AOS动画
    const styleEl = document.createElement("style");
    styleEl.textContent = "[data-aos] { transition-duration: 0s !important; transform: none !important; opacity: 1 !important; }";
    document.head.appendChild(styleEl);
  }
  // 仅在大于992px尺寸下初始化AOS;
  if (window.innerWidth > 992) {
    AOS.init({
      once: true,
    });
  } else {
    // 确保小屏幕上AOS元素立即显示，没有任何动画效果
    $("[data-aos]").each(function () {
      $(this).removeAttr("data-aos").css({ transform: "none", opacity: "1" }).removeClass("aos-init aos-animate");
    });
  }
  if (window.innerWidth < 1280) {
    // 创建基础Swiper配置
    const baseSwiperConfig = {
      spaceBetween: 10,
      loop: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
        clickable: true,
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
          spaceBetween: 20,
        },
      },
    };

    // 初始化所有Swiper实例
    const swiperConfigs = {
      "ios-phone-swiper": {
        ...baseSwiperConfig,
        slidesPerView: 1,
        centeredSlides: false,
      },
      "android-phone-swiper": {
        ...baseSwiperConfig,
        slidesPerView: 1,
        centeredSlides: false,
      },
      "all-tools-swiper": {
        ...baseSwiperConfig,
        slidesPerView: 1.5,
        centeredSlides: true,
        loop: false,
        pagination: {
          el: "#all-tools-swiper .swiper-pagination",
          clickable: true,
        },
        breakpoints: {
          768: {
            slidesPerView: 3,
          },
        },
      },
      "swiper-discover": {
        ...baseSwiperConfig,
        slidesPerView: 1,
        centeredSlides: false,
      },
      "swiper-right": {
        ...baseSwiperConfig,
        slidesPerView: 1.2,
        centeredSlides: true,
        breakpoints: {
          768: {
            slidesPerView: 2,
            spaceBetween: 30,
            centeredSlides: false,
          },
        },
        pagination: {
          el: "#swiper-right .swiper-pagination",
          clickable: true,
        },
      },
      "swiper-customer": {
        ...baseSwiperConfig,
        slidesPerView: 1,
        centeredSlides: true,
        breakpoints: {
          768: {
            slidesPerView: 2,
            spaceBetween: 30,
            centeredSlides: false,
          },
        },
        pagination: {
          el: "#swiper-customer .swiper-pagination",
          clickable: true,
        },
      },
    };

    // 初始化所有Swiper
    Object.entries(swiperConfigs).forEach(([elementId, config]) => {
      new Swiper(`#${elementId}`, config);
    });
  }
  // 更新Swiper函数
  function updateSwiper() {
    var allToolsSwiper = document.querySelector("#all-tools-swiper");
    if (allToolsSwiper && allToolsSwiper.swiper) {
      setTimeout(() => {
        allToolsSwiper.swiper.update();
        allToolsSwiper.swiper.pagination.render();
        allToolsSwiper.swiper.pagination.update();
      }, 10);
    }
  }

  let currentSystem = wsc.is.ios || wsc.is.mac ? "ios" : "android";
  let currentTool = "system-repair"; // 默认工具

  // pc端下保存iTunes幻灯片的HTML内容和位置信息
  const iosCardShowHTML = $(".tools-card-list .ios-card-show").length ? $(".tools-card-list .ios-card-show").prop("outerHTML") : "";
  const iosCardShowPosition = $(".tools-card-list .ios-card-show").index();

  // mobile端下保存iTunes幻灯片的HTML内容和位置信息
  const itunesSlideHTML = $(".swiper-slide.h-auto.ios-slide-show").length ? $(".swiper-slide.h-auto.ios-slide-show").prop("outerHTML") : "";
  const itunesSlidePosition = $(".swiper-slide.h-auto.ios-slide-show").index();

  function updateSolutionImage() {
    // 隐藏所有图片
    $(".system-img-wrapper img").removeClass("show");
    // 根据当前系统和工具，使用 data attributes 选择并显示对应的图片
    const imageSelector = `[data-system="${currentSystem}"][data-tool="${currentTool}"]`;
    $(`.system-img-wrapper img${imageSelector}`).addClass("show");
  }

  /**
   * 根据所选系统（'ios' 或 'android'）更新整个视图。
   * @param {string} system - 目标操作系统。
   */
  function updateSystemView(system) {
    const isIOS = system === "ios";
    currentSystem = system;

    // 1. 更新主切换按钮的状态
    $(".sys-change .ios-change").toggleClass("active", isIOS);
    $(".sys-change .android-change").toggleClass("active", !isIOS);

    // 2. 显示/隐藏特定于系统的UI组件
    $(".sys-ios-custom").toggle(isIOS);
    $(".sys-android-custom").toggle(!isIOS);

    // 3. 处理 iTunes 相关元素的显示/隐藏
    if (isIOS) {
      // 如果是iOS，则添加iTunes幻灯片（移动端）和卡片（PC端）
      if ($("#all-tools-swiper .swiper-slide.h-auto.ios-slide-show").length === 0 && itunesSlideHTML) {
        if (itunesSlidePosition > 0) {
          $("#all-tools-swiper .swiper-slide.h-auto")
            .eq(itunesSlidePosition - 1)
            .after(itunesSlideHTML);
        } else {
          $("#all-tools-swiper .swiper-wrapper").prepend(itunesSlideHTML);
        }
      }
      if ($(".tools-card-list .ios-card-show").length === 0 && iosCardShowHTML) {
        if (iosCardShowPosition > 0) {
          $(".tools-card-list .tool-card")
            .eq(iosCardShowPosition - 1)
            .after(iosCardShowHTML);
        } else {
          $(".tools-card-list").prepend(iosCardShowHTML);
        }
      }
    } else {
      // 如果是Android，则移除这两个元素
      $(".tools-card-list .ios-card-show").remove();
      $("#all-tools-swiper .swiper-slide.h-auto.ios-slide-show").remove();
    }

    // 4. 更新Swiper和解决方案图片
    updateSwiper();
    updateSolutionImage();
  }

  // ---- 初始化和事件绑定 ----

  // 页面加载时，根据检测到的系统设置初始视图
  updateSystemView(currentSystem);

  // 为系统切换按钮绑定点击事件
  $(".sys-change [class*='-change']").on("click", function () {
    if ($(this).hasClass("active")) {
      return;
    }
    const targetSystem = $(this).hasClass("ios-change") ? "ios" : "android";
    updateSystemView(targetSystem);
  });

  // 为其他地方的切换按钮也绑定事件
  $(".right-item .change-to-ios").on("click", function () {
    updateSystemView("ios");
  });
  $(".right-item .change-to-android").on("click", function () {
    updateSystemView("android");
  });

  // 工具区域的鼠标悬停事件，用于切换图片
  $(".system-change-wrapper .system-repair, .system-change-wrapper .data-recovery").on("mouseenter", function () {
    // 更新激活状态
    $(this).addClass("active").siblings().removeClass("active");
    // 更新当前工具
    currentTool = $(this).hasClass("system-repair") ? "system-repair" : "data-recovery";
    // 更新图片显示
    updateSolutionImage();
  });

  // 打字机效果实现
  class TxtType {
    constructor(el, toRotate, period) {
      this.toRotate = toRotate;
      this.el = el;
      this.loopNum = 0;
      this.period = parseInt(period, 10) || 2000;
      this.txt = "";
      this.isDeleting = false;
      this.tick();
    }

    tick() {
      const i = this.loopNum % this.toRotate.length;
      const fullTxt = this.toRotate[i];

      this.txt = this.isDeleting ? fullTxt.substring(0, this.txt.length - 1) : fullTxt.substring(0, this.txt.length + 1);

      this.el.querySelector(".wrap").textContent = this.txt;

      let delta = 150 - Math.random() * 100;

      if (this.isDeleting) {
        delta /= 2;
      }

      if (!this.isDeleting && this.txt === fullTxt) {
        delta = this.period;
        this.isDeleting = true;
      } else if (this.isDeleting && this.txt === "") {
        this.isDeleting = false;
        this.loopNum++;
        delta = 500;
      }

      setTimeout(() => this.tick(), delta);
    }
  }
  const initTypewriter = () => {
    // 如果已经初始化过，则不重复初始化
    if (window.typewriterInitialized) return;
    window.typewriterInitialized = true;

    const elements = document.getElementsByClassName("typewrite");

    if (!elements.length) return;

    Array.from(elements).forEach((element) => {
      const toRotate = element.getAttribute("data-type");
      const period = element.getAttribute("data-period");
      if (toRotate) {
        new TxtType(element, JSON.parse(toRotate), period);
      }
    });
  };
  initTypewriter();

  const swiperProblems = new Swiper("#swiper-problems", {
    slidesPerView: 1,
    spaceBetween: 30,
    loop: true,
    loopedSlides: 2,
    autoplay: {
      delay: 6000,
      disableOnInteraction: false,
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      1280: {
        slidesPerView: 1,
      },
      768: {
        slidesPerView: 2,
      },
    },
    on: {
      slideChange: function () {
        const currentSlide = this.realIndex;
        $(".tab-item").removeClass("active").eq(currentSlide).addClass("active");
      },
    },
  });
  $(" .tab-item").on("click", function () {
    const currentSlide = $(this).index();
    swiperProblems.slideToLoop(currentSlide);
  });
  // 鼠标跟随光圈效果

  // 监听数字滚动部分是否可见
  function isElementFullyInViewport(el) {
    var rect = el.getBoundingClientRect();
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    var windowWidth = window.innerWidth || document.documentElement.clientWidth;
    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= windowHeight && rect.right <= windowWidth;
  }

  var stepVal = true;
  function handleScroll() {
    var myElement = $(".count-box")[0]; // 获取DOM元素
    if (myElement && isElementFullyInViewport(myElement) && stepVal) {
      $(".count-num").countTo();
      stepVal = false;
    }
  }

  // 使用防抖优化滚动事件
  var scrollTimeout;
  $(window).on("scroll", function () {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(handleScroll, 100);
  });
});
