// @import "../../shared/scss/variables";
// @import "../../shared/scss/mixins";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #000;
  color: #fff;
  margin-bottom: 0;
  font-family: "Mulish", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  @media (max-width: 1280px) {
    background-color: #f4f7ff;
  }
  video {
    height: 100%;
    width: 100%;
    object-fit: cover;
    line-height: 0;
    font-size: 0;
    // google去黑线
    filter: grayscale(0);
    // 火狐去黑线
    clip-path: fill-box;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
    font-family: "Mulish", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  h2 {
    text-align: center;
    font-weight: 800;
    font-size: 2.25rem;
  }

  h1,
  h2,
  h3 {
    text-align: center;
  }

  h2 {
    font-size: 2.25rem;
    font-weight: 800;
    text-align: center;
  }

  .opacity-6 {
    opacity: 0.6;
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .blue-text {
    color: #1a8dff;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .btn-wrapper {
      flex-direction: column;
      gap: 8px;
    }
  }

  .btn-wrapper .btn {
    margin: 0;
    border-radius: 12px;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 158px;
    &.btn-lg {
      min-width: 220px;
    }
  }

  @media (max-width: 768px) {
    .btn-wrapper .btn {
      display: block;
      vertical-align: baseline;
    }
  }

  .btn-download {
    background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);
    border: none;
    color: #fff;
    background-color: #0458ff;
  }

  .btn-download:hover {
    color: #fff;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),
      linear-gradient(0deg, #0055fb, #0055fb);
    background-color: #0458ff;
  }

  .swiper-pagination {
    bottom: -4px !important;
  }

  .swiper-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background-color: #c2cee9;
    opacity: 1;
  }

  .swiper-pagination .swiper-pagination-bullet-active {
    width: 64px;
    background: linear-gradient(89.5deg, #0458ff 0%, #0499ff 100%);
    border-radius: 8px;
  }

  .bg-dark-gradient {
    background: linear-gradient(180deg, #0e0e0e 39.41%, #02050c 77.91%);
  }

  .part-banner {
    background: url(./assets/images/banner.jpg) no-repeat center center / cover;
    @media (max-width: 576px) {
      background-image: url(./assets/images/banner-mobile.jpg);
    }

    .center-content {
      padding: 10.375rem 0;
      text-align: center;
      @media (max-width: 576px) {
        padding-top: 54px;
      }
      h1 {
        font-weight: 900;
        font-size: 4.5rem;
        background: linear-gradient(91.24deg, rgba(145, 202, 255, 0.8) -4.34%, rgba(242, 250, 255, 0.8) 46.77%, rgba(158, 240, 255, 0.8) 99.88%),
          linear-gradient(0deg, #ffffff, #ffffff);

        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        display: inline-block;
        @media (max-width: 768px) {
          font-size: 31px;
        }
      }
      h2 {
        font-weight: 900;
        font-size: 2rem;
        text-align: center;
        @media (max-width: 768px) {
          font-size: 14px;
        }
      }
    }

    .prize-list {
      display: flex;
      justify-content: center;
      gap: 3rem;
      @media (max-width: 576px) {
        gap: 24px;
      }
      .prize-item {
        display: flex;
        align-items: center;
        justify-content: center;

        .prize-item-left,
        .prize-item-right {
          flex-shrink: 0;
          @media (max-width: 576px) {
            width: 17px;
          }
        }
        .prize-item-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .prize-item-number {
            font-weight: 900;
            font-size: 1.5rem;
            @media (max-width: 576px) {
              font-size: 12px;
            }
          }
          .prize-item-text {
            font-weight: 400;
            font-size: 0.875rem;
            text-align: center;
            @media (max-width: 576px) {
              font-size: 7px;
            }
          }
        }
      }
    }
  }
  .part-logos {
    background-color: #0e0e0e;
    .logos-list {
      display: flex;
      justify-content: center;
      padding: 2.125rem 0;
      @media (max-width: 768px) {
        padding: 20px 0;
      }
      .logo-item {
        flex: 1 1 33%;
        max-height: 2rem;
        text-align: center;

        &:not(:last-child) {
          border-right: 1px solid rgba(255, 255, 255, 0.1);
        }
        img {
          max-width: 100%;
          max-height: 2rem;
          object-fit: contain;
          @media (max-width: 768px) {
            max-height: 14px;
          }
        }
      }
    }
  }
  .bg-dark1 {
    background: url(./assets/images/dark-bg1.svg) no-repeat center center / cover;
  }
  .dark-bg2 {
    background: url(./assets/images/dark-bg2.svg) no-repeat center center / cover;
  }

  .part-built {
    .pro-repair-wrapper {
      background-color: #1e1f22;
      border-radius: 1.5rem;
      padding: 2.5rem;
      display: flex;
      gap: 1.75rem;
      @media (max-width: 1280px) {
        flex-direction: column;
      }
      @media (max-width: 576px) {
        padding: 1.5rem 1rem;
      }
      .pro-repair-text-wrapper {
        flex: 1;
        max-width: 482px;
        margin: 0 auto;
        @media (max-width: 1280px) {
          max-width: initial;
          width: 100%;
        }
        .pro-repair-title {
          font-weight: 600;
          font-size: 2rem;
          text-align: left;
          @media (max-width: 1280px) {
            text-align: center;
          }
        }
        .pro-repair-accordion-content {
          p {
            margin-top: 10px;
          }
        }
        .pro-repair-accordion {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          .pro-repair-accordion-item {
            border-radius: 0.5rem;
            overflow: hidden;
            padding: 0.75rem 1.5rem;
            position: relative;
            svg {
              transition: transform 0.3s ease-in-out;
            }
            &::after {
              content: "";
              position: absolute;
              inset: 0;
              padding: 1px;
              border-radius: 0.5rem;
              background: #393b3f;
              mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
              mask-composite: exclude;
              pointer-events: none;
            }
            &:has([aria-expanded="true"]) {
              padding-top: 1.5625rem;
              padding-bottom: 1.5625rem;
              background: linear-gradient(86.47deg, rgba(4, 88, 255, 0.2) 1.47%, rgba(4, 153, 255, 0.2) 96.84%);
              &::after {
                background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(0deg, #0055fb, #0055fb);
              }
              svg {
                transform: rotate(-180deg);
              }
            }
            .pro-repair-accordion-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              line-height: 100%;
              gap: 0.5rem;
              cursor: pointer;

              .pro-repair-accordion-title {
                font-weight: 600;
              }
            }
            .pro-repair-accordion-content p {
              font-size: 12px;
              margin-top: 8px;
            }
          }
        }
      }
      .pro-repair-img-wrapper {
        width: 50%;
        flex-shrink: 0;
        @media (max-width: 1280px) {
          width: 100%;
          order: 2;
        }
        .swiper-slide {
          border-radius: 1rem;
          overflow: hidden;
        }

        video,
        img {
          width: 100%;

          height: 100%;
          object-fit: cover;
        }
        .compare-img-box {
          .compare-before {
            position: absolute;
            width: 50%;
            height: 100%;
            left: 0;
            top: 0;
            background-size: auto 100%;
            background-repeat: no-repeat;
            z-index: 2;
          }

          .compare-before::after {
            content: "";
            width: 2px;
            height: 100%;
            background: #fff;
            position: absolute;
            right: 0;
            top: 0;
          }

          .compare-before::before {
            content: "";
            background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);
            background-size: contain;
            background-position: center;
            width: 4.25rem;
            height: 2.5rem;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translate(50%, -50%);
            z-index: 3;
          }

          .compare-before.compare-before-1 {
            background-image: url(./assets/images/photo-formats-before.png);
          }
          .compare-before.compare-before-2 {
            background-image: url(./assets/images/4k-before.png);
          }
          .slider {
            -webkit-appearance: none;
            appearance: none;
            outline: 0;
            margin: 0;
            background: 0 0;
            z-index: 3;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
          }
          .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 2px;
            height: auto;
            background: transparent;
          }
        }
      }
    }
  }

  .part-cameras {
    background: url(./assets/images/mountain-bg.png) no-repeat bottom center / 100% auto;
    .content-img {
      width: 1278px;
      height: 768px;
      margin-top: 1.125rem;
      margin-bottom: 110px;
      margin-left: auto;
      margin-right: auto;
      position: relative;
      .camera-item {
        width: 19.25%;
        min-height: 220px;
        position: absolute;
        opacity: 0.3;
        .camera-name {
          font-size: 1.125rem;
          text-align: center;
          margin-top: 1rem;
        }
        &.camera-item-1 {
          top: 12.21%;
          left: 0;
        }
        &.camera-item-2 {
          top: 0;
          left: 26.84%;
        }
        &.camera-item-3 {
          top: 0;
          right: 26.84%;
        }
        &.camera-item-4 {
          top: 12.21%;
          right: 0;
        }
        &.camera-item-5 {
          bottom: 23.89%;
          right: 0;
        }
        &.camera-item-6 {
          bottom: 0;
          right: 26.84%;
        }
        &.camera-item-7 {
          bottom: 0;
          left: 26.84%;
        }
        &.camera-item-8 {
          bottom: 23.89%;
          left: 0;
        }
      }
      .content-text {
        position: absolute;
        top: 290px;
        left: 50%;
        transform: translateX(-50%);
        width: 644px;
        z-index: 3;
        .content-title {
          font-size: 3rem;
          font-weight: 800;
          text-align: center;
        }
        .content-description {
          font-size: 1.125rem;
          text-align: center;
          margin: 0 58px;
        }
      }
    }
    // 移动端相机展示区域
    .camera-list-mobile {
      display: flex;
      flex-wrap: wrap;
      gap: 10px 14px;
      align-items: center;
      justify-content: center;
      margin-top: 3rem;
      .camera-item-mobile {
        width: calc(50% - 7px);
        max-width: 220px;

        text-align: center;
        .camera-name {
          font-size: 12px;
          opacity: 0.5;
          margin-top: 8px;
        }
      }
    }
  }

  .part-fix {
    .nav {
      display: flex;
      border-radius: 1rem 1rem 0 0;
      background-color: #383838;
      overflow: hidden;
      .nav-item {
        flex: 1 1 50%;
        text-align: center;
        padding: 1rem 0.5rem;
        cursor: pointer;
        font-size: 1.25rem;
        font-weight: 500;
        color: #fff;
        &.active {
          background: linear-gradient(270.71deg, rgba(0, 85, 251, 0) -2.58%, rgba(0, 85, 251, 0.47) 33.98%, #00c1ff 98.49%);
          background-color: #000;
          font-weight: 800;
        }
      }
    }
    .tab-content {
      background-color: #0c0e12;
      border: 1px solid #383838;
      border-radius: 0 0 1rem 1rem;
      border-top: unset;
      overflow: hidden;
      .formats-box {
        display: flex;
        gap: 1.75rem;
        @media (max-width: 1280px) {
          flex-direction: column;
        }
        .left-wrapper {
          height: auto;
          width: calc(50% - 0.875rem);
          border-radius: 1rem;
          overflow: hidden;
          @media (max-width: 1280px) {
            width: 100%;
          }
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .right-wrapper {
          width: calc(50% - 0.875rem);
          border-radius: 1rem;
          border: 2px solid #e6eeff0f;
          display: flex;
          flex-direction: column;
          @media (max-width: 1280px) {
            width: 100%;
          }
          .format-item {
            flex: 1 1 50%;
            display: flex;
            gap: 2rem;
            padding: 1.5rem 2rem;
            align-items: center;
            &:first-child {
              border-bottom: 2px solid #e6eeff0f;
            }
            .format-item-left {
              flex-shrink: 0;
              width: 4rem;
              text-align: center;
            }
            .format-item-content {
              color: #a4a4a4;
            }
          }
        }
      }
      #nav-devices {
        background: url(./assets/images/device-item-bg.jpg) no-repeat center center / cover;
      }
      .devices-box {
        display: flex;
        gap: 1.875rem;
        @media (max-width: 992px) {
          flex-wrap: wrap;
          gap: 1rem;
        }
        .device-item {
          border-radius: 1rem;
          position: relative;
          color: #fff;
          overflow: hidden;
          @media (max-width: 992px) {
            width: calc(50% - 0.5rem);
          }
          &::after {
            content: "";
            position: absolute;
            inset: 0;
            padding: 2px;
            border-radius: 1rem;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.1984) 0%, rgba(255, 255, 255, 0) 100%);
            mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
            mask-composite: exclude;
            pointer-events: none;
          }
          &:hover {
            &::after {
              background: linear-gradient(138.99deg, #0087ff 8.45%, #6ad6ff 89.48%);
            }
          }
          .device-item-text {
            position: absolute;
            bottom: 1.25rem;
            left: 0;
            right: 0;
            text-align: center;
            font-weight: 500;
            font-size: 1.25rem;
            width: 100%;
          }
        }
      }
    }
  }

  .part-scenarios {
    @keyframes marquee1 {
      0% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(-50%);
      }
    }
    @keyframes marquee2 {
      0% {
        transform: translateX(-50%);
      }
      100% {
        transform: translateX(0);
      }
    }
    .scenarios-wrapper {
      max-width: 1770px;
      margin-left: auto;
      margin-right: auto;
      overflow: hidden;
    }
    .scenarios-list {
      display: flex;
      max-width: fit-content;
      width: fit-content;

      margin-top: 2rem;
      margin-left: auto;
      margin-right: auto;
      .scenarios-item {
        position: relative;
        border-radius: 1rem;
        overflow: hidden;
        width: 330px;
        &:not(:last-child) {
          margin-right: 1.875rem;
        }
        .item-title {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          width: 100%;
          font-size: 1.125rem;
          font-weight: 800;
          padding: 0.75rem 1.5rem 1rem;
          text-align: center;
          background: #121212c7;
          color: #fff;
          .right-icon {
            display: none;
          }
          @media (max-width: 768px) {
            padding: 0.8rem 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            .right-icon {
              display: block;
              flex-shrink: 0;
            }
          }
        }
        @media (max-width: 768px) {
          width: 235px;
        }
      }
      @media (max-width: 1770px) {
        &.scenarios-list-1 {
          animation: marquee1 50s linear infinite;
          &:hover {
            animation-play-state: paused;
          }
        }
        &.scenarios-list-2 {
          animation: marquee2 50s linear infinite;
          &:hover {
            animation-play-state: paused;
          }
        }
      }
    }
  }

  .part-tool {
    .tool-item {
      height: 100%;
      position: relative;
      &:hover {
        .default-img {
          display: none;
        }
        .active-img {
          display: block;
        }
      }
      .active-img {
        display: none;
      }

      .download-link {
        display: block;
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 4;
        top: 0;
        left: 0;
        a {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      .tool-item-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 2;
        width: 100%;
        padding: 2rem 1.5rem;
        min-height: 226px;
        @media (max-width: 768px) {
          min-height: 170px;
        }
        .tool-item-title {
          font-size: 1.25rem;
          font-weight: 700;
        }
        .tool-item-description {
          font-size: 0.875rem;
          opacity: 0.6;
        }
      }
    }
  }

  .part-reason {
    background-color: #000;
    position: relative;
    .part-reason-title {
      position: absolute;
      top: 3rem;
      left: 50%;
      transform: translateX(-50%);
      color: #000;
      z-index: 10;
      width: 100%;
      @media (max-width: 1280px) {
        position: relative;
        transform: unset;
        color: #fff;
        left: unset;
        top: unset;
        margin-bottom: 3rem;
      }
    }
    .img-wrapper {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 2;
    }
    .img-wrapper .img-item {
      position: absolute;
      width: 100%;
      top: 0;
      left: 0;
      opacity: 0;
      transition: opacity 0.5s;
    }

    .img-wrapper .img-item.active {
      opacity: 1;
    }

    .content-wrapper .content-item,
    .content-wrapper .content-item > a {
      color: #fff;
      text-decoration: none;
      display: block;
    }

    @keyframes circleOpacity {
      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0;
      }
    }

    @media (min-width: 1280px) {
      .content-wrapper .content-item {
        width: 17.1875%;
        max-width: 330px;
        transition: opacity 0.5s;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(8px);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
      }

      .content-wrapper .content-item h6 {
        display: flex;
        align-items: flex-start;
        gap: 5px;
        color: #000;
        text-align: left;
      }

      .content-wrapper .content-item h6 img {
        margin-top: 3px;
      }

      .content-wrapper .content-item .desc {
        display: none;
        text-align: left;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.7);
        margin-top: 8px;
      }

      .content-wrapper .content-item.active::before {
        content: "";
        width: calc(100% + 22px);
        height: calc(100% + 22px);
        top: -11px;
        left: -11px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 1.5rem;
        position: absolute;
        animation: circleOpacity 2.5s linear infinite;
        pointer-events: none;
      }

      .content-wrapper .content-item.active::after {
        content: "";
        width: calc(100% + 42px);
        height: calc(100% + 42px);
        top: -21px;
        left: -21px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 1.875rem;
        position: absolute;
        animation: circleOpacity 3s ease-in infinite;
        pointer-events: none;
      }

      .content-wrapper .content-item.hideItem {
        opacity: 0;
      }

      .content-wrapper .content-item.active {
        outline: 2px solid #fff;
        outline-offset: 4px;
      }

      .content-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 3;
      }

      .content-wrapper .content-item {
        position: absolute;
      }
    }
    @media (min-width: 1920px) {
      .content-wrapper .content-item {
        max-width: unset;
      }
    }

    @media (max-width: 1280px) {
      #workspaces-tab {
        display: none;
      }

      #workspaces-tab-content > .tab-pane {
        opacity: 1;
        display: flex;
        flex-direction: column-reverse;
        margin-bottom: 3rem;
      }

      .content-wrapper {
        position: relative;
      }

      .content-wrapper .content-item {
        left: 0 !important;
        top: 0 !important;
        transform: none;
        width: 100%;
        margin: 0 auto;
        padding: 0;
        opacity: 0;
        transition: opacity 0.5s;
        pointer-events: none;
      }

      .content-wrapper .content-item:not(:first-child) {
        position: absolute;
      }

      .content-wrapper .content-item.active {
        opacity: 1;
        pointer-events: initial;
      }

      .content-wrapper .content-item h6 {
        display: none;
      }

      .content-wrapper .content-item {
        .desc {
          text-align: center;
          padding: 0 15px;
        }
      }

      .img-wrapper {
        pointer-events: none;
      }

      .mobile-swiper {
        background: #1d1d1d;
        border-radius: 6px;
        position: relative;
        margin: 12px auto;
        width: 560px;
        max-width: 88%;
        padding: 2px 36px;
        color: #fff;
        font-size: 1rem;
        overflow: hidden;
      }

      .mobile-swiper::before {
        content: "";
        position: absolute;
        top: 0;
        left: -1%;
        height: 100%;
        width: 20%;
        background: linear-gradient(90deg, #1d1d1d 35%, rgba(29, 29, 29, 0) 100%);
        z-index: 2;
      }

      .mobile-swiper::after {
        content: "";
        position: absolute;
        top: 0;
        right: -1%;
        height: 100%;
        width: 20%;
        background: linear-gradient(270deg, #1d1d1d 35%, rgba(29, 29, 29, 0) 100%);
        z-index: 2;
      }

      .mobile-swiper .swiper {
        overflow: initial;
        width: 48%;
        margin: 0 auto;
        background: rgba(162, 170, 183, 0.5);
        border-radius: 6px;
      }

      .mobile-swiper .swiper .swiper-slide {
        padding: 10px 12px;
        color: #fff;
        font-weight: 400;
        text-align: center;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .mobile-swiper .swiper .swiper-slide a {
        color: #fff;
        text-decoration: none;
      }

      .mobile-swiper .swiper .swiper-slide-active {
        font-weight: 700;
      }

      .mobile-swiper .swiper-button-next::after,
      .mobile-swiper .swiper-button-prev::after {
        content: none;
      }

      .mobile-swiper .swiper-button-next,
      .mobile-swiper .swiper-button-prev {
        width: 10%;
        height: 100%;
        transform: translateY(-50%);
        background-repeat: no-repeat;
        background-size: 8px auto;
        top: 50%;
        margin-top: 0;
        z-index: 3;
      }

      .mobile-swiper .swiper-button-prev {
        background-image: url("data:image/svg+xml;base64,Cjxzdmcgd2lkdGg9IjgiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCA4IDEyIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNNyAxTDEgNkw3IDExIiBzdHJva2U9IiMwMDZkZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=");
        left: 0;
        background-position: 40% center;
      }

      .mobile-swiper .swiper-button-next {
        background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDggMTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTTEgMUw3IDZMMSAxMSIgc3Ryb2tlPSIjMDA2ZGZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgLz4KPC9zdmc+Cg==");
        right: 0;
        background-position: 60% center;
      }
    }

    @media (max-width: 576px) {
      .font-size-large {
        font-size: 12px;
      }

      .mobile-swiper .swiper .swiper-slide {
        padding: 10px 4px;
        font-weight: 700;
        font-size: 10px;
      }

      .content-wrapper .content-item .desc {
        font-size: 12px;
      }
    }
  }

  .part-steps {
    background: url(./assets/images/step-bg.svg) no-repeat top center / cover;
    background-color: #f2fbff;
    color: #000;
    .steps-wrapper {
      display: flex;
      justify-content: space-between;
      max-width: 1112px;
      margin: 0 auto;
      gap: 1.875rem;
      @media (max-width: 576px) {
        flex-direction: column;
      }

      .repair-step {
        width: calc(100% / 3 - 1.875rem / 2);
        max-width: 294px;
        display: flex;
        flex-direction: column;
        gap: 1.875rem;

        .active-img {
          display: none;
        }
        .default-img {
          display: block;
        }

        .repair-step-img {
          @media (min-width: 1600px) {
            min-height: 236px;
          }
          @media (any-hover: hover) {
            &:hover {
              .default-img {
                display: none;
              }
              .active-img {
                display: block;
              }
            }
          }
        }
        @media (max-width: 576px) {
          width: 100%;
          max-width: unset;
          gap: 1rem;
        }

        .repair-step-content {
          text-align: center;
          @media (max-width: 576px) {
            order: -1;
          }
          .repair-step-title {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.625rem;
            position: relative;

            &::after {
              content: "";
              position: absolute;
              bottom: 48%;
              left: 76%;
              width: 88%;
              height: 1px;
              border: 1px dashed #a9b9c1;
              border-image: repeating-linear-gradient(to right, #a9b9c1 0, #a9b9c1 12px, transparent 2px, transparent 16px) 1;
              @media (max-width: 1280px) {
                width: 60%;
              }
              @media (max-width: 768px) {
                width: 40%;
                left: 90%;
              }
              @media (max-width: 576px) {
                display: none;
              }
            }
            &.no-border {
              &::after {
                display: none;
              }
            }
            .repair-step-title-number {
              width: 1.75rem;
              height: 1.75rem;
              background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 50%;
              font-size: 1.125rem;
              font-weight: 900;
              color: #fff;
            }
          }
          .repair-step-desc {
            font-size: 0.875rem;
            opacity: 0.6;
          }
        }
      }
    }
  }

  .part-methods {
    background-color: #f2fbff;
    color: #000;

    .methods-compare {
      background-color: #fff;
      border-radius: 1rem;
      overflow: hidden;
      padding: 2.5rem 2rem;
      @media (max-width: 576px) {
        padding: 1rem;
      }
      .advantages-title {
        font-weight: 700;
        font-size: 1.25rem;
        color: #0074e8;
      }
      .disadvantages-title {
        font-weight: 700;
        font-size: 1.25rem;
        color: #ff3636;
      }
      .feature-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.75rem;
      }
    }

    .methods-nav-item {
      box-shadow: inset 0 0 0 1px #1a8dff;
      font-size: 1.5rem;
      border-radius: 0.5rem;
      color: #1a8dff;
      cursor: pointer;
      text-align: center;
      line-height: 100%;
      padding: 18px 0.5rem;

      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      @media (max-width: 1280px) {
        font-size: 1rem;
      }
    }

    .methods-nav .col-md-6.active .methods-nav-item {
      background-color: #1a8dff;
      color: #fff;
      font-weight: 700;
    }

    .methods-warning {
      padding: 1.5rem 3rem;
      border: 1px solid #1a8dff;
      border-radius: 0.75rem;
      color: rgba(0, 0, 0, 0.7);
    }

    @media (max-width: 576px) {
      .methods-warning {
        padding: 1rem 1rem;
      }
    }

    .slidebtn-methods {
      display: inline-block;
      width: 48px;
      height: 49px;
      background-image: url("./assets/images/left-blue-icon.svg");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 40%;
      cursor: pointer;
    }

    .slidebtn-methods:hover {
      background-image: url("./assets/images/right-blue-icon.svg");
      transform: rotate(180deg);
    }

    .slidebtn-methods-prev {
      left: -67px;
    }

    .slidebtn-methods-next {
      right: -67px;
      transform: rotate(180deg);
    }

    .slidebtn-methods-next:hover {
      transform: rotate(0);
    }
  }

  .part-faq {
    background-color: #f2fbff;
    color: #000;
    .accordion-box {
      background-color: #fff;
      border-radius: 1.5rem;
      padding: 0.5rem 4rem;
    }

    @media (max-width: 992px) {
      .accordion-box {
        padding: 0.5rem 2rem;
      }
    }

    @media (max-width: 768px) {
      .accordion-box {
        padding: 0.5rem 1rem;
      }
    }

    .accordion-box .accordion-item {
      padding: 1.5rem;
    }

    .accordion-box .accordion-item:not(:last-child) {
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .accordion-box .accordion-item svg {
      transition: all 0.2s linear;
    }

    @media (max-width: 768px) {
      .accordion-box .accordion-item svg {
        width: 1rem;
      }
    }

    @media (max-width: 768px) {
      .accordion-box .accordion-item {
        padding: 1rem 0.5rem;
      }
    }

    .accordion-box .accordion-item [aria-expanded="true"] svg {
      transform: rotate(180deg);
    }

    .accordion-box .accordion-item .serial-number {
      display: inline-flex;
      width: 22px;
      height: 22px;
      align-items: center;
      justify-content: center;
      color: #fff;
      background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
      border-radius: 50%;
      margin-right: 8px;
      font-size: 1rem;
      font-weight: 800;
      flex-shrink: 0;
    }

    @media (max-width: 768px) {
      .accordion-box .accordion-item .serial-number {
        width: 16px;
        height: 16px;
        color: #fff;
      }
    }

    .accordion-box .accordion-item .faq-detail {
      font-size: 14px;
      padding-top: 1rem;
      opacity: 0.7;
      padding-left: 30px;
      padding-right: 32px;
    }

    @media (max-width: 768px) {
      .accordion-box .accordion-item .faq-detail {
        padding-left: 20px;
        padding-right: 16px;
      }
    }
  }

  .part-links {
    background-color: #f2fbff;
    color: #000;
  }

  .part-links .part-links-line {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .part-links .line-border {
    border-right: 1px solid rgba(0, 0, 0, 0.3);
    border-left: 1px solid rgba(0, 0, 0, 0.3);
  }

  @media (max-width: 1280px) {
    .part-links .line-border {
      border-right: unset;
    }
  }

  @media (max-width: 768px) {
    .part-links .line-border {
      border-left: unset;
    }
  }

  .part-links .text-link {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
    margin-top: 1.5rem;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .part-links .text-link:hover {
    color: #0055fb;
  }

  .part-links .part-links-videos {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .part-links .part-links-videos .video-wrapper {
    border-radius: 0.75rem;
  }

  @media (max-width: 1280px) {
    .part-links .part-links-videos {
      flex-direction: row;
      padding-top: 2rem;
    }
  }

  @media (max-width: 576px) {
    .part-links .part-links-videos {
      display: block;
    }
  }

  .part-links .text-line4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .part-footer {
    background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    @media (max-width: 576px) {
      .display-2 {
        font-size: 2.5rem;
      }
    }
  }

  .part-footer-logo {
    height: 4rem;
    width: 14.5rem;
    margin: 0 auto;
  }

  @media (max-width: 576px) {
    .part-footer .btn-outline-action {
      background-color: #fff;
      vertical-align: text-bottom;
    }
  }
  .part-advanced {
    background-color: #f2fbff;
    color: #000;
  }

  .part-advanced .advanced-item {
    border-radius: 1rem;
    background-color: #ffffff;
    overflow: hidden;
    height: 100%;
  }

  .part-advanced .advanced-item .compare-before {
    position: absolute;
    width: 50%;
    height: 100%;
    left: 0;
    top: 0;
    background-size: auto 100%;
    background-repeat: no-repeat;
    z-index: 2;
  }

  .part-advanced .advanced-item .compare-before::after {
    content: "";
    width: 2px;
    height: 100%;
    background: #fff;
    position: absolute;
    right: 0;
    top: 0;
  }

  .part-advanced .advanced-item .compare-before::before {
    content: "";
    background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);
    background-size: contain;
    background-position: center;
    width: 4.25rem;
    height: 2.5rem;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(50%, -50%);
    z-index: 3;
  }

  @keyframes changeWidth {
    0% {
      width: 0;
    }

    50% {
      width: 100%;
    }

    100% {
      width: 0;
    }
  }

  .part-advanced .advanced-item .compare-before.compare-before-1 {
    background-image: url(./assets/images/ai-video-enhancer-before.png);
    animation: changeWidth 8s linear infinite;
    aspect-ratio: 328 / 192;
  }

  .part-advanced .advanced-item .compare-before.compare-before-2 {
    background-image: url(./assets/images/photo-repair-before.png);
    animation: changeWidth 7s linear infinite 1s;
  }

  .part-advanced .advanced-item .compare-before.compare-before-3 {
    background-image: url(./assets/images/file-repair-before.png);
    animation: changeWidth 7s linear infinite;
  }

  .part-advanced .advanced-item .compare-before.compare-before-4 {
    background-image: url(./assets/images/old-photo-restoration-before.png);
    animation: changeWidth 6s linear infinite;
  }

  .part-advanced .advanced-item .slider {
    -webkit-appearance: none;
    appearance: none;
    outline: 0;
    margin: 0;
    background: 0 0;
    z-index: 3;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  .part-advanced .advanced-item .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 2px;
    height: auto;
    background: transparent;
  }

  .part-advanced .advanced-item .item-link {
    color: #000;
  }

  .part-advanced .advanced-item .item-link .normal-arrow {
    display: inline;
  }

  .part-advanced .advanced-item .item-link .active-arrow {
    display: none;
  }

  .part-advanced .advanced-item .item-link .arrow-icon {
    width: 2rem;
    display: inline-block;
  }

  @media (max-width: 576px) {
    .part-advanced .advanced-item .item-link .arrow-icon {
      display: block;
    }
  }

  .part-advanced .advanced-item .item-link:hover {
    color: #0458ff;
  }

  .part-advanced .advanced-item .item-link:hover .normal-arrow {
    display: none;
  }

  .part-advanced .advanced-item .item-link:hover .active-arrow {
    display: inline;
  }

  .part-footer {
    background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #000;
    @media (max-width: 576px) {
      .display-2 {
        font-size: 2.5rem;
      }
    }
  }

  .part-footer-logo {
    height: 4rem;
    width: 14.5rem;
    margin: 0 auto;
  }

  @media (max-width: 576px) {
    .part-footer .btn-outline-action {
      background-color: #fff;
      vertical-align: text-bottom;
    }
  }
}
