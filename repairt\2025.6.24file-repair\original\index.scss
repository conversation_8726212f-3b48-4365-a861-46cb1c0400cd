* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #f5f8ff;
  color: #000;

  @media (max-width: 1280px) {
    background-color: #f4f7ff;
  }
  video {
    height: 100%;
    width: 100%;
    object-fit: cover;
    line-height: 0;
    font-size: 0;
    // google去黑线
    filter: grayscale(0);
    // 火狐去黑线
    clip-path: fill-box;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
  }

  h2 {
    text-align: center;
    font-weight: 800;
    font-size: 2.25rem;
  }

  h1,
  h2,
  h3 {
    text-align: center;
  }

  h2 {
    font-size: 2.25rem;
    font-weight: 800;
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .blue-text {
    color: #2e8eff;
  }

  .gray-text {
    color: #454545;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .btn-wrapper {
      flex-direction: column;
      gap: 8px;
    }
  }

  .btn-wrapper .btn {
    margin: 0;
    border-radius: 8px;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 158px;
    gap: 0.5rem;
    &.btn-lg {
      min-width: 228px;
    }
  }

  @media (max-width: 768px) {
    .btn-wrapper .btn {
      display: block;
      vertical-align: baseline;
    }
  }

  .btn-download {
    background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);
    border: none;
    color: #fff;
    background-color: #0458ff;
  }

  .btn-download:hover {
    color: #fff;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),
      linear-gradient(0deg, #0055fb, #0055fb);
    background-color: #0458ff;
  }

  .swiper-pagination {
    bottom: -4px !important;
  }

  .swiper-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background-color: #c2cee9;
    opacity: 1;
  }

  .swiper-pagination .swiper-pagination-bullet-active {
    width: 64px;
    background: linear-gradient(89.5deg, #0458ff 0%, #0499ff 100%);
    border-radius: 8px;
  }
  .part-banner {
    text-align: center;
    background: url(https://images.wondershare.com/repairit/images2025/File-Repair/banner-bg.jpg) no-repeat center center / cover;
    margin-bottom: -3rem;
    position: relative;
    z-index: 1;
    .sub-title {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
      .blue-tip {
        background: linear-gradient(100.2deg, #0055fb 0%, #00c1ff 100%), linear-gradient(0deg, #d9d9d9, #d9d9d9);
        border-radius: 24px;
        padding: 4px 12px;
        font-weight: 700;
        font-size: 1.25rem;
        line-height: 100%;
        color: #fff;
      }
    }
    h1 {
      background: linear-gradient(95.44deg, #0055fb -28.33%, #00c1ff 96.36%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    .feature-list {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 4px 8px;
      border-radius: 1.5rem;
      border: 1.5px solid #0055fb;
      min-width: 602px;
      flex-wrap: wrap;
      @media (max-width: 768px) {
        min-width: unset;
      }
      .feature-item {
        flex: 1;
        font-weight: 600;
        font-size: 1.125rem;
        color: #0055fb;
        padding: 0 1rem;
        position: relative;
        &.text-gradient {
          background: linear-gradient(92.01deg, #e046ff 4.9%, #7970ff 52.55%, #0066ff 99.48%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }
        @media (max-width: 768px) {
          flex: 1 1 50%;
        }
        &:not(:last-child)::after {
          content: "";
          display: inline-block;
          width: 1px;
          height: 80%;
          background-color: #0055fb;
          top: 50%;
          transform: translateY(-50%);
          position: absolute;
          right: 0;
          @media (max-width: 768px) {
            content: unset;
          }
        }
      }
    }
    @keyframes banner-icon-animation {
      0% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(10px);
      }
      100% {
        transform: translateY(0);
      }
    }
    @keyframes banner-diffuse1 {
      0% {
        transform: scale(0.8);
        opacity: 0.1;
      }

      60% {
        transform: scale(0.9);
        opacity: 0.8;
      }

      100% {
        transform: scale(1);
        opacity: 0;
      }
    }
    .white-wave1 {
      width: 15.93%;
      aspect-ratio: 306 / 214;
      border-radius: 1.5rem;
      border: 3px solid rgba(255, 255, 255, 1);
      z-index: 1;
      position: absolute;
      left: 8.45%;
      top: 37%;
      animation: banner-diffuse1 2s linear infinite;
    }
    .white-wave2 {
      width: 15.93%;
      aspect-ratio: 306 / 214;
      border-radius: 1.5rem;
      border: 3px solid rgba(255, 255, 255, 1);
      z-index: 1;
      position: absolute;
      right: 4.79%;
      top: 30%;
      animation: banner-diffuse1 2s linear infinite;
    }
    .banner-icon {
      position: absolute;
      animation: banner-icon-animation 2s linear infinite;
      z-index: 3;
      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 0.5s;
      }
      &:nth-child(3) {
        animation-delay: 1s;
      }
    }
    #banner-text-swiper {
      height: 22px;
      overflow: hidden;
      @media (max-width: 576px) {
        height: 44px;
      }
    }
    .detail-item {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      gap: 10px;
      font-size: 14px;
    }
  }
  .part-logos {
    position: relative;
    z-index: 2;
    .logos-wrapper {
      background-color: #fff;
      border-radius: 1rem;
      padding: 2rem 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    @media (max-width: 768px) {
      .logos-wrapper {
        padding: 1.5rem 0.5rem;
      }
    }

    .logos-wrapper .logo-item {
      flex: 1 1 33%;
      max-height: 2rem;
      text-align: center;
    }

    .logos-wrapper .logo-item:not(:last-child) {
      border-right: 1px solid rgba(0, 0, 0, 0.1);
    }

    .logos-wrapper .logo-item img {
      max-width: 100%;
      max-height: 2rem;
      object-fit: contain;
    }

    @media (max-width: 768px) {
      .logos-wrapper .logo-item img {
        max-height: 1.1rem;
      }
    }
  }

  .part-productivity {
    @keyframes right-icon-animation {
      0% {
        transform: translateX(0);
      }
      50% {
        transform: translateX(8px);
      }
      100% {
        transform: translateX(0);
      }
    }
    .document-wrapper {
      border-radius: 1rem;
      overflow: hidden;
      background-color: #fff;
      padding: 2.125rem;
      @media (max-width: 576px) {
        padding: 1rem;
      }

      .wrapper-top {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        gap: 3rem;
        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
        }
        .left-content {
          max-width: 866px;
          .wrapper-title {
            font-size: 2rem;
            font-weight: 800;
            color: #000;
            text-align: left;
          }
        }
        .btn-wrapper {
          flex-shrink: 0;
        }
      }
      .card-wrapper {
        display: flex;
        gap: 16px;
        flex-wrap: nowrap;
        margin-top: 2.125rem;
        height: 422px;
        @media (max-width: 1280px) {
          flex-wrap: wrap;
          height: auto;
        }

        .document-card {
          display: block;
          border-radius: 1rem;
          position: relative;
          overflow: hidden;
          flex: 1 1 15%;
          background-repeat: no-repeat;
          background-position: bottom center, center center;
          background-size: auto 45%, 100% 100%;
          color: #000;
          text-decoration: none;
          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);
          @media (any-hover: hover) {
            &:hover {
              .right-icon {
                animation: right-icon-animation 1s linear infinite;
              }
            }
          }
          @media (max-width: 1600px) {
            background-size: auto 36%, 100% 100%;
          }
          @media (max-width: 1280px) {
            flex: 0 1 calc(50% - 8px);
          }
          @media (max-width: 768px) {
            flex: 1 1 100%;
          }

          &.pdf-card {
            background-color: #ffd7db;
            &.active {
              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/pdf-repair.png),
                linear-gradient(100.64deg, #ffd7db 33.42%, #fefff9 132.61%);
              @media (max-width: 1280px) {
                background-image: linear-gradient(100.64deg, #ffd7db 33.42%, #fefff9 132.61%);
                background-size: 100% 100%;
              }
            }
          }
          &.word-card {
            background-color: #f0f9ff;
            &.active {
              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/word-repair.png),
                linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);
              @media (max-width: 1280px) {
                background-image: linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);
                background-size: 100% 100%;
              }
            }
          }
          &.excel-card {
            background-color: #f0fff5;
            &.active {
              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/excel-repair.png),
                linear-gradient(304.71deg, #f0fff5 17.16%, #c3f9d4 77.96%);
              @media (max-width: 1280px) {
                background-image: linear-gradient(304.71deg, #f0fff5 17.16%, #c3f9d4 77.96%);
                background-size: 100% 100%;
              }
            }
          }
          &.ppt-card {
            background-color: #fff9f0;
            &.active {
              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/powerpoint-repair.png),
                linear-gradient(128.81deg, #ffccb0 2.03%, #fff6e9 88.07%);
              @media (max-width: 1280px) {
                background-image: linear-gradient(128.81deg, #ffccb0 2.03%, #fff6e9 88.07%);
                background-size: 100% 100%;
              }
            }
          }
          &.iwork-card {
            background-color: #f0f9ff;
            &.active {
              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/iwork-repair.png),
                linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);
              @media (max-width: 1280px) {
                background-image: linear-gradient(300.54deg, #f0f9ff -1.05%, #b3e2ff 59.77%);
                background-size: 100% 100%;
              }
            }
          }

          @media (min-width: 1280px) {
            &.active {
              flex: 1 1 40%;

              .show-content {
                display: block;
              }
              .hide-content {
                display: none;
              }
            }
          }
        }

        .hide-content {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          padding: 1.75rem 0.625rem;
          display: flex;
          height: 100%;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          text-align: center;
          z-index: 1;
          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);
          @media (max-width: 1280px) {
            display: none;
          }
          .card-hide-icon {
            width: 5.875rem;
            margin-top: auto;
            margin-bottom: auto;
          }
          .card-hide-title {
            font-weight: 500;
            font-size: 1.25rem;
          }
        }
        .show-content {
          display: none;
          width: 100%;
          height: 100%;
          padding: 1.25rem 1rem;

          @media (max-width: 1280px) {
            display: block;
            padding-bottom: 0;
            display: flex;
            flex-direction: column;
          }
          .card-show-title {
            font-size: 1.5rem;
            font-weight: 800;
          }
          .card-show-desc {
            list-style: none;
            padding-left: 1rem;
            max-width: 417px;

            li {
              position: relative;
              font-size: 1rem;
              line-height: 150%;
              &::before {
                content: "•";
                position: absolute;
                left: -1rem;
                top: -0.15rem;
              }
            }
          }
        }
      }
    }
    .archive-wrapper {
      border-radius: 1rem;
      overflow: hidden;
      background-color: #fff;
      padding: 2.125rem;
      @media (max-width: 576px) {
        padding: 1rem;
      }
      .wrapper-top {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        gap: 3rem;
        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
        }
        .left-content {
          max-width: 866px;
          .wrapper-title {
            font-size: 2rem;
            font-weight: 800;
            color: #000;
            text-align: left;
          }
        }
        .btn-wrapper {
          flex-shrink: 0;
        }
      }
      .card-wrapper {
        display: flex;
        gap: 16px;
        flex-wrap: nowrap;
        margin-top: 2.125rem;
        min-height: 283px;
        @media (max-width: 1600px) {
          min-height: 340px;
        }
        @media (max-width: 1280px) {
          flex-wrap: wrap;
        }

        .archive-card {
          display: block;
          border-radius: 1rem;
          position: relative;
          overflow: hidden;
          padding: 1rem;
          flex: 1 1 31.86%;
          background-repeat: no-repeat;
          background-position: right 5% bottom 50%, center center;
          background-size: auto 60%, 100% 100%;
          color: #636363;
          text-decoration: none;
          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);
          @media (any-hover: hover) {
            &:hover {
              .right-icon {
                animation: right-icon-animation 1s linear infinite;
              }
            }
          }
          .archive-card-title {
            font-weight: 800;
            font-size: 1.5rem;
            color: #000;
          }
          .archive-card-content {
            max-width: 460px;
            padding-right: 1.25rem;
            @media (max-width: 768px) {
              max-width: unset;
              padding-right: 0;
            }

            .archive-card-desc {
              font-size: 14px;
              color: inherit;
              margin-bottom: 16px;
            }
            .archive-card-list {
              list-style: none;
              padding-left: 1rem;
              li {
                position: relative;
                font-size: 14px;
                line-height: 150%;
                color: inherit;
                &::before {
                  content: "•";
                  position: absolute;
                  left: -1rem;
                  top: -0.15rem;
                }
              }
            }
          }

          &.rar-repair {
            background-color: #eeefff;
            &.active {
              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/rar-repair.png),
                linear-gradient(280.74deg, #eeeeff 50.38%, #a3b9ff 140.34%);
              @media (max-width: 768px) {
                background-image: linear-gradient(280.74deg, #eeeeff 50.38%, #a3b9ff 140.34%);
                background-size: 100% 100%;
              }
            }
          }
          &.zip-repair {
            background-color: #fff5d0;
            &.active {
              background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/zip-repair.png),
                linear-gradient(90deg, #ffe3a7 0%, #fff8d3 100%);
              background-size: auto 100%, 100% 100%;
              @media (max-width: 768px) {
                background-image: linear-gradient(90deg, #ffe3a7 0%, #fff8d3 100%);
                background-size: 100% 100%;
              }
            }
          }
          &.active {
            flex: 1 1 68.14%;
            color: #000;
          }
        }
      }
    }
    .engineering-wrapper {
      border-radius: 1rem;
      overflow: hidden;
      background-color: #fff;
      padding: 2.125rem;
      @media (max-width: 576px) {
        padding: 1rem;
      }

      .wrapper-top {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        gap: 3rem;
        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
        }
        .left-content {
          max-width: 866px;
          .wrapper-title {
            font-size: 2rem;
            font-weight: 800;
            color: #000;
            text-align: left;
          }
        }
        .btn-wrapper {
          flex-shrink: 0;
        }
      }
      .card-wrapper {
        display: flex;
        gap: 16px;
        flex-wrap: nowrap;
        margin-top: 2.125rem;
        min-height: 315px;
        @media (max-width: 1280px) {
          flex-wrap: wrap;
        }

        .engineering-card {
          display: block;
          border-radius: 1rem;
          position: relative;
          overflow: hidden;
          padding: 1rem;
          flex: 1 1 31.86%;

          color: #636363;
          text-decoration: none;
          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);
          @media (any-hover: hover) {
            &:hover {
              .right-icon {
                animation: right-icon-animation 1s linear infinite;
              }
            }
          }
          .engineering-card-title {
            font-weight: 800;
            font-size: 1.5rem;
            color: #000;
          }
          .engineering-card-content {
            max-width: 425px;
            padding-right: 1.25rem;
            @media (max-width: 768px) {
              padding-right: 0;
              max-width: unset;
            }

            .engineering-card-desc {
              font-size: 14px;
              color: #636363;
              margin-bottom: 16px;
            }
          }

          &.adobe-repair {
            flex: 1 1 68.14%;
            background-image: url(https://images.wondershare.com/repairit/images2025/File-Repair/adobe-file-repair.png),
              linear-gradient(100.16deg, #ddefff 63.73%, #a3d3ff 119.29%);
            background-repeat: no-repeat;
            background-position: right 6% bottom 15%, center center;
            background-size: auto 80%, 100% 100%;
            @media (max-width: 768px) {
              background-image: linear-gradient(100.16deg, #ddefff 63.73%, #a3d3ff 119.29%);
              background-size: 100% 100%;
            }
          }
          &.autocad-repair {
            background-color: #ebf6ff;
            flex: 1 1 50%;
          }
          &.sketchup-repair {
            background-color: #ebf6ff;
            flex: 1 1 50%;
          }
        }
        .autocad-sketchup-wrapper {
          display: flex;
          flex-direction: column;
          gap: 16px;
          @media (max-width: 1280px) {
            width: 100%;
          }
        }
      }
    }
  }

  .part-corruption {
    background-color: #fff;
    .wrapper-top {
      display: flex;
      justify-content: space-between;
      gap: 3rem;
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }
      .wrapper-title {
        font-weight: 800;
        color: #000;
        text-align: left;
      }
    }

    .swiper-corruption-box {
      margin-right: -20vw;
      @media (max-width: 576px) {
        margin-right: unset;
      }
    }
    @media (min-width: 2000px) {
      .swiper-box {
        margin-right: -30vw;
      }
    }

    .corruption-box {
      border-radius: 1rem;
      overflow: hidden;
      position: relative;
      .corruption-box-title {
        position: absolute;
        bottom: 6.4%;
        left: 0;
        font-size: 1.5rem;
        color: #000;
        width: 100%;
        text-align: center;
      }
      &:hover {
        box-shadow: 0px 0px 12px 0px #00d1ff4d;

        .hide-box {
          transform: translateY(0);
        }
      }
    }
    .hide-box {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      border-radius: 1rem;
      transform: translateY(110%);
      transition: all 0.3s ease-in-out;
      padding: 2.375rem;
      background: linear-gradient(147.08deg, #ffffff 34.81%, #c3ecff 100.92%);
      z-index: 1;
      @media (max-width: 992px) {
        padding: 1rem;
      }
      &::after {
        content: "";
        position: absolute;
        inset: 0;
        padding: 1px;
        border-radius: 1rem;
        background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
        mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
        mask-composite: exclude;
        pointer-events: none;
      }
      .hide-box-title {
        font-weight: 800;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }
      .hide-box-desc {
        font-size: 1.25rem;
        color: #636363;
        @media (max-width: 992px) {
          font-size: 1rem;
        }
      }
    }
    .swiper-pagination-box {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding-bottom: 1.25rem;
      @media (max-width: 576px) {
        justify-content: center;
      }
      .swiper-prev,
      .swiper-next {
        cursor: pointer;
        &:hover {
          color: #0496ff;
        }
      }
      .swiper-pagination-number {
        display: inline-flex;
        width: auto;
      }
    }

    @media (min-width: 1280px) {
      #card-swiper .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: nowrap;
      }

      #card-swiper .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(20% - 1.875rem);
      }
    }
    .card-box {
      padding: 2rem 1.5rem;
      border-radius: 1.5rem;
      background-color: #eaf8ff;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      height: 100%;
      .card-icon {
        width: 6rem;
      }
      .card-title {
        font-size: 1.125rem;
        font-weight: 800;
        color: #000;
      }
      .card-desc {
        font-size: 14px;
        color: #000;
        opacity: 0.6;
        text-align: center;
      }
    }
  }

  .part-steps {
    background: radial-gradient(82.32% 135.56% at 31.17% -26.53%, #b6e0ff 0%, #e3f3ff 50%, #e0f2ff 100%);

    .nav-item {
      padding: 1.5rem;
      border-radius: 12px;
      width: 100%;
    }

    .nav-item.active {
      background-color: #fff;
    }

    .nav-item .nav-item-content {
      display: flex;
      align-items: flex-start;
    }
  }
}
main .part-faq .accordion-box {
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 0.5rem 4rem;
}

@media (max-width: 992px) {
  main .part-faq .accordion-box {
    padding: 0.5rem 2rem;
  }
}

@media (max-width: 768px) {
  main .part-faq .accordion-box {
    padding: 0.5rem 1rem;
  }
}

main .part-faq .accordion-box .accordion-item {
  padding: 1.5rem;
}

main .part-faq .accordion-box .accordion-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

main .part-faq .accordion-box .accordion-item svg {
  transition: all 0.2s linear;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item svg {
    width: 1rem;
  }
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item {
    padding: 1rem 0.5rem;
  }
}

main .part-faq .accordion-box .accordion-item [aria-expanded="true"] svg {
  transform: rotate(180deg);
}

main .part-faq .accordion-box .accordion-item .serial-number {
  display: inline-flex;
  width: 22px;
  height: 22px;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
  border-radius: 50%;
  margin-right: 8px;
  font-size: 1rem;
  font-weight: 800;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item .serial-number {
    width: 16px;
    height: 16px;
    color: #fff;
  }
}

main .part-faq .accordion-box .accordion-item .faq-detail {
  font-size: 14px;
  padding-top: 1rem;
  opacity: 0.7;
  padding-left: 30px;
  padding-right: 32px;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item .faq-detail {
    padding-left: 20px;
    padding-right: 16px;
  }
}

main .part-stories .swiper {
  margin: 2rem;
}

@media (max-width: 768px) {
  main .part-stories .swiper {
    margin: 0.5rem;
  }
}

@media (min-width: 768px) {
  main .part-stories .swiper-slide .user-wrapper::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(210, 223, 255, 0.3);
    z-index: 2;
  }

  main .part-stories .swiper-slide.swiper-slide-active .user-wrapper::before {
    content: unset;
  }
}

main .part-stories .user-wrapper {
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.6);
  }
}

main .part-stories .user-wrapper .user-story {
  position: absolute;
  right: 4rem;
  top: 3rem;
  max-width: 360px;
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper .user-story {
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 8px;
    color: #fff;
  }
}

main .part-stories .user-wrapper .user-story .user-occupation {
  font-size: 14px;
  margin-bottom: 16px;
}

main .part-stories .user-wrapper .user-story .user-comments {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.7);
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper .user-story .user-comments {
    color: #fff;
  }
}

main .part-stories .swiper-pagination {
  bottom: -2.5rem !important;
}

main .part-links .part-links-line {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

main .part-links .line-border {
  border-right: 1px solid rgba(0, 0, 0, 0.3);
  border-left: 1px solid rgba(0, 0, 0, 0.3);
}

@media (max-width: 1280px) {
  main .part-links .line-border {
    border-right: unset;
  }
}

@media (max-width: 768px) {
  main .part-links .line-border {
    border-left: unset;
  }
}

main .part-links .text-link {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  margin-top: 1.5rem;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

main .part-links .text-link:hover {
  color: #0055fb;
}

main .part-links .part-links-videos {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

main .part-links .part-links-videos .video-wrapper {
  border-radius: 0.75rem;
}

@media (max-width: 1280px) {
  main .part-links .part-links-videos {
    flex-direction: row;
    padding-top: 2rem;
  }
}

@media (max-width: 576px) {
  main .part-links .part-links-videos {
    display: block;
  }
}

main .part-links .text-line4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

main .part-footer {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  @media (max-width: 576px) {
    .display-2 {
      font-size: 2.5rem;
    }
  }
}

main .part-footer-logo {
  height: 4rem;
  width: 14.5rem;
  margin: 0 auto;
}

@media (max-width: 576px) {
  main .part-footer .btn-outline-action {
    background-color: #fff;
    vertical-align: text-bottom;
  }
}

main .part-advanced .advanced-item {
  border-radius: 1rem;
  background-color: #ffffff;
  overflow: hidden;
  height: 100%;
}

main .part-advanced .advanced-item .compare-before {
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0;
  top: 0;
  background-size: auto 100%;
  background-repeat: no-repeat;
  z-index: 2;
}

main .part-advanced .advanced-item .compare-before::after {
  content: "";
  width: 2px;
  height: 100%;
  background: #fff;
  position: absolute;
  right: 0;
  top: 0;
}

main .part-advanced .advanced-item .compare-before::before {
  content: "";
  background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);
  background-size: contain;
  background-position: center;
  width: 4.25rem;
  height: 2.5rem;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  z-index: 3;
}

@keyframes changeWidth {
  0% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

main .part-advanced .advanced-item .compare-before.compare-before-1 {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/File-Repair-before.png);
  animation: changeWidth 8s linear infinite;
  aspect-ratio: 328 / 192;
}

main .part-advanced .advanced-item .compare-before.compare-before-2 {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/Video-Repair-before.png);
  animation: changeWidth 7s linear infinite;
}

main .part-advanced .advanced-item .compare-before.compare-before-3 {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/Photo-Repair-before.png);
  animation: changeWidth 7s linear infinite 1s;
}

main .part-advanced .advanced-item .compare-before.compare-before-4 {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/Audio-Repair-before.png);
  animation: changeWidth 6s linear infinite;
}

main .part-advanced .advanced-item .slider {
  -webkit-appearance: none;
  appearance: none;
  outline: 0;
  margin: 0;
  background: 0 0;
  z-index: 3;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

main .part-advanced .advanced-item .slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 2px;
  height: auto;
  background: transparent;
}

main .part-advanced .advanced-item .item-link {
  color: #000;
}

main .part-advanced .advanced-item .item-link .normal-arrow {
  display: inline;
}

main .part-advanced .advanced-item .item-link .active-arrow {
  display: none;
}

main .part-advanced .advanced-item .item-link .arrow-icon {
  width: 2rem;
  display: inline-block;
}

@media (max-width: 576px) {
  main .part-advanced .advanced-item .item-link .arrow-icon {
    display: block;
  }
}

main .part-advanced .advanced-item .item-link:hover {
  color: #0458ff;
}

main .part-advanced .advanced-item .item-link:hover .normal-arrow {
  display: none;
}

main .part-advanced .advanced-item .item-link:hover .active-arrow {
  display: inline;
}
