<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-WPNBJKV");
    </script>
    <!-- End Google Tag Manager -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=0,maximum-scale=1, minimum-scale=1" />
    <meta
      name="description"
      content="FamiSafe is a powerful parental control app available on both Android and iOS devices. It helps parents track their kid's location, set screen time limits, block apps, detect disturbing content,etc." />
    <title>[Official]FamiSafe - The Most Reliable Parental Control App</title>

    <link rel="canonical" href="https://famisafe.wondershare.com/" />
    <link rel="alternate" href="https://famisafe.wondershare.com/" hreflang="en" />
    <link rel="alternate" href="https://famisafe.wondershare.com/es/" hreflang="es" />
    <link rel="alternate" href="https://famisafe.wondershare.com/br/" hreflang="pt-br" />
    <link rel="alternate" href="https://famisafe.wondershare.com/fr/" hreflang="fr" />
    <link rel="alternate" href="https://famisafe.wondershare.com/de/" hreflang="de" />
    <link rel="alternate" href="https://famisafe.wondershare.com/it/" hreflang="it" />
    <link rel="alternate" href="https://famisafe.wondershare.kr/" hreflang="ko" />
    <link rel="alternate" href="https://famisafe.wondershare.jp/" hreflang="ja" />
    <link rel="alternate" href="https://famisafe.wondershare.com/" hreflang="x-default" />

    <link rel="shortcut icon" type="image/x-icon" href="https://famisafe.wondershare.com/favicon.ico" />
    <!--font for new famisafe-->
    <!--<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Comfortaa&display=swap" />-->
    <!--font for new famisafe-->
    <link rel="stylesheet" href="https://neveragain.allstatics.com/2019/assets/style/bootstrap-famisafe.min.css" />
    <link href="https://famisafe.wondershare.com/assets/css/app.css" rel="stylesheet" />

    <link rel="stylesheet" href="https://neveragain.allstatics.com/2019/assets/vendor/swiper7-bundle.min.css" />
    <link rel="stylesheet" href="https://famisafe.wondershare.com/assets/css/MessinaSans.css" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      main {
        overflow: visible !important;
        background-color: #fff;
        color: #000;
        font-family: "Messina Sans", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif,
          "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      }

      @media (max-width: 992px) {
        main {
          overflow: hidden !important;
        }
      }

      main h1,
      main h2,
      main h3,
      main h4,
      main h5,
      main h6,
      main p,
      main div,
      main span,
      main ul,
      main li {
        margin-bottom: 0;
        font-family: "Messina Sans", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif,
          "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      }

      main h2 {
        text-align: center;
        font-weight: 700;
        font-size: 3.5rem;
      }

      @media (max-width: 768px) {
        main h2 {
          font-size: 24px;
        }
      }

      main .opacity-7 {
        opacity: 0.7;
      }

      main .text-purple {
        color: #7a57ee;
      }

      main .btn-wrapper {
        display: flex;
        justify-content: center;
        gap: 1rem;
      }

      @media (max-width: 768px) {
        main .btn-wrapper {
          flex-direction: column;
          align-items: center;
          gap: 8px;
        }
      }

      main .btn-wrapper .btn {
        margin: 0;
        border-radius: 8px;
        text-transform: capitalize;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 158px;
      }

      @media (min-width: 1280px) {
        main .btn-wrapper .btn.btn-lg {
          min-width: 224px;
        }
      }

      @media (max-width: 768px) {
        main .btn-wrapper .btn {
          width: 280px;
          height: 48px;
        }
      }

      @media (max-width: 768px) {
        main .btn-wrapper .btn {
          display: block;
          vertical-align: baseline;
        }
      }

      @keyframes gradientAnimation {
        0% {
          background-position: 0% 50%;
        }

        50% {
          background-position: 100% 50%;
        }

        100% {
          background-position: 0% 50%;
        }
      }

      main .btn-white {
        color: #7a57ee;
      }

      main .btn-white:hover {
        color: #7a57ee;
      }

      main .btn-colorful {
        background: linear-gradient(94.2deg, #7a57ee 41.38%, #39a0fa 73.42%, #24e3c8 96.58%);
        background-size: 200% 200%;
        animation: gradientAnimation 3s infinite linear;
        transition: transform 0.2s ease-in-out;
        color: #fff;
      }

      main .btn-colorful:hover {
        transform: scale(1.05);
        color: #fff;
      }

      main .btn-purple-bg {
        border: none;
        color: #fff;
        background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg.png) no-repeat center center/contain;
        aspect-ratio: 232 / 64;
        width: 232px;
        transition: transform 0.3s ease-in-out;
      }

      @media (max-width: 768px) {
        main .btn-purple-bg {
          background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg-mobile.png) no-repeat center center/contain;
          aspect-ratio: 280 / 48;
          margin: 0 auto;
        }
      }

      main .btn-purple-bg:hover {
        transform: translateY(-8px);
        color: #fff;
      }

      main .btn-purple-bg2 {
        border: none;
        color: #fff;
        background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg2.png) no-repeat center center/contain;
        aspect-ratio: 280 / 64;
        width: 280px;
        transition: transform 0.3s ease-in-out;
        display: flex;
        gap: 0.5rem;
        box-shadow: 0px 14px 19.8px 0px #7858ff42;
      }

      main .btn-purple-bg2:hover {
        color: #fff;
      }

      main .swiper-pagination {
        bottom: -4px !important;
      }

      main .swiper-pagination .swiper-pagination-bullet {
        width: 8px;
        height: 8px;
        background: rgba(0, 0, 0, 0.14);
        opacity: 1;
      }

      main .swiper-pagination .swiper-pagination-bullet-active {
        width: 54px;
        background: linear-gradient(90deg, #ad8fff 0%, #7c65fe 100%);
        border-radius: 8px;
      }

      main .part-banner {
        background: url(https://famisafe.wondershare.com/images/images-2025/index/banner.jpg) no-repeat center center/cover;
      }

      @media (max-width: 768px) {
        main .part-banner {
          background: url(https://famisafe.wondershare.com/images/images-2025/index/banner-mobile.png) no-repeat center center/cover;
          text-align: center;
        }
      }

      main .part-banner .sub-title {
        display: flex;
        gap: 12px;
        align-items: center;
        justify-content: flex-start;
      }

      @media (max-width: 768px) {
        main .part-banner .sub-title {
          justify-content: center;
        }
      }

      main .part-banner .sub-title .colorful-tip {
        background: linear-gradient(96.75deg, #7a57ee 36.5%, #0dc1ed 72.94%, #00d2ab 96.47%);
        border-radius: 24px;
        padding: 4px 12px;
        font-weight: 700;
        font-size: 1.25rem;
        line-height: 100%;
        color: #fff;
      }

      main .part-banner h1 {
        font-weight: 700;
        font-size: 4.125rem;
        line-height: 100%;
      }

      @media (max-width: 768px) {
        main .part-banner h1 {
          font-size: 32px;
        }
      }

      main .part-banner .system-list {
        display: flex;
        gap: 1rem;
      }

      main .part-banner .system-list a {
        text-decoration: none;
      }

      main .part-banner .system-list a:hover {
        color: #7a57ee;
      }

      main .part-honour {
        background-color: #fbf8ff;
      }

      @keyframes ToRight {
        0% {
          transform: translate3d(0, 0, 0);
        }

        100% {
          transform: translate3d(-50%, 0, 0);
          -webkit-transform: translate3d(-50%, 0, 0);
          -moz-transform: translate3d(-50%, 0, 0);
          -ms-transform: translate3d(-50%, 0, 0);
          -o-transform: translate3d(-50%, 0, 0);
        }
      }

      main .part-honour .honour-list {
        display: flex;
        flex-wrap: nowrap;
        animation: ToRight 18s linear infinite;
        width: fit-content;
      }

      main .part-honour .honour-list .honour-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: auto;
        margin: 0 3rem;
      }

      @media (max-width: 768px) {
        main .part-honour .honour-list .honour-item {
          margin: 0 15px;
        }
      }

      main .part-honour .honour-list .honour-item .honour-logo {
        height: 64px;
        width: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }

      main .part-honour .honour-list .honour-item .honour-intro {
        white-space: nowrap;
        text-align: center;
      }

      @media (max-width: 768px) {
        main .part-honour .honour-list .honour-item .honour-intro {
          display: none;
        }
      }

      main .part-safeguard .nav {
        display: flex;
        justify-content: center;
        gap: 2.875rem;
        flex-wrap: nowrap;
      }

      main .part-safeguard .nav .nav-item {
        text-decoration: none;
        border-radius: 1rem;
        background-color: #f8f7ff;
        color: #000;
        font-size: 1.5rem;
        flex: 1 1 calc(100% / 3);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1.25rem;
        cursor: pointer;
      }

      main .part-safeguard .nav .nav-item.active {
        background-color: #7a57ee;
        color: #fff;
        font-weight: 700;
      }

      main .part-safeguard .safeguard-box {
        position: relative;
        border-radius: 0.5rem;
        overflow: hidden;
      }

      main .part-safeguard .safeguard-box .feature-card {
        position: absolute;
        width: 100%;
        padding: 1.5rem;
        border-radius: 8px;
        background: linear-gradient(111.89deg, #ffffff -0.85%, rgba(255, 255, 255, 0.39) 78.51%);
        backdrop-filter: blur(16.5px);
        max-width: 250px;
        transition: transform 0.3s ease-in-out;
      }

      main .part-safeguard .safeguard-box .feature-card::after {
        content: "";
        position: absolute;
        inset: 0;
        border-radius: 8px;
        padding: 5px;
        background: linear-gradient(135.73deg, rgba(255, 255, 255, 0) 41.9%, rgba(255, 255, 255, 0.45) 118.83%);
        mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
        mask-composite: exclude;
        pointer-events: none;
      }

      main .part-safeguard .safeguard-box .feature-card:hover {
        transform: translateY(-8px);
      }

      main .part-safeguard .safeguard-box .feature-card .feature-card-icon {
        width: 40%;
        position: absolute;
        top: 0;
        left: 0;
        transform: translate(-41%, -25%);
      }

      main .part-safeguard .safeguard-box .feature-card .feature-card-title {
        font-weight: 700;
        color: var(--color-title);
        text-align: center;
      }

      main .part-safeguard .safeguard-box .feature-card .feature-card-description {
        text-align: center;
        font-size: 0.875rem;
      }

      main .part-safeguard .safeguard-box-mobile {
        border-radius: 1rem;
        overflow: hidden;
        position: relative;
      }

      main .part-safeguard .safeguard-box-mobile .title {
        font-weight: 700;
        font-size: 20px;
        color: #000;
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding: 16px;
      }

      main .part-safeguard .safeguard-box-mobile .feature-list {
        position: absolute;
        bottom: 0;
        left: 0;
        margin: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        width: calc(100% - 32px);
        bottom: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(9px);
        padding: 12px;
      }

      main .part-safeguard .safeguard-box-mobile .feature-list .feature-list-wrapper {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: auto;
      }

      main .part-safeguard .safeguard-box-mobile .feature-list .feature-list-wrapper .feature-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 8px;
        font-size: 16px;
      }

      main .part-safeguard .left-btn,
      main .part-safeguard .right-btn {
        position: absolute;
        z-index: 2;
        top: 34px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #000;
        border-radius: 50%;
        color: #ffff;
        border: 1.5px solid white;
      }

      main .part-safeguard .left-btn:hover,
      main .part-safeguard .right-btn:hover {
        background-color: #fff;
        color: #9281ff;
      }

      main .part-safeguard .left-btn {
        left: 16px;
      }

      main .part-safeguard .right-btn {
        right: 16px;
      }

      main .part-digital {
        --digital-item-height: 87vh;
        height: calc(var(--digital-item-height) * 5);
        position: relative;
        background: rgba(244, 243, 255, 0.64);
      }

      @media (max-width: 1280px) {
        main .part-digital {
          height: auto;
        }
      }

      main .part-digital .digital-wrapper {
        height: var(--digital-item-height);
        position: sticky;
        top: 72px;
        display: flex;
        flex-direction: column;
      }

      @media (min-width: 2000px) {
        main .part-digital .digital-wrapper {
          top: 10vh;
        }
      }

      @media (max-width: 1280px) {
        main .part-digital .digital-wrapper {
          height: auto;
          position: relative;
          top: unset;
          text-align: center;
        }
      }

      @media (max-width: 576px) {
        main .part-digital .digital-wrapper .container {
          padding-right: 0;
        }
      }

      main .part-digital h2 {
        margin-bottom: 6.875rem;
        margin-top: 4rem;
      }

      @media (max-width: 1280px) {
        main .part-digital h2 {
          margin-top: 0;
          margin-bottom: 3rem;
        }
      }

      main .part-digital .family-title {
        font-weight: 700;
        font-size: 18px;
        color: #000;
        display: inline-block;
        padding: 4px 8px;
        background-color: #d7cdfa;
        border-radius: 999px;
        margin-bottom: 8px;
      }

      main .part-digital .digital-box {
        display: flex;
        background: linear-gradient(237.63deg, rgba(248, 247, 255, 0) 35.13%, rgba(248, 247, 255, 0.8) 88.35%),
          linear-gradient(211.11deg, #e2deff 14.16%, #e3dfff 42.27%);
        border-radius: 1rem;
        overflow-y: visible;
        justify-content: space-between;
        min-height: 418px;
        position: relative;
      }

      @media (max-width: 1280px) {
        main .part-digital .digital-box {
          background: unset;
          overflow: hidden;
          display: block;
          border-radius: 16px;
        }
      }

      @media (max-width: 576px) {
        main .part-digital .digital-box {
          margin: 0 auto;
          border-radius: 0;
        }
      }

      main .part-digital .digital-box .text-content {
        width: 45.84%;
        padding: 1.5rem 1.5rem 1.5rem 6.25rem;
        overflow: hidden;
      }

      @media (max-width: 1600px) {
        main .part-digital .digital-box .text-content {
          padding: 1.5rem 1.5rem 1.5rem 2.25rem;
        }
      }

      @media (max-width: 1280px) {
        main .part-digital .digital-box .text-content {
          width: 100%;
          padding: unset;
          border-radius: 0 0 16px 16px;
          overflow: hidden;
        }

        main .part-digital .digital-box .text-content .mobile-img-wrapper {
          border-radius: 16px;
          overflow: hidden;
        }
      }

      main .part-digital .digital-box .text-content .digital-item {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      @media (max-width: 1280px) {
        main .part-digital .digital-box .text-content .digital-item {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: auto;
          z-index: 3;
          background: #9788ffc9;
          backdrop-filter: blur(10px);
          padding: 10px 22px;
          display: block;
          text-align: center;
          border-radius: 0 0 16px 16px;
          overflow: hidden;
        }
      }
      @media (max-width: 768.1px) {
        main .part-digital .digital-box .text-content .digital-item {
          min-height: 152px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }
      }
      @media (max-width: 1280px) {
        main .part-digital .digital-box .text-content .digital-item .digital-item-link {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 4;
        }
      }

      main .part-digital .digital-box .text-content .digital-item .digital-item-title {
        font-weight: 700;
        font-size: 2.5rem;
        color: #7a57ee;
        margin-bottom: 2rem;
      }

      @media (max-width: 1280px) {
        main .part-digital .digital-box .text-content .digital-item .digital-item-title {
          font-size: 24px;
          line-height: 32px;
          font-weight: 700;
          color: #fff;
          margin-bottom: 0;
        }
      }

      main .part-digital .digital-box .text-content .digital-item .digital-item-description {
        font-size: 1.125rem;
        line-height: 2rem;
        padding-bottom: 1rem;
      }

      main .part-digital .digital-box .text-content .digital-item .digital-item-arrow {
        width: 32px;
        height: 32px;
        color: #7a57ee;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 8px auto 0;
      }

      @media (max-width: 1280px) {
        main .part-digital .digital-box .text-content .digital-item .digital-item-description {
          font-size: 16px;
          line-height: 20px;
          padding-bottom: 0;
          color: #fff;
        }
      }

      main .part-digital .digital-box .img-content {
        width: 54.16%;
        overflow: visible;
      }

      main .part-digital .digital-box .img-content #digital-img-swiper {
        overflow: visible;
      }

      main .part-digital .digital-box .img-content .img-item-wrapper {
        position: relative;
        height: 100%;
      }

      main .part-digital .digital-box .img-content .img-item-wrapper img {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
      }

      main .part-feature {
        background: rgba(244, 243, 255, 0.64);
      }

      main .part-feature .feature-item {
        position: relative;
        border-radius: 1rem;
        overflow: hidden;
      }

      @media (any-hover: hover) {
        main .part-feature .feature-item:hover {
          box-shadow: 0px 7px 14px 0px #d4cff7;
        }

        main .part-feature .feature-item:hover .feature-detail-card {
          opacity: 1;
        }
      }

      main .part-feature .feature-item-title {
        font-weight: 700;
        font-size: 1.25rem;
        padding: 1.5rem;
        position: absolute;
        left: 0;
        top: 0;
      }

      @media (min-width: 576px) {
        main .part-feature .feature-item .feature-detail-card {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          z-index: 5;
          text-decoration: none;
          opacity: 0;
          transition: opacity 0.3s ease-in-out;
          background: linear-gradient(180deg, rgba(122, 87, 238, 0.6) 0%, #7a57ee 64.68%);
          backdrop-filter: blur(20px);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 1.5rem;
          padding: 1.5rem 3rem;
          margin: 0 auto;
          color: #fff;
          text-align: center;
        }

        main .part-feature .feature-item .feature-detail-card-title {
          font-weight: 700;
          font-size: 1.5rem;
        }

        main .part-feature .feature-item .feature-detail-card-description {
          font-size: 1.125rem;
        }

        main .part-feature .feature-item .feature-detail-card-arrow {
          flex-shrink: 0;
          color: #fff;
          width: 2.5rem;
          height: 2.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          border: 2px solid rgba(255, 255, 255, 0.6);
        }

        main .part-feature .feature-item .feature-detail-card-arrow:hover {
          border-color: #fff;
          background-color: #fff;
          color: #7a57ee;
        }
      }

      @media (max-width: 576px) {
        main .part-feature #feature-swiper {
          margin-left: -15px;
          margin-right: -15px;
          position: relative;
        }

        main .part-feature #feature-swiper::before {
          content: "";
          position: absolute;
          left: 0;
          bottom: 0;
          z-index: 2;
          width: 11.2%;
          height: 100%;
          background: linear-gradient(90deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);
          pointer-events: none;
        }

        main .part-feature #feature-swiper::after {
          content: "";
          position: absolute;
          right: 0;
          bottom: 0;
          z-index: 2;
          width: 11.2%;
          height: 100%;
          background: linear-gradient(270deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);
          pointer-events: none;
        }

        main .part-feature #feature-swiper .swiper-slide {
          overflow: visible;
        }

        main .part-feature #feature-swiper .swiper-slide.swiper-slide-active {
          z-index: 5;
        }

        main .part-feature #feature-swiper .swiper-slide.swiper-slide-active .feature-item {
          overflow: visible;
        }

        main .part-feature #feature-swiper .swiper-slide.swiper-slide-active .feature-item img {
          border: 4.28px solid #ffffff;
          box-shadow: 0px 10.69px 19.36px 0px #2803ec3d;
          border-radius: 1rem;
        }

        main .part-feature #feature-swiper .feature-detail-card {
          display: none;
        }

        main .part-feature .feature-item-mobile {
          height: 100%;
        }

        main .part-feature #feature-text-mobile-swiper .feature-detail-card {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          text-decoration: none;
        }

        main .part-feature .mobile-feature-text {
          margin-top: 2.125rem;
          border-radius: 1rem;
          background: linear-gradient(360deg, #7d58f9 15.62%, #a78dff 100.05%);
          box-shadow: 0px 6px 12.9px 0px #e3dffe;
          padding: 1rem;
          text-align: center;
          color: #fff;
          position: relative;
        }

        main .part-feature .mobile-feature-text::before {
          content: "";
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%, -100%);
          width: 18px;
          height: 6px;
          background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-top.png) no-repeat center center/contain;
        }

        main .part-feature .mobile-feature-text .feature-detail-card-title {
          font-weight: 700;
          font-size: 18px;
          line-height: 32px;
          margin-bottom: 8px;
          color: #fff;
        }

        main .part-feature .mobile-feature-text .feature-detail-card-description {
          font-size: 16px;
          line-height: 20px;
          color: #fff;
          margin-bottom: 8px;
        }

        main .part-feature .mobile-feature-text .feature-detail-card-arrow {
          width: 32px;
          height: 32px;
          color: #7a57ee;
          background-color: #fff;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 auto;
        }
      }

      main .part-geonection .geonection-wrapper {
        background: url(https://famisafe.wondershare.com/images/images-2025/index/geonection-part-bg.png) no-repeat center center/cover;
        position: relative;
        border-radius: 1.5rem;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5.375rem 0 3.625rem;
      }

      @media (max-width: 992px) {
        main .part-geonection .geonection-wrapper {
          flex-direction: column;
          padding: 24px 0;
          justify-content: center;
          align-items: center;
          text-align: center;
        }
      }

      main .part-geonection .geonection-wrapper .geonection-logo {
        position: absolute;
        top: 1.75rem;
        left: 1.5rem;
        z-index: 1;
      }

      @media (max-width: 992px) {
        main .part-geonection .geonection-wrapper .geonection-logo {
          position: relative;
          width: 150px;
          top: unset;
          left: unset;
        }
      }

      main .part-geonection .geonection-wrapper .geonection-content {
        flex: 0 0 40.4%;
        position: relative;
        z-index: 1;
        padding-left: 5rem;
      }

      @media (max-width: 1280px) {
        main .part-geonection .geonection-wrapper .geonection-content {
          padding-left: 3rem;
        }
      }

      @media (max-width: 992px) {
        main .part-geonection .geonection-wrapper .geonection-content {
          flex: auto;
          padding-left: unset;
          padding: 0 16px;
        }
      }

      main .part-geonection .geonection-wrapper .geonection-content .geonection-item-title {
        font-size: 2.5rem;
        font-weight: 700;
      }

      @media (max-width: 992px) {
        main .part-geonection .geonection-wrapper .geonection-content .geonection-item-title {
          font-size: 24px;
          line-height: 36px;
          margin-top: 16px;
          margin-bottom: 12px;
        }
      }

      main .part-geonection .geonection-wrapper .geonection-content .btn-green {
        background: linear-gradient(90.52deg, #92d45d 36.28%, #6ad018 75.06%);
        box-shadow: 0px 4px 4px 0px #ffffff40 inset, 0px -3px 4px 0px #ffffff40 inset;
        color: #000;
        font-size: 18px;
      }

      main .part-geonection .geonection-wrapper .geonection-img {
        flex: 0 0 59.6%;
        position: relative;
        z-index: 1;
      }

      @media (max-width: 992px) {
        main .part-geonection .geonection-wrapper .geonection-img {
          flex: auto;
          margin-top: 36px;
        }
      }

      @media (max-width: 768px) {
        main .part-customer .mobile-container {
          max-width: 540px;
          margin: 0 auto;
        }
      }

      main .part-customer .img-container {
        position: relative;
        line-height: 0;
        overflow: hidden;
        height: 100%;
      }

      main .part-customer .img-container img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      @media (max-width: 1280px) {
        main .part-customer .assetsSwiper .box-style .img-container img {
          all: unset;
          max-width: 100%;
        }
      }

      main .part-customer .assetsSwiper .box-style .assets-title {
        position: absolute;
        width: 100%;
        text-align: center;
        font-size: 1.25rem;
        font-weight: 700;
        color: #fff;
        margin-bottom: 22px;
        padding: 0 16px;
        bottom: 0;
        left: 0;
        z-index: 2;
      }

      main .part-customer .assetsSwiper .swiper-slide.active .box-style .assets-title {
        display: none;
      }

      @media (max-width: 1280px) {
        main .part-customer .assetsSwiper .box-style .assets-title {
          display: none;
        }
      }

      @media (min-width: 1280px) {
        main .part-customer .assetsSwiper-box {
          position: relative;
        }

        main .part-customer .assetsSwiper .swiper-wrapper {
          aspect-ratio: 1920 / 456;
          gap: 14px;
          justify-content: space-between;
        }

        main .part-customer .assetsSwiper .swiper-slide {
          width: 12.2%;
          display: block;
          overflow: hidden;
          border-radius: 1rem;
          position: relative;
          transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
        }

        main .part-customer .assetsSwiper .swiper-slide.active {
          width: 48.13%;
          opacity: 1;
        }

        main .part-customer .assetsSwiper .swiper-slide .box-style {
          height: 100%;
          position: relative;
          border-radius: 1rem;
          overflow: hidden;
        }

        @keyframes fadeIn {
          from {
            visibility: hidden;
          }

          to {
            visibility: visible;
          }
        }

        main .part-customer .assetsSwiper .swiper-slide .box-style .customer-info-box {
          visibility: hidden;
        }

        main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box {
          animation: fadeIn 0.01s ease-in-out;
          animation-delay: 0.8s;
          animation-fill-mode: forwards;
          position: absolute;
          margin: 24px;
          margin-bottom: 2.25rem;
          bottom: 0;
          left: 0;
          width: calc(100% - 24px * 2);
          background: rgba(0, 0, 0, 0.33);
          backdrop-filter: blur(10px);
          border-radius: 1rem;
          padding: 0.5rem 1rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 3rem;
        }

        main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box .customer-info {
          font-size: 1rem;
          font-weight: 400;
          color: #fff;
          line-height: 100%;
        }

        main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 {
          position: absolute;
          left: 60px;
          top: 24px;
        }

        main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 .customer-info .customer-name {
          font-weight: 700;
          font-size: 1.25rem;
          line-height: 100%;
          color: #fff;
        }

        main .part-customer .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 .customer-info .customer-desc {
          font-weight: 400;
          font-size: 1rem;
          line-height: 100%;
          color: #fff;
        }

        main .part-customer .assetsSwiper .swiper-slide:not(.active) .box-style::after {
          content: "";
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }

      @media (max-width: 1280px) {
        main .part-customer .assetsSwiper {
          overflow: initial;
          padding-top: 24px;
        }
      }

      @media (max-width: 1280px) and (max-width: 768px) {
        main .part-customer .assetsSwiper {
          padding-top: 12px;
        }
      }

      @media (max-width: 1280px) {
        main .part-customer .assetsSwiper-box {
          padding: 0 15px;
        }

        main .part-customer .assetsSwiper .swiper-slide {
          opacity: 0.5;
        }

        main .part-customer .assetsSwiper .swiper-slide-active {
          opacity: 1;
        }

        main .part-customer .assetsSwiper .rounded-16 {
          border-radius: 8px;
        }

        main .part-customer .customer-info-box {
          position: absolute;
          margin: 16px;
          bottom: 0;
          left: 0;
          width: calc(100% - 16px * 2);
          background-color: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(4px);
          border-radius: 8px;
          padding: 16px 24px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #fff;
        }

        main .part-customer .customer-info-box .btn-wrapper {
          display: none;
        }

        main .part-customer .customer-info-box2 {
          position: absolute;
          top: 16px;
          right: 16px;
          display: flex;
          align-items: stretch;
          justify-content: center;
          color: #fff;
        }

        main .part-customer .customer-info-box2 .customer-name {
          font-size: 18px;
          font-weight: 700;
        }

        main .part-customer .customer-info-box2 .customer-desc {
          font-size: 16px;
          font-weight: 400;
        }
      }

      main .part-saying .saying-box {
        display: flex;
        justify-content: space-between;
        background: linear-gradient(180deg, rgba(254, 232, 226, 0) 23.47%, rgba(254, 232, 226, 0.86) 100%), linear-gradient(0deg, #7a57ee, #7a57ee);
        border-radius: 1.5rem;
        overflow: hidden;
        color: #fff;
        gap: 8.75rem;
        max-height: 732px;
      }

      @media (max-width: 1600px) {
        main .part-saying .saying-box {
          gap: 3.75rem;
        }
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box {
          flex-direction: column;
          gap: unset;
          max-height: unset;
        }
      }

      main .part-saying .saying-box .left-box {
        flex: 0 1 48%;
        padding: 2rem 4rem;
        padding-right: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .left-box {
          flex: initial;
          padding: 30px 24px;
          text-align: center;
          align-items: center;
        }
      }

      main .part-saying .saying-box .left-box .saying-title {
        text-align: left;
        font-size: 3.5rem;
        line-height: 1.2;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .left-box .saying-title {
          text-align: center;
          font-size: 24px;
          font-weight: 700;
        }
      }

      main .part-saying .saying-box .left-box .white-divider {
        width: 100%;
        height: 1px;
        background-color: #fff;
        opacity: 0.7;
        margin: 3rem 0;
        max-width: 395px;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .left-box .white-divider {
          display: none;
        }
      }

      main .part-saying .saying-box .left-box .count-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        flex-wrap: wrap;
        gap: 3.5rem 4rem;
        max-width: 344px;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .left-box .count-list {
          max-width: unset;
          gap: 24px 50px;
          justify-content: space-around;
          margin-top: 24px;
        }
      }

      main .part-saying .saying-box .left-box .count-list .count-box {
        flex: 0 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        gap: 4px;
        color: #fff;
        max-width: 170px;
      }

      @media (max-width: 576px) {
        main .part-saying .saying-box .left-box .count-list .count-box {
          flex: 0 0 40%;
        }
      }

      main .part-saying .saying-box .left-box .count-list .count-box .count {
        font-size: 2.5rem;
        font-weight: 700;
      }

      main .part-saying .saying-box .left-box .count-list .count-box .count-desc {
        font-size: 1.25rem;
      }

      main .part-saying .saying-box .right-box {
        flex: 0 0 52%;
        padding-right: 3rem;
        display: flex;
        gap: 1.5rem;
        position: relative;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .right-box {
          flex: initial;
          padding-right: 0;
          gap: 0;
          width: fit-content;
        }
      }

      main .part-saying .saying-box .right-box::after {
        position: absolute;
        z-index: 1;
        bottom: 0;
        right: 0;
        content: "";
        width: 100%;
        height: 22.4%;
        background: linear-gradient(0deg, #ebd3e4 0%, rgba(211, 185, 230, 0) 93.33%);
        pointer-events: none;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .right-box::after {
          display: none;
        }
      }

      main .part-saying .saying-box .right-box::before {
        position: absolute;
        z-index: 1;
        top: 0;
        right: 0;
        content: "";
        width: 100%;
        height: 22.4%;
        background: linear-gradient(180deg, #7a57ee 0%, rgba(122, 87, 238, 0) 93.33%);
        pointer-events: none;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .right-box::before {
          display: none;
        }
      }

      main .part-saying .saying-box .right-box .user-card-list {
        display: flex;
        flex-direction: column;
        height: fit-content;
        flex-wrap: nowrap;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .right-box .user-card-list {
          flex-direction: row;
        }
      }

      @keyframes marquee1 {
        0% {
          transform: translateY(0);
        }

        100% {
          transform: translateY(-50%);
        }
      }

      @keyframes marquee2 {
        0% {
          transform: translateY(-50%);
        }

        100% {
          transform: translateY(0);
        }
      }

      @keyframes marquee1-mobile {
        0% {
          transform: translateX(0);
        }

        100% {
          transform: translateX(-50%);
        }
      }

      main .part-saying .saying-box .right-box .user-card-list.list1 {
        animation: marquee1 40s linear infinite;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .right-box .user-card-list.list1 {
          animation: marquee1-mobile 40s linear infinite;
        }
      }

      main .part-saying .saying-box .right-box .user-card-list.list2 {
        animation: marquee2 40s linear infinite;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .right-box .user-card-list.list2 {
          animation: marquee2-mobile 40s linear infinite;
        }
      }

      main .part-saying .saying-box .right-box .user-card-list.list1-mobile {
        animation: marquee1-mobile 60s linear infinite;
        margin: 30px 0;
      }

      main .part-saying .saying-box .right-box .user-card-list .user-card {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin: 1rem 0;
        background-color: #fff;
        border-radius: 1rem;
        overflow: hidden;
        padding: 1.5rem;
        color: #000;
      }

      @media (max-width: 1280px) {
        main .part-saying .saying-box .right-box .user-card-list .user-card {
          margin: 0 5px;
          width: 276px;
          padding: 20px;
        }
      }

      main .part-saying .saying-box .right-box .user-card-list .user-card .useer-info {
        display: flex;
        gap: 0.875rem;
        align-items: center;
      }

      main .part-saying .saying-box .right-box .user-card-list .user-card .useer-info .user-avatar {
        width: 3.625rem;
      }

      main .part-saying .saying-box .right-box .user-card-list .user-card .useer-info .user-name {
        font-size: 1.125rem;
        font-weight: 700;
        margin-bottom: 6px;
        line-height: 100%;
      }

      main .part-saying .saying-box .right-box .user-card-list .user-card .user-desc {
        font-size: 0.875rem;
        color: #000;
      }

      @media (min-width: 1280px) {
        main .part-grow .swiper-wrapper {
          flex-wrap: wrap;
          gap: 8px;
          display: flex;
        }

        main .part-grow .swiper-wrapper .swiper-slide.what-new-slide {
          flex: 0 1 calc(61.2% - 8px);
        }

        main .part-grow .swiper-wrapper .swiper-slide.tutorials-slide {
          flex: 0 1 calc(38.8% - 8px);
        }

        main .part-grow .swiper-wrapper .swiper-slide.social-media-slide {
          flex: 0 1 calc(44.1% - 8px);
        }

        main .part-grow .swiper-wrapper .swiper-slide.blogs-slide {
          flex: 0 1 calc(55.9% - 8px);
        }
      }

      main .part-grow .social-media-mobile-list {
        display: flex;
        gap: 16px;
        justify-content: center;
        align-items: center;
        margin-top: 24px;
      }

      main .part-grow .grow-box {
        border-radius: 1rem;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
        height: 100%;
        justify-content: space-between;
      }

      @media (max-width: 1280px) {
        main .part-grow .grow-box {
          flex-direction: column;
        }

        main .part-grow .grow-box .content-wrapper {
          flex: auto !important;
          padding: 16px !important;
          text-align: center;
        }

        main .part-grow .grow-box .img-wrapper {
          width: 100% !important;
          box-sizing: border-box;
        }
      }

      @keyframes jump {
        0% {
          transform: translateX(5px);
        }

        50% {
          transform: translateX(-3px);
        }

        100% {
          transform: translateX(5px);
        }
      }

      @media (any-hover: hover) {
        main .part-grow .grow-box:hover .img-wrapper {
          transform: scale(1.1);
        }

        main .part-grow .grow-box:hover .black-arrow {
          animation: jump 1s infinite;
        }
      }

      main .part-grow .grow-box .card-link {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
      }

      main .part-grow .grow-box .title {
        font-size: 2rem;
        font-weight: 700;
      }

      main .part-grow .what-new {
        background: linear-gradient(300.68deg, #b7a3ff 21.43%, #f3ddff 55.08%, #ffe9dd 83.49%);
      }

      main .part-grow .what-new .content-wrapper {
        flex: 0 1 46.52%;
        padding: 1.5rem 0;
        padding-left: 3.75rem;
      }

      main .part-grow .what-new .img-wrapper {
        width: 53.48%;
        transition: transform 0.3s linear;
        align-self: flex-end;
      }

      main .part-grow .tutorials {
        background: linear-gradient(180deg, #d2d2ff 0%, #ece7fe 100%);
      }

      main .part-grow .tutorials .content-wrapper {
        flex: 0 1 51.4%;
        padding: 1rem;
        padding-left: 2.25rem;
      }

      main .part-grow .tutorials .img-wrapper {
        width: 48.6%;
        transition: transform 0.3s linear;
        align-self: flex-end;
      }

      main .part-grow .social-media .social-media-list {
        display: flex;
        gap: 1.75rem;
        flex-wrap: nowrap;
        position: absolute;
        bottom: 40%;
        left: 17%;
        width: 65%;
      }

      main .part-grow .social-media .social-media-list .social-media-item {
        flex: 1;
        transition: transform 0.3s linear;
      }

      main .part-grow .social-media .social-media-list .social-media-item:hover {
        transform: scale(1.4);
      }

      main .part-grow .blogs {
        background: linear-gradient(313.48deg, #d3faf9 42.66%, #cbcbff 95.75%);
      }

      main .part-grow .blogs .content-wrapper {
        flex: 0 1 41.03%;
        padding: 1rem;
        padding-left: 3.75rem;
      }

      main .part-grow .blogs .img-wrapper {
        width: 58.97%;
        transition: transform 0.3s linear;
        align-self: flex-end;
      }

      main .part-protection .protection-box {
        border-radius: 1.5rem;
        background: linear-gradient(299.31deg, rgba(215, 199, 255, 0.67) 13.16%, rgba(178, 165, 255, 0.67) 67.6%);
        padding-left: 80px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1.875rem;
        position: relative;
      }

      @media (max-width: 1280px) {
        main .part-protection .protection-box {
          flex-direction: column;
          padding-left: 0;
          gap: 0px;
          justify-content: space-between;
          background: #e2dfff;
          padding: 30px;
        }
      }

      main .part-protection .protection-box .trusted-box,
      main .part-protection .protection-box .privacy-box {
        flex: 1;
        border-radius: 8px;
        overflow: hidden;
        background: linear-gradient(141.94deg, #ffffff 21.96%, rgba(255, 255, 255, 0.7) 93.72%);
        padding: 1.5rem 2rem;
        text-align: center;
      }

      @media (max-width: 1280px) {
        main .part-protection .protection-box .trusted-box,
        main .part-protection .protection-box .privacy-box {
          background: unset;
          padding-bottom: 0;
        }
      }

      main .part-protection .protection-box .trusted-box .title,
      main .part-protection .protection-box .privacy-box .title {
        font-size: 1.5rem;
        font-weight: 700;
        position: relative;
        text-align: center;
        display: inline-flex;
        margin-bottom: 1.25rem;
      }

      @media (max-width: 1600px) {
        main .part-protection .protection-box .trusted-box .title,
        main .part-protection .protection-box .privacy-box .title {
          margin-bottom: 8px;
        }
      }

      @media (max-width: 1280px) {
        main .part-protection .protection-box .trusted-box .title,
        main .part-protection .protection-box .privacy-box .title {
          color: #7a57ee;
          margin-bottom: 16px;
        }
      }

      main .part-protection .protection-box .trusted-box .title::before,
      main .part-protection .protection-box .privacy-box .title::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        transform: translate(-110%, -5%);
        aspect-ratio: 47 / 42;
        background: url(https://famisafe.wondershare.com/images/images-2025/index/feather-left.svg) no-repeat center center/contain;
        width: 2.625rem;
      }

      main .part-protection .protection-box .trusted-box .title::after,
      main .part-protection .protection-box .privacy-box .title::after {
        content: "";
        position: absolute;
        bottom: 0;
        right: 0;
        transform: translate(110%, -5%);
        aspect-ratio: 47 / 42;
        background: url(https://famisafe.wondershare.com/images/images-2025/index/feather-right.svg) no-repeat center center/contain;
        width: 2.625rem;
      }

      main .part-protection .protection-box .purple-divider {
        width: 100%;
        height: 1px;
        background-color: #bbadfe;
        margin: 24px 0;
      }

      main .part-protection .protection-box .purple-lock {
        width: 33.3%;
      }

      @media (max-width: 1280px) {
        main .part-protection .protection-box .purple-lock {
          width: 122px;
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      main .part-footer .footer-box {
        border-radius: 1rem;
        overflow: hidden;
        background-color: #e2dfff;
        background: url(https://famisafe.wondershare.com/images/images-2025/index/footer-bg.jpg) no-repeat center center/cover;
        margin: 0 2.625rem;
        padding: 6rem 3rem;
        text-align: center;
      }

      @media (max-width: 768px) {
        main .part-footer .footer-box {
          margin: 0 15px;
          padding: 30px 15px;
        }
      }

      #modal-youtube .btn-action {
        background-color: #8c5bde;
        border-color: #8c5bde;
      }
    </style>

    <!--test font for famisafe-->
    <style>
      body,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      .h1,
      .h2,
      .h3,
      .h4,
      .h5,
      .h6 {
        font-family: "Muli", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
          "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      }
      body,
      a {
        color: #313131;
      }
      .text-primary {
        color: #313131 !important;
      }
      .text-secondary {
        color: #313131 !important;
      }
      .text-secondary-6 {
        color: #4a4a4a !important;
      }
      main#wsc-main .resource-list .item .title a {
        color: #313131;
      }
      main#wsc-main .topic-main .resource-category a:not(.btn),
      main#wsc-main .topic-main .resource-category a:hover:not(.btn) {
        color: #313131;
      }
      main#wsc-main .contens_tab_side .tabNav_side dd li a {
        color: #313131 !important;
      }
      main#wsc-main .main-info h5 a,
      main#wsc-main .main-info p a,
      main#wsc-main .nav-category li a {
        color: #313131 !important;
      }
    </style>
    <!--test font for famisafe-->
    <script type="text/javascript">
      var CHANNEL_ID = "150";
      var SITE_ID = "150";
      var CMS_LANGUAGE = "en";
      var TEMPLATE_ID = "9488";
      var PAGE_ID = "363087";
      var TEMPLATE_MODULE = "other";
      var TEMPLATE_TYPE = "index";
    </script>
  </head>
  <body data-pro="famisafe" data-cat="template" data-nav="basic" data-sys="auto" data-dev="auto" data-lan="auto">
    <style>
      .addthis-smartlayers-mobile {
        display: none;
      }
      .upload-box {
        position: sticky;
        top: 0;
        width: 100%;
        background: #ffffff;
        z-index: 100;
      }
      .mobile-top-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
      }
    </style>
    <div class="d-lg-none upload-box" onclick="$('.upload-box').hide()">
      <div class="mobile-top-bar">
        <div class="d-flex align-items-center">
          <div style="width: 38px; line-height: 40px; text-align: center">
            <svg t="1650618211451" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2004" width="10" height="10">
              <path
                d="M512 456.310154L94.247385 38.557538a39.542154 39.542154 0 0 0-55.689847 0 39.266462 39.266462 0 0 0 0 55.689847L456.310154 512 38.557538 929.752615a39.542154 39.542154 0 0 0 0 55.689847 39.266462 39.266462 0 0 0 55.689847 0L512 567.689846l417.752615 417.752616c15.163077 15.163077 40.290462 15.36 55.689847 0a39.266462 39.266462 0 0 0 0-55.689847L567.689846 512 985.442462 94.247385a39.542154 39.542154 0 0 0 0-55.689847 39.266462 39.266462 0 0 0-55.689847 0L512 456.310154z"
                p-id="2005"
                fill="#666666"></path>
            </svg>
          </div>
          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/famisafe-square.svg" class="img-fluid" alt="famisafe logo" style="width: 40px" />
          <div class="ml-2">
            <h6 class="mb-0 mt-1" style="font-size: 14px">FamiSafe</h6>
            <div style="min-height: 18px">
              <p class="mb-0 font-size-small text-gray-8 mt-2 sys-ios">Best parental control App</p>
              <p class="mb-0 font-size-small text-gray-8 mt-2 sys-android">Best parental control App</p>
            </div>
          </div>
        </div>
        <div>
          <div class="mr-3 sys-ios">
            <a
              href="https://app.adjust.com/1f8zmkeb_1fnvm9bh"
              target="_blank"
              class="btn btn-sm btn-outline-action"
              style="border-radius: 10px; padding: 8px 10px"
              >Try Now</a
            >
          </div>
          <div class="mr-3 sys-android">
            <a
              href="https://app.adjust.com/1f8zmkeb_1fnvm9bh"
              target="_blank"
              class="btn btn-sm btn-outline-action"
              style="border-radius: 10px; padding: 8px 10px"
              >Try Now</a
            >
          </div>
        </div>
      </div>
    </div>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WPNBJKV" height="0" width="0" style="display: none; visibility: hidden"></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- 头部公共样式块 -->
    <link rel="stylesheet" href="https://neveragain.allstatics.com/2019/assets/style/wsc-header-footer-2020.min.css" />
    <link rel="stylesheet" href="https://www.wondershare.com/assets/header-footer-2021.css" />
    <style>
      @media ((min-width: 1280px)) {
        .wsc-header2020-navbar-nav-toggle.creativity.creativity-en {
          min-width: 576px;
        }
        .wsc-header2020-navbar-nav-toggle.diagram-grahics,
        .wsc-header2020-navbar-nav-toggle.diagram-graphics,
        .wsc-header2020-navbar-nav-toggle.utility {
          min-width: 400px;
          width: auto;
        }
        .wsc-header2020-navbar-nav-toggle.explore-ai {
          min-width: 445px;
        }
      }
    </style>
    <!-- 头部公共样式块 -->
    <link rel="stylesheet" href="https://neveragain.allstatics.com/2019/assets/style/wsc-header-footer-2020.min.css" />
    <link rel="stylesheet" href="https://www.wondershare.com/assets/header-footer-2021.css" />
    <style>
      .wsc-header2020 .wsc-header2020-navbar-famisafe .wsc-header2020-navbar-linkBtn-outline,
      .wsc-header2020 .wsc-header2020-navbar-famisafe .wsc-header2020-navbar-linkBtn,
      .wsc-header2020 .wsc-header2020-navbar-famisafe .wsc-header2020-dropdown-learnMore button {
        border-radius: 0rem;
      }
      /*.wsc-header2020 .wsc-header2020-navbar-main {*/
      /*  box-shadow: none;*/
      /*}*/

      .wsc-header2020 .wsc-header2020-navbar-main .navbar-mobile-download,
      .wsc-header2020 .wsc-header2020-navbar-main .navbar-mobile-pricing {
        display: none;
      }
      @media (max-width: 1279.98px) {
        .wsc-header2020 li.wsc-header2020-navbar-dropdown:first-child .wsc-header2020-dropdownMenuBody-title {
          margin-bottom: 0;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbar-content {
          justify-content: flex-start;
        }
        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-mobile-button {
          display: inline-block;
          position: absolute;
          top: 50%;
          right: 50px;
          transform: translateY(-50%);
          background-color: #8c5bde;
          border-radius: 999px;
          color: #fff;
          font-weight: 700;
          font-size: 14px;
          line-height: 1.4;
          letter-spacing: -2%;
          padding: 7px 12.5px;
          font-weight: 700;
          text-decoration: none;
        }
        .wsc-header2020 .wsc-header2020-navbar-main .navbar-mobile-download {
          display: block;
          padding: 16px;
        }
        .wsc-header2020 .wsc-header2020-navbar-main .navbar-mobile-pricing {
          display: block;
        }
        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-download-button {
          background-color: #8c5bde;
          border-radius: 999px;
          font-weight: 700;
          font-size: 14px;
          line-height: 16.8px;
          letter-spacing: -2%;
          color: #fff;
          padding: 12px 0;
          display: block;
          text-align: center;
          text-decoration: none;
        }
        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-download-button:hover {
          color: #fff !important;
        }
        .wsc-header2020 .wsc-header2020-navbar-main .pc-show {
          display: none;
        }
        /* .wsc-header2020 .wsc-header2020-navbar-main li.wsc-header2020-navbar-dropdown:first-child .wsc-header2020-dropdownMenuBody-box{padding: 0 16px 0 24px} */
      }
      .wsc-header2020 .wsc-header2020-navbar-famisafe .wsc-header2020-navbar-linkBtn-outline,
      .wsc-header2020 .wsc-header2020-navbar-famisafe .wsc-header2020-navbar-linkBtn,
      .wsc-header2020 .wsc-header2020-navbar-famisafe .wsc-header2020-dropdown-learnMore button {
        border-radius: 50rem;
      }

      @media (min-width: 1280px) {
        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-mobile-button {
          display: none;
        }
      }
      @media (min-width: 1600px) {
        .wsc-header2020 .wsc-header2020-dropdownMenu-body {
          padding: 32px 0;
        }
      }

      .wsc-header2020-navbar-main .wsc-header2020-navbar-link:hover {
        color: #8c5bde !important;
      }
      a.subtitle-link:hover {
        color: #8c5bde;
        text-decoration: none;
      }
      @media (max-width: 767.98px) {
        .wsc-header2020-navbar-main .wsc-header2020-navbar-collapse img {
          display: none;
        }
      }
    </style>
    <style>
      /* all */
      .wsc-header2020 .wsc-header2020-navbar-linkBtn-outline {
        font-size: 14px;
      }
      .wsc-header2020 .wsc-header2020-navbar-linkBtn {
        font-size: 14px;
      }
      .wsc-header2020 .wsc-header2020-dropdownMenuBody-list li {
        padding: 0 0 24px;
      }
      @media (max-width: 1280px) {
        .wsc-header2020 .wsc-header2020-dropdownMenuBody-hr-vertical {
          width: 1px;
          border-left: 1px solid #ececec;
          flex-grow: 0;
        }
        .wsc-header2020 .wsc-header2020-dropdownMenuBody-title {
          margin-bottom: 0;
        }
        .wsc-header2020 .wsc-header2020-dropdownMenuBody-list li {
          padding: 6px 0;
        }
      }
      .wsc-header2020 .wsc-header2020-dropdownMenuBody-list li a {
        color: rgba(0, 0, 0, 0.9);
        position: relative;
      }
      .wsc-header2020 .wsc-header2020-dropdownMenuBody-list li a:hover {
        color: #8c5bde;
        text-decoration: underline;
      }
      .wsc-header2020 .wsc-header2020-dropdownMenuBody-list li a:hover:after {
        content: ">";
        position: absolute;
        right: -2px;
        transform: translate(100%, 0);
        color: #8c5bde;
      }
      .wsc-header2020 .wsc-header2020-dropdownMenuBody-list li:has(.new-tip) a:hover:after {
        content: unset;
      }
      .wsc-header2020 .wsc-header2020-navbar-famisafe .wsc-header2020-navbar-linkBtn:hover {
        background-color: #7738e1;
        border-color: #7738e1;
      }

      /* Products */
      @media (min-width: 1280px) {
        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-dropdownMenuBody-box {
          padding: 8px 15px 8px 15px;
          height: 100%;
        }
      }

      .wsc-header2020-dropdownMenuBody-content .product-item {
        max-width: 40%;
        padding: 0;
        height: auto;
        flex: 0 1 33.33%;
      }

      @media (max-width: 1280px) {
        .wsc-header2020-dropdownMenuBody-content .product-item {
          max-width: unset;
        }
      }
      .wsc-header2020-dropdownMenuBody-content .product-box {
        border-radius: 24px;
        background-color: #fcfaff;
        padding: 24px;
        display: flex;
        align-items: start;
        width: 100%;
        height: 100%;
      }

      .wsc-header2020-dropdownMenuBody-content .product-item:first-child .product-box {
        background-color: #f8f4ff;
      }

      .wsc-header2020-dropdownMenuBody-content .product-box .product-box-icon {
        margin-right: 16px;
      }

      @media (max-width: 576px) {
        .wsc-header2020-dropdownMenuBody-content .product-box .product-box-icon img {
          width: 32px;
          height: 32px;
        }
      }
      .product-box-info {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .wsc-header2020-dropdownMenuBody-content .product-box .product-box-title {
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        font-weight: 700;
        font-size: 18px;
        gap: 8px;
        color: #000;
        text-decoration: none;
      }
      .wsc-header2020-dropdownMenuBody-content .product-box .product-box-title:hover {
        color: #8c5bde;
      }

      .wsc-header2020-dropdownMenuBody-content .product-box-title:hover .right-arrow {
        color: #fff;
        background-color: #8c5bde;
        border-color: #8c5bde;
      }

      .wsc-header2020-dropdownMenuBody-content .product-box .right-arrow {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #8c5bde;
        color: #8c5bde;
        line-height: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
      .wsc-header2020-dropdownMenuBody-content .product-box .title-content {
        display: flex;
        align-items: center;
        gap: 4px;
      }
      .wsc-header2020-dropdownMenuBody-content .product-box .product-box-content {
        font-size: 14px;
        color: rgba(00, 00, 00, 0.9);
        margin: 8px 0 16px;
        white-space: initial;
        font-size: 14px;
        position: relative;
        display: inline-block;
        width: fit-content;
      }

      .wsc-header2020-dropdownMenuBody-content .product-box .product-box-btn {
        align-items: end;
        display: block;
        text-align: center;
        padding: 8px;
        border-radius: 1000px;
        border: 2px solid #8c5bde;
        font-size: 14px;
        line-height: 18px;
        color: #8c5bde;
        font-weight: 700;
        background-color: transparent;
        margin-top: auto;
      }
      .wsc-header2020-dropdownMenuBody-content .product-box .product-box-btn:hover {
        background-color: #8c5bde;
        color: #fff;
        text-decoration: none;
      }
      .wsc-header2020-dropdownMenuBody-content .product-item:first-child .product-box .product-box-btn {
        background-color: #8c5bde;
        color: #fff;
      }
      .wsc-header2020-dropdownMenuBody-content .product-item:first-child .product-box .product-box-btn:hover {
        background-color: #7738e1;
        border-color: #7738e1;
        color: #fff;
      }
      /* Features */
      @media (min-width: 1280px) {
        .wsc-header2020-dropdownMenuBody-content .wsc-header2020-dropdownMenuBody-item {
          padding: 0 15px;
        }
        .wsc-header2020-dropdownMenuBody-content .features-box1 {
          height: 100%;
          padding: 24px 30px !important;
          background-color: #f8f5ff;
          border-radius: 16px;
        }
        .wsc-header2020-dropdownMenuBody-content .features-box2 {
          height: 100%;
          padding: 24px 30px !important;
          background-color: rgba(255, 214, 135, 0.14);
          border-radius: 16px;
        }
        .wsc-header2020-dropdownMenuBody-content .features-box3 {
          height: 100%;
          padding: 24px 30px !important;
          background-color: rgba(217, 255, 214, 0.46);
          border-radius: 16px;
        }
      }
      .wsc-header2020-dropdownMenuBody-content .new-tip {
        margin-left: 8px;
        background: linear-gradient(246.49deg, #5db7f8 -16.08%, #975df8 83.6%);
        padding: 3px 5px;
        border-radius: 100px;
        font-weight: 900;
        color: #fff;
        font-size: 10px;
        line-height: 100%;
        letter-spacing: -0.04;
      }
      /* resource */
      @media (min-width: 1280px) {
        .wsc-header2020-dropdownMenuBody-content .resource-box {
          padding: 24px 30px !important;
          background-color: #f8f4ff;
          border-radius: 16px;
        }
        .wsc-header2020-dropdownMenuBody-content .resource-item {
          flex: 0 1 27%;
          max-width: 40% !important;
        }
        .wsc-header2020-dropdownMenuBody-content .resource-item:nth-child(1) {
          flex: 0 1 30%;
          max-width: 40% !important;
        }
        .wsc-header2020-dropdownMenuBody-content .resource-item:nth-child(2) {
          flex: 0 1 18.7%;
          max-width: 20% !important;
        }

        @media (min-width: 1600px) {
          .wsc-header2020-dropdownMenuBody-content .resource-item {
            padding: 0 0 0 48px;
          }
        }
      }
    </style>
    <header class="wsc-header2020">
      <nav class="wsc-header2020-navbar-master wsc-header202004-navbar-wondershare">
        <div class="wsc-header2020-container">
          <div class="wsc-header2020-navbar-content">
            <div class="wsc-header2020-navbar-brand">
              <a href="https://www.wondershare.com/"></a>
              <div></div>
            </div>

            <button class="wsc-header2020-navbar-collapse-toggle" type="button" aria-expanded="false">
              <svg
                class="wsc-header2020-navbar-collapse-toggle-icon"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="https://www.w3.org/2000/svg">
                <path d="M4 6H20M20 12L4 12M20 18H4" stroke="white" stroke-width="1.5"></path>
              </svg>
              <svg
                class="wsc-header2020-navbar-collapse-toggle-icon-close"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="https://www.w3.org/2000/svg">
                <path d="M18 18L12 12M6 6L12 12M18 6L12 12M6 18L12 12" stroke="white" stroke-width="1.5"></path>
              </svg>
            </button>
            <div class="wsc-header2020-navbar-collapse">
              <ul class="wsc-header2020-navbar-nav active_menu">
                <div
                  data-toggle="ad"
                  data-text="Explore the Possibilities of FamiSafe's New Feature: Call & Messages!"
                  data-link="https://famisafe.wondershare.com/call-and-messages.html?from=website-banner"
                  data-link-text="Learn More!"
                  data-image="https://mobiletrans.wondershare.com/images/images2019/hot-icon.gif"></div>

                <!-- 一级导航头部公共块 -->
                <li class="wsc-header2020-navbar-item with-toggle">
                  <a class="wsc-header2020-navbar-link active_menu_a" href="javascript:">
                    Video Creativity
                    <svg class="ml-2" width="13" height="9" viewBox="0 0 13 9" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M12 1L6.5 7L1 1" stroke="currentColor" stroke-width="2" />
                    </svg>
                  </a>
                  <div class="wsc-header2020-navbar-nav-toggle creativity creativity-en">
                    <div class="row no-gutters px-4">
                      <div class="left border-control">
                        <div class="mb-4 font-size-small">Video Creativity Products</div>
                        <a href="https://filmora.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/filmora-square.svg" alt="wondershare filmora logo" />
                          <div class="pl-2">
                            <strong class="text-black">Filmora</strong>
                            <div class="font-size-small">Complete video editing tool.</div>
                          </div>
                        </a>
                        <a href="https://videoconverter.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/uniconverter-square.svg" alt="wondershare uniconverter logo" />
                          <div class="pl-2">
                            <strong class="text-black">UniConverter</strong>
                            <div class="font-size-small">High-speed media conversion.</div>
                          </div>
                        </a>
                        <a href="https://democreator.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/democreator-square.svg" alt="wondershare democreator logo" />
                          <div class="pl-2">
                            <strong class="text-black">DemoCreator</strong>
                            <div class="font-size-small">Efficient tutorial video maker.</div>
                          </div>
                        </a>
                        <a href="https://virbo.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/virbo-square.svg" alt="wondershare virbo logo" />
                          <div class="pl-2">
                            <strong class="text-black">Virbo</strong>
                            <div class="font-size-small">Powerful AI video generator.</div>
                          </div>
                        </a>
                        <a href="https://www.wondershare.com/shop/individuals.html#creativity" target="_blank" class="vap-btn mt-2">View all products</a>
                      </div>
                      <div class="right">
                        <div class="mt-lg-0 my-4 font-size-small">Explore</div>
                        <ul class="list-unstyled explore">
                          <li><a href="https://www.wondershare.com/products-solutions/digital-creativity/" target="_blank" class="text-black">Overview</a></li>
                          <li>
                            <a href="https://www.wondershare.com/products-solutions/digital-creativity/video.html" target="_blank" class="text-black">Video</a>
                          </li>
                          <li>
                            <a href="https://www.wondershare.com/products-solutions/digital-creativity/photo.html" target="_blank" class="text-black">Photo</a>
                          </li>
                          <li><a href="https://www.wondershare.com/creative-center.html" target="_blank" class="text-black">Creative Center</a></li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </li>
                <li class="wsc-header2020-navbar-item with-toggle">
                  <a class="wsc-header2020-navbar-link active_menu_a" href="javascript:">
                    Diagram & Graphics
                    <svg class="ml-2" width="13" height="9" viewBox="0 0 13 9" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M12 1L6.5 7L1 1" stroke="currentColor" stroke-width="2" />
                    </svg>
                  </a>
                  <div class="wsc-header2020-navbar-nav-toggle diagram-grahics">
                    <div class="row no-gutters px-4">
                      <div class="left">
                        <div class="mb-4 font-size-small">Diagram & Graphics Products</div>
                        <a href="https://edrawmax.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img
                            src="https://neveragain.allstatics.com/2019/assets/icon/logo/edraw-max-square.svg"
                            width="40"
                            height="40"
                            alt="wondershare edrawmax logo" />
                          <div class="pl-2">
                            <strong class="text-black">EdrawMax</strong>
                            <div class="font-size-small">Simple diagramming.</div>
                          </div>
                        </a>
                        <a href="https://edrawmind.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/edraw-mindmaster-square.svg" alt="wondershare EdrawMind logo" />
                          <div class="pl-2">
                            <strong class="text-black">EdrawMind</strong>
                            <div class="font-size-small">Collaborative mind mapping.</div>
                          </div>
                        </a>
                        <a href="https://www.wondershare.com/edraw-project/" target="_blank" class="d-flex align-items-center mb-4">
                          <img
                            src="https://neveragain.allstatics.com/2019/assets/icon/logo/edraw-project-square.svg"
                            width="40"
                            height="40"
                            alt="wondershare edrawproj logo" />
                          <div class="pl-2">
                            <strong class="text-black">EdrawProj</strong>
                            <div class="font-size-small">A professional Gantt chart tool.</div>
                          </div>
                        </a>
                        <!--<a href="https://mockitt.wondershare.com/home.html" target="_blank" class="d-flex align-items-center mb-4">
                      <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/mockitt-square.svg" width="40" height="40" alt="wondershare edrawproj logo" />
                      <div class="pl-2">
                        <strong class="text-black">Mockitt</strong>
                        <div class="font-size-small">Design, prototype & collaborate online.</div>
                      </div>
                    </a>-->
                        <a href="https://www.wondershare.com/shop/individuals.html#graphic" target="_blank" class="vap-btn mt-2">View all products</a>
                      </div>
                    </div>
                  </div>
                </li>
                <li class="wsc-header2020-navbar-item with-toggle">
                  <a class="wsc-header2020-navbar-link active_menu_a" href="javascript:">
                    PDF Solutions
                    <svg class="ml-2" width="13" height="9" viewBox="0 0 13 9" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M12 1L6.5 7L1 1" stroke="currentColor" stroke-width="2" />
                    </svg>
                  </a>
                  <div class="wsc-header2020-navbar-nav-toggle pdf">
                    <div class="row no-gutters px-4">
                      <div class="left">
                        <div class="mb-4 font-size-small">PDF Solutions Products</div>
                        <a href="https://pdf.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/pdfelement-square.svg" alt="wondershare pdfelement logo" />
                          <div class="pl-2">
                            <strong class="text-black">PDFelement</strong>
                            <div class="font-size-small">PDF creation and editing.</div>
                          </div>
                        </a>
                        <a href="https://pdf.wondershare.com/document-cloud/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/document-cloud-square.svg" alt="wondershare document cloud logo" />
                          <div class="pl-2">
                            <strong class="text-black">Document Cloud</strong>
                            <div class="font-size-small">Cloud-based document management.</div>
                          </div>
                        </a>
                        <a href="https://pdf.wondershare.com/pdf-reader.html" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/pdfelement-reader-square.svg" alt="wondershare pdf reader logo" />
                          <div class="pl-2">
                            <strong class="text-black">PDF Reader</strong>
                            <div class="font-size-small">Simple and free PDF reading.</div>
                          </div>
                        </a>
                        <a href="https://www.hipdf.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/hipdf-square.svg" alt="wondershare pdf reader logo" />
                          <div class="pl-2">
                            <strong class="text-black">HiPDF</strong>
                            <div class="font-size-small">Free All-In-One Online PDF Tool.</div>
                          </div>
                        </a>
                        <a href="https://www.wondershare.com/shop/individuals.html#document" target="_blank" class="vap-btn mt-2">View all products</a>
                      </div>
                    </div>
                  </div>
                </li>
                <li class="wsc-header2020-navbar-item with-toggle">
                  <a class="wsc-header2020-navbar-link active_menu_a" href="javascript:">
                    Data Management
                    <svg class="ml-2" width="13" height="9" viewBox="0 0 13 9" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M12 1L6.5 7L1 1" stroke="currentColor" stroke-width="2" />
                    </svg>
                  </a>
                  <div class="wsc-header2020-navbar-nav-toggle utility">
                    <div class="row no-gutters px-4">
                      <div class="left">
                        <div class="mb-4 font-size-small">Data Management Products</div>
                        <a href="https://recoverit.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/recoverit-square.svg" alt="wondershare recoverit logo" />
                          <div class="pl-2">
                            <strong class="text-black">Recoverit</strong>
                            <div class="font-size-small">Lost file recovery.</div>
                          </div>
                        </a>
                        <a href="https://repairit.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/repairit-square.svg" alt="wondershare repairit logo" />
                          <div class="pl-2">
                            <strong class="text-black">Repairit</strong>
                            <div class="font-size-small">Repair broken videos, photos, etc.</div>
                          </div>
                        </a>
                        <a href="https://drfone.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/drfone-square.svg" alt="wondershare drfone logo" />
                          <div class="pl-2">
                            <strong class="text-black">Dr.Fone</strong>
                            <div class="font-size-small">Mobile device management.</div>
                          </div>
                        </a>
                        <a href="https://mobiletrans.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/mobiletrans-square.svg" alt="wondershare mobiletrans logo" />
                          <div class="pl-2">
                            <strong class="text-black">MobileTrans</strong>
                            <div class="font-size-small">Phone to phone transfer.</div>
                          </div>
                        </a>
                        <a href="https://famisafe.wondershare.com/" target="_blank" class="d-flex align-items-center mb-4">
                          <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/famisafe-square.svg" alt="wondershare famisafe logo" />
                          <div class="pl-2">
                            <strong class="text-black">FamiSafe</strong>
                            <div class="font-size-small">Parental control app.</div>
                          </div>
                        </a>
                        <a href="https://www.wondershare.com/shop/individuals.html#utility" target="_blank" class="vap-btn mt-2">View all products</a>
                      </div>
                    </div>
                  </div>
                </li>
                <li class="wsc-header2020-navbar-item with-toggle">
                  <a class="wsc-header2020-navbar-link active_menu_a" href="javascript:">
                    Explore AI
                    <svg class="ml-2" width="13" height="9" viewBox="0 0 13 9" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M12 1L6.5 7L1 1" stroke="currentColor" stroke-width="2" />
                    </svg>
                  </a>
                  <div class="wsc-header2020-navbar-nav-toggle explore-ai">
                    <div class="row no-gutters px-4">
                      <div class="left border-control">
                        <div class="mb-4 font-size-small">AI Solutions</div>
                        <a href="https://www.wondershare.com/ai-solutions/marketing.html" target="_blank" class="d-flex align-items-center mb-4">
                          <strong class="text-black font-size-small">Marketing</strong>
                        </a>
                        <a href="https://www.wondershare.com/ai-solutions/social-media.html" target="_blank" class="d-flex align-items-center mb-4">
                          <strong class="text-black font-size-small">Social Media</strong>
                        </a>
                        <a href="https://www.wondershare.com/ai-solutions/education.html" target="_blank" class="d-flex align-items-center mb-4">
                          <strong class="text-black font-size-small">Education</strong>
                        </a>
                        <a href="https://www.wondershare.com/ai-solutions/business.html" target="_blank" class="d-flex align-items-center">
                          <strong class="text-black font-size-small">Business</strong>
                        </a>
                      </div>
                      <div class="right">
                        <div class="mt-lg-0 my-4 font-size-small">Resources</div>
                        <a href="https://www.wondershare.com/ai.html" target="_blank" class="d-flex align-items-center mb-4">
                          <strong class="text-black font-size-small">AI Tools</strong>
                        </a>
                        <a href="https://www.wondershare.com/ai-newsroom.html" target="_blank" class="d-flex align-items-center mb-4">
                          <strong class="text-black font-size-small">AI Newsroom</strong>
                        </a>
                      </div>
                    </div>
                  </div>
                </li>
                <li class="wsc-header2020-navbar-item">
                  <a class="wsc-header2020-navbar-link active_menu_a" href="https://www.wondershare.com/business/enterprise.html" target="_blank">Business</a>
                </li>
                <li class="wsc-header2020-navbar-item">
                  <a class="wsc-header2020-navbar-link active_menu_a" href="https://www.wondershare.com/shop/individuals.html" target="_blank">Shop</a>
                </li>
                <li class="wsc-header2020-navbar-item">
                  <a class="wsc-header2020-navbar-link active_menu_a" href="https://support.wondershare.com/" target="_blank">Support</a>
                </li>
                <li class="wsc-header2020-navbar-item wondershare-user-panel log-out">
                  <a
                    href="https://famisafe.wondershare.com/main/sign-up"
                    data-source="3"
                    class="wsc-header2020-navbar-linkBtn login-link"
                    style="background-color: #006dff; color: #fff; font-weight: 600; border-radius: 4px"
                    >Sign in</a
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>
      </nav>
      <nav class="wsc-header2020-navbar-main wsc-header2020-navbar-famisafe">
        <div class="wsc-header2020-container">
          <div class="wsc-header2020-navbar-content">
            <div class="wsc-header2020-navbar-brand" style="white-space: nowrap">
              <a
                style="padding-right: 2px"
                href="https://app.adjust.com/1ll0iqxi_1lxesbpf?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up">
                <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/famisafe-square.svg" loading="lazy" alt="famisafe logo" />
              </a>
              <a href="https://famisafe.wondershare.com/" target="_blank">
                <strong style="font-size: 16px; vertical-align: middle">FamiSafe</strong>
              </a>
            </div>
            <a class="wsc-header2020-mobile-button" href="https://app.adjust.com/1f8zmkeb_1fnvm9bh" target="_blank">Try Now</a>
            <button class="wsc-header2020-navbar-collapse-toggle" type="button" aria-expanded="false">
              <svg
                class="wsc-header2020-navbar-collapse-toggle-icon"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="https://www.w3.org/2000/svg">
                <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
              </svg>
              <svg
                class="wsc-header2020-navbar-collapse-toggle-icon-close"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="https://www.w3.org/2000/svg">
                <path d="M6 15L12 9L18 15" stroke="black" stroke-width="1.5" />
              </svg>
            </button>
            <div class="wsc-header2020-navbar-collapse">
              <ul class="wsc-header2020-navbar-nav">
                <!-- Products -->
                <li class="wsc-header2020-navbar-dropdown">
                  <nav class="wsc-header2020-navbarDropdown-toggle" aria-expanded="false">
                    <span>Products</span>
                    <div class="wsc-header2020-dropdown-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                        <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5" />
                      </svg>
                    </div>
                  </nav>
                  <div class="wsc-header2020-navbarDropdown-menu">
                    <div class="wsc-header2020-container">
                      <div class="wsc-header2020-dropdownMenu-content">
                        <div class="wsc-header2020-dropdownMenu-body">
                          <div class="wsc-header2020-dropdownMenuBody-content">
                            <div class="wsc-header2020-dropdownMenuBody-item product-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <div class="product-box">
                                  <div class="product-box-icon">
                                    <img src="https://famisafe.wondershare.com/images/images-2025/header/home-icon.svg" alt="school icon" />
                                  </div>
                                  <div class="product-box-info">
                                    <a href="https://famisafe.wondershare.com/" target="_blank" class="product-box-title">
                                      <div class="title-content">
                                        <span class="content-detail">FamiSafe</span>
                                        <span> <img src="https://famisafe.wondershare.com/images/images-2025/header/fire.svg" alt="fire icon" /> </span>
                                        <span> <img src="https://famisafe.wondershare.com/images/images-2025/header/crown.svg" alt="crown icon" /> </span>
                                      </div>
                                      <span class="right-arrow">
                                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path
                                            d="M1 5H9M9 5L5 1M9 5L5 9"
                                            stroke="currentColor"
                                            stroke-width="1.5"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        </svg>
                                      </span>
                                    </a>
                                    <div class="product-box-content">Safeguard Your Children's Digital Life</div>
                                    <a
                                      href="https://app.adjust.com/1f1c7jcf_1fljm2q7?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                                      class="product-box-btn"
                                      >Try It Free</a
                                    >
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="wsc-header2020-dropdownMenuBody-item product-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <div class="product-box">
                                  <div class="product-box-icon">
                                    <img src="https://famisafe.wondershare.com/images/images-2025/header/school-icon.svg" alt="school icon" />
                                  </div>
                                  <div class="product-box-info">
                                    <a href="https://famisafe.wondershare.com/school.html" target="_blank" class="product-box-title">
                                      <div class="title-content"><span class="content-detail">FamiSafe for School</span></div>
                                      <span class="right-arrow">
                                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path
                                            d="M1 5H9M9 5L5 1M9 5L5 9"
                                            stroke="currentColor"
                                            stroke-width="1.5"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        </svg>
                                      </span>
                                    </a>
                                    <div class="product-box-content">Keep Schools & Parents Connected</div>
                                    <a
                                      href="https://app.adjust.com/1mf4wiip_1mk6hw8s?fallback=https%3A%2F%2Ffamisafeapp.wondershare.com%2Fedu-sign-up.html&redirect_macos=https%3A%2F%2Ffamisafeapp.wondershare.com%2Fedu-sign-up.html"
                                      class="product-box-btn"
                                      >Try It Free</a
                                    >
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div class="wsc-header2020-dropdownMenuBody-item product-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <div class="product-box">
                                  <div class="product-box-icon">
                                    <img src="https://famisafe.wondershare.com/images/images-2025/header/geonection-icon.svg" alt="geonection icon" />
                                  </div>
                                  <div class="product-box-info">
                                    <a href="https://famisafe.wondershare.com/share-live-location.html" target="_blank" class="product-box-title">
                                      <div class="title-content"><span class="content-detail">Geonection</span></div>
                                      <span class="right-arrow">
                                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path
                                            d="M1 5H9M9 5L5 1M9 5L5 9"
                                            stroke="currentColor"
                                            stroke-width="1.5"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        </svg>
                                      </span>
                                    </a>
                                    <div class="product-box-content">Bridge Distance Unite Psychologically</div>
                                    <a
                                      href="https://app.adjust.com/1mabbtf7_1mqiavfq?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                                      class="product-box-btn"
                                      >Try It Free</a
                                    >
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>

                <!-- Features -->
                <li class="wsc-header2020-navbar-dropdown">
                  <nav class="wsc-header2020-navbarDropdown-toggle" aria-expanded="false">
                    <span>Features</span>
                    <div class="wsc-header2020-dropdown-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                        <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5" />
                      </svg>
                    </div>
                  </nav>

                  <div class="wsc-header2020-navbarDropdown-menu">
                    <div class="wsc-header2020-container">
                      <div class="wsc-header2020-dropdownMenu-content">
                        <div class="wsc-header2020-dropdownMenu-body">
                          <div class="wsc-header2020-dropdownMenuBody-content">
                            <div class="wsc-header2020-dropdownMenuBody-item">
                              <div class="wsc-header2020-dropdownMenuBody-box features-box1">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold" style="font-size: 16px">Device Activity</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/call-and-messages.html" target="_blank">Calls & Messages</a
                                    ><span class="new-tip">NEW</span>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/screen-time.html" target="_blank">Screen Time</a><span class="new-tip">NEW</span>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/screen-viewer.html" target="_blank">Screen Viewer</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/app-blocker.html" target="_blank">App Rules</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/one-way-audio.html" target="_blank">One-way Audio</a
                                    ><span class="new-tip">NEW</span>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/phone-activity-report.html" target="_blank">Activity Report </a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                            <div class="wsc-header2020-dropdownMenuBody-item">
                              <div class="wsc-header2020-dropdownMenuBody-box features-box2">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold" style="font-size: 16px">Content Safety</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/youtube-parental-controls.html" target="_blank">YouTube Parental Control </a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/tiktok-history.html" target="_blank">TikTok History</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/detect-suspicious-photos.html" target="_blank">Inappropriate Pictures</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/detect-suspicious-text.html" target="_blank">Social App Detection</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/block-websites.html" target="_blank">Web Filter</a>
                                  </li>
                                  <li>
                                    <a
                                      href="https://famisafe.wondershare.com/parental-control/check-your-childs-online-web-browser-history.html"
                                      target="_blank"
                                      >Browser History</a
                                    >
                                  </li>
                                </ul>
                              </div>
                            </div>
                            <div class="wsc-header2020-dropdownMenuBody-item">
                              <div class="wsc-header2020-dropdownMenuBody-box features-box3">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold subtitle-link align-middle" style="font-size: 16px">Location Service</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list menubody-item">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/kids-location-tracking.html" target="_blank">Location Tracking</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/driving-history-report.html" target="_blank">Driving Report </a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/sos-alert.html" target="_blank">SOS Alert</a><span class="new-tip">NEW</span>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                <!-- Blog -->
                <li class="wsc-header2020-navbar-dropdown">
                  <nav class="wsc-header2020-navbarDropdown-toggle" aria-expanded="false">
                    <span>Blog</span>
                    <div class="wsc-header2020-dropdown-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                        <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5" />
                      </svg>
                    </div>
                  </nav>

                  <div class="wsc-header2020-navbarDropdown-menu">
                    <div class="wsc-header2020-container">
                      <div class="wsc-header2020-dropdownMenu-content">
                        <div class="wsc-header2020-dropdownMenu-body">
                          <div class="wsc-header2020-dropdownMenuBody-content">
                            <div class="wsc-header2020-dropdownMenuBody-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold" style="font-size: 16px">Location Tracker</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/track-location/" target="_blank">Mobile Tracker</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/location-sharing/" target="_blank">Location Sharing</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/family-tracker/" target="_blank">Family Tracker</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/teen-driving-tips/" target="_blank">Teen Driving</a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                            <div class="wsc-header2020-dropdownMenuBody-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold s" style="font-size: 16px">Screen Time</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/screen-time-control/" target="_blank">Screen Time Control</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/ios-parenting/" target="_blank">iOS Parental Control</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/android-parenting/" target="_blank">Android Parental Control</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/desktop-control/" target="_blank">Desktop Parental Control</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/chromebook-guide/" target="_blank">Chromebook Control</a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                            <div class="wsc-header2020-dropdownMenuBody-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold" style="font-size: 16px">App Blocker</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list menubody-item">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/game-blocking/" target="_blank">Block Games</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/youtube-control/" target="_blank">Block YouTube </a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/block-app/" target="_blank">Block Apps</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/block-porn/" target="_blank">Block Porn</a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                            <div class="wsc-header2020-dropdownMenuBody-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold" style="font-size: 16px">Activity Monitor</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list menubody-item">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/internet-filter/" target="_blank">Web Filtering</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/phone-monitoring/" target="_blank">Phone Monitoring</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/detect-sexting/" target="_blank">Teen Sexting</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/anti-bullying/" target="_blank">Anti Bullying</a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                            <div class="wsc-header2020-dropdownMenuBody-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold" style="font-size: 16px">Parenting Knowledge</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list menubody-item">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/parenting-tips/" target="_blank">Parenting Tips</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/learn-slang/" target="_blank">Teen Slang</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/app-review/" target="_blank">Trending App Review</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/parental-app-review/" target="_blank">Parental App Review</a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                          <div class="mt-xl-2 text-center">
                            <a class="wsc-header2020-navbar-linkBtn-outline text-capitalize" href="https://famisafe.wondershare.com/topic/">Read More></a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>

                <!-- Resource -->
                <li class="wsc-header2020-navbar-dropdown">
                  <nav class="wsc-header2020-navbarDropdown-toggle" aria-expanded="false">
                    <span>Resource</span>
                    <div class="wsc-header2020-dropdown-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                        <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5" />
                      </svg>
                    </div>
                  </nav>

                  <div class="wsc-header2020-navbarDropdown-menu">
                    <div class="wsc-header2020-container">
                      <div class="wsc-header2020-dropdownMenu-content">
                        <div class="wsc-header2020-dropdownMenu-body">
                          <div class="wsc-header2020-dropdownMenuBody-content">
                            <div class="wsc-header2020-dropdownMenuBody-item resource-item">
                              <div class="wsc-header2020-dropdownMenuBody-box resource-box">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold" style="font-size: 16px">Featured Topics</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>

                                <ul class="wsc-header2020-dropdownMenuBody-list d-xl-flex justify-content-between">
                                  <div>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/internet-safety-for-kids.html" target="_blank">Digital Child Security</a>
                                    </li>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/screen-time-for-kids.html" target="_blank">Balance Screen Time</a>
                                    </li>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/artificial-intelligent-concerns-activity-win-cash.html" target="_blank"
                                        >AI Concerns Activity</a
                                      >
                                    </li>
                                    <li class="pb-xl-0">
                                      <a href="https://famisafe.wondershare.com/teen-sexting.html" target="_blank">Teen Sexing</a>
                                    </li>
                                  </div>
                                  <div>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/block-porn.html" target="_blank">Block Porns</a>
                                    </li>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/campaigns/stop-sextortion.html" target="_blank">Stop Sextortion</a>
                                    </li>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/no-cyberbullying-on-social-media.html" target="_blank">Stop Cyberbullying</a>
                                    </li>
                                  </div>
                                </ul>
                                <ul class="wsc-header2020-dropdownMenuBody-list pl-5"></ul>
                              </div>
                            </div>

                            <div class="wsc-header2020-dropdownMenuBody-item resource-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold s" style="font-size: 16px">FamiSafe Guide</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list">
                                  <li>
                                    <a href="https://famisafe.wondershare.com/user-guide/" target="_blank">User Guide</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/guide.html" target="_blank">User Guide for School </a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/video.html" target="_blank">Video Guide</a>
                                  </li>
                                  <li>
                                    <a href="https://famisafe.wondershare.com/faq/" target="_blank">User FAQs</a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                            <div class="wsc-header2020-dropdownMenuBody-hr-vertical"></div>
                            <div class="wsc-header2020-dropdownMenuBody-item resource-item">
                              <div class="wsc-header2020-dropdownMenuBody-box">
                                <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                  <h5 class="font-weight-bold" style="font-size: 16px">Explore</h5>
                                  <div class="wsc-header2020-dropdownMenuBodyTitle-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                                      <path d="M6 9L12 15L18 9" stroke="black" stroke-width="1.5"></path>
                                    </svg>
                                  </div>
                                </nav>
                                <ul class="wsc-header2020-dropdownMenuBody-list d-xl-flex flex-wrap justify-content-between">
                                  <div>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/review.html" target="_blank">Parents Review</a>
                                    </li>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/media-reviews.html" target="_blank">Media Review</a>
                                    </li>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/annual-review-and-outlook.html" target="_blank">Annual Report</a>
                                    </li>
                                  </div>
                                  <div>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/campaigns.html" target="_blank">Brand Campaigns</a>
                                    </li>
                                    <li>
                                      <a href="https://famisafe.wondershare.com/partener.html" target="_blank">Become Partner</a>
                                    </li>
                                  </div>
                                  <div class="w-100 text-center d-xl-block d-none">
                                    <a
                                      class="wsc-header2020-navbar-linkBtn-outline text-capitalize w-100 mx-0 d-flex align-items-center justify-content-center"
                                      href="https://famisafe.wondershare.com/download.html"
                                      ><span class="mr-1"
                                        ><svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M3 14.1667V17.5H18V14.1667" stroke="currentcolor" stroke-width="2" />
                                          <path d="M10.5 1.66666V12.5M10.5 12.5L15.5 8.179M10.5 12.5L5.5 8.179" stroke="currentcolor" stroke-width="2" />
                                        </svg>
                                      </span>
                                      Download App</a
                                    >
                                  </div>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>

                <li class="wsc-header2020-navbar-item pc-show">
                  <a
                    class="wsc-header2020-navbar-linkBtn text-capitalize custom-login"
                    href="https://app.adjust.com/1f1c7jcf_1fljm2q7?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&amp;redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%102Fsign-up"
                    >Try It Free</a
                  >
                  <a
                    class="wsc-header2020-navbar-linkBtn-outline text-capitalize custom-register mx-lg-2"
                    href="https://famisafe.wondershare.com/store/family.html"
                    >Pricing</a
                  >
                </li>
                <li class="wsc-header2020-navbar-item pc-show">
                  <a class="wsc-header2020-navbar-link" href="https://famisafe.wondershare.com/search.html">
                    <svg
                      xmlns="https://www.w3.org/2000/svg"
                      viewBox="0 0 32 32"
                      width="20"
                      height="20"
                      fill="none"
                      stroke="currentcolor"
                      stroke-linecap="butt"
                      stroke-linejoin="bevel"
                      stroke-width="2"
                      style="color: #000000">
                      <circle cx="14" cy="14" r="12"></circle>
                      <path d="M23 23 L30 30"></path>
                    </svg>
                  </a>
                </li>
                <li class="wsc-header2020-navbar-item navbar-mobile-pricing">
                  <a href="https://famisafe.wondershare.com/store/family.html" class="wsc-header2020-navbar-link">Pricing</a>
                </li>
                <li class="wsc-header2020-navbar-item navbar-mobile-download">
                  <a href=" https://app.adjust.com/1f8zmkeb_1fnvm9bh" class="wsc-header2020-download-button">Download</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </nav>
    </header>
    <script>
      (function () {
        // PC头部交互改hover
        if (document.body.clientWidth >= 1280) {
          var oHeaderDropdownMenu = document.querySelector(".wsc-header2020-navbar-main").querySelectorAll(".wsc-header2020-navbar-dropdown");
          var oHeaderNavbarDropdown = document.querySelector(".wsc-header2020-navbar-main").querySelectorAll(".wsc-header2020-navbarDropdown-menu");
          var nowIndex = oHeaderDropdownMenu.length;

          oHeaderDropdownMenu.forEach(function (oMenu, index) {
            let currentIndex = index;
            oMenu.addEventListener("mouseenter", function (item) {
              if (currentIndex != nowIndex) {
                if (nowIndex < oHeaderDropdownMenu.length) {
                  oHeaderDropdownMenu[nowIndex].querySelector(".wsc-header2020-navbarDropdown-toggle").setAttribute("aria-expanded", "false");
                  var svgElement = oHeaderDropdownMenu[nowIndex].querySelector(".wsc-header2020-dropdown-icon svg");
                  svgElement.style.rotate = "0deg";
                }
                oHeaderDropdownMenu[currentIndex].querySelector(".wsc-header2020-navbarDropdown-toggle").setAttribute("aria-expanded", "true");
                var svgElementCurrent = oHeaderDropdownMenu[currentIndex].querySelector(".wsc-header2020-dropdown-icon svg");
                svgElementCurrent.style.rotate = "180deg";
                nowIndex = currentIndex;
              }
            });
          });
          oHeaderNavbarDropdown.forEach(function (oNavbar, index) {
            oNavbar.addEventListener("mouseleave", function (item) {
              oHeaderDropdownMenu[nowIndex].querySelector(".wsc-header2020-navbarDropdown-toggle").setAttribute("aria-expanded", "false");
              var svgElementLeave = oHeaderDropdownMenu[nowIndex].querySelector(".wsc-header2020-dropdown-icon svg");
              svgElementLeave.style.rotate = "0deg";
              nowIndex = oHeaderDropdownMenu.length;
            });
          });
        }
      })();
    </script>
    <style>
      [data-target=".fixed-banner"] {
        z-index: -100;
        opacity: 0;
      }
    </style>
    <!-- <a href="https://famisafe.wondershare.com/main/sign-up"><div class="fixed-bottom d-block d-lg-none bg-secondary py-3 text-center text-white font-size-huge font-weight-bold transition bottom-login" style="z-index:-1;opacity:0">Try It Free</div></a> -->
    <a href="https://app.adjust.com/1fq9n6wn_1fzjq905"
      ><div
        class="fixed-bottom d-block d-lg-none bg-secondary py-3 text-center text-white font-size-huge font-weight-bold transition bottom-login"
        style="z-index: 99">
        Try It Free
      </div></a
    >
    <script>
      function throttle(delay, fn) {
        var canRun = true;
        return function () {
          if (!canRun) return;
          canRun = false;
          setTimeout(() => {
            fn.call();
            canRun = true;
          }, delay);
        };
      }
      var bottom_login = document.querySelector(".bottom-login");
      // window.addEventListener('scroll', throttle(1000,function () {
      //     window.scrollY >= window.screen.height ?
      //     (bottom_login.style.zIndex = 10,bottom_login.style.opacity = 1)
      //     : (bottom_login.style.zIndex = -1,bottom_login.style.opacity = 0)
      // }),{passive:true})
    </script>

    <!-- 底部下载引导样式 -->
    <!-- pc端底部下载引导 start -->
    <aside class="footer-float-block position-fixed w-100 position-relative" style="bottom: 0; left: 0; z-index: 8">
      <div class="d-xl-block d-none">
        <a href="https://famisafe.wondershare.com/main/sign-up" target="_blank">
          <img src="https://famisafe.wondershare.com/images/images-2025/famisafe/pc-download-bottom.png" class="img-fluid w-100" alt="pc-download-bottom" />
        </a>
        <svg
          width="32"
          height="32"
          viewBox="0 0 32 32"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="position-absolute with-hand"
          style="left: 30px; top: 50%; transform: translateY(-50%)"
          onclick="document.querySelector('.footer-float-block').style.display = 'none';">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M24.8492 8.84923C25.2397 8.4587 25.2397 7.82554 24.8492 7.43501C24.4587 7.04449 23.8255 7.04449 23.435 7.43501L16.1421 14.7279L8.8492 7.43501C8.45867 7.04449 7.82551 7.04449 7.43498 7.43501C7.04446 7.82554 7.04446 8.4587 7.43498 8.84923L14.7279 16.1421L7.43494 23.4351C7.04441 23.8256 7.04441 24.4587 7.43494 24.8493C7.82546 25.2398 8.45862 25.2398 8.84915 24.8493L16.1421 17.5563L23.435 24.8493C23.8256 25.2398 24.4587 25.2398 24.8492 24.8493C25.2398 24.4587 25.2398 23.8256 24.8492 23.4351L17.5563 16.1421L24.8492 8.84923Z"
            fill="#fff" />
        </svg>
      </div>
    </aside>
    <!-- pc端底部下载引导 end -->
    <main class="wsc-main p-0">
      <section class="part-banner py-md-5 py-4">
        <div class="container my-xl-5 my-lg-3">
          <div class="row mt-md-4">
            <div class="col-xxl-7 col-lg-9">
              <div class="sub-title mb-md-3 mb-2">
                <h2 class="font-weight-normal font-size-extra">Wondershare FamiSafe</h2>
                <span class="colorful-tip">V8.3</span>
              </div>
              <h1 class="mb-md-4 mb-2">
                Next-Gen <br />
                <span class="text-purple"> Parental Control App, </span><br />
                Powered by AI
              </h1>
              <p class="font-size-large text-gray pb-4">
                Empower your family with smart parental controls to manage screen <br class="d-lg-block d-none" />
                time, track locations, and block distracting apps — anytime, anywhere.
              </p>

              <!-- pc -->
              <div class="btn-wrapper justify-content-start pt-md-4 d-md-flex d-none">
                <a href="https://famisafe.wondershare.com/main/sign-up" target="_blank" class="btn btn-colorful btn-lg d-md-flex d-none">Start Free Trail</a>

                <a href="https://famisafe.wondershare.com/store/family.html" target="_blank" class="btn btn-lg btn-outline-secondary">Buy Now </a>
              </div>
              <!-- mobile -->
              <div class="btn-wrapper justify-content-start mt-5 d-md-none d-flex position-relative">
                <canvas
                  id="RobotCanvas"
                  width="100"
                  height="100"
                  class="position-absolute"
                  style="pointer-events: none; top: 0; left: 50%; transform: translate(-50%, -70%); width: 100px"></canvas>
                <a href="https://famisafe.wondershare.com/main/sign-up" target="_blank" class="btn btn-purple-bg btn-lg">Start Free Trail</a>

                <a href="https://famisafe.wondershare.com/store/family.html" target="_blank" class="btn btn-lg btn-outline-secondary">Buy Now </a>
              </div>
              <img
                loading="lazy"
                src="https://famisafe.wondershare.com/images/images-2025/index/banner-mobile-img.png"
                alt="banner-mobile-img"
                class="img-fluid w-100 d-md-none d-block mb-n4" />
              <div class="system-list d-md-flex d-none mt-xl-5 mt-4">
                <span>Available on：</span>
                <a href="https://app.adjust.com/45vgk3w" target="_blank" class="android-link">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M4.99999 15C4.99999 15.4583 5.37499 15.8333 5.83332 15.8333H6.66666V18.75C6.66666 19.4417 7.22499 20 7.91666 20C8.60832 20 9.16666 19.4417 9.16666 18.75V15.8333H10.8333V18.75C10.8333 19.4417 11.3917 20 12.0833 20C12.775 20 13.3333 19.4417 13.3333 18.75V15.8333H14.1667C14.625 15.8333 15 15.4583 15 15V6.66667H4.99999V15ZM2.91666 6.66667C2.22499 6.66667 1.66666 7.225 1.66666 7.91667V13.75C1.66666 14.4417 2.22499 15 2.91666 15C3.60832 15 4.16666 14.4417 4.16666 13.75V7.91667C4.16666 7.225 3.60832 6.66667 2.91666 6.66667ZM17.0833 6.66667C16.3917 6.66667 15.8333 7.225 15.8333 7.91667V13.75C15.8333 14.4417 16.3917 15 17.0833 15C17.775 15 18.3333 14.4417 18.3333 13.75V7.91667C18.3333 7.225 17.775 6.66667 17.0833 6.66667ZM12.9417 1.8L14.025 0.716667C14.1917 0.55 14.1917 0.291667 14.025 0.125C13.8583 -0.0416667 13.6 -0.0416667 13.4333 0.125L12.2 1.35833C11.5181 1.01299 10.7644 0.833133 9.99999 0.833333C9.19999 0.833333 8.44999 1.025 7.78332 1.35833L6.54166 0.125C6.37499 -0.0416667 6.11666 -0.0416667 5.94999 0.125C5.78332 0.291667 5.78332 0.55 5.94999 0.716667L7.04166 1.80833C6.40879 2.2714 5.89407 2.87723 5.53932 3.57659C5.18457 4.27596 4.99979 5.04914 4.99999 5.83333H15C15 4.175 14.1917 2.70833 12.9417 1.8ZM8.33332 4.16667H7.49999V3.33333H8.33332V4.16667ZM12.5 4.16667H11.6667V3.33333H12.5V4.16667Z"
                      fill=" currentColor" />
                  </svg>
                </a>

                <a href="https://app.adjust.com/yh60w5g" target="_blank" class="ios-link">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M12.9167 0.833313H6.24999C5.69746 0.833313 5.16755 1.05281 4.77685 1.44351C4.38615 1.83421 4.16666 2.36411 4.16666 2.91665V17.0833C4.16666 17.6358 4.38615 18.1658 4.77685 18.5565C5.16755 18.9472 5.69746 19.1666 6.24999 19.1666H12.9167C13.4692 19.1666 13.9991 18.9472 14.3898 18.5565C14.7805 18.1658 15 17.6358 15 17.0833V2.91665C15 2.36411 14.7805 1.83421 14.3898 1.44351C13.9991 1.05281 13.4692 0.833313 12.9167 0.833313ZM9.58332 18.3333C8.89166 18.3333 8.33332 17.775 8.33332 17.0833C8.33332 16.3916 8.89166 15.8333 9.58332 15.8333C10.275 15.8333 10.8333 16.3916 10.8333 17.0833C10.8333 17.775 10.275 18.3333 9.58332 18.3333ZM13.3333 15H5.83332V3.33331H13.3333V15Z"
                      fill=" currentColor" />
                  </svg>
                </a>
                <a href="https://download.wondershare.com/famisafe_full7740.exe" target="_blank" class="win-link">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M8.88939 9.46727V1.67522L0 3.01455V9.46727H8.88939ZM10 10.5327V18.4917L20 20V10.5327H10ZM10 9.46727H20V0L10 1.5062V9.46727ZM8.88939 10.5327H0V16.9855L8.88939 18.3248V10.5327Z"
                      fill=" currentColor" />
                  </svg>
                </a>
                <a href="https://download.wondershare.com/famisafe_full7739.pkg" target="_blank" class="mac-link">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M11.0071 6.63466V6.46941L10.4958 6.50514C10.3506 6.51407 10.2412 6.54533 10.1675 6.5967C10.0938 6.65029 10.0581 6.72175 10.0581 6.81554C10.0581 6.9071 10.0938 6.97856 10.1675 7.03216C10.2389 7.08575 10.3372 7.11255 10.4578 7.11255C10.536 7.11255 10.6074 7.10139 10.6744 7.07682C10.7414 7.05226 10.7995 7.02099 10.8486 6.97856C10.8977 6.93613 10.9357 6.887 10.9647 6.82894C10.9937 6.76865 11.0071 6.70389 11.0071 6.63466ZM10 0C4.41938 0 0 4.41938 0 10C0 15.5806 4.41938 20 10 20C15.5806 20 20 15.5806 20 10C20 4.41938 15.5806 0 10 0ZM11.9406 5.92675C11.9875 5.795 12.0545 5.68111 12.1416 5.58508C12.2287 5.49129 12.3314 5.4176 12.452 5.36623C12.5726 5.31487 12.7088 5.29031 12.8584 5.29031C12.9924 5.29031 13.1152 5.31041 13.2224 5.3506C13.3318 5.3908 13.4234 5.4444 13.5016 5.51362C13.5797 5.58062 13.64 5.66101 13.6869 5.75034C13.7316 5.84189 13.7584 5.93792 13.7673 6.03841H13.3653C13.3542 5.98258 13.3363 5.93122 13.3117 5.88432C13.2849 5.83743 13.2515 5.795 13.209 5.75927C13.1666 5.72354 13.1152 5.69674 13.0594 5.67664C13.0013 5.65654 12.9366 5.64761 12.8651 5.64761C12.7803 5.64761 12.7021 5.66548 12.6329 5.69897C12.5636 5.73247 12.5034 5.7816 12.4542 5.84413C12.4051 5.90665 12.3671 5.98258 12.3403 6.07414C12.3135 6.16347 12.3001 6.26396 12.3001 6.37338C12.3001 6.48727 12.3135 6.58999 12.3403 6.67932C12.3671 6.76865 12.4051 6.84457 12.4565 6.9071C12.5056 6.96963 12.5659 7.01653 12.6373 7.05002C12.7066 7.08352 12.7847 7.09915 12.8674 7.09915C13.0058 7.09915 13.1175 7.06789 13.2023 7.00313C13.2894 6.93836 13.3452 6.84457 13.372 6.71952H13.774C13.7628 6.82894 13.7316 6.92943 13.6824 7.02099C13.6333 7.11255 13.5686 7.18848 13.4926 7.25324C13.4145 7.318 13.3229 7.36713 13.2157 7.40063C13.1108 7.43636 12.9924 7.45422 12.8674 7.45422C12.7177 7.45422 12.5815 7.42966 12.4587 7.38053C12.3381 7.3314 12.2331 7.25994 12.146 7.16615C12.059 7.07235 11.992 6.9607 11.9451 6.82671C11.8982 6.69272 11.8736 6.5431 11.8736 6.37561C11.8714 6.20813 11.8937 6.06074 11.9406 5.92675ZM6.22823 5.31934H6.63019V5.67664H6.63689C6.66146 5.61635 6.69272 5.56275 6.73292 5.51809C6.77311 5.47119 6.81778 5.431 6.86914 5.3975C6.9205 5.364 6.97856 5.33944 7.03886 5.32157C7.10138 5.30371 7.16615 5.29477 7.23537 5.29477C7.38276 5.29477 7.50782 5.3305 7.60831 5.39973C7.71103 5.47119 7.78249 5.57168 7.82492 5.70344H7.83609C7.86289 5.64091 7.89862 5.58285 7.94328 5.53372C7.98794 5.48459 8.03707 5.43993 8.09513 5.4042C8.15096 5.36847 8.21572 5.34167 8.28272 5.32381C8.34971 5.30594 8.42117 5.29477 8.4971 5.29477C8.59982 5.29477 8.69361 5.31041 8.77847 5.3439C8.86333 5.3774 8.93479 5.4243 8.99509 5.48459C9.05538 5.54489 9.10228 5.61858 9.13354 5.70344C9.16481 5.7883 9.18267 5.88432 9.18267 5.98928V7.42519H8.76284V6.09201C8.76284 5.95355 8.72711 5.84636 8.65565 5.77043C8.58419 5.69451 8.4837 5.65654 8.35194 5.65654C8.28718 5.65654 8.22912 5.66771 8.17329 5.69004C8.1197 5.71237 8.0728 5.74364 8.03484 5.78606C7.99687 5.82626 7.96561 5.87539 7.94328 5.93345C7.92095 5.98928 7.90978 6.05181 7.90978 6.12104V7.42966H7.49888V6.05851C7.49888 5.99821 7.48995 5.94239 7.46985 5.89326C7.45199 5.84413 7.42519 5.8017 7.38946 5.76597C7.35373 5.73024 7.3113 5.70344 7.26217 5.68557C7.21304 5.66771 7.15721 5.65654 7.09469 5.65654C7.02992 5.65654 6.96963 5.66771 6.9138 5.69227C6.85797 5.71684 6.81108 5.75034 6.77311 5.79277C6.73292 5.8352 6.70165 5.88656 6.68155 5.94462C6.66146 6.00268 6.62573 6.06521 6.62573 6.13444V7.42742H6.22823V5.31934ZM7.14158 15.4109C5.29701 15.4109 4.14247 14.1291 4.14247 12.0858C4.14247 10.0424 5.29701 8.75614 7.14158 8.75614C8.98615 8.75614 10.1362 10.0424 10.1362 12.0858C10.1362 14.1291 8.98392 15.4109 7.14158 15.4109ZM10.5538 7.42296C10.4868 7.44082 10.4154 7.44975 10.3461 7.44975C10.2412 7.44975 10.1452 7.43412 10.0581 7.40509C9.97097 7.37606 9.89504 7.33363 9.83251 7.2778C9.76999 7.22421 9.72086 7.15721 9.68513 7.08129C9.6494 7.00313 9.63153 6.91827 9.63153 6.82447C9.63153 6.63912 9.70076 6.49397 9.83921 6.39125C9.97767 6.28629 10.1764 6.22599 10.4377 6.21036L11.0071 6.17686V6.01384C11.0071 5.89326 10.9692 5.79946 10.891 5.73694C10.8129 5.67441 10.7057 5.64091 10.5628 5.64091C10.5069 5.64091 10.4533 5.64761 10.4042 5.66101C10.3551 5.67441 10.3126 5.69674 10.2747 5.7213C10.2367 5.7481 10.2054 5.77937 10.1809 5.81733C10.1563 5.85306 10.1385 5.89549 10.1295 5.94015H9.73426C9.73649 5.84636 9.75882 5.7615 9.80348 5.68111C9.84815 5.60071 9.90621 5.53372 9.9799 5.47566C10.0536 5.4176 10.1429 5.37293 10.2434 5.33944C10.3439 5.30817 10.4556 5.29031 10.5739 5.29031C10.7012 5.29031 10.8173 5.30594 10.9223 5.33944C11.0272 5.37293 11.1166 5.4176 11.1903 5.47789C11.264 5.53819 11.3198 5.60965 11.36 5.69451C11.4002 5.77937 11.4203 5.87539 11.4203 5.97812V7.42519H11.0183V7.07459H11.0071C10.9781 7.13041 10.9402 7.18401 10.8933 7.22867C10.8464 7.27557 10.795 7.31353 10.7392 7.34703C10.6856 7.38053 10.623 7.40509 10.5538 7.42296ZM13.2068 15.4109C11.7999 15.4109 10.8441 14.674 10.7794 13.5172H11.695C11.7664 14.1782 12.4051 14.6159 13.2805 14.6159C14.1201 14.6159 14.7231 14.1782 14.7231 13.5797C14.7231 13.0616 14.3569 12.749 13.5083 12.5346L12.682 12.3292C11.494 12.0344 10.9536 11.494 10.9536 10.6052C10.9536 9.51094 11.9093 8.75168 13.2693 8.75168C14.6003 8.75168 15.527 9.51541 15.5628 10.6141H14.6561C14.5936 9.9531 14.0531 9.54667 13.2492 9.54667C12.4498 9.54667 11.8959 9.95757 11.8959 10.5516C11.8959 11.0205 12.2443 11.2975 13.0929 11.5118L13.7896 11.686C15.1161 12.0076 15.661 12.5301 15.661 13.4681C15.661 14.665 14.7164 15.4109 13.2068 15.4109ZM7.14158 9.5757C5.87762 9.5757 5.08933 10.5449 5.08933 12.0835C5.08933 13.6199 5.87986 14.5869 7.14158 14.5869C8.40107 14.5869 9.19384 13.6177 9.19384 12.0835C9.19384 10.5449 8.39884 9.5757 7.14158 9.5757Z"
                      fill="currentColor" />
                  </svg>
                </a>
                <a href="https://chromewebstore.google.com/detail/famisafe/kdcadgckcdgebkmhedjkebadbefeeona" target="_blank" class="chrome-link">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M7.83334 8.74998L11.8083 1.86665C10.5792 1.59215 9.30375 1.60028 8.07819 1.89043C6.85263 2.18058 5.70894 2.74517 4.73334 3.54165L7.78334 8.83331L7.83334 8.74998ZM17.95 7.49998C17.1833 5.06665 15.325 3.11665 12.95 2.21665L9.9 7.49998H17.95ZM18.1667 8.33331H11.925L12.1667 8.74998L16.1333 15.625C17.5506 14.095 18.3366 12.0855 18.3333 9.99998C18.3333 9.42498 18.275 8.87498 18.1667 8.33331ZM7.11667 9.99998L3.86667 4.37498C2.97375 5.34202 2.32368 6.5076 1.97013 7.77546C1.61658 9.04331 1.56969 10.3771 1.83334 11.6666H8.075L7.11667 9.99998ZM2.05 12.5C2.81667 14.9333 4.675 16.8833 7.05 17.7833L10.1 12.5H2.05ZM11.4417 12.5L8.19167 18.1333C9.42083 18.4078 10.6963 18.3997 11.9218 18.1095C13.1474 17.8194 14.2911 17.2548 15.2667 16.4583L12.2167 11.1666L11.4417 12.5Z"
                      fill="currentColor" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="part-honour py-xl-3">
        <div class="py-4">
          <div class="honour-box text-center overflow-hidden">
            <div class="honour-list">
              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-1.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">The National Parenting Center</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">SEAL OF APPROVAL</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" width="59" src="https://famisafe.wondershare.com/frontpage/icon-honour-2.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">2024 National Parenting Product Awards</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">WINNER</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-3.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">Mom's Choice Awards</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">HONORING EXCELLENCE</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-4.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">The National Parenting Center</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">WINNER</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-5.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">Made For Mums Awards 2021</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">BRONZE</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-6.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">Mom's Choice Awards</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">HONORING EXCELLENCE</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" width="59" src="https://famisafe.wondershare.com/frontpage/icon-honour-7.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">Parents’ Picks Awards 2024</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">WINNER</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-1.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">The National Parenting Center</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">SEAL OF APPROVAL</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" width="59" src="https://famisafe.wondershare.com/frontpage/icon-honour-2.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">2024 National Parenting Product Awards</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">WINNER</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-3.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">Mom's Choice Awards</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">HONORING EXCELLENCE</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-4.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">The National Parenting Center</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">WINNER</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-5.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">Made For Mums Awards 2021</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">BRONZE</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" src="https://famisafe.wondershare.com/frontpage/icon-honour-6.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">Mom's choice awards</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">HONORING EXCELLENCE</p>
                </div>
              </div>

              <div class="honour-item">
                <div class="honour-logo">
                  <img loading="lazy" class="img-fluid" width="59" src="https://famisafe.wondershare.com/frontpage/icon-honour-7.png" alt="honour-1" />
                </div>
                <div class="align-middle honour-intro">
                  <p class="font-size-small font-weight-bold my-2">Parents’ Picks Awards 2024</p>
                  <p class="font-size-small font-weight-bold mb-0" style="color: #8c5bde">WINNER</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="part-safeguard py-5 overflow-hidden">
        <div class="container my-xl-5 my-lg-3">
          <h2 class="mb-4">See, Guide, and Safeguard Your Kids</h2>
          <p class="text-center mb-3">Leave digital parenting challenges behind—enjoy true peace of mind, anytime, anywhere.</p>
          <!-- pc code -->
          <div class="d-xl-block d-none">
            <nav class="nav mt-xl-5 mt-4" role="tablist">
              <span
                class="nav-item active"
                id="#nav-watch-device-use"
                data-toggle="tab"
                data-target="#nav-watch-device-use"
                role="tab"
                aria-controls="nav-watch-device-use"
                aria-selected="true"
                ><span>Watch Device Use</span></span
              >
              <span
                class="nav-item"
                id="nav-guide-online-content-tab"
                data-toggle="tab"
                data-target="#nav-guide-online-content"
                role="tab"
                aria-controls="nav-guide-online-content"
                aria-selected="false"
                ><span>Guide Online Content</span></span
              >
              <span
                class="nav-item"
                id="nav-protect-in-real-life-tab"
                data-toggle="tab"
                data-target="#nav-protect-in-real-life"
                role="tab"
                aria-controls="nav-protect-in-real-life"
                aria-selected="false"
                ><span>Protect in Real Life</span></span
              >
            </nav>
            <div class="tab-content mt-4">
              <div class="tab-pane fade show active" id="nav-watch-device-use" role="tabpanel" aria-labelledby="nav-watch-device-use-tab">
                <div class="safeguard-box safeguard-box-1">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/watch-device-use.png"
                    alt="watch-device-use"
                    class="img-fluid w-100" />
                  <div class="feature-card feature-card-1" style="left: 17%; bottom: 30%; --color-title: #5638ff">
                    <div class="feature-card-icon">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/check-daily-reports.png"
                        alt="check-daily-reports"
                        class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Check Daily Reports</div>
                      <p class="feature-card-description">
                        How much screen time did they really have? Get the full picture of your child's digital day in one simple report.
                      </p>
                    </div>
                  </div>
                  <div class="feature-card feature-card-2" style="left: 62%; bottom: 11%; --color-title: #00a591">
                    <div class="feature-card-icon">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/monitor-calls-chats.png"
                        alt="monitor-calls-chats"
                        class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Monitor Calls & Chats</div>
                      <p class="feature-card-description">
                        Worried about who your child is talking to? Stay informed about their communications while respecting their privacy.
                      </p>
                    </div>
                  </div>
                  <div class="feature-card feature-card-3" style="left: 70%; bottom: 48%; --color-title: #0795ed">
                    <div class="feature-card-icon">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/capture-live-screen.png"
                        alt="capture-live-screen"
                        class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Capture Live Screen</div>
                      <p class="feature-card-description">
                        Need to see what's happening right now? Get real-time visibility into your child's device activity.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="tab-pane fade" id="nav-guide-online-content" role="tabpanel" aria-labelledby="nav-guide-online-content-tab">
                <div class="safeguard-box safeguard-box-2">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/guide-online-content.png"
                    alt="guide-online-content"
                    class="img-fluid w-100" />
                  <div class="feature-card feature-card-1" style="left: 18%; bottom: 58%; --color-title: #00a591">
                    <div class="feature-card-icon" style="left: auto; right: 0%; transform: translate(41%, -39%)">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/filter-sites.png"
                        alt="filter-sites"
                        class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Filter Sites</div>
                      <p class="feature-card-description">
                        Concerned about inappropriate content online? Automatically block harmful websites before they reach your child.
                      </p>
                    </div>
                  </div>
                  <div class="feature-card feature-card-2" style="left: 23%; bottom: 8%; --color-title: #5638ff">
                    <div class="feature-card-icon" style="transform: translate(-51%, -45%)">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/block-apps.png" alt="block-apps" class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Block Apps</div>
                      <p class="feature-card-description">
                        Gaming taking over homework time? Set healthy boundaries with smart app management that teaches balance.
                      </p>
                    </div>
                  </div>
                  <div class="feature-card feature-card-3" style="left: 68%; bottom: 23%; --color-title: #0795ed">
                    <div class="feature-card-icon" style="left: auto; right: 0%; transform: translate(20%, -35%)">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/detect-risks.png"
                        alt="detect-risks"
                        class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Detect Risks</div>
                      <p class="feature-card-description">
                        Worried about what videos they're watching? AI-powered detection alerts you to potentially risky content.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="tab-pane fade" id="nav-protect-in-real-life" role="tabpanel" aria-labelledby="nav-protect-in-real-life-tab">
                <div class="safeguard-box safeguard-box-3">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/protect-in-real-life.png"
                    alt="protect-in-real-life"
                    class="img-fluid w-100" />
                  <div class="feature-card feature-card-1" style="left: 6%; bottom: 35%; --color-title: #00a591">
                    <div class="feature-card-icon" style="left: auto; right: 0%; transform: translate(41%, -39%)">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/track-live-location.png"
                        alt="track-live-location"
                        class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Track Live Location</div>
                      <p class="feature-card-description">Running late from soccer practice? Know where your child is and when they'll be home safely.</p>
                    </div>
                  </div>
                  <div class="feature-card feature-card-2" style="left: 69%; bottom: 12%; --color-title: #5638ff">
                    <div class="feature-card-icon" style="transform: translate(-51%, -45%)">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/ensure-driving-safety.png"
                        alt="ensure-driving-safety"
                        class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Ensure Driving Safety</div>
                      <p class="feature-card-description">
                        New driver in the family? Monitor driving habits and get reports to help them stay safe on the road.
                      </p>
                    </div>
                  </div>
                  <div class="feature-card feature-card-3" style="left: 66%; bottom: 57%; --color-title: #0795ed">
                    <div class="feature-card-icon">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/receive-sos-alerts.png"
                        alt="receive-sos-alerts"
                        class="img-fluid" />
                    </div>
                    <div class="feature-card-content">
                      <div class="feature-card-title">Receive SOS alerts</div>
                      <p class="feature-card-description">
                        What if they need help and can't call? Emergency alerts ensure you're the first to know when they need you.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- mobile code -->
          <div class="d-xl-none d-block">
            <div class="swiper pt-4 pb-5 position-relative" id="safeguard-mobile-swiper">
              <div class="swiper-wrapper">
                <div class="swiper-slide">
                  <div class="safeguard-box-mobile">
                    <img
                      loading="lazy"
                      src="https://famisafe.wondershare.com/images/images-2025/index/watch-device-use-mobile.jpg"
                      alt="watch-device-use"
                      class="img-fluid w-100" />
                    <div class="title">Watch Device Use</div>
                    <div class="feature-list">
                      <div class="feature-list-wrapper">
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Check Daily Reports</div>
                        </div>
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Monitor Calls & Chats</div>
                        </div>
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Capture Live Screen</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="swiper-slide">
                  <div class="safeguard-box-mobile">
                    <img
                      loading="lazy"
                      src="https://famisafe.wondershare.com/images/images-2025/index/guide-online-content-mobile.jpg"
                      alt="guide-online-content-mobile"
                      class="img-fluid w-100" />
                    <div class="title">Guide Online Content</div>
                    <div class="feature-list">
                      <div class="feature-list-wrapper">
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Filter Sites</div>
                        </div>
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Block Apps</div>
                        </div>
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Detect Risks</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="swiper-slide">
                  <div class="safeguard-box-mobile">
                    <img
                      loading="lazy"
                      src="https://famisafe.wondershare.com/images/images-2025/index/protect-in-real-life-mobile.jpg"
                      alt="protect-in-real-life-mobile"
                      class="img-fluid w-100" />
                    <div class="title">Protect in Real Life</div>
                    <div class="feature-list">
                      <div class="feature-list-wrapper">
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Track Live Location</div>
                        </div>
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Receive SOS alerts</div>
                        </div>
                        <div class="feature-item">
                          <img
                            loading="lazy"
                            src="https://famisafe.wondershare.com/images/images-2025/index/right-icon.svg"
                            alt="right-icon"
                            class="img-fluid" />
                          <div>Ensure Driving Safety</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-pagination"></div>
              <div class="left-btn d-md-none">
                <svg width="9" height="14" viewBox="0 0 9 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 1L1 6.57143L8 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </div>
              <div class="right-btn d-md-none">
                <svg width="9" height="14" viewBox="0 0 9 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 1L8 6.57143L1 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="part-digital py-5">
        <div class="digital-wrapper">
          <div class="container h-100 overflow-hidden">
            <!-- pc title -->
            <h2 class="text-lg-left d-xl-block d-none">
              <div class="mb-2">Family's #1 Digital</div>
              <div>Parentaing Solution</div>
            </h2>
            <!-- mobile title -->
            <h3 class="family-title d-xl-none d-inline-block">Family's #1</h3>
            <h2 class="d-xl-none d-block">Digital Parentaing Solution</h2>
            <div class="digital-box">
              <div class="text-content">
                <div class="swiper h-100 pb-xl-0 pb-4" id="digital-text-swiper">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide h-auto">
                      <div class="mobile-img-wrapper d-xl-none d-block">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/screen-time-mobile.jpg"
                          alt="screen-time"
                          class="img-fluid w-100" />
                      </div>
                      <div class="digital-item">
                        <a href="https://famisafe.wondershare.com/screen-time.html" target="_blank" class="digital-item-link d-xl-none"> </a>
                        <div class="digital-item-title">Screen Time</div>
                        <div class="digital-item-description">Set daily screen time limits and schedules to promote healthy digital habits.</div>
                        <div class="digital-item-arrow d-xl-none d-flex">
                          <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                              stroke="#7A57EE"
                              stroke-width="2.04665"
                              stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>
                        </div>
                        <div class="btn-wrapper justify-content-start mt-5 d-xl-flex d-none">
                          <a
                            href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                            target="_blank"
                            class="btn btn-lg btn-purple-bg"
                            >Start Free Trail</a
                          >
                          <a href="https://famisafe.wondershare.com/screen-time.html" target="_blank" class="btn btn-lg btn-outline-secondary">Learn More</a>
                        </div>
                      </div>
                    </div>
                    <div class="swiper-slide h-auto">
                      <div class="mobile-img-wrapper d-xl-none d-block">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/app-rules-mobile.jpg"
                          alt="app-rules"
                          class="img-fluid w-100" />
                      </div>
                      <div class="digital-item">
                        <a href="https://famisafe.wondershare.com/app-blocker.html" target="_blank" class="digital-item-link d-xl-none"> </a>
                        <div class="digital-item-title">App Rules</div>
                        <div class="digital-item-description">Block or allow specific apps to prevent distractions and ensure appropriate usage.</div>
                        <div class="digital-item-arrow d-xl-none d-flex">
                          <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                              stroke="#7A57EE"
                              stroke-width="2.04665"
                              stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>
                        </div>
                        <div class="btn-wrapper justify-content-start mt-5 d-xl-flex d-none">
                          <a
                            href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                            target="_blank"
                            class="btn btn-lg btn-purple-bg"
                            >Start Free Trail</a
                          >
                          <a href="https://famisafe.wondershare.com/app-blocker.html" target="_blank" class="btn btn-lg btn-outline-secondary">Learn More</a>
                        </div>
                      </div>
                    </div>
                    <div class="swiper-slide h-auto">
                      <div class="mobile-img-wrapper d-xl-none d-block">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/screen-viewer-mobile.jpg"
                          alt="screen-viewer"
                          class="img-fluid w-100" />
                      </div>
                      <div class="digital-item">
                        <a href="https://famisafe.wondershare.com/screen-viewer.html" target="_blank" class="digital-item-link d-xl-none"> </a>
                        <div class="digital-item-title">Screen Viewer</div>
                        <div class="digital-item-description">View your child's device screen in real-time to monitor their activity.</div>
                        <div class="digital-item-arrow d-xl-none d-flex">
                          <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                              stroke="#7A57EE"
                              stroke-width="2.04665"
                              stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>
                        </div>
                        <div class="btn-wrapper justify-content-start mt-5 d-xl-flex d-none">
                          <a
                            href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                            target="_blank"
                            class="btn btn-lg btn-purple-bg"
                            >Start Free Trail</a
                          >
                          <a href="https://famisafe.wondershare.com/screen-viewer.html" target="_blank" class="btn btn-lg btn-outline-secondary">Learn More</a>
                        </div>
                      </div>
                    </div>
                    <div class="swiper-slide h-auto">
                      <div class="mobile-img-wrapper d-xl-none d-block">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/one-way-audio-mobile.jpg"
                          alt="one-way-audio"
                          class="img-fluid w-100" />
                      </div>
                      <div class="digital-item">
                        <a href="https://famisafe.wondershare.com/one-way-audio.html" target="_blank" class="digital-item-link d-xl-none"> </a>
                        <div class="digital-item-title">One-way Audio</div>
                        <div class="digital-item-description">Listen to the surroundings of your child's device to ensure their safety.</div>
                        <div class="digital-item-arrow d-xl-none d-flex">
                          <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                              stroke="#7A57EE"
                              stroke-width="2.04665"
                              stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>
                        </div>
                        <div class="btn-wrapper justify-content-start mt-5 d-xl-flex d-none">
                          <a
                            href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                            target="_blank"
                            class="btn btn-lg btn-purple-bg"
                            >Start Free Trail</a
                          >
                          <a href="https://famisafe.wondershare.com/one-way-audio.html" target="_blank" class="btn btn-lg btn-outline-secondary">Learn More</a>
                        </div>
                      </div>
                    </div>
                    <div class="swiper-slide h-auto">
                      <div class="mobile-img-wrapper d-xl-none d-block">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/location-tracking-mobile.jpg"
                          alt="location-tracking"
                          class="img-fluid w-100" />
                      </div>
                      <div class="digital-item">
                        <a href="https://famisafe.wondershare.com/kids-location-tracking.html" target="_blank" class="digital-item-link d-xl-none"> </a>
                        <div class="digital-item-title">Location Tracking</div>
                        <div class="digital-item-description">Track your child’s location in real time, check history, and get alerts for safe zones.</div>
                        <div class="digital-item-arrow d-xl-none d-flex">
                          <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                              stroke="#7A57EE"
                              stroke-width="2.04665"
                              stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>
                        </div>
                        <div class="btn-wrapper justify-content-start mt-5 d-xl-flex d-none">
                          <a
                            href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                            target="_blank"
                            class="btn btn-lg btn-purple-bg"
                            >Start Free Trail</a
                          >
                          <a href="https://famisafe.wondershare.com/kids-location-tracking.html" target="_blank" class="btn btn-lg btn-outline-secondary"
                            >Learn More</a
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="swiper-pagination"></div>
                </div>
              </div>
              <!-- pc端图片 -->
              <div class="img-content d-xl-block d-none">
                <div class="swiper h-100" id="digital-img-swiper">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide h-auto">
                      <div class="img-item-wrapper">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/screen-time.png"
                          alt="screen-time"
                          class="img-fluid w-100" />
                      </div>
                    </div>
                    <div class="swiper-slide h-auto">
                      <div class="img-item-wrapper">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/app-rules.png"
                          alt="app-rules"
                          class="img-fluid w-100" />
                      </div>
                    </div>
                    <div class="swiper-slide h-auto">
                      <div class="img-item-wrapper">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/screen-viewer.png"
                          alt="screen-viewer"
                          class="img-fluid w-100" />
                      </div>
                    </div>
                    <div class="swiper-slide h-auto">
                      <div class="img-item-wrapper">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/one-way-audio.png"
                          alt="one-way-audio"
                          class="img-fluid w-100" />
                      </div>
                    </div>
                    <div class="swiper-slide h-auto">
                      <div class="img-item-wrapper">
                        <img
                          loading="lazy"
                          src="https://famisafe.wondershare.com/images/images-2025/index/location-tracking.png"
                          alt="location-tracking"
                          class="img-fluid w-100" />
                      </div>
                    </div>
                  </div>
                  <div class="swiper-pagination"></div>
                </div>
              </div>
            </div>
            <!-- mobile btns -->
            <div class="btn-wrapper mt-3 d-xl-none d">
              <a
                href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                target="_blank"
                class="btn btn-purple-bg btn-lg"
                >Try It Free</a
              >
            </div>
          </div>
        </div>
      </section>
      <section class="part-feature pb-5 pt-sm-0 pt-4">
        <div class="container mb-xl-5 mb-lg-3">
          <h2>More Parental Control Features</h2>
          <!-- pc -->
          <div class="swiper py-5" id="feature-swiper">
            <div class="swiper-wrapper my-xl-2">
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/youtube-parental-control.png"
                    alt="youtube-parental-control"
                    class="img-fluid w-100" />
                  <div class="feature-item-title">YouTube Parental Control</div>
                  <a href="https://famisafe.wondershare.com/youtube-parental-controls.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">YouTube Parental Control</div>
                    <div class="feature-detail-card-description">Monitor and manage your child's YouTube watch history and block inappropriate content.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/social-app-detection.png"
                    alt="social-app-detection"
                    class="img-fluid w-100" />
                  <div class="feature-item-title">Social App Detection</div>
                  <a href="https://famisafe.wondershare.com/detect-suspicious-text.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">Social App Detection</div>
                    <div class="feature-detail-card-description">Monitor 14+ social platforms and customize the suspicious words at once.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/calls-messages.png"
                    alt="calls-messages"
                    class="img-fluid w-100" />
                  <div class="feature-item-title">Calls & Messages</div>
                  <a href="https://famisafe.wondershare.com/call-and-messages.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">Calls & Messages</div>
                    <div class="feature-detail-card-description">Monitor call logs and text messages to detect potential risks or inappropriate content.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/activity-report.png"
                    alt="activity-report"
                    class="img-fluid w-100" />
                  <div class="feature-item-title">Activity Report</div>
                  <a href="https://famisafe.wondershare.com/phone-activity-report.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">Activity Report</div>
                    <div class="feature-detail-card-description">Receive detailed reports on your child's device usage and app activities.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/tiktok-history.png"
                    alt="tiktok-history"
                    class="img-fluid w-100" />
                  <div class="feature-item-title">TikTok History</div>
                  <a href="https://famisafe.wondershare.com/tiktok-history.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">TikTok History</div>
                    <div class="feature-detail-card-description">Track your child's TikTok viewing history to ensure age-appropriate content.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/inappropriate-pictures.png"
                    alt="inappropriate-pictures"
                    class="img-fluid w-100" />
                  <div class="feature-item-title">Inappropriate Pictures</div>
                  <a href="https://famisafe.wondershare.com/detect-suspicious-photos.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">Inappropriate Pictures</div>
                    <div class="feature-detail-card-description">Detect and alert you to explicit images on your child's device.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/web-filter.png" alt="web-filter" class="img-fluid w-100" />
                  <div class="feature-item-title">Web Filter</div>
                  <a href="https://famisafe.wondershare.com/block-websites.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">Web Filter</div>
                    <div class="feature-detail-card-description">Block access to inappropriate websites to ensure safe browsing.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/driving-report.png"
                    alt="driving-report"
                    class="img-fluid w-100" />
                  <div class="feature-item-title">Driving Report</div>
                  <a href="https://famisafe.wondershare.com/driving-history-report.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">Driving Report</div>
                    <div class="feature-detail-card-description">Monitor your teen's driving habits, including speed and driving routes.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
              <div class="swiper-slide h-auto">
                <div class="feature-item">
                  <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/sos-alert.png" alt="sos-alert" class="img-fluid w-100" />
                  <div class="feature-item-title">SOS Alert</div>
                  <a href="https://famisafe.wondershare.com/sos-alert.html" target="_blank" class="feature-detail-card">
                    <div class="feature-detail-card-title">SOS Alert</div>
                    <div class="feature-detail-card-description">Allow your child to send instant emergency alerts with their location.</div>
                    <div class="feature-detail-card-arrow">
                      <svg width="13" height="21" viewBox="0 0 13 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M1.84851 1.53052L11.0584 10.7404L1.84851 19.9503"
                          stroke="currentColor"
                          stroke-width="2.04665"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                    </div>
                  </a>
                </div>
              </div>
            </div>
            <div class="swiper-pagination" style="bottom: 0"></div>
          </div>
          <!-- mobile 文案 -->
          <div class="mobile-feature-text d-sm-none d-block">
            <div class="swiper" id="feature-text-mobile-swiper">
              <div class="swiper-wrapper">
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/youtube-parental-controls.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">YouTube Parental Control</div>
                      <div class="feature-detail-card-description">Monitor and manage your child's YouTube watch history and block inappropriate content.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/detect-suspicious-text.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">Social App Detection</div>
                      <div class="feature-detail-card-description">Monitor 14+ social platforms and customize the suspicious words at once.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/call-and-messages.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">Calls & Messages</div>
                      <div class="feature-detail-card-description">Monitor call logs and text messages to detect potential risks or inappropriate content.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/phone-activity-report.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">Activity Report</div>
                      <div class="feature-detail-card-description">Receive detailed reports on your child's device usage and app activities.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/tiktok-history.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">TikTok History</div>
                      <div class="feature-detail-card-description">Track your child's TikTok viewing history to ensure age-appropriate content.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/detect-suspicious-photos.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">Inappropriate Pictures</div>
                      <div class="feature-detail-card-description">Detect and alert you to explicit images on your child's device.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/block-websites.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">Web Filter</div>
                      <div class="feature-detail-card-description">Block access to inappropriate websites to ensure safe browsing.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/driving-history-report.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">Driving Report</div>
                      <div class="feature-detail-card-description">Monitor your teen's driving habits, including speed and driving routes.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
                <div class="swiper-slide h-auto">
                  <div class="feature-item-mobile">
                    <a href="https://famisafe.wondershare.com/sos-alert.html" target="_blank" class="feature-detail-card">
                      <div class="feature-detail-card-title">SOS Alert</div>
                      <div class="feature-detail-card-description">Allow your child to send instant emergency alerts with their location.</div>
                      <div class="feature-detail-card-arrow">
                        <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M1.26514 1.58008L7.89768 8.21263L1.26514 14.8452"
                            stroke="#7A57EE"
                            stroke-width="2.04665"
                            stroke-linecap="round"
                            stroke-linejoin="round" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
              <div class="swiper-pagination" style="bottom: 0"></div>
            </div>
          </div>
        </div>
      </section>
      <section class="part-geonection py-5">
        <div class="container mt-xl-5 mt-lg-3">
          <div class="geonection-wrapper">
            <div class="geonection-logo">
              <img
                loading="lazy"
                src="https://famisafe.wondershare.com/images/images-2025/index/geonection-horizontal-black.svg"
                alt="Geonection"
                class="img-fluid" />
            </div>
            <div class="geonection-content">
              <div class="geonection-item-title mb-3">Secured Sharing, <br />See Loved Ones at Hands</div>
              <div class="geonection-item-description font-size-large mb-3">Geonection protects your location sharing with end-to-end encryption.</div>
              <div class="btn-wrapper justify-content-lg-start mt-4">
                <a href="https://famisafe.wondershare.com/share-live-location.html" target="_blank" class="btn btn-lg btn-outline-black d-lg-flex d-none"
                  >Try It Free</a
                >
                <a href="https://famisafe.wondershare.com/share-live-location.html" target="_blank" class="btn btn-lg btn-green d-lg-none d-flex"
                  >Try It Free</a
                >
              </div>
            </div>
            <div class="geonection-img">
              <picture>
                <source srcset="https://famisafe.wondershare.com/images/images-2025/index/geonection-mobile.png" media="(max-width: 992px)" />
                <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/geonection-img.png" alt="Geonection" class="img-fluid" />
              </picture>
            </div>
          </div>
        </div>
      </section>
      <section class="part-customer py-5 overflow-hidden">
        <h2>Real Families, Real Stories</h2>
        <div class="position-relative assetsSwiper-box">
          <div class="assetsSwiper py-xl-5 pb-4 pt-5 swiper" id="assetsSwiper">
            <div class="swiper-wrapper align-items-stretch">
              <div class="swiper-slide">
                <div class="box-style">
                  <div class="rounded-16 img-container">
                    <picture>
                      <source srcset="https://famisafe.wondershare.com/images/images-2025/index/story-1-mobile.jpg" media="(max-width: 768px)" />
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/story-1.jpg"
                        alt="customer1"
                        class="img-fluid w-100"
                        style="object-position: 27%" />
                    </picture>
                  </div>
                  <div class="assets-title">Mom of a High-School Sophomore</div>
                  <div class="customer-info-box">
                    <div class="customer-info">
                      “Late after choir practice, my daughter walks home alone. Geonection lets me follow her route live and sends a ping once she’s safely
                      inside.”
                    </div>
                    <div class="btn-wrapper">
                      <a
                        href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                        target="_blank"
                        class="btn btn-lg btn-purple-bg2">
                        <span>Start Free Trail</span>
                        <svg width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M18.9763 6.2718L12.9354 0.586225C12.2972 -0.0144089 11.25 0.438053 11.25 1.31443L11.25 2.5C11.25 3.05228 10.8023 3.5 10.25 3.5L1.25 3.5C0.697716 3.5 0.25 3.94771 0.25 4.5L0.25 9.5C0.25 10.0523 0.697715 10.5 1.25 10.5L10.25 10.5C10.8023 10.5 11.25 10.9477 11.25 11.5L11.25 12.6856C11.25 13.5619 12.2972 14.0144 12.9354 13.4138L18.9763 7.7282C19.3958 7.33336 19.3958 6.66664 18.9763 6.2718Z"
                            fill="white" />
                        </svg>
                      </a>
                    </div>
                  </div>
                  <div class="customer-info-box2">
                    <div class="customer-info">
                      <div class="customer-name">Grace H</div>
                      <div class="customer-desc">Mom of a High-School Sophomore</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="box-style">
                  <div class="rounded-16 img-container">
                    <picture>
                      <source srcset="https://famisafe.wondershare.com/images/images-2025/index/story-2-mobile.jpg" media="(max-width: 768px)" />
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/story-2.jpg"
                        alt="customer2"
                        class="img-fluid w-100"
                        style="object-position: 39%" />
                    </picture>
                  </div>
                  <div class="assets-title">Dad Working Overseas</div>
                  <div class="customer-info-box">
                    <div class="customer-info">“Even from 8 000 km away, I auto-lock games during homework hours and unlock them for breaks.”</div>
                    <div class="btn-wrapper">
                      <a
                        href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                        target="_blank"
                        class="btn btn-lg btn-purple-bg2">
                        <span>Start Free Trail</span>
                        <svg width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M18.9763 6.2718L12.9354 0.586225C12.2972 -0.0144089 11.25 0.438053 11.25 1.31443L11.25 2.5C11.25 3.05228 10.8023 3.5 10.25 3.5L1.25 3.5C0.697716 3.5 0.25 3.94771 0.25 4.5L0.25 9.5C0.25 10.0523 0.697715 10.5 1.25 10.5L10.25 10.5C10.8023 10.5 11.25 10.9477 11.25 11.5L11.25 12.6856C11.25 13.5619 12.2972 14.0144 12.9354 13.4138L18.9763 7.7282C19.3958 7.33336 19.3958 6.66664 18.9763 6.2718Z"
                            fill="white" />
                        </svg>
                      </a>
                    </div>
                  </div>
                  <div class="customer-info-box2">
                    <div class="customer-info">
                      <div class="customer-name">Omar K.</div>
                      <div class="customer-desc">Dad Working Overseas</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="box-style">
                  <div class="rounded-16 img-container">
                    <picture>
                      <source srcset="https://famisafe.wondershare.com/images/images-2025/index/story-3-mobile.jpg" media="(max-width: 768px)" />
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/story-3.jpg"
                        alt="customer3"
                        class="img-fluid w-100"
                        style="object-position: 31%" />
                    </picture>
                  </div>
                  <div class="assets-title">Mom of a 14-year-old</div>
                  <div class="customer-info-box">
                    <div class="customer-info">“FamiSafe flagged ‘hurt myself’ in my son’s chat. I stepped in, and we got him help the same day.”</div>
                    <div class="btn-wrapper">
                      <a
                        href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                        target="_blank"
                        class="btn btn-lg btn-purple-bg2">
                        <span>Start Free Trail</span>
                        <svg width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M18.9763 6.2718L12.9354 0.586225C12.2972 -0.0144089 11.25 0.438053 11.25 1.31443L11.25 2.5C11.25 3.05228 10.8023 3.5 10.25 3.5L1.25 3.5C0.697716 3.5 0.25 3.94771 0.25 4.5L0.25 9.5C0.25 10.0523 0.697715 10.5 1.25 10.5L10.25 10.5C10.8023 10.5 11.25 10.9477 11.25 11.5L11.25 12.6856C11.25 13.5619 12.2972 14.0144 12.9354 13.4138L18.9763 7.7282C19.3958 7.33336 19.3958 6.66664 18.9763 6.2718Z"
                            fill="white" />
                        </svg>
                      </a>
                    </div>
                  </div>
                  <div class="customer-info-box2">
                    <div class="customer-info">
                      <div class="customer-name">Claire B</div>
                      <div class="customer-desc">Mom of a 14-year-old</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide active">
                <div class="box-style">
                  <div class="rounded-16 img-container">
                    <picture>
                      <source srcset="https://famisafe.wondershare.com/images/images-2025/index/story-4-mobile.jpg" media="(max-width: 768px)" />
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/story-4.jpg"
                        alt="customer4"
                        class="img-fluid w-100"
                        style="object-position: 45%" />
                    </picture>
                  </div>
                  <div class="assets-title">Grandpa of 8-year-old</div>
                  <div class="customer-info-box">
                    <div class="customer-info">
                      “When my granddaughter didn’t answer after school, FamiSafe’s real-time map showed she was still at orchestra practice. One glance spared
                      a panic call and gave me total peace of mind.”
                    </div>
                    <div class="btn-wrapper">
                      <a
                        href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                        target="_blank"
                        class="btn btn-lg btn-purple-bg2">
                        <span>Start Free Trail</span>
                        <svg width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M18.9763 6.2718L12.9354 0.586225C12.2972 -0.0144089 11.25 0.438053 11.25 1.31443L11.25 2.5C11.25 3.05228 10.8023 3.5 10.25 3.5L1.25 3.5C0.697716 3.5 0.25 3.94771 0.25 4.5L0.25 9.5C0.25 10.0523 0.697715 10.5 1.25 10.5L10.25 10.5C10.8023 10.5 11.25 10.9477 11.25 11.5L11.25 12.6856C11.25 13.5619 12.2972 14.0144 12.9354 13.4138L18.9763 7.7282C19.3958 7.33336 19.3958 6.66664 18.9763 6.2718Z"
                            fill="white" />
                        </svg>
                      </a>
                    </div>
                  </div>
                  <div class="customer-info-box2">
                    <div class="customer-info">
                      <div class="customer-name">David T.</div>
                      <div class="customer-desc">Grandpa of 8-year-old</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="box-style">
                  <div class="rounded-16 img-container">
                    <picture>
                      <source srcset="https://famisafe.wondershare.com/images/images-2025/index/story-5-mobile.jpg" media="(max-width: 768px)" />
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/story-5.jpg"
                        alt="customer5"
                        class="img-fluid w-100"
                        style="object-position: 61%" />
                    </picture>
                  </div>
                  <div class="assets-title">Mom of an ADHD Gamer</div>
                  <div class="customer-info-box">
                    <div class="customer-info">“Finishing chores now earns my son 15 bonus minutes—FamiSafe turned limits into motivation.”</div>
                    <div class="btn-wrapper">
                      <a
                        href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                        target="_blank"
                        class="btn btn-lg btn-purple-bg2">
                        <span>Start Free Trail</span>
                        <svg width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M18.9763 6.2718L12.9354 0.586225C12.2972 -0.0144089 11.25 0.438053 11.25 1.31443L11.25 2.5C11.25 3.05228 10.8023 3.5 10.25 3.5L1.25 3.5C0.697716 3.5 0.25 3.94771 0.25 4.5L0.25 9.5C0.25 10.0523 0.697715 10.5 1.25 10.5L10.25 10.5C10.8023 10.5 11.25 10.9477 11.25 11.5L11.25 12.6856C11.25 13.5619 12.2972 14.0144 12.9354 13.4138L18.9763 7.7282C19.3958 7.33336 19.3958 6.66664 18.9763 6.2718Z"
                            fill="white" />
                        </svg>
                      </a>
                    </div>
                  </div>
                  <div class="customer-info-box2">
                    <div class="customer-info">
                      <div class="customer-name">Leah G</div>
                      <div class="customer-desc">Mom of an ADHD Gamer</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="assetsSwiper-pagination swiper-pagination"></div>
          </div>
          <!-- mobile btn -->
          <div class="btn-wrapper mt-4">
            <a
              href="https://app.adjust.com/1g2ryrlk_1gp7te8h?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
              target="_blank"
              class="btn btn-lg btn-purple-bg d-xl-none d-flex"
              >Try It Free</a
            >
          </div>
        </div>
      </section>
      <section class="part-saying py-5">
        <div class="container">
          <div class="saying-box">
            <div class="left-box">
              <h2 class="mb-4 saying-title">What Parents Are Saying About <span style="color: #3cf4d3">FamiSafe</span></h2>
              <p>Real experiences from families who use it to keep their kids safe online.</p>
              <div class="white-divider my-xxl-5 my-4"></div>
              <div class="count-list">
                <div class="count-box">
                  <div class="count"><span class="count-num" data-from="0" data-to="200" data-speed="800" data-refresh-interval="10">200</span>+</div>
                  <div class="count-desc">Countries</div>
                </div>
                <div class="count-box">
                  <div class="count"><span class="count-num" data-from="0" data-to="35" data-speed="800" data-refresh-interval="10">35</span>M+</div>
                  <div class="count-desc">Users</div>
                </div>
                <div class="count-box">
                  <div class="count"><span class="count-num" data-from="0" data-to="18" data-speed="800" data-refresh-interval="10">18</span>w+</div>
                  <div class="count-desc">Families</div>
                </div>
                <div class="count-box">
                  <div class="count"><span class="count-num" data-from="0" data-to="15" data-speed="800" data-refresh-interval="10">15</span>w+</div>
                  <div class="count-desc">Subscribers</div>
                </div>
              </div>
            </div>
            <div class="right-box">
              <!-- pc 竖版无缝滚动 -->
              <div class="user-card-list list1 d-xl-flex d-none">
                <!-- first list -->
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/john-gilman.png" alt="user-avatar" class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">John Gilman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    A capable porn blocker app I have experienced in my life. I also used other apps but found this one as more comfortable, quick, reliable,
                    and effective in application.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/uxprjt-lattka.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Uxprjt Lattka</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    Checking out my daughter’s photo gallery is an unbelievable feature that this parental control app offers. I totally love it. I also use the
                    screen time monitoring quite often.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/maria-s.png" alt="user-avatar" class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Maria S.</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    I must say this app is a second parent to my kids. I, as a mother, quickly locate where the babysitter is taking my child. It turns the
                    signal on as soon as they are out from the safe zone.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/kayla-huppman.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Kayla Huppman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    Great app, had it for awhile for both my kids. So glad they added in certain features to keep track of. I loved that I could keep track of
                    where they were supposed to be at given times!
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/renee-wooldridge.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Renée Wooldridge</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    My problem was resolved and I love the new features. My grandson found a way around security to open apps. The nightmare is that he can open
                    Google Play Store and download apps that I don't want him to have.
                  </div>
                </div>
                <!-- copy line (和上面一致，用作无缝滚动) -->
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/john-gilman.png" alt="user-avatar" class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">John Gilman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    A capable porn blocker app I have experienced in my life. I also used other apps but found this one as more comfortable, quick, reliable,
                    and effective in application.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/uxprjt-lattka.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Uxprjt Lattka</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    Checking out my daughter’s photo gallery is an unbelievable feature that this parental control app offers. I totally love it. I also use the
                    screen time monitoring quite often.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/maria-s.png" alt="user-avatar" class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Maria S.</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    I must say this app is a second parent to my kids. I, as a mother, quickly locate where the babysitter is taking my child. It turns the
                    signal on as soon as they are out from the safe zone.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/kayla-huppman.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Kayla Huppman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    Great app, had it for awhile for both my kids. So glad they added in certain features to keep track of. I loved that I could keep track of
                    where they were supposed to be at given times!
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/renee-wooldridge.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Renée Wooldridge</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    My problem was resolved and I love the new features. My grandson found a way around security to open apps. The nightmare is that he can open
                    Google Play Store and download apps that I don't want him to have.
                  </div>
                </div>
              </div>
              <div class="user-card-list list2 d-xl-flex d-none">
                <!-- first line -->
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/the-thompson-family.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">The Thompson Family</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    The driving report feature although being newly introduced works flawlessly and it is such a help for parents whose children have just
                    started driving. Thank you so much.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/emily-lawson.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Emily Lawson</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    App works great so far for the couple days I've been using it. Both devices are androids and everything runs smoothly. I like how you can
                    make it flag certain keywords instead of just general content.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/rodica-mihai.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Rodica Mihai</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    That's the best parental control app I've had so far. I can see everything my child is accessing online. And the location of my child is OK,
                    sometimes it's a delay of few minutes but is updating periodically.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/sharon-carnegie.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Sharon Carnegie</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">Really easy to navigate and understand, great functionality and keeps my daughter safe. Definitely recommend!</div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/john-gilman-2.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">John Gilman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    I just learned how Famisafes new YouTube monitoring function works. Well, I must say the functioning is quite smooth and more helpful with
                    regards to YouTube controls.
                  </div>
                </div>
                <!-- copy line -->
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/the-thompson-family.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">The Thompson Family</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    The driving report feature although being newly introduced works flawlessly and it is such a help for parents whose children have just
                    started driving. Thank you so much.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/emily-lawson.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Emily Lawson</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    App works great so far for the couple days I've been using it. Both devices are androids and everything runs smoothly. I like how you can
                    make it flag certain keywords instead of just general content.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/rodica-mihai.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Rodica Mihai</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    That's the best parental control app I've had so far. I can see everything my child is accessing online. And the location of my child is OK,
                    sometimes it's a delay of few minutes but is updating periodically.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/sharon-carnegie.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Sharon Carnegie</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">Really easy to navigate and understand, great functionality and keeps my daughter safe. Definitely recommend!</div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/john-gilman-2.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">John Gilman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    I just learned how Famisafes new YouTube monitoring function works. Well, I must say the functioning is quite smooth and more helpful with
                    regards to YouTube controls.
                  </div>
                </div>
              </div>
              <!-- 移动端竖版无缝滚动 -->
              <div class="user-card-list list1-mobile d-xl-none">
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/john-gilman.png" alt="user-avatar" class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">John Gilman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    A capable porn blocker app I have experienced in my life. I also used other apps but found this one as more comfortable, quick, reliable,
                    and effective in application.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/uxprjt-lattka.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Uxprjt Lattka</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    Checking out my daughter’s photo gallery is an unbelievable feature that this parental control app offers. I totally love it. I also use the
                    screen time monitoring quite often.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/maria-s.png" alt="user-avatar" class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Maria S.</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    I must say this app is a second parent to my kids. I, as a mother, quickly locate where the babysitter is taking my child. It turns the
                    signal on as soon as they are out from the safe zone.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/kayla-huppman.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Kayla Huppman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    Great app, had it for awhile for both my kids. So glad they added in certain features to keep track of. I loved that I could keep track of
                    where they were supposed to be at given times!
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/renee-wooldridge.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Renée Wooldridge</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    My problem was resolved and I love the new features. My grandson found a way around security to open apps. The nightmare is that he can open
                    Google Play Store and download apps that I don't want him to have.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/the-thompson-family.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">The Thompson Family</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    The driving report feature although being newly introduced works flawlessly and it is such a help for parents whose children have just
                    started driving. Thank you so much.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/emily-lawson.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Emily Lawson</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    App works great so far for the couple days I've been using it. Both devices are androids and everything runs smoothly. I like how you can
                    make it flag certain keywords instead of just general content.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/rodica-mihai.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Rodica Mihai</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    That's the best parental control app I've had so far. I can see everything my child is accessing online. And the location of my child is OK,
                    sometimes it's a delay of few minutes but is updating periodically.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/sharon-carnegie.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Sharon Carnegie</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">Really easy to navigate and understand, great functionality and keeps my daughter safe. Definitely recommend!</div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/john-gilman-2.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">John Gilman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    I just learned how Famisafes new YouTube monitoring function works. Well, I must say the functioning is quite smooth and more helpful with
                    regards to YouTube controls.
                  </div>
                </div>
                <!-- second list 复制 -->
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/john-gilman.png" alt="user-avatar" class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">John Gilman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    A capable porn blocker app I have experienced in my life. I also used other apps but found this one as more comfortable, quick, reliable,
                    and effective in application.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/uxprjt-lattka.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Uxprjt Lattka</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    Checking out my daughter’s photo gallery is an unbelievable feature that this parental control app offers. I totally love it. I also use the
                    screen time monitoring quite often.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/maria-s.png" alt="user-avatar" class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Maria S.</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    I must say this app is a second parent to my kids. I, as a mother, quickly locate where the babysitter is taking my child. It turns the
                    signal on as soon as they are out from the safe zone.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/kayla-huppman.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Kayla Huppman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    Great app, had it for awhile for both my kids. So glad they added in certain features to keep track of. I loved that I could keep track of
                    where they were supposed to be at given times!
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/renee-wooldridge.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Renée Wooldridge</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    My problem was resolved and I love the new features. My grandson found a way around security to open apps. The nightmare is that he can open
                    Google Play Store and download apps that I don't want him to have.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/the-thompson-family.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">The Thompson Family</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    The driving report feature although being newly introduced works flawlessly and it is such a help for parents whose children have just
                    started driving. Thank you so much.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/emily-lawson.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Emily Lawson</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    App works great so far for the couple days I've been using it. Both devices are androids and everything runs smoothly. I like how you can
                    make it flag certain keywords instead of just general content.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/rodica-mihai.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Rodica Mihai</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    That's the best parental control app I've had so far. I can see everything my child is accessing online. And the location of my child is OK,
                    sometimes it's a delay of few minutes but is updating periodically.
                  </div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/sharon-carnegie.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">Sharon Carnegie</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">Really easy to navigate and understand, great functionality and keeps my daughter safe. Definitely recommend!</div>
                </div>
                <div class="user-card">
                  <div class="useer-info">
                    <div class="user-avatar">
                      <img
                        loading="lazy"
                        src="https://famisafe.wondershare.com/images/images-2025/index/john-gilman-2.png"
                        alt="user-avatar"
                        class="img-fluid" />
                    </div>
                    <div>
                      <div class="user-name">John Gilman</div>
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/star-icon.svg" alt="star-icon" class="img-fluid" />
                    </div>
                  </div>
                  <div class="user-desc">
                    I just learned how Famisafes new YouTube monitoring function works. Well, I must say the functioning is quite smooth and more helpful with
                    regards to YouTube controls.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="part-grow py-5">
        <div class="container mt-xl-5 mt-lg-3">
          <h2>Grow Together With the FamiSafe Community</h2>
          <div class="social-media-mobile-list d-xl-none">
            <a href="https://www.youtube.com/@WondershareFamiSafe" target="_blank" class="social-media-item">
              <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/youtube.svg" alt="Youtube" class="img-fluid" />
            </a>
            <a href="https://www.facebook.com/famisafe/" target="_blank" class="social-media-item">
              <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/facebook.svg" alt="Facebook" class="img-fluid" />
            </a>
            <a href="https://www.instagram.com/wondershare_famisafe/" target="_blank" class="social-media-item">
              <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/instagram.svg" alt="Instagram" class="img-fluid" />
            </a>
            <a href="https://twitter.com/famisafe" target="_blank" class="social-media-item">
              <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/twitter.svg" alt="Twitter" class="img-fluid" />
            </a>
            <a href="https://www.tiktok.com/@wondershare_famisafe" target="_blank" class="social-media-item">
              <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/tik-tok.svg" alt="Tik-Tok" class="img-fluid" />
            </a>
          </div>
          <div class="swiper pt-5 pb-xl-0 pb-4" id="grow-swiper">
            <div class="swiper-wrapper">
              <div class="swiper-slide what-new-slide h-auto">
                <div class="grow-box what-new">
                  <a href="https://famisafe.wondershare.com/whats-new/" target="_blank" class="card-link"></a>
                  <div class="content-wrapper">
                    <h3 class="title mb-4">What's New</h3>
                    <div class="pb-4">Discover smarter tools, improved features, and exciting updates that make digital parenting easier than ever.</div>
                    <div class="black-arrow mt-xl-3">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/black-arrow.svg" alt="black-arrow" class="img-fluid" />
                    </div>
                  </div>
                  <div class="img-wrapper">
                    <picture>
                      <source srcset="https://famisafe.wondershare.com/images/images-2025/index/what-new-mobile.png" media="(max-width: 1280px)" />
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/what-new.png" alt="what-new" class="img-fluid" />
                    </picture>
                  </div>
                </div>
              </div>
              <div class="swiper-slide tutorials-slide h-auto">
                <div class="grow-box tutorials">
                  <a href="https://famisafe.wondershare.com/user-guide/" target="_blank" class="card-link"></a>
                  <div class="content-wrapper">
                    <h3 class="title mb-4">Tutorials</h3>
                    <div class="pb-4">Discover step-by-step guides to help you and your child use FamiSafe effectively.</div>
                    <div class="black-arrow mt-xl-3">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/black-arrow.svg" alt="black-arrow" class="img-fluid" />
                    </div>
                  </div>
                  <div class="img-wrapper">
                    <picture>
                      <source srcset="https://famisafe.wondershare.com/images/images-2025/index/tutorials-mobile.png" media="(max-width: 1280px)" />
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/tutorials.png" alt="tutorials" class="img-fluid" />
                    </picture>
                  </div>
                </div>
              </div>
              <div class="swiper-slide social-media-slide h-auto">
                <div class="grow-box social-media">
                  <img
                    loading="lazy"
                    src="https://famisafe.wondershare.com/images/images-2025/index/social-media-bg.png"
                    alt="social-media-bg"
                    class="img-fluid w-100"
                    style="height: 100%; object-fit: cover" />

                  <div class="social-media-list">
                    <a href="https://www.youtube.com/@WondershareFamiSafe" target="_blank" class="social-media-item">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/youtube.png" alt="Youtube" class="img-fluid" />
                    </a>
                    <a href="https://www.facebook.com/famisafe/" target="_blank" class="social-media-item">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/facebook.png" alt="Facebook" class="img-fluid" />
                    </a>
                    <a href="https://www.instagram.com/wondershare_famisafe/" target="_blank" class="social-media-item">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/instagram.png" alt="Instagram" class="img-fluid" />
                    </a>
                    <a href="https://twitter.com/famisafe" target="_blank" class="social-media-item">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/twitter.png" alt="Twitter" class="img-fluid" />
                    </a>
                    <a href="https://www.tiktok.com/@wondershare_famisafe" target="_blank" class="social-media-item">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/tik-tok.png" alt="Tik-Tok" class="img-fluid" />
                    </a>
                  </div>
                </div>
              </div>
              <div class="swiper-slide blogs-slide h-auto">
                <div class="grow-box blogs">
                  <a href="https://famisafe.wondershare.com/topic/" target="_blank" class="card-link"></a>
                  <div class="content-wrapper">
                    <h3 class="title mb-4">Blogs</h3>
                    <div class="pb-4">Get expert tips and parenting advice to help keep your kids safe online.</div>
                    <div class="black-arrow mt-xl-3">
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/black-arrow.svg" alt="black-arrow" class="img-fluid" />
                    </div>
                  </div>
                  <div class="img-wrapper">
                    <picture>
                      <source srcset="https://famisafe.wondershare.com/images/images-2025/index/blogs-mobile.png" media="(max-width: 1280px)" />
                      <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/blogs.png" alt="blogs" class="img-fluid" />
                    </picture>
                  </div>
                </div>
              </div>
            </div>
            <div class="swiper-pagination"></div>
          </div>
        </div>
      </section>
      <section class="part-protection py-5">
        <div class="container mt-sm-0 mt-5">
          <div class="protection-box">
            <div class="trusted-box">
              <h4 class="title">Trusted Protection</h4>
              <p>FamiSafe uses bank-level encryption, ensuring your child's data stays fully secure and accessible only to you.</p>
            </div>
            <div class="purple-divider d-xl-none"></div>
            <div class="privacy-box">
              <h4 class="title">Privacy Promise</h4>
              <p>Zero data collection, secure local storage, and strict GDPR & COPPA compliance—your family’s privacy always comes first.</p>
            </div>
            <picture class="purple-lock">
              <source srcset="https://famisafe.wondershare.com/images/images-2025/index/purple-lock-mobile.png" media="(max-width: 1280px)" />
              <img loading="lazy" src="https://famisafe.wondershare.com/images/images-2025/index/purple-lock.png" alt="purple-lock" class="img-fluid" />
            </picture>
          </div>
        </div>
      </section>
      <section class="part-footer py-5">
        <div class="footer-box my-xl-5 my-lg-3">
          <div class="FS-logo">
            <img
              loading="lazy"
              src="https://famisafe.wondershare.com/images/images-2025/index/famisafe-horizontal-black.svg"
              alt="FamiSafe-horizontal-black"
              class="img-fluid" />
            <h2 class="mt-5 footer-title mb-2">Start FamiSafe Parental Controls Today</h2>
            <p>Trusted by parents. Built for families. Designed to make digital parenting easier.</p>
            <div class="btn-wrapper pt-md-5 pt-4">
              <a href="https://famisafe.wondershare.com/main/sign-up" target="_blank" class="btn btn-colorful btn-lg d-md-flex d-none">Start Free Trail</a>
              <a href="https://famisafe.wondershare.com/main/sign-up" target="_blank" class="btn btn-purple-bg btn-lg d-md-none">Start Free Trail</a>
              <a href="https://famisafe.wondershare.com/store/family.html" target="_blank" class="btn btn-lg btn-white">Buy Now </a>
            </div>
          </div>
        </div>
      </section>
    </main>
  </body>
  <!-- 购买引导弹窗 start -->
  <style>
    .modal-backdrop {
      min-height: 100dvh;
    }

    #modal-promotions .modal-content {
      all: unset;
      pointer-events: auto;
      margin: 12px;
      text-align: center;
      max-width: 500px;
      cursor: pointer;
    }
    #modal-promotions .close-link {
      position: absolute;
      top: 4px;
      right: 10px;
      transform: translate(0, 0);
      width: 26px;
      height: 26px;
      cursor: pointer;
    }
    @media (max-width: 576px) {
      #modal-promotions .close-link {
        width: 24px;
        height: 24px;
        bottom: -18px;
      }
    }
    .slide-promotions {
      position: fixed;
      bottom: 50%;
      right: -15px;
      max-width: 240px;
      z-index: 100;
      transform: translate(100%, 50%);
      transition: all 0.3s ease-in-out;
    }
    .slide-promotions.show {
      transform: translate(0, 50%);
    }
  </style>
  <aside>
    <!-- 中间 -->
    <div id="modal-promotions" class="modal fade" tabindex="-1" aria-modal="true" aria-hidden="true" role="dialog">
      <div class="modal-dialog modal-dialog-centered mx-auto" role="document">
        <div class="modal-content position-relative">
          <a
            href="https://store.wondershare.com/index.php?method=index&submod=checkout&pid=13657&license_id=5287&sub_lid=0&currency=USD&language=English&verify=1b22b9774e4d7cf7786419e5e42cc860"
            target="_blank">
            <img src="https://famisafe.wondershare.com/images/images-2025/famisafe/index-modal.png" alt="modal element" class="w-100" />
          </a>
          <a href="javascript:void(0)" class="close-link" data-dismiss="modal" aria-label="Close">
            <img src="https://famisafe.wondershare.com/images/images-2025/famisafe/close-icon.svg" alt="close" class="img-fluid w-100" />
          </a>
        </div>
      </div>
    </div>
    <!-- 侧边 -->
    <div class="slide-promotions d-xl-block d-none">
      <a
        href="https://store.wondershare.com/index.php?method=index&submod=checkout&pid=13657&license_id=5287&sub_lid=0&currency=USD&language=English&verify=1b22b9774e4d7cf7786419e5e42cc860"
        target="_blank">
        <img src="https://famisafe.wondershare.com/images/images-2025/famisafe/index-modal.png" alt="purchase-modal" class="w-100" />
      </a>
    </div>
  </aside>
  <script>
    document.addEventListener("DOMContentLoaded", function () {
      // 设置cookie的函数
      function setCookie(name, value, days) {
        var expires = "";
        if (days) {
          var date = new Date();
          date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
          expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
      }

      // 获取cookie的函数
      function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(";");
        for (var i = 0; i < ca.length; i++) {
          var c = ca[i];
          while (c.charAt(0) == " ") c = c.substring(1, c.length);
          if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
      }

      // 检查今天是否已关闭过弹窗
      const modalClosed = getCookie("modalPromotionsClosed");
      const today = new Date().toDateString();

      // 不管是否关闭过弹窗，都直接显示侧边栏
      $(".slide-promotions").addClass("show");

      // 如果今天还没关闭过，15秒后弹窗
      if (modalClosed !== today) {
        setTimeout(function () {
          $("#modal-promotions").modal("show");
        }, 15000);
      }

      // 监听弹窗关闭事件，记录关闭cookie
      $("#modal-promotions").on("hidden.bs.modal", function () {
        setCookie("modalPromotionsClosed", today, 1);
      });
    });
  </script>
  <!-- 购买引导弹窗 end -->

  <script type="text/javascript" src="https://famisafe.wondershare.com/assets/js/chunk-vendors.js"></script>
  <script type="text/javascript" src="https://famisafe.wondershare.com/assets/js/app.js"></script>

  <div data-toggle="gotop" data-type="ws2020"></div>
  <!-- 移动端商城弹窗 
    <style>
      .modal-backdrop {
        min-height: 100dvh;
      }
      #modal-mobileEnterApp1 .modal-content {
        all: unset;
        pointer-events: auto;
        margin: 24px;
        text-align: center;
        border-radius: 12px;
        background-color: #fff;
        padding: 20px 20px 14px;
        width: 100%;
      }
      #modal-mobileEnterApp1 .sys-ios .modal-main-text {
        text-align: left;
        font-size: 16px;
        font-weight: 400;
        line-height: 100%;
        margin-bottom: 34px;
      }
      #modal-mobileEnterApp1 .sys-android .modal-main-text {
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        line-height: 100%;
        margin-bottom: 20px;
      }

      #modal-mobileEnterApp1 .sys-ios .modal-main-btn {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        text-decoration: none;
      }
      #modal-mobileEnterApp1 .sys-android .modal-main-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-decoration: none;
      }
      #modal-mobileEnterApp1 .sys-ios .modal-main-btn .close-link {
        font-size: 18px;
        line-height: 100%;
        font-weight: 400;
        color: rgba(151, 93, 248, 0.75);
        margin-right: 26px;
        text-decoration: none;
      }
      #modal-mobileEnterApp1 .sys-android .modal-main-btn .close-link {
        width: 48%;
        border-radius: 8px;
        display: inline-block;
        background-color: #f0f0f0;
        font-size: 18px;
        line-height: 100%;
        font-weight: 400;
        color: #636363;
        height: 40px;
        text-decoration: none;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      #modal-mobileEnterApp1 .sys-ios .modal-main-btn .open-btn {
        font-size: 20px;
        line-height: 100%;
        font-weight: 400;
        color: #975df8;
        text-decoration: none;
      }
      #modal-mobileEnterApp1 .sys-android .modal-main-btn .open-btn {
        width: 48%;
        font-size: 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #975df8;
        line-height: 100%;
        font-weight: 400;
        color: #975df8;
        color: #fff;
        font-weight: 700;
        font-size: 20px;
        text-decoration: none;
        height: 40px;
      }
    </style>
    <div id="modal-mobileEnterApp1" class="modal fade show d-md-none" data-backdrop="static" tabindex="-1" aria-modal="true" aria-hidden="false" role="dialog">
      <div class="modal-dialog modal-dialog-centered mx-0" role="document">
        <div class="modal-content">
          <div class="sys-ios">
            <div class="modal-main-text">Open this page in "App Store"?</div>
            <div class="modal-main-btn">
              <a href="javascript:void(0)" class="close-link" data-dismiss="modal" aria-label="Close">Cancel</a>
              <a
                href="https://app.adjust.com/1l660rk8_1lriug98?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                class="open-btn"
                >Open</a
              >
            </div>
          </div>
          <div class="sys-android">
            <div class="modal-main-text">Open this page in "Google Play"?</div>
            <div class="modal-main-btn">
              <a href="javascript:void(0)" class="close-link" data-dismiss="modal" aria-label="Close">Cancel</a>
              <a
                href="https://app.adjust.com/1l660rk8_1lriug98?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
                class="open-btn"
                >Open</a
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const PATHNAME_LIST = ["/store/family.html", "/store/school.html", "/geonection/store/individuals.html"];
        const COOKIE_NAME = "modalButtonClicked";

        if (window.innerWidth < 768 && !PATHNAME_LIST.includes(window.location.pathname)) {
          // 获取模态框元素
          const modal = document.getElementById("modal-mobileEnterApp1");

          // Cookie 管理辅助函数
          function setCookie(name, value, expiryDate) {
            const expires = "expires=" + expiryDate.toUTCString();
            document.cookie = name + "=" + value + ";" + expires + ";path=/";
          }

          function getCookie(name) {
            const cookieName = name + "=";
            const cookies = document.cookie.split(";");
            for (let i = 0; i < cookies.length; i++) {
              let cookie = cookies[i].trim();
              if (cookie.indexOf(cookieName) === 0) {
                return cookie.substring(cookieName.length, cookie.length);
              }
            }
            return "";
          }

          // 获取次日凌晨时间（0点0分0秒）
          function getTomorrowMidnight() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            return tomorrow;
          }

          // 检查用户今天是否已经点击过按钮
          function hasClickedToday() {
            const clickStatus = getCookie(COOKIE_NAME);
            return clickStatus === "true";
          }

          // 如果用户今天没有点击过按钮，则显示模态框
          if (!hasClickedToday()) {
            $(modal).modal("show");
          }

          // 处理"open app"按钮点击
          const tryButtonList = document.querySelectorAll(".modal-main-btn .open-btn");
          if (tryButtonList) {
            tryButtonList.forEach((tryButton) => {
              tryButton.addEventListener("click", function () {
                // 设置cookie，次日凌晨过期
                const tomorrowMidnight = getTomorrowMidnight();
                setCookie(COOKIE_NAME, "true", tomorrowMidnight);
                $(modal).modal("hide");
              });
            });
          }
        }
      });
    </script>
-->
  <!-- 移动端侧边收缩条 start -->
  <style>
    .side-collapse-box {
      position: relative;
      position: fixed;
      bottom: 170px;
      right: 0;
      z-index: 100;
      transition: transform 0.3s ease-in-out;
    }
    .side-collapse-box.collapsed {
      transform: translateX(calc(100% - 26px));
    }
    .side-collapse-box.collapsed .arrow-icon {
      transform: rotate(180deg);
    }

    .side-collapse-link {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 1;
      padding: 4px 9px 4px 28px;
      border-radius: 999px;
      position: relative;

      background: linear-gradient(182.47deg, #975df8 -146.64%, #975df8 136.98%);
      box-shadow: 0px -2.97px 5.45px 0px #f2ccff40 inset, 0px 3.96px 13.18px 0px #ffffff7a inset;
    }
    .side-collapse-link .dinosaur-icon {
      position: absolute;
      left: -13px;
      bottom: -3px;
      z-index: 3;
    }
    .side-collapse-link .arrow-icon {
      position: absolute;
      left: -15px;
      bottom: 7px;
      z-index: 3;
    }
    .side-collapse-box .side-collapse-text {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: #fff;
      visibility: visible;
      text-decoration: none;
    }
    .side-collapse-box .side-collapse-close-icon {
      position: absolute;
      height: 100%;
      display: flex;
      align-items: center;
      right: 0px;
      padding-right: 8px;
      top: 50%;
      transform: translateY(-50%);
      line-height: 0;
      z-index: 100;
      cursor: pointer;
    }
  </style>
  <div class="d-md-none slide-mobile">
    <!-- 侧边收缩条 -->
    <div class="side-collapse-box">
      <div class="side-collapse-link">
        <a
          href="https://app.adjust.com/1pmzrxvt_1pquu3x0?fallback=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up&redirect_macos=https%3A%2F%2Ffamisafe.wondershare.com%2Fmain%2Fsign-up"
          class="side-collapse-text"
          >Open in FamiSafe</a
        >

        <img src="https://famisafe.wondershare.com/images/images-2025/bottom-float-block/dinosaur-icon.svg" alt="dinosaur" class="dinosaur-icon" />
        <img src="https://famisafe.wondershare.com/images/images-2025/bottom-float-block/arrow-purple.svg" alt="arrow" class="arrow-icon" />
      </div>
    </div>
  </div>
  <script>
    // 侧边收缩条
    // 排除为购买页
    const PATHNAME_LIST = ["/store/family.html", "/store/school.html", "/geonection/store/individuals.html"];
    let autoExpandTimer = null;
    const sideCollapseBox = document.querySelector(".side-collapse-box");
    const dinosaurIcon = document.querySelector(".side-collapse-link .dinosaur-icon");
    const TIME_OUT = 20000; //20秒

    // 处理关闭按钮点击
    if (PATHNAME_LIST.includes(window.location.pathname)) {
      document.querySelector(".slide-mobile").classList.add("d-none");
    } else {
      dinosaurIcon.addEventListener("click", function () {
        if (sideCollapseBox.classList.contains("collapsed")) {
          sideCollapseBox.classList.remove("collapsed");
          // 清除之前的定时器（如果存在）
          if (autoExpandTimer) {
            clearTimeout(autoExpandTimer);
          }
        } else {
          sideCollapseBox.classList.add("collapsed");
          // 设置新的20秒自动展开定时器
          autoExpandTimer = setTimeout(() => {
            if (sideCollapseBox.classList.contains("collapsed")) {
              sideCollapseBox.classList.remove("collapsed");
            }
          }, TIME_OUT);
        }
      });
    }
  </script>
  <!-- 移动端侧边收缩条 end -->

  <!-- 脚部公共块 -->
  <style>
    @media (min-width: 1440px) {
      .wsc-footer2020 .wsc-footer202004-bottom .wsc-footer2020-mobile-language-menu {
        right: 50%;
        transform: translateX(50%);
      }
    }

    @media (min-width: 1280px) {
      .wsc-footer2020 .wsc-footer202004-bottom .wsc-footer2020-mobile-language-menu {
        padding: 12px 0;
      }
      .wsc-footer2020 .wsc-footer202004-bottom .wsc-footer2020-mobile-language-link {
        padding: 4px 24px;
      }
    }
  </style>
  <footer class="wsc-footer2020 wsc-footer2021">
    <div class="wsc-footer2020-top wsc-footer202004-top">
      <div class="wsc-footer2020-container">
        <div class="wsc-footer2020-top-content">
          <div class="wsc-footer2020-nav">
            <a href="https://www.wondershare.com/" target="_blank">
              <img
                class="wsc-footer2020-nav-logo"
                src="https://neveragain.allstatics.com/2019/assets/icon/logo/wondershare-slogan-vertical-white.svg"
                alt="wondershare creativity simplified" />
            </a>
          </div>
          <div class="wsc-footer2020-subnav">
            <div class="wsc-footer2020-subnav-content">
              <div class="wsc-footer2020-dropdown">
                <nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                  <h5 class="wsc-footer2020-dropdown-title">Hero Products</h5>
                  <div class="wsc-footer2020-dropdown-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="white" stroke-width="1.5"></path>
                    </svg>
                  </div>
                </nav>
                <div class="wsc-footer2020-dropdown-menu">
                  <ul>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://filmora.wondershare.com/" target="_blank">Filmora</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://videoconverter.wondershare.com/" target="_blank">UniConverter</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://recoverit.wondershare.com/" target="_blank">Recoverit</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://drfone.wondershare.com/" target="_blank">Dr.Fone</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://pdf.wondershare.com/" target="_blank">PDFelement</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://famisafe.wondershare.com/" target="_blank">FamiSafe</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/shop/individuals.html" target="_blank">All Products</a>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="wsc-footer2020-dropdown">
                <nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                  <h5 class="wsc-footer2020-dropdown-title">Wondershare</h5>
                  <div class="wsc-footer2020-dropdown-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="white" stroke-width="1.5"></path>
                    </svg>
                  </div>
                </nav>
                <div class="wsc-footer2020-dropdown-menu">
                  <ul>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/creative-center.html" target="_blank">Creative Center</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/about/" target="_blank">About Us</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/news/" target="_blank">Newsroom</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/about/global-presence.html" target="_blank">Global Presence</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/about/founders-speech.html" target="_blank">Founder's Speech</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/about/careers.html" target="_blank">Careers</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/education.html" target="_blank">Education</a>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="wsc-footer2020-dropdown">
                <nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                  <h5 class="wsc-footer2020-dropdown-title">Help Center</h5>
                  <div class="wsc-footer2020-dropdown-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="white" stroke-width="1.5"></path>
                    </svg>
                  </div>
                </nav>
                <div class="wsc-footer2020-dropdown-menu">
                  <ul>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/about/contact-us.html" target="_blank">Contact Us</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/explore/inspiration.html" target="_blank">Video Community</a>
                    </li>
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://support.wondershare.com/" target="_blank">Support Center</a>
                    </li>
                    <!--<li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://support.wondershare.com/en/retrieve" target="_blank">Activation & Registration</a></li>-->
                    <li class="wsc-footer2020-subnav-item">
                      <a class="wsc-footer2020-subnav-link" href="https://accounts.wondershare.com/web/login?f=sitefoot" target="_blank">Account</a>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="wsc-footer2020-dropdown">
                <!--<nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                  <h5 class="wsc-footer2020-dropdown-title">Group member</h5>
                  <div class="wsc-footer2020-dropdown-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="white" stroke-width="1.5"></path>
                    </svg>
                  </div>
                </nav>-->
                <div class="wsc-footer2020-dropdown-menu">
                  <ul>
                    <!--<li class="wsc-footer2020-subnav-item"> <a class="wsc-footer2020-subnav-link shallow" href="https://www.wondershare.com/edrawsoft/" target="_blank"> <img class="wsc-footer2020-subnavLink-logo" src="https://neveragain.allstatics.com/2019/assets/icon/logo/edraw-horizontal-white.svg" alt="wondershare edraw logo" /> </a> </li>
                    <li class="wsc-footer2020-subnav-item"> <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/ufotosoft/" target="_blank"> <img class="wsc-footer2020-subnavLink-logo" src="https://neveragain.allstatics.com/2019/assets/icon/logo/ufoto-horizontal-white.svg" alt="wondershare ufoto logo" /> </a> </li> -->
                    <nav class="wsc-footer2020-dropdown-toggle sub-menu-title" aria-expanded="true">
                      <h5 class="wsc-footer2020-dropdown-title">Follow us</h5>
                    </nav>
                    <ul>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-iconlink" href="https://www.facebook.com/wondershare/" rel="nofollow" target="_blank">
                          <img src="https://images.wondershare.com/icon/footer-facebook-icon.svg" width="28" height="28" alt="Facebook" />
                        </a>
                        <a class="wsc-footer2020-subnav-iconlink" href="https://www.instagram.com/wondershare/" rel="nofollow" target="_blank">
                          <img src="https://images.wondershare.com/icon/footer-ins-icon.svg" width="28" height="28" alt="Ins" />
                        </a>
                        <a class="wsc-footer2020-subnav-iconlink" href="https://twitter.com/wondershare" rel="nofollow" target="_blank">
                          <img src="https://images.wondershare.com/icon/footer-twitter-icon.svg" width="28" height="28" alt="Twitter" />
                        </a>
                        <a class="wsc-footer2020-subnav-iconlink" href="https://www.youtube.com/user/Wondershare" rel="nofollow" target="_blank">
                          <img
                            src="https://images.wondershare.com/icon/footer-youtube-icon.svg"
                            width="28"
                            height="28"
                            alt="Youtube"
                            style="transform: scale(1.15)" />
                        </a>
                        <a class="wsc-footer2020-subnav-iconlink" href="https://www.wondershare.com/about/social-media.html" target="_blank">
                          <svg width="16" height="4" viewBox="0 0 16 4" fill="none" xmlns="https://www.w3.org/2000/svg">
                            <circle opacity="0.8" cx="2" cy="2" r="2" fill="white"></circle>
                            <circle opacity="0.8" cx="8" cy="2" r="2" fill="white"></circle>
                            <circle opacity="0.8" cx="14" cy="2" r="2" fill="white"></circle>
                          </svg>
                        </a>
                      </li>
                    </ul>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="wsc-footer2020-bottom wsc-footer202004-bottom">
      <div class="wsc-footer2020-container">
        <div class="wsc-footer2020-bottom-content">
          <div class="wsc-footer2020-copyright">
            <div class="wsc-footer2020-copyright-top">
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/company/terms_conditions.html" target="_blank">Terms and Conditions</a>
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/privacy.html" target="_blank">Privacy</a>
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/company/terms-of-use.html" target="_blank">Terms of Use</a>
              <a
                class="wsc-footer2020-copyright-link"
                id="do-not-share-link"
                style="display: none"
                href="https://www.wondershare.com/do-not-sell-or-share-my-personal-information.html"
                target="_blank"
                >Do Not Sell or Share My Personal Information</a
              >
              <a class="wsc-footer2020-copyright-link" id="cookie-preference-link" href="?cmpscreencustom" target="_self">Cookie Preferences</a>
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/refund-policy.html" target="_blank">Refund Policy</a>
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/uninstall.html" target="_blank">Uninstall</a>
            </div>
            <div class="wsc-footer2020-copyright-bottom">
              <p>
                Copyright © <span id="copyright-year"></span>
                <script>
                  document.querySelector("#copyright-year").outerHTML = new Date().getFullYear();
                </script>
                Wondershare. All rights reserved.
              </p>
            </div>
          </div>
          <div class="wsc-footer2020-mobile-language">
            <nav class="wsc-footer2020-mobile-language-toggle" aria-expanded="false">
              <span style="white-space: nowrap">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    opacity="0.8"
                    d="M10 0C4.47768 0 0 4.47768 0 10C0 15.5223 4.47768 20 10 20C15.5223 20 20 15.5223 20 10C20 4.47768 15.5223 0 10 0ZM10 5C9.34152 5 8.69866 4.94196 8.07589 4.83259C8.66071 2.48438 9.54018 1.25 10 1.25C10.4598 1.25 11.3393 2.48438 11.9241 4.83259C11.3013 4.93973 10.6585 5 10 5ZM13.1406 4.54464C12.8371 3.30134 12.4308 2.25446 11.9554 1.47768C13.375 1.80357 14.6629 2.47545 15.7277 3.39955C14.9397 3.88616 14.0692 4.27232 13.1406 4.54464ZM6.85938 4.54464C5.9308 4.27232 5.0625 3.88393 4.27455 3.39955C5.33929 2.47545 6.625 1.8058 8.04464 1.47991C7.5692 2.25446 7.16518 3.30134 6.85938 4.54464ZM13.7388 9.375C13.7098 8.09152 13.5893 6.87723 13.3951 5.77232C14.567 5.4375 15.6563 4.93973 16.6272 4.30357C17.8192 5.6875 18.5804 7.4442 18.7188 9.375H13.7388ZM1.28125 9.375C1.41964 7.44643 2.17857 5.68973 3.3683 4.3058C4.33929 4.94196 5.4308 5.43527 6.60491 5.77009C6.41071 6.87723 6.29018 8.09152 6.26116 9.375H1.28125ZM7.51116 9.375C7.54018 8.1317 7.65402 7.02232 7.82143 6.0558C8.52679 6.1808 9.25223 6.25 10 6.25C10.7455 6.25 11.4732 6.18304 12.1786 6.05804C12.346 7.02455 12.4576 8.13393 12.4888 9.375H7.51116ZM16.6272 15.6964C15.6563 15.0603 14.567 14.5625 13.3951 14.2277C13.5893 13.1205 13.7098 11.9062 13.7388 10.625H18.7188C18.5804 12.5558 17.8192 14.3125 16.6272 15.6964ZM7.82143 13.9442C7.65402 12.9777 7.54241 11.8683 7.51116 10.625H12.4866C12.4576 11.8661 12.346 12.9754 12.1763 13.942C11.4732 13.817 10.7455 13.75 10 13.75C9.25446 13.75 8.52679 13.8192 7.82143 13.9442ZM3.37054 15.6942C2.1808 14.3103 1.41964 12.5536 1.28348 10.625H6.26339C6.29241 11.9085 6.41295 13.1228 6.60714 14.2299C5.43304 14.5647 4.34152 15.058 3.37054 15.6942ZM10 18.75C9.54018 18.75 8.66071 17.5156 8.07589 15.1674C8.69866 15.0603 9.34152 15 10 15C10.6585 15 11.3013 15.0603 11.9241 15.1674C11.3393 17.5156 10.4598 18.75 10 18.75ZM11.9554 18.5223C12.4308 17.7455 12.8371 16.6987 13.1406 15.4554C14.0714 15.7277 14.9397 16.1138 15.7277 16.5982C14.6629 17.5246 13.375 18.1964 11.9554 18.5223ZM8.04464 18.5223C6.625 18.1964 5.33929 17.5268 4.27455 16.6027C5.06027 16.1161 5.92857 15.7299 6.85938 15.4576C7.16518 16.6987 7.5692 17.7455 8.04464 18.5223Z"
                    fill="white" />
                </svg>
                <span class="ml-2">English</span>
              </span>
              <div class="wsc-footer2020-mobile-language-icon">
                <svg width="10" height="5" viewBox="0 0 10 5" fill="none" xmlns="https://www.w3.org/2000/svg">
                  <path d="M5 5L0.669873 0.499999L9.33013 0.5L5 5Z" fill="#C4C4C4"></path>
                </svg>
              </div>
            </nav>
            <div class="wsc-footer2020-mobile-language-menu text-left">
              <a class="wsc-footer2020-mobile-language-link active" href="https://www.wondershare.com/">English</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.ae" target="_blank">Arabic - العربية</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.cn" target="_blank">Chinese - 简体中文</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.tw" target="_blank">Chinese Traditional - 繁體中文</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.fr" target="_blank">French - Français</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.de" target="_blank">German - Deutsch</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.co.id" target="_blank">Indonesian - Bahasa Indonesia</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.it" target="_blank">Italian - Italiano</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.jp" target="_blank">Japanese - 日本語</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.kr" target="_blank">Korean - 한국어</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.com.br" target="_blank">Portuguese - Português</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.es" target="_blank">Spanish - Español</a>
              <a class="wsc-footer2020-mobile-language-link" href="https://www.wondershare.com.ru/" target="_blank">Russian-Русский</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <script src="https://neveragain.allstatics.com/2019/assets/script/wsc-header-footer-2020.js"></script>
  <script src="https://www.wondershare.com/assets/haeder-footer-2021.js"></script>
  <script>
    (async function () {
      setTimeout(function () {
        var link = document.querySelector("a#cookie-preference-link");
        if (link) {
          link.target = "_self";
        }
      }, 500);

      var element = document.querySelector("a#do-not-share-link");
      fetch("https://api-web.wondershare.cc/v3/baseapi/web/country-by-ip")
        .then(function (response) {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json(); // 解析 JSON 数据
        })
        .then(function (res) {
          var country = res.data.country;
          if (country == "US") {
            element.style.display = "inline-block";
          }
        })
        .catch(function (error) {
          element.style.display = "none";
        });
    })();
  </script>
  <!-- js common -->
  <script src="https://neveragain.allstatics.com/2019/assets/vendor/wsc-vendor.js"></script>
  <script src="https://neveragain.allstatics.com/2019/assets/script/wsc-override-dm.js"></script>
  <script src="https://neveragain.allstatics.com/2019/assets/script/wsc-common.js"></script>
  <!--<script src="https://famisafe.wondershare.com/script/multi-language/download-redirect.js"></script>-->
  <script>
    /* global $ wsc */
    $(function () {
      wsc.common.init();
    });
  </script>
  <style>
    .famisafe-fixed-block {
      position: fixed;
      top: 50%;
      right: 10px;
      z-index: 9999;
      transform: translateY(-30%);
      -ms-transform: translateY(-30%);
      -webkit-transform: translateY(-30%);
    }
    .famisafe-fixed-close {
      font-size: 1.5rem;
      font-weight: 700;
      line-height: 1;
      color: #000;
      text-shadow: 0 1px 0 #fff;
      cursor: pointer;
      position: relative;
      top: -0.5rem;
      opacity: 0.5;
      text-align: right;
    }
    .famisafe-fixed-close:hover {
      opacity: 0.75;
    }
  </style>

  <script>
    if (document.getElementsByClassName("famisafe-fixed-close")[0]) {
      document.getElementsByClassName("famisafe-fixed-close")[0].addEventListener("click", function () {
        document.getElementsByClassName("famisafe-fixed-block")[0].style.display = "none";
      });
    }
  </script>
  <script src="https://famisafe.wondershare.com/script/common.js"></script>
  <script>
    $(function () {
      $(window).on("scroll", function () {
        if ($(window).scrollTop() > $("header#wsc-header").height() * 4) {
          $("aside#wsc-plugin-float").fadeIn("300");
        } else {
          $("aside#wsc-plugin-float").fadeOut("300");
        }
      });
    });
  </script>

  <!--web notification code-->
  <script>
    var _NOTIFICATION_CONFIG = {
      worker: "https://famisafe.wondershare.com/web-notification/sw.js", // sw.js的地址要根据自己的域名换成自己的静态资源路径
      wsNotificationJsPath: "https://dc-static.wondershare.com/notification/wsNotification.js", // 固定不变
      trackUrl: "https://prod-web.wondershare.cc/api/v1/prodweb/notification", // 请求通知权限订阅接口地址，固定不变
    };
    (function () {
      var d = document,
        g = d.createElement("script"),
        s = d.getElementsByTagName("script")[0];
      g.type = "text/javascript";
      g.async = true;
      g.defer = true;
      g.src = _NOTIFICATION_CONFIG.wsNotificationJsPath;
      s.parentNode.insertBefore(g, s);
    })();
  </script>
  <!-- end web notification code-->

  <script type="text/javascript" src="https://images.wondershare.com/scripts/affiliate.js"></script>
  <script>
    // 设置头部导航login、try now 链接
    var navArr = document.querySelectorAll(".wsc-header2020-navbar-dropdown");
    var edu_register = "https://famisafeapp.wondershare.com/edu-sign-up.html";
    var common_register = "https://famisafe.wondershare.com/main/sign-up?";
    var edu_login = "https://famisafeapp.wondershare.com/edu-login.html?";
    var common_login = "https://famisafeapp.wondershare.com/login.html?";
    for (var i = 0; i < navArr.length; i++) {
      navArr[i].onclick = function () {
        // 如果点击school
        if (this.classList.contains("nav-school")) {
          document.querySelector(".custom-login").href = edu_login;
          document.querySelector(".custom-register").href = edu_register;
        } else {
          document.querySelector(".custom-login").href = common_login;
          document.querySelector(".custom-register").href = common_register;
        }
      };
    }
  </script>
  <!-- 登陆注册-->
  <script>
    $(function () {
      // 测试环境url
      //var url = 'http://api.pc.phonedata.vip/v1/member/register';
      // 现网url
      var url = "https://api.famisafe.com/v1/member/register";
      // 渠道
      var adver = "sso";
      // 语言
      var lang = "en";
      $.ajax({
        type: "GET",
        //url: url+'?lang='+lang,
        url: url + "?lang=" + lang,
        //dataType: 'json',
        // with credentials
        xhrFields: {
          withCredentials: true,
        },
        // cross domain
        crossDomain: true,
        success: function (data) {
          $("form").prepend('<input name="' + data.data["csrf_param"] + '" type="hidden" value="' + data.data["csrf_token"] + '">');
        },
        error: function () {},
      });

      $(".btn_sign").click(function () {
        $.ajax({
          type: "POST",
          //url: url+'?f='+adver+'&lang='+lang,
          url: url + "?f=" + adver + "&lang=" + lang,
          // dataType: 'json',
          data: $("form").serialize(),
          // with credentials
          xhrFields: {
            withCredentials: true,
          },
          // cross domain
          crossDomain: true,
          success: function (data) {
            $('form input[name="' + data.data["csrf_param"] + '"]').val(data.data["csrf_token"]);
            if (data.status === "ok") {
              window.location.href = "https://web.famisafe.com/main/buy";
            } else {
              $(".col_input input").each(function () {
                var id = $(this).attr("id");
                if (data.msg[id]) {
                  $("#" + id)
                    .parent()
                    .find(".helptips")
                    .removeClass("success")
                    .addClass("error")
                    .text(data.msg[id]);
                } else {
                  $("#" + id)
                    .parent()
                    .find(".helptips")
                    .removeClass("error")
                    .addClass("success")
                    .text("");
                }
              });
              if (data.msg["agreeMe"]) {
                $(".agree_tips").find(".agree_info").addClass("show");
              } else {
                $(".agree_tips").find(".agree_info").removeClass("show");
              }
            }
          },
          error: function (data) {
            alert(data.message || "Network error!");
          },
        });
      });
    });
  </script>

  <script type="text/javascript" src="https://www.wondershare.com/ga360/js/ga360-add.js"></script>
  <script>
    if (
      !(
        navigator.userAgent.match(/Android/i) ||
        navigator.userAgent.match(/webOS/i) ||
        navigator.userAgent.match(/iPhone/i) ||
        navigator.userAgent.match(/iPad/i) ||
        navigator.userAgent.match(/iPod/i) ||
        navigator.userAgent.match(/BlackBerry/i) ||
        navigator.userAgent.match(/Windows Phone/i)
      )
    ) {
      var script = document.createElement("script");
      script.id = "ws-snippet";
      script.type = "text/javascript";
      script.async = true;
      script.src = "https://crm-static.wondershare.cc/chatbot/latest/assets/snippet.js?key=1da495ff-2895-e8b3-77d9-4ba3dd4bace0";
      document.body.appendChild(script);
    }
  </script>

  <script src="https://neveragain.allstatics.com/2019/assets/vendor/swiper7-bundle.min.js"></script>
  <script src="https://www.wondershare.com/assets/js/countTo.min.js"></script>
  <script>
    $(() => {
      // --- Configuration & State ---
      const isDesktop = window.innerWidth >= 1280;
      let isTransitioning = false;
      let lastSlideIndex = 0;
      let stepVal = true;
      let digitalTextSwiper, digitalImgSwiper;

      // --- Utility Functions ---
      const throttle = (func, limit) => {
        let inThrottle;
        return (...args) => {
          if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
          }
        };
      };

      const wait = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

      const setOpacity = (selector, opacity, duration) => {
        $(selector).css({
          opacity: opacity,
          transition: `opacity ${duration}ms ease-in-out`,
        });
        return wait(duration);
      };

      const isElementFullyInViewport = (el) => {
        if (!el) return false;
        const rect = el.getBoundingClientRect();
        return (
          rect.top >= 0 &&
          rect.left >= 0 &&
          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
      };

      // --- Event Handlers & Initializers ---

      const handleResize = () => {
        const newIsDesktop = window.innerWidth >= 1280;
        if (isDesktop !== newIsDesktop) {
          window.location.reload();
        }
      };
      $(window).on("resize", throttle(handleResize, 200));

      // Component-specific logic
      if (isDesktop) {
        // Digital Section Swipers (Desktop)
        digitalTextSwiper = new Swiper("#digital-text-swiper", {
          slidesPerView: 1,
          speed: 300,
          allowTouchMove: false,
          direction: "horizontal",
          effect: "fade",
          fadeEffect: { crossFade: true },
        });
        digitalImgSwiper = new Swiper("#digital-img-swiper", {
          slidesPerView: 1,
          speed: 300,
          allowTouchMove: false,
          effect: "fade",
          fadeEffect: { crossFade: true },
        });

        const customSlideTransition = async (targetIndex) => {
          if (isTransitioning) return;
          isTransitioning = true;
          try {
            await setOpacity("#digital-text-swiper, #digital-img-swiper", "0", 200);
            digitalTextSwiper.slideTo(targetIndex, 100, false);
            digitalImgSwiper.slideTo(targetIndex, 100, false);
            await wait(100);
            const textPromise = setOpacity("#digital-text-swiper", "1", 300);
            await wait(200);
            const imgPromise = setOpacity("#digital-img-swiper", "1", 400);
            await Promise.all([textPromise, imgPromise]);
          } finally {
            isTransitioning = false;
          }
        };

        const handleDigitalScroll = () => {
          const apartSection = $(".part-digital")[0];
          if (apartSection) {
            const totalSlides = digitalTextSwiper.slides.length;
            const offset = apartSection.getBoundingClientRect();
            if (offset.top < 72 && offset.bottom - window.innerHeight > 0) {
              const perc = Math.round((100 * Math.abs(offset.top)) / (offset.height - $(window).height()));
              const slideIndex = Math.min(Math.floor((perc * totalSlides) / 100), totalSlides);
              if (slideIndex !== lastSlideIndex && slideIndex !== digitalTextSwiper.activeIndex) {
                lastSlideIndex = slideIndex;
                customSlideTransition(slideIndex);
              }
            }
          }
        };
        $(window).on("scroll", throttle(handleDigitalScroll, 100));

        // Assets Swiper Hover (Desktop)
        $(".assetsSwiper .swiper-slide").mouseenter(function () {
          $(this).addClass("active").siblings().removeClass("active");
          $(".assetsSwiper-box").css("--assetIndex", $(this).index());
        });
      } else {
        // Digital Section Swiper (Mobile)
        $("#grow-swiper .social-media-slide").remove();
        new Swiper("#digital-text-swiper", {
          slidesPerView: 1.2,
          spaceBetween: 15,
          loop: true,
          allowTouchMove: true,
          autoplay: { delay: 3000, disableOnInteraction: false },
          breakpoints: { 576: { slidesPerView: 2 } },
          pagination: { el: "#digital-text-swiper .swiper-pagination", clickable: true },
        });

        // Safeguard & Grow Swipers (Mobile)
        new Swiper("#safeguard-mobile-swiper", {
          slidesPerView: 1,
          spaceBetween: 20,
          loop: true,
          // autoplay: { delay: 3000, disableOnInteraction: false },
          breakpoints: { 768: { slidesPerView: 2 } },
          pagination: { el: "#safeguard-mobile-swiper .swiper-pagination", clickable: true },
          navigation: { nextEl: "#safeguard-mobile-swiper .right-btn", prevEl: "#safeguard-mobile-swiper .left-btn" },
        });
        new Swiper("#grow-swiper", {
          slidesPerView: 1,
          spaceBetween: 20,
          loop: true,
          autoplay: { delay: 3000, disableOnInteraction: false },
          breakpoints: { 768: { slidesPerView: 2 } },
          pagination: { el: "#grow-swiper .swiper-pagination", clickable: true },
        });

        // Assets Swiper (Mobile)
        new Swiper("#assetsSwiper", {
          slidesPerView: 1,
          spaceBetween: 20,
          centeredSlides: true,
          autoplay: { delay: 3000, disableOnInteraction: false },
          loop: true,
          loopedSlides: 2,
          breakpoints: { 576: { slidesPerView: 1.6, spaceBetween: 20, centeredSlides: true } },
          pagination: { el: ".assetsSwiper-pagination", clickable: true },
        });
      }

      // Feature Swiper (Common)
      const featureTextSwiper = new Swiper("#feature-text-mobile-swiper", {
        slidesPerView: 1,
        spaceBetween: 15,
        effect: "fade",
        allowTouchMove: false,
        fadeEffect: { crossFade: true },
      });

      // 1. Define base Swiper options for #feature-swiper.
      const swiperOptions = {
        loop: true,
        autoplay: { delay: 3000, disableOnInteraction: false },
        pagination: { el: "#feature-swiper .swiper-pagination", clickable: true },
        on: {
          slideChange: function () {
            featureTextSwiper.slideTo(this.realIndex, 100, false);
          },
        },
        // 2. Define breakpoints for responsive behavior (desktop-first).
        breakpoints: {
          1600: { slidesPerView: 4, spaceBetween: 20 },
          992: { slidesPerView: 3, spaceBetween: 15 },
          576: { slidesPerView: 2, spaceBetween: 15 },
        },
      };

      // 3. Add mobile-specific "creative" effect for screens <= 576px.
      if (window.innerWidth <= 576) {
        Object.assign(swiperOptions, {
          effect: "creative",
          watchSlidesProgress: true,
          centeredSlides: true,
          slidesPerView: 1.8,
          spaceBetween: -20,
          creativeEffect: {
            prev: {
              shadow: false,
              translate: ["-85%", "5%", 0],
              rotate: [0, 0, -10],
              scale: 0.85,
              opacity: 1,
              origin: "bottom",
            },
            next: {
              shadow: false,
              translate: ["85%", "5%", 0],
              rotate: [0, 0, 10],
              scale: 0.85,
              opacity: 1,
              origin: "bottom",
            },
            limitProgress: 2,
          },
        });
      }

      // 4. Initialize Swiper and add desktop-only event listeners.
      const featureSwiper = new Swiper("#feature-swiper", swiperOptions);
      if (window.innerWidth > 576) {
        $("#feature-swiper").on("mouseenter", function () {
          featureSwiper.autoplay.stop();
        });
        $("#feature-swiper").on("mouseleave", function () {
          $(this).removeClass("active");
        });
      }

      // Count-up numbers (Common)
      const handleCountUpScroll = () => {
        const myElement = $(".count-box")[0];
        if (stepVal && isElementFullyInViewport(myElement)) {
          $(".count-num").countTo();
          stepVal = false;
        }
      };
      $(window).on("scroll", throttle(handleCountUpScroll, 200));
    });
  </script>
  <script>
    // 移动端按钮动画
    if (window.innerWidth <= 768) {
      const riveScript = document.createElement("script");
      riveScript.src = "https://famisafe.wondershare.com/script/rive-app-canvas.min.js";
      document.body.appendChild(riveScript);
      riveScript.onload = () => {
        const r = new rive.Rive({
          src: "https://famisafe.wondershare.com/riv/famisafe_dinosaur.riv",
          canvas: document.getElementById("RobotCanvas"),
          autoplay: true,
          stateMachines: "State Machine 1",
          artboard: "Artboard",
          onLoad: () => {
            // Ensure the drawing surface matches the canvas size and device pixel ratio
            r.resizeDrawingSurfaceToCanvas();
          },
        });
      };
    }
  </script>
</html>
