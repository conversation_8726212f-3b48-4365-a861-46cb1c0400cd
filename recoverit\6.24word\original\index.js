import "./index.scss";

$(() => {
  // 新页面去除冲突样式文件
  $('link[href="https://recoverit.wondershare.com/assets/app.bundle.css"]').remove();
  var customerSwiper = new Swiper("#customer-swiper", {
    slidesPerView: 1,
    centeredSlides: true,
    spaceBetween: 10,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    navigation: {
      nextEl: ".part-customer .right-btn",
      prevEl: ".part-customer .left-btn",
    },
    pagination: {
      el: "#customer-swiper .swiper-pagination",
      clickable: true,
    },
  });

  var methodsSwiper = new Swiper(".swiper-methods", {
    loop: true,
    allowTouchMove: false,
    on: {
      slideChange: function () {
        $(".methods-nav .col-md-4").eq(this.realIndex).addClass("active").siblings().removeClass("active");
      },
    },
    navigation: {
      prevEl: ".slidebtn-methods-prev",
      nextEl: ".slidebtn-methods-next",
    },
  });
  $(".methods-nav .col-md-4").on("click", function () {
    methodsSwiper.slideTo($(this).index() + 1);
  });

  // pc端卡片 mobile端swiper
  if (window.innerWidth < 992) {
    const tipSwiper = new Swiper("#best-swiper", {
      slidesPerView: 1,
      centeredSlides: true,
      spaceBetween: 15,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
      },
      pagination: {
        el: "#best-swiper .swiper-pagination",
        clickable: true,
      },
    });
  }

  // 监听数字滚动部分是否可见
  function isElementFullyInViewport(el) {
    var rect = el.getBoundingClientRect();
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    var windowWidth = window.innerWidth || document.documentElement.clientWidth;
    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= windowHeight && rect.right <= windowWidth;
  }

  var stepVal = true;
  function handleScroll() {
    var myElement = $(".count-box")[0]; // 获取DOM元素
    if (myElement && isElementFullyInViewport(myElement) && stepVal) {
      $(".count-num").countTo();
      stepVal = false;
    }
  }

  // 使用防抖优化滚动事件
  var scrollTimeout;
  $(window).on("scroll", function () {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(handleScroll, 100);
  });
});
