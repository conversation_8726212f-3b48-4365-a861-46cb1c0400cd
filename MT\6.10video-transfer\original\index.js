import "./index.scss";

$(() => {
  const swiperScenarios = new Swiper("#swiper-scenarios", {
    slidesPerView: 1,
    spaceBetween: 30,
    loop: true,

    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    on: {
      slideChange: function () {
        const currentSlide = this.realIndex;
        $(".tab-item").removeClass("active").eq(currentSlide).addClass("active");
      },
    },
    navigation: {
      nextEl: ".part-scenarios .right-btn",
      prevEl: ".part-scenarios .left-btn",
    },
  });
  $(" .tab-item").on("click", function () {
    const currentSlide = $(this).index();
    swiperScenarios.slideToLoop(currentSlide);
  });

  $(".part-solutions .arrow").on("click", function () {
    $(".part-solutions .step-wrapper").slideToggle();
    $(this).toggleClass("active");
    $(".part-solutions .check-title").toggleClass("d-none");
  });
  $(".part-solutions .grey-arrow-wrapper").on("click", function () {
    $(".part-solutions .step-wrapper").slideToggle();
    $(this).toggleClass("active");
    $(".part-solutions .check-title").toggleClass("d-none");
  });

  // 切换标签和折叠详情的函数
  let currentTabIndex = 0;
  const tabs = ["#high-speed-tab", "#all-major-formats-tab", "#no-quality-loss-tab"];
  const switchInterval = 3000;

  $(tabs[0]).tab("show");

  // 切换标签和折叠详情的函数
  const switchTab = () => {
    currentTabIndex = (currentTabIndex + 1) % tabs.length;
    $(tabs[currentTabIndex]).tab("show");
    $(tabs[currentTabIndex].replace("-tab", "-collapse")).collapse("show");
  };

  if (window.innerWidth > 768) {
    // 设置自动切换定时器
    let autoSwitchInterval = setInterval(switchTab, switchInterval);

    // 用户手动点击标签时重置计时器
    $(".nav-item").on("click", function () {
      clearInterval(autoSwitchInterval);
      const clickedTabId = $(this).attr("id");
      currentTabIndex = Math.max(0, tabs.indexOf(`#${clickedTabId}`));
      autoSwitchInterval = setInterval(switchTab, switchInterval);
    });
  }

  if (window.innerWidth < 1280) {
    const methodsSwiper = new Swiper("#swiper-methods", {
      slidesPerView: 1.0,
      // centeredSlides: true,
      spaceBetween: 30,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        992: {
          slidesPerView: 3,
          spaceBetween: 15,
        },
        576: {
          slidesPerView: 2,
          spaceBetween: 15,
        },
      },
      pagination: {
        el: "#swiper-methods .swiper-pagination",
        clickable: true,
      },
    });
    const featuresSwiper = new Swiper("#swiper-features", {
      slidesPerView: 1.0,
      // centeredSlides: true,
      spaceBetween: 30,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        992: {
          slidesPerView: 3,
          spaceBetween: 15,
        },
        768: {
          slidesPerView: 2,
          spaceBetween: 15,
        },
      },
      pagination: {
        el: "#swiper-features .swiper-pagination",
        clickable: true,
      },
    });
    const scrollCardSwiper = new Swiper("#swiper-scrollCard", {
      slidesPerView: 1.0,
      // centeredSlides: true,
      spaceBetween: 30,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
          spaceBetween: 15,
        },
      },
      pagination: {
        el: "#swiper-scrollCard .swiper-pagination",
        clickable: true,
      },
    });
  }

  $(".part-methods .step-btn").on("click", function () {
    $(".part-methods .step-detail").slideToggle();
    $(".part-methods .step-btn").toggleClass("active");
  });

  // 监听数字滚动部分是否可见
  function isElementFullyInViewport(el) {
    var rect = el.getBoundingClientRect();
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    var windowWidth = window.innerWidth || document.documentElement.clientWidth;
    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= windowHeight && rect.right <= windowWidth;
  }

  var stepVal = true;
  function formatWithComma(value, options) {
    return value.toLocaleString(); // 使用内建的 toLocaleString() 方法来添加逗号
  }

  function handleScroll() {
    var myElement = $(".growth-numbers-item")[0]; // 获取DOM元素
    if (myElement && isElementFullyInViewport(myElement) && stepVal) {
      $(".count-num").countTo({ from: 0, formatter: formatWithComma });
      stepVal = false;
    }
  }
  // 使用防抖优化滚动事件
  var scrollTimeout;
  $(window).on("scroll", function () {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(handleScroll, 100);
  });

  if (window.innerWidth > 1280) {
    gsap.registerPlugin(ScrollTrigger);
    let cards = gsap.utils.toArray(".scrollCard");

    // 预加载所有图片
    const preloadImages = () => {
      const images = document.querySelectorAll(".scrollCard img");
      const imagePromises = Array.from(images).map((img) => {
        return new Promise((resolve, reject) => {
          if (img.complete) {
            resolve();
          } else {
            img.onload = resolve;
            img.onerror = reject;
          }
        });
      });
      return Promise.all(imagePromises);
    };

    // 等待所有图片加载完成后再初始化动画
    preloadImages()
      .then(() => {
        let stackHeight = window.innerHeight * 0.1;
        cards.forEach((card, i) => {
          if (i !== cards.length - 1) {
            gsap.fromTo(
              card,
              { opacity: 1 },
              {
                scale: 0.7,
                opacity: 0,
                ease: "power1.out",
                scrollTrigger: {
                  trigger: card,
                  pin: true,
                  // markers: true,
                  scrub: 0.5,
                  start: "top " + stackHeight,
                  end: "top " + stackHeight,
                  pinSpacing: false,
                  endTrigger: ".last-card",
                  invalidateOnRefresh: true,
                },
              }
            );
          }
        });
      })
      .catch((error) => {
        console.error("图片加载失败:", error);
      });

    window.addEventListener("resize", function () {
      ScrollTrigger.refresh(); // 重新计算动画的触发点和位置
    });
    // 添加ResizeObserver来监听页面高度变化
    const resizeObserver = new ResizeObserver((entries) => {
      // 使用防抖，避免频繁触发
      clearTimeout(window.resizeTimeout);
      window.resizeTimeout = setTimeout(() => {
        ScrollTrigger.refresh();
        console.log("resize");
      }, 500);
    });

    // 监听整个文档的高度变化
    resizeObserver.observe(document.documentElement);

    // 监听body的高度变化
    resizeObserver.observe(document.body);

    // 在组件销毁时断开观察
    $(window).on("unload", () => {
      resizeObserver.disconnect();
    });
  }
});
