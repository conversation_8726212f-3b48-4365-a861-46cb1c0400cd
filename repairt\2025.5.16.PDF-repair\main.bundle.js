/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 787:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

eval("\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\nvar injectStylesIntoStyleTag = __webpack_require__(72);\nvar injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleDomAPI.js\nvar styleDomAPI = __webpack_require__(825);\nvar styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertBySelector.js\nvar insertBySelector = __webpack_require__(659);\nvar insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\nvar setAttributesWithoutAttributes = __webpack_require__(56);\nvar setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertStyleElement.js\nvar insertStyleElement = __webpack_require__(540);\nvar insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleTagTransform.js\nvar styleTagTransform = __webpack_require__(113);\nvar styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);\n// EXTERNAL MODULE: ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/index.scss\nvar cjs_js_src = __webpack_require__(2);\n;// ./src/index.scss\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (styleTagTransform_default());\noptions.setAttributes = (setAttributesWithoutAttributes_default());\noptions.insert = insertBySelector_default().bind(null, \"head\");\noptions.domAPI = (styleDomAPI_default());\noptions.insertStyleElement = (insertStyleElement_default());\n\nvar update = injectStylesIntoStyleTag_default()(cjs_js_src/* default */.A, options);\n\n\n\n\n       /* harmony default export */ const src = (cjs_js_src/* default */.A && cjs_js_src/* default */.A.locals ? cjs_js_src/* default */.A.locals : undefined);\n\n;// ./src/index.js\n\n$(() => {\n  // 自动切换part-step标签页，每3秒切换一次\n  let currentStepTabIndex = 0;\n  const stepTabs = [\"#step-1-tab\", \"#step-2-tab\", \"#step-3-tab\"];\n  const stepSwitchInterval = 3000;\n\n  // 切换标签的函数\n  const switchStepTab = () => {\n    currentStepTabIndex = (currentStepTabIndex + 1) % stepTabs.length;\n    $(stepTabs[currentStepTabIndex]).tab(\"show\");\n  };\n\n  // 设置自动切换定时器\n  let autoSwitchInterval = setInterval(switchStepTab, stepSwitchInterval);\n  // 用户手动点击标签时重置计时器\n  $(\".part-step .nav-item\").on(\"click\", function () {\n    clearInterval(autoSwitchInterval);\n    const clickedTabId = $(this).attr(\"id\");\n    currentStepTabIndex = Math.max(0, stepTabs.indexOf(`#${clickedTabId}`));\n    autoSwitchInterval = setInterval(switchStepTab, stepSwitchInterval);\n  });\n  var customerSwiper = new Swiper(\"#customer-swiper\", {\n    slidesPerView: 1,\n    centeredSlides: true,\n    spaceBetween: 10,\n    loop: true,\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false\n    },\n    navigation: {\n      nextEl: \".part-customer .right-btn\",\n      prevEl: \".part-customer .left-btn\"\n    },\n    pagination: {\n      el: \"#customer-swiper .swiper-pagination\",\n      clickable: true\n    }\n  });\n  if (window.innerWidth < 992) {\n    const devicesSwiper = new Swiper(\"#swiper-tips\", {\n      slidesPerView: 1.01,\n      centeredSlides: true,\n      spaceBetween: 15,\n      loop: true,\n      loopedSlides: 2,\n      autoplay: {\n        delay: 2500,\n        disableOnInteraction: false\n      },\n      breakpoints: {\n        768: {\n          slidesPerView: 2\n        }\n      },\n      pagination: {\n        el: \"#swiper-tips .swiper-pagination\",\n        clickable: true\n      }\n    });\n  }\n  if (window.innerWidth > 1279) {\n    $(\".assetsSwiper .swiper-slide\").mouseenter(function () {\n      $(this).addClass(\"active\").siblings().removeClass(\"active\");\n      $(\".assetsSwiper-box\").css(\"--assetIndex\", $(this).index());\n    });\n  } else {\n    var assetsSwiper = new Swiper(\"#assetsSwiper\", {\n      slidesPerView: 1,\n      spaceBetween: 20,\n      centeredSlides: true,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      loop: true,\n      loopedSlides: 2,\n      breakpoints: {\n        768: {\n          slidesPerView: 1,\n          spaceBetween: 20,\n          centeredSlides: true\n        }\n      },\n      pagination: {\n        el: \".assetsSwiper-pagination\",\n        clickable: true\n      }\n    });\n  }\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///787\n");

/***/ }),

/***/ 2:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(354);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(314);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `*{margin:0;padding:0;box-sizing:border-box}main{background-color:#f5f8ff}main h1,main h2,main h3,main h4,main h5,main h6,main p,main div{margin-bottom:0;color:#000;font-family:\"Mulish\",sans-serif}main h1,main h2{font-size:2.5rem;font-weight:700;text-align:center}@media(max-width: 768px){main h1,main h2{font-size:24px;text-align:center}}@media(max-width: 576px){main .display-3{font-size:2.5rem}}main .opacity-7{opacity:.7}main .text-blue{color:#2a80ff}main .btn-wrapper{display:flex;justify-content:center;gap:1rem}@media(max-width: 768px){main .btn-wrapper{flex-direction:column;gap:8px}}main .btn{margin:0;border-radius:12px;text-transform:capitalize;display:flex;align-items:center;justify-content:center}@media(min-width: 992px){main .btn{height:51px}main .btn.btn-lg{height:4rem}}@media(max-width: 768px){main .btn{display:block;vertical-align:baseline}}main .gradient-text{background:linear-gradient(138.99deg, #0087ff 8.45%, #6ad6ff 89.48%);color:transparent;background-clip:text;-webkit-background-clip:text}main .btn-download{background:linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);border:none;color:#fff;background-color:#0458ff}main .btn-download:hover{color:#fff;background:linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)),linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),linear-gradient(0deg, #0055fb, #0055fb);background-color:#0458ff}main .part-banner{position:relative}main .part-banner .small-title{color:#13171a;font-size:1.875rem;margin-bottom:1rem;font-weight:700;line-height:90%}@media(max-width: 768px){main .part-banner{background:linear-gradient(180deg, #c0eeff 0%, #f8fbff 96.46%)}}main .part-banner .banner-left-download{z-index:10;position:absolute;height:100%;top:0;left:0;width:33%}@media(max-width: 768px){main .part-banner .banner-left-download{display:none}}main .part-banner .banner-right-download{z-index:10;position:absolute;height:100%;top:0;right:0;width:33%}@media(max-width: 768px){main .part-banner .banner-right-download{display:none}}main .part-banner .video-wrapper{line-height:0;font-size:0}main .part-banner .video-wrapper video{height:100%;width:100%;object-fit:cover;min-height:533px}@media(max-width: 768px){main .part-banner .video-wrapper{display:none}}main .part-banner .part-banner-content{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;margin:0 auto}@media(max-width: 768px){main .part-banner .part-banner-content{position:relative;padding:3rem 0;text-align:center}}main .part-banner .part-banner-content h1{color:#13171a;line-height:110%}@media(max-width: 576px){main .part-banner .part-banner-content h1{font-size:26px}}main .part-banner .part-banner-content h1 span{color:transparent;background:linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),linear-gradient(0deg, #0055fb, #0055fb);background-clip:text;-webkit-background-clip:text}main .part-banner .part-banner-content h2{font-size:1.875rem;font-weight:700;line-height:100%;color:#13171a}@media(max-width: 576px){main .part-banner .part-banner-content h2{font-size:1.25rem;margin-bottom:1rem}}main .part-banner .part-banner-content .btn{min-width:289px}@media(max-width: 768px){main .part-banner .part-banner-content .btn{min-width:unset}}main .part-banner .part-banner-content .logo-list{display:flex;align-items:center;justify-content:center;gap:14.4px}main .part-banner .part-banner-content .logo-list .split-line{position:relative;height:16px;width:2px;top:70%;border-radius:1.5px;background-color:rgba(0,0,0,.7)}@media(max-width: 768px){main .part-banner .part-banner-content .logo-list{flex-wrap:wrap}}@media(max-width: 768px){main .part-banner .part-banner-content .logo-list .logo-img{flex:1;max-height:24px;object-fit:contain}}main .part-files{background-color:#fff}main .part-files .file-box{height:100%;display:flex;flex-direction:column;border-radius:1rem;overflow:hidden}main .part-files .file-box .btn-outline-action{border:1px solid #2e8eff;color:#2e8eff}main .part-files .file-box .btn-outline-action:hover,main .part-files .file-box .btn-outline-action:focus{background-color:#2e8eff;color:#fff}main .part-files .file-box .file-box-content{background-color:#f9f9f9;padding:1.5rem 2rem;flex:1;display:flex;flex-direction:column}main .part-files .file-box .file-box-content p{font-size:.875rem;color:#5f5f5f}main .part-files .file-box .file-box-content p .content-title{font-weight:700;color:#000}@media(max-width: 576px){main .part-files .file-box .file-box-content{padding:8px}}main .part-files .file-box .file-box-content .box-title{font-weight:700;font-size:1.25rem;color:#000;text-decoration:none;display:inline-block}main .part-files .file-box .file-box-content .box-title:hover{text-decoration:underline}@media(max-width: 576px){main .part-files .col-6{padding-right:8px;padding-left:8px}main .part-files .col-6:nth-child(odd){padding-right:4px}main .part-files .col-6:nth-child(even){padding-left:4px}}.part-step{background:url(https://images.wondershare.com/repairit/images2025/PDF-repair/step-bg.svg) no-repeat center center/cover}.part-step .nav{display:flex;flex-direction:column;gap:1.5rem;height:100%}@media(max-width: 768px){.part-step .nav{gap:1rem}}.part-step .nav .nav-item{flex:1}.part-step .nav .nav-item.active .step-item{position:relative;box-shadow:0px 8px 12px 0px #7cc5dc3d;border-color:transparent;border:unset}.part-step .nav .nav-item.active .step-item::after{content:\"\";position:absolute;inset:0;border-radius:1rem;padding:1px;background:linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);mask:linear-gradient(#000 0 0) content-box,linear-gradient(#000 0 0);mask-composite:exclude;pointer-events:none}.part-step .nav .nav-item.active .step-item .step-item-number{font-size:2.625rem;font-weight:700}.part-step .nav .nav-item.active .step-item .right-content .title{font-size:1.25rem;font-weight:700}.part-step .nav .nav-item.active .step-item .right-content .detail{display:block}.part-step .nav .nav-item .step-item{height:100%;border-radius:1rem;display:flex;justify-content:flex-start;align-items:center;gap:1.25rem;padding:1.5rem;color:#000;cursor:pointer;position:relative;overflow:hidden;background-color:#fff;border:1px solid #bce0fe}.part-step .nav .nav-item .step-item .step-item-number{width:22px;display:flex;align-items:center;justify-content:center;font-weight:700}.part-step .nav .nav-item .step-item .right-content{display:flex;flex-direction:column;justify-content:center}.part-step .nav .nav-item .step-item .right-content .detail{display:none;color:#787878;font-size:14px}.part-step .feature-list{display:flex;justify-content:center;gap:1rem}@media(max-width: 768px){.part-step .feature-list{flex-direction:column;justify-content:flex-start;align-items:flex-start}}.part-step .feature-list .feature-item{flex:1;display:flex;justify-content:center;align-items:center;padding:1.5rem 1rem;gap:4px}@media(max-width: 768px){.part-step .feature-list .feature-item{padding:0 1rem}.part-step .feature-list .feature-item img{width:36px}}.part-step .feature-list .feature-item .feature-item-detail{font-size:1.25rem;font-weight:500;color:#444}.part-step .note-box{background-color:#ecfaff;border-radius:1.375rem;padding:1.125rem 1.5rem;display:flex;align-items:flex-start;gap:.9375rem}.part-step .note-box .left-icon{flex-shrink:0}.part-step .note-box .right-content .content-detail{font-size:.875rem;font-weight:500;color:#636363}main .part-highlights{background-color:#f5f8ff}main .part-highlights .btn-white{color:#0c7dfa}main .part-highlights .blue-cricle1{position:absolute;left:41%;bottom:42%;width:23%;animation:icon-rotate 3s linear infinite}main .part-highlights .blue-cricle2{position:absolute;left:40%;bottom:42%;width:21%;animation:icon-rotate 3s linear infinite}main .part-highlights .win-icon-link{position:absolute;left:56%;bottom:55%;width:27%}main .part-highlights .mac-icon-link{position:absolute;left:22%;bottom:55%;width:27%}main .part-highlights .assetsSwiper-box{position:relative;margin-bottom:5rem}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-wrapper{gap:16px;justify-content:space-between}}@media(min-width: 1280px)and (max-width: 1600px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-wrapper{gap:8px}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide{width:7.1%;display:block;height:auto;overflow:hidden;border-radius:1rem;position:relative;transition:.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);min-height:430px}}@media(min-width: 1280px)and (max-width: 1600px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide{min-height:400px}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style{padding:1.875rem 1.5rem;width:100%;height:100%;position:relative;border-radius:1.5rem;color:#fff;background:linear-gradient(269.24deg, #6fb3ff -4.48%, #1989ff 54.28%);overflow:hidden}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .right-icon{position:absolute;right:2.1rem;top:1.875rem}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .box-title{color:#fff;bottom:1.125rem;left:2.125rem;position:absolute;font-size:1.25rem;writing-mode:sideways-lr;height:525px;z-index:3;width:0%;transition:.8s cubic-bezier(0.05, 0.61, 0.41, 0.95)}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content{opacity:0;height:calc(100% - 3.875rem);width:100%;display:flex;margin-top:3.875rem;padding-top:2.25rem;border-top:1px solid rgba(242,242,242,.3);justify-content:space-between;min-width:774px;transition:.8s cubic-bezier(0.05, 0.61, 0.41, 0.95)}}@media(min-width: 1280px)and (max-width: 1600px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content{min-width:634px}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .left-img-wrapper{flex:0 0 45%;margin-top:-2.25rem;margin-left:-1.5rem;margin-bottom:-1.875rem}}@media(min-width: 1280px)and (max-width: 1600px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .left-img-wrapper{flex:0 0 51%}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .right-detial-wrapper{flex:1;padding-top:1.5rem;display:flex;justify-content:space-between;flex-direction:column;color:#fff}}@media(min-width: 1280px)and (max-width: 1600px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .right-detial-wrapper{padding-top:unset}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active{width:58.3%;opacity:1}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active .box-style .box-title{transform:rotate(90deg);transform-origin:bottom left;bottom:95%;font-size:2rem;font-weight:600}}@media(min-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active .box-style .card-content{opacity:1}}@media(min-width: 1280px){@keyframes fadeIn{from{visibility:hidden}to{visibility:visible}}}@media(max-width: 1280px){main .part-highlights .assetsSwiper-box .box-style{height:100%;background:#1888fe;border-radius:1.5rem;overflow:hidden;padding:1.5rem}main .part-highlights .assetsSwiper-box .box-style .top-content{display:flex;justify-content:space-between;align-items:center;padding-bottom:1.5rem;border-bottom:1px solid #f2f2f2}main .part-highlights .assetsSwiper-box .box-style .top-content .right-icon{width:2.5rem}main .part-highlights .assetsSwiper-box .box-style .top-content .box-title{font-size:2rem;font-weight:600;color:#fff}}@media(max-width: 1280px)and (max-width: 768px){main .part-highlights .assetsSwiper-box .box-style .top-content .box-title{font-size:1.5rem}}@media(max-width: 1280px){main .part-highlights .assetsSwiper-box .box-style .card-content{width:100%;display:flex;justify-content:space-between;gap:1rem}}@media(max-width: 1280px)and (max-width: 768px){main .part-highlights .assetsSwiper-box .box-style .card-content{flex-direction:column;align-items:center}}@media(max-width: 1280px){main .part-highlights .assetsSwiper-box .box-style .card-content .left-img-wrapper{flex:0 0 35%;max-width:240px}}@media(max-width: 1280px)and (max-width: 768px){main .part-highlights .assetsSwiper-box .box-style .card-content .left-img-wrapper{order:2}}@media(max-width: 1280px){main .part-highlights .assetsSwiper-box .box-style .card-content .right-detial-wrapper{flex:1;padding-top:1.5rem;display:flex;justify-content:space-between;flex-direction:column}}@media(max-width: 1280px)and (max-width: 768px){main .part-highlights .assetsSwiper-box .box-style .card-content .right-detial-wrapper{order:1}}@media(max-width: 1280px){main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide{height:auto}main .part-highlights .assetsSwiper-box .assetsSwiper .rounded-16{border-radius:8px}}@keyframes move{0%{transform:translateX(5px)}50%{transform:translateX(-3px)}100%{transform:translateX(5px)}}main .part-customer{background-color:#f5f8ff}main .part-customer .customer-wrapper{border-radius:2rem;overflow:hidden;background-color:#fff;display:flex;flex-direction:column;height:100%}main .part-customer .customer-wrapper .customer-img{position:relative}main .part-customer .customer-wrapper .customer-img .customer-info-list{position:absolute;top:20px;left:40px;display:flex;align-items:center;font-size:16px;color:#fff;font-weight:600;gap:24px}@media(max-width: 768px){main .part-customer .customer-wrapper .customer-img .customer-info-list{display:none}}main .part-customer .customer-wrapper .customer-img .customer-info-list.right{right:32px;left:unset}main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-title,main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-profession,main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-age{position:relative;color:#fff;background-color:rgba(0,0,0,.21);border-radius:0 3px 3px 0;padding-right:6px}main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-title::before,main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-profession::before,main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-age::before{content:\"\";position:absolute;height:100%;aspect-ratio:22/31;left:0;top:0;transform:translateX(-97%);background:url(https://images.wondershare.com/repairit/images2025/PPT-repair/left-tip.png) no-repeat center center/contain}main .part-customer .customer-wrapper .customer-detail{flex:1;display:flex;gap:.5rem}@media(max-width: 992px){main .part-customer .customer-wrapper .customer-detail{flex-direction:column}}main .part-customer .customer-wrapper .customer-detail .problem-wrapper{flex:1 1 41.2%;padding:1.875rem 1.5rem;display:flex;align-items:flex-start;justify-content:center;gap:12px}@media(max-width: 992px){main .part-customer .customer-wrapper .customer-detail .problem-wrapper{padding:1rem;padding-bottom:0}}main .part-customer .customer-wrapper .customer-detail .problem-wrapper .left-icon{width:4.5rem;flex-shrink:0}@media(max-width: 576px){main .part-customer .customer-wrapper .customer-detail .problem-wrapper .left-icon{width:2.5rem}}main .part-customer .customer-wrapper .customer-detail .problem-wrapper .right-content .title{font-weight:800;font-size:1.5rem;color:#000;margin-bottom:12px}main .part-customer .customer-wrapper .customer-detail .problem-wrapper .right-content .detail{font-size:1.125rem;color:#3c3c3c}main .part-customer .customer-wrapper .customer-detail .customer-detail-dividing{border-right:1px dashed rgba(0,0,0,.07)}main .part-customer .customer-wrapper .customer-detail .how-wrapper{flex:1 1 58.8%;padding:1.875rem 1.5rem;display:flex;align-items:flex-start;justify-content:center;gap:12px}@media(max-width: 992px){main .part-customer .customer-wrapper .customer-detail .how-wrapper{padding:1rem;padding-top:0}}@keyframes icon-rotate{0%{transform:rotate(360deg)}100%{transform:rotate(0deg)}}main .part-customer .customer-wrapper .customer-detail .how-wrapper .left-icon{text-decoration:none;animation:icon-rotate 3s linear infinite;flex-shrink:0;width:4.5rem}@media(max-width: 576px){main .part-customer .customer-wrapper .customer-detail .how-wrapper .left-icon{width:2.5rem}}main .part-customer .customer-wrapper .customer-detail .how-wrapper .right-content .title{font-weight:800;font-size:1.5rem;color:#000;margin-bottom:12px}main .part-customer .customer-wrapper .customer-detail .how-wrapper .right-content .detail{font-size:1.125rem;color:#3c3c3c}main .part-customer .left-btn,main .part-customer .right-btn{background-color:silver;width:2.25rem;aspect-ratio:1/1;display:flex;justify-content:center;align-items:center;border-radius:50%;cursor:pointer;position:absolute;top:36%}@media(max-width: 576px){main .part-customer .left-btn,main .part-customer .right-btn{display:none}}main .part-customer .left-btn:hover,main .part-customer .right-btn:hover{background-color:#006dff}main .part-customer .right-btn{right:-3.25rem}@media(max-width: 768px){main .part-customer .right-btn{right:1.55rem}}main .part-customer .left-btn{left:-3.25rem}@media(max-width: 768px){main .part-customer .left-btn{left:1.55rem}}.part-faq .accordion-item{padding:2rem 0;border-bottom:1px solid #e2e2e2}@media(max-width: 576px){.part-faq .accordion-item{padding:1.5rem 0}}.part-faq .open-icon{display:none;color:inherit}.part-faq .close-icon{display:block;color:inherit}.part-faq .accordion-item [aria-expanded=true] .faq-title{font-weight:700;color:#4d99ff}.part-faq .accordion-item [aria-expanded=true] .open-icon{display:block}.part-faq .accordion-item [aria-expanded=true] .close-icon{display:none}.part-faq .accordion-item .faq-title{display:flex;align-items:center;justify-content:left;gap:8px;flex-shrink:0;max-width:95%;font-weight:600;font-size:1.5rem}@media(max-width: 576px){.part-faq .accordion-item .faq-title{font-size:1.25rem;max-width:90%}}.part-faq .accordion-item .faq-title svg{flex-shrink:0}.part-faq .accordion-item .faq-title .title-desc{color:inherit}@media(min-width: 992px){main .part-tip #swiper-tips .swiper-wrapper{gap:1.875rem;flex-wrap:wrap}main .part-tip #swiper-tips .swiper-wrapper .swiper-slide{flex:1 1 calc(33% - 1.875rem)}}main .part-tip .tip-item{border-radius:2rem;position:relative;overflow:hidden;background-size:cover;background-position:center;background-repeat:no-repeat;padding:3rem 2rem;color:#000;z-index:3;transition:all .2s;display:flex;justify-content:center;align-items:center;flex-direction:column;background-color:#fff}main .part-tip .tip-item:hover{box-shadow:0px 0px 12px 0px #00d1ff4d}main .part-tip .tip-item:hover::after{content:\"\";position:absolute;inset:0;border-radius:2rem;padding:2px;background:linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),linear-gradient(127.35deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 36.09%);mask:linear-gradient(#000 0 0) content-box,linear-gradient(#000 0 0);-webkit-mask-image:linear-gradient(#000 0 0) content-box,linear-gradient(#000 0 0);mask-composite:exclude}main .part-tip .tip-item:hover .text-detail{top:2px}main .part-tip .tip-item .tip-icon{height:6rem;width:6rem}main .part-tip .tip-item .text-detail{position:absolute;width:calc(100% - 4px);height:calc(100% - 4px);padding:0rem 2rem;display:flex;justify-content:center;flex-direction:column;z-index:2;border-radius:2rem;left:2px;top:100%;overflow:hidden;background-image:url(https://images.wondershare.com/repairit/images2025/PPT-repair/tip-card-bg.svg) !important;transition:all .3s;background-size:cover;background-position:center;background-repeat:no-repeat}main .part-links .part-links-line{height:100%;display:flex;flex-direction:column;justify-content:center}main .part-links .line-border{border-right:1px solid rgba(0,0,0,.3);border-left:1px solid rgba(0,0,0,.3)}@media(max-width: 1280px){main .part-links .line-border{border-right:unset}}@media(max-width: 768px){main .part-links .line-border{border-left:unset}}main .part-links .text-link{font-size:.875rem;color:rgba(0,0,0,.7);margin-top:1.5rem;display:block;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}main .part-links .text-link:hover{color:#0055fb}main .part-links .part-links-videos{height:100%;display:flex;flex-direction:column;justify-content:space-between}main .part-links .part-links-videos .video-wrapper{border-radius:.75rem}@media(max-width: 1280px){main .part-links .part-links-videos{flex-direction:row;padding-top:2rem}}@media(max-width: 576px){main .part-links .part-links-videos{display:block}}main .part-links .text-line4{display:-webkit-box;-webkit-line-clamp:4;-webkit-box-orient:vertical;overflow:hidden}@keyframes changeWidth{0%{width:0}50%{width:100%}100%{width:0}}main .part-feature .intelligence-content{position:absolute;top:0;left:0%;z-index:2}main .part-feature .intelligence-item{border-radius:1.5rem;overflow:hidden;background-color:#fff;color:#000}main .part-feature .intelligence-item .compare-before{position:absolute;width:50%;height:100%;left:0;top:0;background-size:auto 100%;background-repeat:no-repeat;z-index:2;animation:changeWidth 6s linear infinite}main .part-feature .intelligence-item .compare-before::after{content:\"\";width:2px;height:100%;background:#fff;position:absolute;right:0;top:0}main .part-feature .intelligence-item .compare-before::before{content:\"\";background-image:url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);background-size:contain;background-position:center;background-repeat:no-repeat;width:5rem;height:3rem;position:absolute;right:0;top:50%;transform:translate(50%, -50%);z-index:3}@media(max-width: 768px){main .part-feature .intelligence-item .compare-before::before{width:3rem;height:2rem}}main .part-feature .intelligence-item .compare-before.compare-before-1{background-image:url(https://images.wondershare.com/repairit/images2025/PDF-repair/word-file-repair-before.jpg)}main .part-feature .intelligence-item .compare-before.compare-before-2{background-image:url(https://images.wondershare.com/repairit/images2025/PDF-repair/excel-file-repair-before.jpg);animation:changeWidth 8s linear infinite}main .part-feature .intelligence-item .compare-before.compare-before-3{background-image:url(https://images.wondershare.com/repairit/images2025/PDF-repair/powerpoint-file-repair-before.jpg);animation:changeWidth 7s linear infinite}main .part-feature .intelligence-item .item-link{color:#000;display:flex;align-items:center;justify-content:center}@media(max-width: 768px){main .part-feature .intelligence-item .item-link{margin-bottom:.5rem}}main .part-feature .intelligence-item .item-link .normal-arrow{display:inline}@media(max-width: 768px){main .part-feature .intelligence-item .item-link .normal-arrow,.active-arrow{height:2.5rem;width:2.5rem}}main .part-feature .intelligence-item .item-link .active-arrow{display:none}main .part-feature .intelligence-item .item-link:hover{color:#0458ff}main .part-feature .intelligence-item .item-link:hover .normal-arrow{display:none}main .part-feature .intelligence-item .item-link:hover .active-arrow{display:inline}main .part-footer{background-image:url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);background-size:cover;background-position:center;background-repeat:no-repeat}main .part-footer .btn-download{padding-top:18.5px;padding-bottom:18.5px}@media(max-width: 992px){main .part-footer .btn-download{padding-top:15.2px;padding-bottom:15.2px}}main .part-footer .part-footer-logo{height:4rem;width:14.5rem;margin:0 auto}@media(max-width: 576px){main .part-footer .display-2{font-size:2.25rem}main .part-footer a{display:block}main .part-footer .btn-outline-action{background-color:#fff;vertical-align:text-bottom}main .part-footer .btn-outline-action:hover{color:#fff;background-color:#006dff;border-color:#006dff}}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/index.scss\"],\"names\":[],\"mappings\":\"AAAA,EACE,QAAA,CACA,SAAA,CACA,qBAAA,CAyEF,KACE,wBAAA,CAEF,gEAQE,eAAA,CACA,UAAA,CACA,+BAAA,CAGF,gBAEE,gBAAA,CACA,eAAA,CACA,iBAAA,CAGF,yBACE,gBAEE,cAAA,CACA,iBAAA,CAAA,CAIJ,yBACE,gBACE,gBAAA,CAAA,CAIJ,gBACE,UAAA,CAGF,gBACE,aAAA,CAGF,kBACE,YAAA,CACA,sBAAA,CACA,QAAA,CAGF,yBACE,kBACE,qBAAA,CACA,OAAA,CAAA,CAIJ,UACE,QAAA,CACA,kBAAA,CACA,yBAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CAOF,yBACE,UACE,WAAA,CAEF,iBACE,WAAA,CAAA,CASJ,yBACE,UACE,aAAA,CACA,uBAAA,CAAA,CAIJ,oBACE,oEAAA,CACA,iBAAA,CACA,oBAAA,CACA,4BAAA,CAGF,mBACE,8DAAA,CACA,WAAA,CACA,UAAA,CACA,wBAAA,CAGF,yBACE,UAAA,CACA,qLAAA,CAEA,wBAAA,CAGF,kBACE,iBAAA,CACA,+BACE,aAAA,CACA,kBAAA,CACA,kBAAA,CACA,eAAA,CACA,eAAA,CAIJ,yBACE,kBACE,8DAAA,CAAA,CAIJ,wCACE,UAAA,CACA,iBAAA,CACA,WAAA,CACA,KAAA,CACA,MAAA,CACA,SAAA,CACA,yBAPF,wCAQI,YAAA,CAAA,CAGJ,yCACE,UAAA,CACA,iBAAA,CACA,WAAA,CACA,KAAA,CACA,OAAA,CACA,SAAA,CACA,yBAPF,yCAQI,YAAA,CAAA,CAIJ,iCACE,aAAA,CACA,WAAA,CAGF,uCACE,WAAA,CACA,UAAA,CACA,gBAAA,CACA,gBAAA,CAGF,yBACE,iCACE,YAAA,CAAA,CAIJ,uCACE,iBAAA,CACA,KAAA,CACA,MAAA,CACA,UAAA,CACA,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,kBAAA,CACA,aAAA,CAGF,yBACE,uCACE,iBAAA,CACA,cAAA,CACA,iBAAA,CAAA,CAIJ,0CACE,aAAA,CACA,gBAAA,CAGF,yBACE,0CACE,cAAA,CAAA,CAIJ,+CACE,iBAAA,CACA,2GAAA,CACA,oBAAA,CACA,4BAAA,CAGF,0CACE,kBAAA,CACA,eAAA,CACA,gBAAA,CACA,aAAA,CAGF,yBACE,0CACE,iBAAA,CACA,kBAAA,CAAA,CAIJ,4CACE,eAAA,CAGF,yBACE,4CACE,eAAA,CAAA,CAIJ,kDACE,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,UAAA,CAEA,8DACE,iBAAA,CACA,WAAA,CACA,SAAA,CACA,OAAA,CACA,mBAAA,CACA,+BAAA,CAIJ,yBACE,kDACE,cAAA,CAAA,CAIJ,yBACE,4DACE,MAAA,CACA,eAAA,CACA,kBAAA,CAAA,CAIJ,iBACE,qBAAA,CAGF,2BACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,eAAA,CACA,+CACE,wBAAA,CACA,aAAA,CACA,0GAEE,wBAAA,CACA,UAAA,CAKN,6CACE,wBAAA,CACA,mBAAA,CACA,MAAA,CACA,YAAA,CACA,qBAAA,CACA,+CACE,iBAAA,CACA,aAAA,CACA,8DACE,eAAA,CACA,UAAA,CAKN,yBACE,6CACE,WAAA,CAAA,CAIJ,wDACE,eAAA,CACA,iBAAA,CACA,UAAA,CACA,oBAAA,CACA,oBAAA,CAGF,8DACE,yBAAA,CAGF,yBACE,wBACE,iBAAA,CACA,gBAAA,CAGF,uCACE,iBAAA,CAGF,wCACE,gBAAA,CAAA,CAIJ,WACE,uHAAA,CACA,gBACE,YAAA,CACA,qBAAA,CACA,UAAA,CACA,WAAA,CACA,yBALF,gBAMI,QAAA,CAAA,CAIJ,0BACE,MAAA,CACA,4CACE,iBAAA,CACA,qCAAA,CACA,wBAAA,CACA,YAAA,CACA,mDACE,UAAA,CACA,iBAAA,CACA,OAAA,CACA,kBAAA,CACA,WAAA,CACA,mEAAA,CACA,oEAAA,CACA,sBAAA,CACA,mBAAA,CAGF,8DACE,kBAAA,CACA,eAAA,CAGA,kEACE,iBAAA,CACA,eAAA,CAEF,mEACE,aAAA,CAMR,qCACE,WAAA,CACA,kBAAA,CACA,YAAA,CACA,0BAAA,CACA,kBAAA,CACA,WAAA,CACA,cAAA,CACA,UAAA,CACA,cAAA,CACA,iBAAA,CACA,eAAA,CACA,qBAAA,CACA,wBAAA,CACA,uDACE,UAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,eAAA,CAEF,oDACE,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,4DACE,YAAA,CACA,aAAA,CACA,cAAA,CAKN,yBACE,YAAA,CACA,sBAAA,CACA,QAAA,CACA,yBAJF,yBAKI,qBAAA,CACA,0BAAA,CACA,sBAAA,CAAA,CAEF,uCACE,MAAA,CACA,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,mBAAA,CACA,OAAA,CACA,yBAPF,uCAQI,cAAA,CACA,2CACE,UAAA,CAAA,CAGJ,4DACE,iBAAA,CACA,eAAA,CACA,UAAA,CAIN,qBACE,wBAAA,CACA,sBAAA,CACA,uBAAA,CACA,YAAA,CACA,sBAAA,CACA,YAAA,CACA,gCACE,aAAA,CAGA,oDACE,iBAAA,CACA,eAAA,CACA,aAAA,CAMR,sBACE,wBAAA,CACA,iCACE,aAAA,CAEF,oCACE,iBAAA,CACA,QAAA,CACA,UAAA,CACA,SAAA,CACA,wCAAA,CAEF,oCACE,iBAAA,CACA,QAAA,CACA,UAAA,CACA,SAAA,CACA,wCAAA,CAEF,qCACE,iBAAA,CACA,QAAA,CACA,UAAA,CACA,SAAA,CAEF,qCACE,iBAAA,CACA,QAAA,CACA,UAAA,CACA,SAAA,CAIJ,wCACE,iBAAA,CACA,kBAAA,CAGF,0BACE,sEACE,QAAA,CACA,6BAAA,CAAA,CACA,iDAHF,sEAII,OAAA,CAAA,CALN,0BASE,oEACE,UAAA,CACA,aAAA,CACA,WAAA,CACA,eAAA,CACA,kBAAA,CACA,iBAAA,CACA,mDAAA,CACA,gBAAA,CAAA,CACA,iDATF,oEAUI,gBAAA,CAAA,CAnBN,0BAuBE,+EACE,uBAAA,CACA,UAAA,CACA,WAAA,CACA,iBAAA,CACA,oBAAA,CACA,UAAA,CACA,qEAAA,CACA,eAAA,CAAA,CA/BJ,0BAkCE,2FACE,iBAAA,CACA,YAAA,CACA,YAAA,CAAA,CArCJ,0BAwCE,0FACE,UAAA,CACA,eAAA,CACA,aAAA,CACA,iBAAA,CACA,iBAAA,CACA,wBAAA,CACA,YAAA,CACA,SAAA,CACA,QAAA,CACA,mDAAA,CAAA,CAlDJ,0BAqDE,6FACE,SAAA,CACA,4BAAA,CACA,UAAA,CACA,YAAA,CACA,mBAAA,CACA,mBAAA,CACA,yCAAA,CACA,6BAAA,CAEA,eAAA,CACA,mDAAA,CAAA,CAIJ,iDACE,6FACE,eAAA,CAAA,CAIJ,0BACE,+GACE,YAAA,CACA,mBAAA,CACA,mBAAA,CACA,uBAAA,CAAA,CACA,iDALF,+GAMI,YAAA,CAAA,CAPN,0BAWE,mHACE,MAAA,CACA,kBAAA,CACA,YAAA,CACA,6BAAA,CACA,qBAAA,CACA,UAAA,CAAA,CACA,iDAPF,mHAQI,iBAAA,CAAA,CAnBN,0BAuBE,2EACE,WAAA,CACA,SAAA,CAAA,CAzBJ,0BA4BE,iGACE,uBAAA,CACA,4BAAA,CACA,UAAA,CACA,cAAA,CACA,eAAA,CAAA,CAjCJ,0BAoCE,oGACE,SAAA,CAAA,CArCJ,0BAwCE,kBACE,KACE,iBAAA,CAGF,GACE,kBAAA,CAAA,CAAA,CAKN,0BACE,mDACE,WAAA,CACA,kBAAA,CAEA,oBAAA,CACA,eAAA,CACA,cAAA,CAGF,gEACE,YAAA,CACA,6BAAA,CACA,kBAAA,CACA,qBAAA,CACA,+BAAA,CAGF,4EACE,YAAA,CAGF,2EACE,cAAA,CACA,eAAA,CACA,UAAA,CAAA,CAIJ,gDACE,2EACE,gBAAA,CAAA,CAIJ,0BACE,iEACE,UAAA,CACA,YAAA,CACA,6BAAA,CACA,QAAA,CAAA,CAIJ,gDACE,iEACE,qBAAA,CACA,kBAAA,CAAA,CAIJ,0BACE,mFACE,YAAA,CACA,eAAA,CAAA,CAIJ,gDACE,mFACE,OAAA,CAAA,CAIJ,0BACE,uFACE,MAAA,CACA,kBAAA,CACA,YAAA,CACA,6BAAA,CAEA,qBAAA,CAAA,CAIJ,gDACE,uFACE,OAAA,CAAA,CAIJ,0BACE,oEACE,WAAA,CAGF,kEACE,iBAAA,CAAA,CAIJ,gBACE,GACE,yBAAA,CAGF,IACE,0BAAA,CAGF,KACE,yBAAA,CAAA,CAIJ,oBACE,wBAAA,CAGF,sCACE,kBAAA,CACA,eAAA,CACA,qBAAA,CACA,YAAA,CACA,qBAAA,CACA,WAAA,CAGF,oDACE,iBAAA,CAGF,wEACE,iBAAA,CACA,QAAA,CACA,SAAA,CACA,YAAA,CACA,kBAAA,CACA,cAAA,CACA,UAAA,CACA,eAAA,CACA,QAAA,CAGF,yBACE,wEACE,YAAA,CAAA,CAIJ,8EACE,UAAA,CACA,UAAA,CAGF,2QAGE,iBAAA,CACA,UAAA,CACA,gCAAA,CACA,yBAAA,CACA,iBAAA,CAGF,mSAGE,UAAA,CACA,iBAAA,CACA,WAAA,CACA,kBAAA,CACA,MAAA,CACA,KAAA,CACA,0BAAA,CACA,0HAAA,CAGF,uDACE,MAAA,CACA,YAAA,CACA,SAAA,CAGF,yBACE,uDACE,qBAAA,CAAA,CAIJ,wEACE,cAAA,CACA,uBAAA,CACA,YAAA,CACA,sBAAA,CACA,sBAAA,CACA,QAAA,CAGF,yBACE,wEACE,YAAA,CACA,gBAAA,CAAA,CAIJ,mFACE,YAAA,CACA,aAAA,CAGF,yBACE,mFACE,YAAA,CAAA,CAIJ,8FACE,eAAA,CACA,gBAAA,CACA,UAAA,CACA,kBAAA,CAGF,+FACE,kBAAA,CACA,aAAA,CAGF,iFACE,uCAAA,CAGF,oEACE,cAAA,CACA,uBAAA,CACA,YAAA,CACA,sBAAA,CACA,sBAAA,CACA,QAAA,CAGF,yBACE,oEACE,YAAA,CACA,aAAA,CAAA,CAIJ,uBACE,GACE,wBAAA,CAGF,KACE,sBAAA,CAAA,CAIJ,+EACE,oBAAA,CACA,wCAAA,CACA,aAAA,CACA,YAAA,CAGF,yBACE,+EACE,YAAA,CAAA,CAIJ,0FACE,eAAA,CACA,gBAAA,CACA,UAAA,CACA,kBAAA,CAGF,2FACE,kBAAA,CACA,aAAA,CAGF,6DAEE,uBAAA,CACA,aAAA,CACA,gBAAA,CACA,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,iBAAA,CACA,cAAA,CACA,iBAAA,CACA,OAAA,CAGF,yBACE,6DAEE,YAAA,CAAA,CAIJ,yEAEE,wBAAA,CAGF,+BACE,cAAA,CAGF,yBACE,+BACE,aAAA,CAAA,CAIJ,8BACE,aAAA,CAGF,yBACE,8BACE,YAAA,CAAA,CAKF,0BACE,cAAA,CACA,+BAAA,CACA,yBAHF,0BAII,gBAAA,CAAA,CAGJ,qBACE,YAAA,CACA,aAAA,CAEF,sBACE,aAAA,CACA,aAAA,CAGF,0DACE,eAAA,CACA,aAAA,CAGF,0DACE,aAAA,CAEF,2DACE,YAAA,CAGF,qCACE,YAAA,CACA,kBAAA,CACA,oBAAA,CACA,OAAA,CACA,aAAA,CACA,aAAA,CACA,eAAA,CACA,gBAAA,CACA,yBATF,qCAUI,iBAAA,CACA,aAAA,CAAA,CAEF,yCACE,aAAA,CAEF,iDACE,aAAA,CAKN,yBACE,4CACE,YAAA,CACA,cAAA,CAGF,0DACE,6BAAA,CAAA,CAIJ,yBACE,kBAAA,CACA,iBAAA,CACA,eAAA,CACA,qBAAA,CACA,0BAAA,CACA,2BAAA,CACA,iBAAA,CACA,UAAA,CACA,SAAA,CACA,kBAAA,CACA,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,qBAAA,CACA,qBAAA,CAGF,+BACE,qCAAA,CAGF,sCACE,UAAA,CACA,iBAAA,CACA,OAAA,CACA,kBAAA,CACA,WAAA,CACA,0JAAA,CACA,oEAAA,CACA,kFAAA,CACA,sBAAA,CAGF,4CACE,OAAA,CAGF,mCACE,WAAA,CACA,UAAA,CAGF,sCACE,iBAAA,CACA,sBAAA,CACA,uBAAA,CACA,iBAAA,CACA,YAAA,CACA,sBAAA,CACA,qBAAA,CACA,SAAA,CACA,kBAAA,CACA,QAAA,CACA,QAAA,CACA,eAAA,CACA,8GAAA,CACA,kBAAA,CACA,qBAAA,CACA,0BAAA,CACA,2BAAA,CAGF,kCACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CAGF,8BACE,qCAAA,CACA,oCAAA,CAGF,0BACE,8BACE,kBAAA,CAAA,CAIJ,yBACE,8BACE,iBAAA,CAAA,CAIJ,4BACE,iBAAA,CACA,oBAAA,CACA,iBAAA,CACA,aAAA,CACA,eAAA,CACA,kBAAA,CACA,sBAAA,CAGF,kCACE,aAAA,CAGF,oCACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,6BAAA,CAGF,mDACE,oBAAA,CAGF,0BACE,oCACE,kBAAA,CACA,gBAAA,CAAA,CAIJ,yBACE,oCACE,aAAA,CAAA,CAIJ,6BACE,mBAAA,CACA,oBAAA,CACA,2BAAA,CACA,eAAA,CAGF,uBACE,GACE,OAAA,CAGF,IACE,UAAA,CAGF,KACE,OAAA,CAAA,CAIJ,yCACE,iBAAA,CACA,KAAA,CACA,OAAA,CACA,SAAA,CAGF,sCACE,oBAAA,CACA,eAAA,CACA,qBAAA,CACA,UAAA,CAGF,sDACE,iBAAA,CACA,SAAA,CACA,WAAA,CACA,MAAA,CACA,KAAA,CACA,yBAAA,CACA,2BAAA,CACA,SAAA,CACA,wCAAA,CAGF,6DACE,UAAA,CACA,SAAA,CACA,WAAA,CACA,eAAA,CACA,iBAAA,CACA,OAAA,CACA,KAAA,CAGF,8DACE,UAAA,CACA,4FAAA,CACA,uBAAA,CACA,0BAAA,CACA,2BAAA,CACA,UAAA,CACA,WAAA,CACA,iBAAA,CACA,OAAA,CACA,OAAA,CACA,8BAAA,CACA,SAAA,CAGF,yBACE,8DACE,UAAA,CACA,WAAA,CAAA,CAIJ,uEACE,+GAAA,CAGF,uEACE,gHAAA,CACA,wCAAA,CAGF,uEACE,qHAAA,CACA,wCAAA,CAGF,iDACE,UAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CAGF,yBACE,iDACE,mBAAA,CAAA,CAIJ,+DACE,cAAA,CAGF,yBACE,6EAEE,aAAA,CACA,YAAA,CAAA,CAIJ,+DACE,YAAA,CAGF,uDACE,aAAA,CAGF,qEACE,YAAA,CAGF,qEACE,cAAA,CAGF,kBACE,oGAAA,CACA,qBAAA,CACA,0BAAA,CACA,2BAAA,CAGF,gCACE,kBAAA,CACA,qBAAA,CAGF,yBACE,gCACE,kBAAA,CACA,qBAAA,CAAA,CAIJ,oCACE,WAAA,CACA,aAAA,CACA,aAAA,CAGF,yBACE,6BACE,iBAAA,CAGF,oBACE,aAAA,CAGF,sCACE,qBAAA,CACA,0BAAA,CACA,4CACE,UAAA,CACA,wBAAA,CACA,oBAAA,CAAA\",\"sourcesContent\":[\"* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\n// main {\\n//   background-color: #f5f8ff;\\n//   color: #000;\\n\\n//   h1,\\n//   h2,\\n//   h3,\\n//   h4,\\n//   h5,\\n//   h6,\\n//   p,\\n//   div,\\n//   span {\\n//     margin-bottom: 0;\\n//   }\\n\\n// h1,\\n// h2,\\n// h3 {\\n//   text-align: center;\\n// }\\n// h2 {\\n//   font-size: 2.25rem;\\n//   font-weight: 800;\\n// }\\n\\n// .opacity-7 {\\n//   opacity: 0.7;\\n// }\\n\\n// .blue-text {\\n//   color: #0055fb;\\n// }\\n\\n// .btn-wrapper {\\n//   display: flex;\\n\\n//   justify-content: center;\\n//   gap: 1rem;\\n//   @media (max-width: 768px) {\\n//     flex-direction: column;\\n//     gap: 8px;\\n//   }\\n//   .btn {\\n//     margin: 0;\\n//     border-radius: 8px;\\n//     text-transform: capitalize;\\n//     display: flex;\\n//     align-items: center;\\n//     justify-content: center;\\n//     min-width: 228px;\\n//     @media (max-width: 768px) {\\n//       display: block;\\n//       vertical-align: baseline;\\n//     }\\n//   }\\n// }\\n// .btn-download {\\n//   background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);\\n//   border: none;\\n//   color: #fff;\\n//   background-color: #0458ff;\\n\\n//   &:hover {\\n//     color: #fff;\\n//     background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),\\n//       linear-gradient(0deg, #0055fb, #0055fb);\\n//     background-color: #0458ff;\\n//   }\\n// }\\nmain {\\n  background-color: #f5f8ff;\\n}\\nmain h1,\\nmain h2,\\nmain h3,\\nmain h4,\\nmain h5,\\nmain h6,\\nmain p,\\nmain div {\\n  margin-bottom: 0;\\n  color: #000;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n}\\n\\nmain h1,\\nmain h2 {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  text-align: center;\\n}\\n\\n@media (max-width: 768px) {\\n  main h1,\\n  main h2 {\\n    font-size: 24px;\\n    text-align: center;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  main .display-3 {\\n    font-size: 2.5rem;\\n  }\\n}\\n\\nmain .opacity-7 {\\n  opacity: 0.7;\\n}\\n\\nmain .text-blue {\\n  color: #2a80ff;\\n}\\n\\nmain .btn-wrapper {\\n  display: flex;\\n  justify-content: center;\\n  gap: 1rem;\\n}\\n\\n@media (max-width: 768px) {\\n  main .btn-wrapper {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n}\\n\\nmain .btn {\\n  margin: 0;\\n  border-radius: 12px;\\n  text-transform: capitalize;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  // .wsc-icon {\\n  //   display: flex;\\n  //   align-items: center;\\n  // }\\n}\\n\\n@media (min-width: 992px) {\\n  main .btn {\\n    height: 51px;\\n  }\\n  main .btn.btn-lg {\\n    height: 4rem;\\n  }\\n}\\n// @media (max-width: 576px) {\\n//   main .btn {\\n//     height: 40px;\\n//   }\\n// }\\n\\n@media (max-width: 768px) {\\n  main .btn {\\n    display: block;\\n    vertical-align: baseline;\\n  }\\n}\\n\\nmain .gradient-text {\\n  background: linear-gradient(138.99deg, #0087ff 8.45%, #6ad6ff 89.48%);\\n  color: transparent;\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n}\\n\\nmain .btn-download {\\n  background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);\\n  border: none;\\n  color: #fff;\\n  background-color: #0458ff;\\n}\\n\\nmain .btn-download:hover {\\n  color: #fff;\\n  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),\\n    linear-gradient(0deg, #0055fb, #0055fb);\\n  background-color: #0458ff;\\n}\\n\\nmain .part-banner {\\n  position: relative;\\n  .small-title {\\n    color: #13171a;\\n    font-size: 1.875rem;\\n    margin-bottom: 1rem;\\n    font-weight: 700;\\n    line-height: 90%;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-banner {\\n    background: linear-gradient(180deg, #c0eeff 0%, #f8fbff 96.46%);\\n  }\\n}\\n\\nmain .part-banner .banner-left-download {\\n  z-index: 10;\\n  position: absolute;\\n  height: 100%;\\n  top: 0;\\n  left: 0;\\n  width: 33%;\\n  @media (max-width: 768px) {\\n    display: none;\\n  }\\n}\\nmain .part-banner .banner-right-download {\\n  z-index: 10;\\n  position: absolute;\\n  height: 100%;\\n  top: 0;\\n  right: 0;\\n  width: 33%;\\n  @media (max-width: 768px) {\\n    display: none;\\n  }\\n}\\n\\nmain .part-banner .video-wrapper {\\n  line-height: 0;\\n  font-size: 0;\\n}\\n\\nmain .part-banner .video-wrapper video {\\n  height: 100%;\\n  width: 100%;\\n  object-fit: cover;\\n  min-height: 533px;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-banner .video-wrapper {\\n    display: none;\\n  }\\n}\\n\\nmain .part-banner .part-banner-content {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  margin: 0 auto;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-banner .part-banner-content {\\n    position: relative;\\n    padding: 3rem 0;\\n    text-align: center;\\n  }\\n}\\n\\nmain .part-banner .part-banner-content h1 {\\n  color: #13171a;\\n  line-height: 110%;\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-banner .part-banner-content h1 {\\n    font-size: 26px;\\n  }\\n}\\n\\nmain .part-banner .part-banner-content h1 span {\\n  color: transparent;\\n  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(0deg, #0055fb, #0055fb);\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n}\\n\\nmain .part-banner .part-banner-content h2 {\\n  font-size: 1.875rem;\\n  font-weight: 700;\\n  line-height: 100%;\\n  color: #13171a;\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-banner .part-banner-content h2 {\\n    font-size: 1.25rem;\\n    margin-bottom: 1rem;\\n  }\\n}\\n\\nmain .part-banner .part-banner-content .btn {\\n  min-width: 289px;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-banner .part-banner-content .btn {\\n    min-width: unset;\\n  }\\n}\\n\\nmain .part-banner .part-banner-content .logo-list {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 14.4px;\\n\\n  .split-line {\\n    position: relative;\\n    height: 16px;\\n    width: 2px;\\n    top: 70%;\\n    border-radius: 1.5px;\\n    background-color: rgba($color: #000000, $alpha: 0.7);\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-banner .part-banner-content .logo-list {\\n    flex-wrap: wrap;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-banner .part-banner-content .logo-list .logo-img {\\n    flex: 1;\\n    max-height: 24px;\\n    object-fit: contain;\\n  }\\n}\\n\\nmain .part-files {\\n  background-color: #fff;\\n}\\n\\nmain .part-files .file-box {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  border-radius: 1rem;\\n  overflow: hidden;\\n  .btn-outline-action {\\n    border: 1px solid #2e8eff;\\n    color: #2e8eff;\\n    &:hover,\\n    &:focus {\\n      background-color: #2e8eff;\\n      color: #fff;\\n    }\\n  }\\n}\\n\\nmain .part-files .file-box .file-box-content {\\n  background-color: #f9f9f9;\\n  padding: 1.5rem 2rem;\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  p {\\n    font-size: 0.875rem;\\n    color: #5f5f5f;\\n    .content-title {\\n      font-weight: 700;\\n      color: #000;\\n    }\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-files .file-box .file-box-content {\\n    padding: 8px;\\n  }\\n}\\n\\nmain .part-files .file-box .file-box-content .box-title {\\n  font-weight: 700;\\n  font-size: 1.25rem;\\n  color: #000;\\n  text-decoration: none;\\n  display: inline-block;\\n}\\n\\nmain .part-files .file-box .file-box-content .box-title:hover {\\n  text-decoration: underline;\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-files .col-6 {\\n    padding-right: 8px;\\n    padding-left: 8px;\\n  }\\n\\n  main .part-files .col-6:nth-child(odd) {\\n    padding-right: 4px;\\n  }\\n\\n  main .part-files .col-6:nth-child(even) {\\n    padding-left: 4px;\\n  }\\n}\\n\\n.part-step {\\n  background: url(https://images.wondershare.com/repairit/images2025/PDF-repair/step-bg.svg) no-repeat center center / cover;\\n  .nav {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 1.5rem;\\n    height: 100%;\\n    @media (max-width: 768px) {\\n      gap: 1rem;\\n    }\\n  }\\n\\n  .nav .nav-item {\\n    flex: 1;\\n    &.active .step-item {\\n      position: relative;\\n      box-shadow: 0px 8px 12px 0px #7cc5dc3d;\\n      border-color: transparent;\\n      border: unset;\\n      &::after {\\n        content: \\\"\\\";\\n        position: absolute;\\n        inset: 0;\\n        border-radius: 1rem;\\n        padding: 1px;\\n        background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);\\n        mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);\\n        mask-composite: exclude;\\n        pointer-events: none;\\n      }\\n\\n      .step-item-number {\\n        font-size: 2.625rem;\\n        font-weight: 700;\\n      }\\n      .right-content {\\n        .title {\\n          font-size: 1.25rem;\\n          font-weight: 700;\\n        }\\n        .detail {\\n          display: block;\\n        }\\n      }\\n    }\\n  }\\n\\n  .nav .nav-item .step-item {\\n    height: 100%;\\n    border-radius: 1rem;\\n    display: flex;\\n    justify-content: flex-start;\\n    align-items: center;\\n    gap: 1.25rem;\\n    padding: 1.5rem;\\n    color: #000;\\n    cursor: pointer;\\n    position: relative;\\n    overflow: hidden;\\n    background-color: #fff;\\n    border: 1px solid #bce0fe;\\n    .step-item-number {\\n      width: 22px;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      font-weight: 700;\\n    }\\n    .right-content {\\n      display: flex;\\n      flex-direction: column;\\n      justify-content: center;\\n      .detail {\\n        display: none;\\n        color: #787878;\\n        font-size: 14px;\\n      }\\n    }\\n  }\\n\\n  .feature-list {\\n    display: flex;\\n    justify-content: center;\\n    gap: 1rem;\\n    @media (max-width: 768px) {\\n      flex-direction: column;\\n      justify-content: flex-start;\\n      align-items: flex-start;\\n    }\\n    .feature-item {\\n      flex: 1;\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      padding: 1.5rem 1rem;\\n      gap: 4px;\\n      @media (max-width: 768px) {\\n        padding: 0 1rem;\\n        img {\\n          width: 36px;\\n        }\\n      }\\n      .feature-item-detail {\\n        font-size: 1.25rem;\\n        font-weight: 500;\\n        color: #444444;\\n      }\\n    }\\n  }\\n  .note-box {\\n    background-color: #ecfaff;\\n    border-radius: 1.375rem;\\n    padding: 1.125rem 1.5rem;\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 0.9375rem;\\n    .left-icon {\\n      flex-shrink: 0;\\n    }\\n    .right-content {\\n      .content-detail {\\n        font-size: 0.875rem;\\n        font-weight: 500;\\n        color: #636363;\\n      }\\n    }\\n  }\\n}\\n\\nmain .part-highlights {\\n  background-color: #f5f8ff;\\n  .btn-white {\\n    color: #0c7dfa;\\n  }\\n  .blue-cricle1 {\\n    position: absolute;\\n    left: 41%;\\n    bottom: 42%;\\n    width: 23%;\\n    animation: icon-rotate 3s linear infinite;\\n  }\\n  .blue-cricle2 {\\n    position: absolute;\\n    left: 40%;\\n    bottom: 42%;\\n    width: 21%;\\n    animation: icon-rotate 3s linear infinite;\\n  }\\n  .win-icon-link {\\n    position: absolute;\\n    left: 56%;\\n    bottom: 55%;\\n    width: 27%;\\n  }\\n  .mac-icon-link {\\n    position: absolute;\\n    left: 22%;\\n    bottom: 55%;\\n    width: 27%;\\n  }\\n}\\n\\nmain .part-highlights .assetsSwiper-box {\\n  position: relative;\\n  margin-bottom: 5rem;\\n}\\n\\n@media (min-width: 1280px) {\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-wrapper {\\n    gap: 16px;\\n    justify-content: space-between;\\n    @media (max-width: 1600px) {\\n      gap: 8px;\\n    }\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide {\\n    width: 7.1%;\\n    display: block;\\n    height: auto;\\n    overflow: hidden;\\n    border-radius: 1rem;\\n    position: relative;\\n    transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);\\n    min-height: 430px;\\n    @media (max-width: 1600px) {\\n      min-height: 400px;\\n    }\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style {\\n    padding: 1.875rem 1.5rem;\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n    border-radius: 1.5rem;\\n    color: #fff;\\n    background: linear-gradient(269.24deg, #6fb3ff -4.48%, #1989ff 54.28%);\\n    overflow: hidden;\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .right-icon {\\n    position: absolute;\\n    right: 2.1rem;\\n    top: 1.875rem;\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .box-title {\\n    color: #fff;\\n    bottom: 1.125rem;\\n    left: 2.125rem;\\n    position: absolute;\\n    font-size: 1.25rem;\\n    writing-mode: sideways-lr;\\n    height: 525px;\\n    z-index: 3;\\n    width: 0%;\\n    transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content {\\n    opacity: 0;\\n    height: calc(100% - 3.875rem);\\n    width: 100%;\\n    display: flex;\\n    margin-top: 3.875rem;\\n    padding-top: 2.25rem;\\n    border-top: 1px solid rgba($color: #f2f2f2, $alpha: 0.3);\\n    justify-content: space-between;\\n    // gap: 3rem;\\n    min-width: 774px;\\n    transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);\\n  }\\n}\\n\\n@media (min-width: 1280px) and (max-width: 1600px) {\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content {\\n    min-width: 634px;\\n  }\\n}\\n\\n@media (min-width: 1280px) {\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .left-img-wrapper {\\n    flex: 0 0 45%;\\n    margin-top: -2.25rem;\\n    margin-left: -1.5rem;\\n    margin-bottom: -1.875rem;\\n    @media (max-width: 1600px) {\\n      flex: 0 0 51%;\\n    }\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .right-detial-wrapper {\\n    flex: 1;\\n    padding-top: 1.5rem;\\n    display: flex;\\n    justify-content: space-between;\\n    flex-direction: column;\\n    color: #fff;\\n    @media (max-width: 1600px) {\\n      padding-top: unset;\\n    }\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active {\\n    width: 58.3%;\\n    opacity: 1;\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active .box-style .box-title {\\n    transform: rotate(90deg);\\n    transform-origin: bottom left;\\n    bottom: 95%;\\n    font-size: 2rem;\\n    font-weight: 600;\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active .box-style .card-content {\\n    opacity: 1;\\n  }\\n\\n  @keyframes fadeIn {\\n    from {\\n      visibility: hidden;\\n    }\\n\\n    to {\\n      visibility: visible;\\n    }\\n  }\\n}\\n\\n@media (max-width: 1280px) {\\n  main .part-highlights .assetsSwiper-box .box-style {\\n    height: 100%;\\n    background: #1888fe;\\n\\n    border-radius: 1.5rem;\\n    overflow: hidden;\\n    padding: 1.5rem;\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .box-style .top-content {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    padding-bottom: 1.5rem;\\n    border-bottom: 1px solid #f2f2f2;\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .box-style .top-content .right-icon {\\n    width: 2.5rem;\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .box-style .top-content .box-title {\\n    font-size: 2rem;\\n    font-weight: 600;\\n    color: #fff;\\n  }\\n}\\n\\n@media (max-width: 1280px) and (max-width: 768px) {\\n  main .part-highlights .assetsSwiper-box .box-style .top-content .box-title {\\n    font-size: 1.5rem;\\n  }\\n}\\n\\n@media (max-width: 1280px) {\\n  main .part-highlights .assetsSwiper-box .box-style .card-content {\\n    width: 100%;\\n    display: flex;\\n    justify-content: space-between;\\n    gap: 1rem;\\n  }\\n}\\n\\n@media (max-width: 1280px) and (max-width: 768px) {\\n  main .part-highlights .assetsSwiper-box .box-style .card-content {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n}\\n\\n@media (max-width: 1280px) {\\n  main .part-highlights .assetsSwiper-box .box-style .card-content .left-img-wrapper {\\n    flex: 0 0 35%;\\n    max-width: 240px;\\n  }\\n}\\n\\n@media (max-width: 1280px) and (max-width: 768px) {\\n  main .part-highlights .assetsSwiper-box .box-style .card-content .left-img-wrapper {\\n    order: 2;\\n  }\\n}\\n\\n@media (max-width: 1280px) {\\n  main .part-highlights .assetsSwiper-box .box-style .card-content .right-detial-wrapper {\\n    flex: 1;\\n    padding-top: 1.5rem;\\n    display: flex;\\n    justify-content: space-between;\\n\\n    flex-direction: column;\\n  }\\n}\\n\\n@media (max-width: 1280px) and (max-width: 768px) {\\n  main .part-highlights .assetsSwiper-box .box-style .card-content .right-detial-wrapper {\\n    order: 1;\\n  }\\n}\\n\\n@media (max-width: 1280px) {\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide {\\n    height: auto;\\n  }\\n\\n  main .part-highlights .assetsSwiper-box .assetsSwiper .rounded-16 {\\n    border-radius: 8px;\\n  }\\n}\\n\\n@keyframes move {\\n  0% {\\n    transform: translateX(5px);\\n  }\\n\\n  50% {\\n    transform: translateX(-3px);\\n  }\\n\\n  100% {\\n    transform: translateX(5px);\\n  }\\n}\\n\\nmain .part-customer {\\n  background-color: #f5f8ff;\\n}\\n\\nmain .part-customer .customer-wrapper {\\n  border-radius: 2rem;\\n  overflow: hidden;\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n\\nmain .part-customer .customer-wrapper .customer-img {\\n  position: relative;\\n}\\n\\nmain .part-customer .customer-wrapper .customer-img .customer-info-list {\\n  position: absolute;\\n  top: 20px;\\n  left: 40px;\\n  display: flex;\\n  align-items: center;\\n  font-size: 16px;\\n  color: #fff;\\n  font-weight: 600;\\n  gap: 24px;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-customer .customer-wrapper .customer-img .customer-info-list {\\n    display: none;\\n  }\\n}\\n\\nmain .part-customer .customer-wrapper .customer-img .customer-info-list.right {\\n  right: 32px;\\n  left: unset;\\n}\\n\\nmain .part-customer .customer-wrapper .customer-img .customer-info-list .customer-title,\\nmain .part-customer .customer-wrapper .customer-img .customer-info-list .customer-profession,\\nmain .part-customer .customer-wrapper .customer-img .customer-info-list .customer-age {\\n  position: relative;\\n  color: #fff;\\n  background-color: rgba(0, 0, 0, 0.21);\\n  border-radius: 0 3px 3px 0;\\n  padding-right: 6px;\\n}\\n\\nmain .part-customer .customer-wrapper .customer-img .customer-info-list .customer-title::before,\\nmain .part-customer .customer-wrapper .customer-img .customer-info-list .customer-profession::before,\\nmain .part-customer .customer-wrapper .customer-img .customer-info-list .customer-age::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  height: 100%;\\n  aspect-ratio: 22 / 31;\\n  left: 0;\\n  top: 0;\\n  transform: translateX(-97%);\\n  background: url(https://images.wondershare.com/repairit/images2025/PPT-repair/left-tip.png) no-repeat center center/contain;\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail {\\n  flex: 1;\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n@media (max-width: 992px) {\\n  main .part-customer .customer-wrapper .customer-detail {\\n    flex-direction: column;\\n  }\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .problem-wrapper {\\n  flex: 1 1 41.2%;\\n  padding: 1.875rem 1.5rem;\\n  display: flex;\\n  align-items: flex-start;\\n  justify-content: center;\\n  gap: 12px;\\n}\\n\\n@media (max-width: 992px) {\\n  main .part-customer .customer-wrapper .customer-detail .problem-wrapper {\\n    padding: 1rem;\\n    padding-bottom: 0;\\n  }\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .problem-wrapper .left-icon {\\n  width: 4.5rem;\\n  flex-shrink: 0;\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-customer .customer-wrapper .customer-detail .problem-wrapper .left-icon {\\n    width: 2.5rem;\\n  }\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .problem-wrapper .right-content .title {\\n  font-weight: 800;\\n  font-size: 1.5rem;\\n  color: #000;\\n  margin-bottom: 12px;\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .problem-wrapper .right-content .detail {\\n  font-size: 1.125rem;\\n  color: #3c3c3c;\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .customer-detail-dividing {\\n  border-right: 1px dashed rgba(0, 0, 0, 0.07);\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .how-wrapper {\\n  flex: 1 1 58.8%;\\n  padding: 1.875rem 1.5rem;\\n  display: flex;\\n  align-items: flex-start;\\n  justify-content: center;\\n  gap: 12px;\\n}\\n\\n@media (max-width: 992px) {\\n  main .part-customer .customer-wrapper .customer-detail .how-wrapper {\\n    padding: 1rem;\\n    padding-top: 0;\\n  }\\n}\\n\\n@keyframes icon-rotate {\\n  0% {\\n    transform: rotate(360deg);\\n  }\\n\\n  100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .how-wrapper .left-icon {\\n  text-decoration: none;\\n  animation: icon-rotate 3s linear infinite;\\n  flex-shrink: 0;\\n  width: 4.5rem;\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-customer .customer-wrapper .customer-detail .how-wrapper .left-icon {\\n    width: 2.5rem;\\n  }\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .how-wrapper .right-content .title {\\n  font-weight: 800;\\n  font-size: 1.5rem;\\n  color: #000;\\n  margin-bottom: 12px;\\n}\\n\\nmain .part-customer .customer-wrapper .customer-detail .how-wrapper .right-content .detail {\\n  font-size: 1.125rem;\\n  color: #3c3c3c;\\n}\\n\\nmain .part-customer .left-btn,\\nmain .part-customer .right-btn {\\n  background-color: #c0c0c0;\\n  width: 2.25rem;\\n  aspect-ratio: 1 / 1;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  position: absolute;\\n  top: 36%;\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-customer .left-btn,\\n  main .part-customer .right-btn {\\n    display: none;\\n  }\\n}\\n\\nmain .part-customer .left-btn:hover,\\nmain .part-customer .right-btn:hover {\\n  background-color: #006dff;\\n}\\n\\nmain .part-customer .right-btn {\\n  right: -3.25rem;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-customer .right-btn {\\n    right: 1.55rem;\\n  }\\n}\\n\\nmain .part-customer .left-btn {\\n  left: -3.25rem;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-customer .left-btn {\\n    left: 1.55rem;\\n  }\\n}\\n\\n.part-faq {\\n  .accordion-item {\\n    padding: 2rem 0;\\n    border-bottom: 1px solid #e2e2e2;\\n    @media (max-width: 576px) {\\n      padding: 1.5rem 0;\\n    }\\n  }\\n  .open-icon {\\n    display: none;\\n    color: inherit;\\n  }\\n  .close-icon {\\n    display: block;\\n    color: inherit;\\n  }\\n\\n  .accordion-item [aria-expanded=\\\"true\\\"] .faq-title {\\n    font-weight: 700;\\n    color: #4d99ff;\\n  }\\n\\n  .accordion-item [aria-expanded=\\\"true\\\"] .open-icon {\\n    display: block;\\n  }\\n  .accordion-item [aria-expanded=\\\"true\\\"] .close-icon {\\n    display: none;\\n  }\\n\\n  .accordion-item .faq-title {\\n    display: flex;\\n    align-items: center;\\n    justify-content: left;\\n    gap: 8px;\\n    flex-shrink: 0;\\n    max-width: 95%;\\n    font-weight: 600;\\n    font-size: 1.5rem;\\n    @media (max-width: 576px) {\\n      font-size: 1.25rem;\\n      max-width: 90%;\\n    }\\n    svg {\\n      flex-shrink: 0;\\n    }\\n    .title-desc {\\n      color: inherit;\\n    }\\n  }\\n}\\n\\n@media (min-width: 992px) {\\n  main .part-tip #swiper-tips .swiper-wrapper {\\n    gap: 1.875rem;\\n    flex-wrap: wrap;\\n  }\\n\\n  main .part-tip #swiper-tips .swiper-wrapper .swiper-slide {\\n    flex: 1 1 calc(33% - 1.875rem);\\n  }\\n}\\n\\nmain .part-tip .tip-item {\\n  border-radius: 2rem;\\n  position: relative;\\n  overflow: hidden;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n  padding: 3rem 2rem;\\n  color: #000;\\n  z-index: 3;\\n  transition: all 0.2s;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-direction: column;\\n  background-color: #fff;\\n}\\n\\nmain .part-tip .tip-item:hover {\\n  box-shadow: 0px 0px 12px 0px #00d1ff4d;\\n}\\n\\nmain .part-tip .tip-item:hover::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 2rem;\\n  padding: 2px;\\n  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(127.35deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 36.09%);\\n  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);\\n  -webkit-mask-image: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);\\n  mask-composite: exclude;\\n}\\n\\nmain .part-tip .tip-item:hover .text-detail {\\n  top: 2px;\\n}\\n\\nmain .part-tip .tip-item .tip-icon {\\n  height: 6rem;\\n  width: 6rem;\\n}\\n\\nmain .part-tip .tip-item .text-detail {\\n  position: absolute;\\n  width: calc(100% - 4px);\\n  height: calc(100% - 4px);\\n  padding: 0rem 2rem;\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: column;\\n  z-index: 2;\\n  border-radius: 2rem;\\n  left: 2px;\\n  top: 100%;\\n  overflow: hidden;\\n  background-image: url(https://images.wondershare.com/repairit/images2025/PPT-repair/tip-card-bg.svg) !important;\\n  transition: all 0.3s;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\nmain .part-links .part-links-line {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n}\\n\\nmain .part-links .line-border {\\n  border-right: 1px solid rgba(0, 0, 0, 0.3);\\n  border-left: 1px solid rgba(0, 0, 0, 0.3);\\n}\\n\\n@media (max-width: 1280px) {\\n  main .part-links .line-border {\\n    border-right: unset;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-links .line-border {\\n    border-left: unset;\\n  }\\n}\\n\\nmain .part-links .text-link {\\n  font-size: 0.875rem;\\n  color: rgba(0, 0, 0, 0.7);\\n  margin-top: 1.5rem;\\n  display: block;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n}\\n\\nmain .part-links .text-link:hover {\\n  color: #0055fb;\\n}\\n\\nmain .part-links .part-links-videos {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n}\\n\\nmain .part-links .part-links-videos .video-wrapper {\\n  border-radius: 0.75rem;\\n}\\n\\n@media (max-width: 1280px) {\\n  main .part-links .part-links-videos {\\n    flex-direction: row;\\n    padding-top: 2rem;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-links .part-links-videos {\\n    display: block;\\n  }\\n}\\n\\nmain .part-links .text-line4 {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 4;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n\\n@keyframes changeWidth {\\n  0% {\\n    width: 0;\\n  }\\n\\n  50% {\\n    width: 100%;\\n  }\\n\\n  100% {\\n    width: 0;\\n  }\\n}\\n\\nmain .part-feature .intelligence-content {\\n  position: absolute;\\n  top: 0;\\n  left: 0%;\\n  z-index: 2;\\n}\\n\\nmain .part-feature .intelligence-item {\\n  border-radius: 1.5rem;\\n  overflow: hidden;\\n  background-color: #fff;\\n  color: #000;\\n}\\n\\nmain .part-feature .intelligence-item .compare-before {\\n  position: absolute;\\n  width: 50%;\\n  height: 100%;\\n  left: 0;\\n  top: 0;\\n  background-size: auto 100%;\\n  background-repeat: no-repeat;\\n  z-index: 2;\\n  animation: changeWidth 6s linear infinite;\\n}\\n\\nmain .part-feature .intelligence-item .compare-before::after {\\n  content: \\\"\\\";\\n  width: 2px;\\n  height: 100%;\\n  background: #fff;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\nmain .part-feature .intelligence-item .compare-before::before {\\n  content: \\\"\\\";\\n  background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);\\n  background-size: contain;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n  width: 5rem;\\n  height: 3rem;\\n  position: absolute;\\n  right: 0;\\n  top: 50%;\\n  transform: translate(50%, -50%);\\n  z-index: 3;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-feature .intelligence-item .compare-before::before {\\n    width: 3rem;\\n    height: 2rem;\\n  }\\n}\\n\\nmain .part-feature .intelligence-item .compare-before.compare-before-1 {\\n  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/word-file-repair-before.jpg);\\n}\\n\\nmain .part-feature .intelligence-item .compare-before.compare-before-2 {\\n  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/excel-file-repair-before.jpg);\\n  animation: changeWidth 8s linear infinite;\\n}\\n\\nmain .part-feature .intelligence-item .compare-before.compare-before-3 {\\n  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/powerpoint-file-repair-before.jpg);\\n  animation: changeWidth 7s linear infinite;\\n}\\n\\nmain .part-feature .intelligence-item .item-link {\\n  color: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-feature .intelligence-item .item-link {\\n    margin-bottom: 0.5rem;\\n  }\\n}\\n\\nmain .part-feature .intelligence-item .item-link .normal-arrow {\\n  display: inline;\\n}\\n\\n@media (max-width: 768px) {\\n  main .part-feature .intelligence-item .item-link .normal-arrow,\\n  .active-arrow {\\n    height: 2.5rem;\\n    width: 2.5rem;\\n  }\\n}\\n\\nmain .part-feature .intelligence-item .item-link .active-arrow {\\n  display: none;\\n}\\n\\nmain .part-feature .intelligence-item .item-link:hover {\\n  color: #0458ff;\\n}\\n\\nmain .part-feature .intelligence-item .item-link:hover .normal-arrow {\\n  display: none;\\n}\\n\\nmain .part-feature .intelligence-item .item-link:hover .active-arrow {\\n  display: inline;\\n}\\n\\nmain .part-footer {\\n  background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\nmain .part-footer .btn-download {\\n  padding-top: 18.5px;\\n  padding-bottom: 18.5px;\\n}\\n\\n@media (max-width: 992px) {\\n  main .part-footer .btn-download {\\n    padding-top: 15.2px;\\n    padding-bottom: 15.2px;\\n  }\\n}\\n\\nmain .part-footer .part-footer-logo {\\n  height: 4rem;\\n  width: 14.5rem;\\n  margin: 0 auto;\\n}\\n\\n@media (max-width: 576px) {\\n  main .part-footer .display-2 {\\n    font-size: 2.25rem;\\n  }\\n\\n  main .part-footer a {\\n    display: block;\\n  }\\n\\n  main .part-footer .btn-outline-action {\\n    background-color: #fff;\\n    vertical-align: text-bottom;\\n    &:hover {\\n      color: #fff;\\n      background-color: #006dff;\\n      border-color: #006dff;\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2\n");

/***/ }),

/***/ 314:
/***/ ((module) => {

eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///314\n");

/***/ }),

/***/ 354:
/***/ ((module) => {

eval("\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzU0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsY0FBYztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2Nzcy1sb2FkZXIvZGlzdC9ydW50aW1lL3NvdXJjZU1hcHMuanM/YWYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoaXRlbSkge1xuICB2YXIgY29udGVudCA9IGl0ZW1bMV07XG4gIHZhciBjc3NNYXBwaW5nID0gaXRlbVszXTtcbiAgaWYgKCFjc3NNYXBwaW5nKSB7XG4gICAgcmV0dXJuIGNvbnRlbnQ7XG4gIH1cbiAgaWYgKHR5cGVvZiBidG9hID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB2YXIgYmFzZTY0ID0gYnRvYSh1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoY3NzTWFwcGluZykpKSk7XG4gICAgdmFyIGRhdGEgPSBcInNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LFwiLmNvbmNhdChiYXNlNjQpO1xuICAgIHZhciBzb3VyY2VNYXBwaW5nID0gXCIvKiMgXCIuY29uY2F0KGRhdGEsIFwiICovXCIpO1xuICAgIHJldHVybiBbY29udGVudF0uY29uY2F0KFtzb3VyY2VNYXBwaW5nXSkuam9pbihcIlxcblwiKTtcbiAgfVxuICByZXR1cm4gW2NvbnRlbnRdLmpvaW4oXCJcXG5cIik7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///354\n");

/***/ }),

/***/ 72:
/***/ ((module) => {

eval("\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///72\n");

/***/ }),

/***/ 659:
/***/ ((module) => {

eval("\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU5LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanM/YjIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIG1lbW8gPSB7fTtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBnZXRUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0eXBlb2YgbWVtb1t0YXJnZXRdID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIHN0eWxlVGFyZ2V0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcih0YXJnZXQpO1xuXG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIHJldHVybiBoZWFkIG9mIGlmcmFtZSBpbnN0ZWFkIG9mIGlmcmFtZSBpdHNlbGZcbiAgICBpZiAod2luZG93LkhUTUxJRnJhbWVFbGVtZW50ICYmIHN0eWxlVGFyZ2V0IGluc3RhbmNlb2Ygd2luZG93LkhUTUxJRnJhbWVFbGVtZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUaGlzIHdpbGwgdGhyb3cgYW4gZXhjZXB0aW9uIGlmIGFjY2VzcyB0byBpZnJhbWUgaXMgYmxvY2tlZFxuICAgICAgICAvLyBkdWUgdG8gY3Jvc3Mtb3JpZ2luIHJlc3RyaWN0aW9uc1xuICAgICAgICBzdHlsZVRhcmdldCA9IHN0eWxlVGFyZ2V0LmNvbnRlbnREb2N1bWVudC5oZWFkO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgICAgICBzdHlsZVRhcmdldCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG1lbW9bdGFyZ2V0XSA9IHN0eWxlVGFyZ2V0O1xuICB9XG4gIHJldHVybiBtZW1vW3RhcmdldF07XG59XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gaW5zZXJ0QnlTZWxlY3RvcihpbnNlcnQsIHN0eWxlKSB7XG4gIHZhciB0YXJnZXQgPSBnZXRUYXJnZXQoaW5zZXJ0KTtcbiAgaWYgKCF0YXJnZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBmaW5kIGEgc3R5bGUgdGFyZ2V0LiBUaGlzIHByb2JhYmx5IG1lYW5zIHRoYXQgdGhlIHZhbHVlIGZvciB0aGUgJ2luc2VydCcgcGFyYW1ldGVyIGlzIGludmFsaWQuXCIpO1xuICB9XG4gIHRhcmdldC5hcHBlbmRDaGlsZChzdHlsZSk7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydEJ5U2VsZWN0b3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///659\n");

/***/ }),

/***/ 540:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTQwLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRTdHlsZUVsZW1lbnQuanM/ZGU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBpbnNlcnRTdHlsZUVsZW1lbnQob3B0aW9ucykge1xuICB2YXIgZWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgb3B0aW9ucy5zZXRBdHRyaWJ1dGVzKGVsZW1lbnQsIG9wdGlvbnMuYXR0cmlidXRlcyk7XG4gIG9wdGlvbnMuaW5zZXJ0KGVsZW1lbnQsIG9wdGlvbnMub3B0aW9ucyk7XG4gIHJldHVybiBlbGVtZW50O1xufVxubW9kdWxlLmV4cG9ydHMgPSBpbnNlcnRTdHlsZUVsZW1lbnQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///540\n");

/***/ }),

/***/ 56:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLGNBQWMsS0FBd0MsR0FBRyxzQkFBaUIsR0FBRyxDQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanM/ZGRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMoc3R5bGVFbGVtZW50KSB7XG4gIHZhciBub25jZSA9IHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gXCJ1bmRlZmluZWRcIiA/IF9fd2VicGFja19ub25jZV9fIDogbnVsbDtcbiAgaWYgKG5vbmNlKSB7XG4gICAgc3R5bGVFbGVtZW50LnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///56\n");

/***/ }),

/***/ 825:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n  var needLayer = typeof obj.layer !== \"undefined\";\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += \"}\";\n  }\n  if (obj.media) {\n    css += \"}\";\n  }\n  if (obj.supports) {\n    css += \"}\";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === \"undefined\") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///825\n");

/***/ }),

/***/ 113:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTEzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc3R5bGVUYWdUcmFuc2Zvcm0uanM/MWRkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzdHlsZVRhZ1RyYW5zZm9ybShjc3MsIHN0eWxlRWxlbWVudCkge1xuICBpZiAoc3R5bGVFbGVtZW50LnN0eWxlU2hlZXQpIHtcbiAgICBzdHlsZUVsZW1lbnQuc3R5bGVTaGVldC5jc3NUZXh0ID0gY3NzO1xuICB9IGVsc2Uge1xuICAgIHdoaWxlIChzdHlsZUVsZW1lbnQuZmlyc3RDaGlsZCkge1xuICAgICAgc3R5bGVFbGVtZW50LnJlbW92ZUNoaWxkKHN0eWxlRWxlbWVudC5maXJzdENoaWxkKTtcbiAgICB9XG4gICAgc3R5bGVFbGVtZW50LmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZVRleHROb2RlKGNzcykpO1xuICB9XG59XG5tb2R1bGUuZXhwb3J0cyA9IHN0eWxlVGFnVHJhbnNmb3JtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///113\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__(787);
/******/ 	
/******/ })()
;