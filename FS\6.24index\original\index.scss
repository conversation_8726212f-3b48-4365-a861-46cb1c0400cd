* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  overflow: visible !important;
  background-color: #fff;
  color: #000;
  font-family: "Messina Sans", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  @media (max-width: 992px) {
    overflow: hidden !important;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
    font-family: "Messina Sans", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  h2 {
    text-align: center;
    font-weight: 700;
    font-size: 3.5rem;
    @media (max-width: 768px) {
      font-size: 24px;
    }
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .text-purple {
    color: #7a57ee;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .btn-wrapper {
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }
  }

  .btn-wrapper .btn {
    margin: 0;
    border-radius: 8px;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 158px;
    @media (min-width: 1280px) {
      &.btn-lg {
        min-width: 224px;
      }
    }
    @media (max-width: 768px) {
      width: 280px;
      height: 48px;
    }
  }

  @media (max-width: 768px) {
    .btn-wrapper .btn {
      display: block;
      vertical-align: baseline;
    }
  }
  @keyframes gradientAnimation {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  .btn-white {
    color: #7a57ee;
    &:hover {
      color: #7a57ee;
    }
  }

  .btn-colorful {
    background: linear-gradient(94.2deg, #7a57ee 41.38%, #39a0fa 73.42%, #24e3c8 96.58%);
    background-size: 200% 200%;
    animation: gradientAnimation 3s infinite linear;
    transition: transform 0.2s ease-in-out;
    color: #fff;
    &:hover {
      transform: scale(1.05);
      color: #fff;
    }
  }

  .btn-purple-bg {
    border: none;
    color: #fff;
    background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg.png) no-repeat center center / contain;
    aspect-ratio: 232 / 64;
    width: 232px;
    transition: transform 0.3s ease-in-out;
    @media (max-width: 768px) {
      background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg-mobile.png) no-repeat center center / contain;
      aspect-ratio: 280 / 48;

      margin: 0 auto;
    }

    &:hover {
      transform: translateY(-8px);
      color: #fff;
    }
  }

  .btn-purple-bg2 {
    border: none;
    color: #fff;
    background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-btn-bg2.png) no-repeat center center / contain;
    aspect-ratio: 280 / 64;
    width: 280px;
    transition: transform 0.3s ease-in-out;
    display: flex;
    gap: 0.5rem;
    box-shadow: 0px 14px 19.8px 0px #7858ff42;
    &:hover {
      color: #fff;
    }
  }

  .swiper-pagination {
    bottom: -4px !important;
  }

  .swiper-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background: rgba(0, 0, 0, 0.14);
    opacity: 1;
  }

  .swiper-pagination .swiper-pagination-bullet-active {
    width: 54px;
    background: linear-gradient(90deg, #ad8fff 0%, #7c65fe 100%);
    border-radius: 8px;
  }
  .part-banner {
    background: url(https://famisafe.wondershare.com/images/images-2025/index/banner.jpg) no-repeat center center / cover;
    @media (max-width: 768px) {
      background: url(https://famisafe.wondershare.com/images/images-2025/index/banner-mobile.png) no-repeat center center / cover;
      text-align: center;
    }
    .sub-title {
      display: flex;
      gap: 12px;
      align-items: center;
      justify-content: flex-start;
      @media (max-width: 768px) {
        justify-content: center;
      }
      .colorful-tip {
        background: linear-gradient(96.75deg, #7a57ee 36.5%, #0dc1ed 72.94%, #00d2ab 96.47%);
        border-radius: 24px;
        padding: 4px 12px;
        font-weight: 700;
        font-size: 1.25rem;
        line-height: 100%;
        color: #fff;
      }
    }
    h1 {
      font-weight: 700;
      font-size: 4.125rem;
      line-height: 100%;
      @media (max-width: 768px) {
        font-size: 32px;
      }
    }
    .system-list {
      display: flex;
      gap: 1rem;

      a {
        text-decoration: none;
        &:hover {
          color: #7a57ee;
        }
      }
    }
  }
  .part-honour {
    background-color: #fbf8ff;
    @keyframes ToRight {
      0% {
        transform: translate3d(0, 0, 0);
      }
      100% {
        transform: translate3d(-50%, 0, 0);
        -webkit-transform: translate3d(-50%, 0, 0);
        -moz-transform: translate3d(-50%, 0, 0);
        -ms-transform: translate3d(-50%, 0, 0);
        -o-transform: translate3d(-50%, 0, 0);
      }
    }
    .honour-list {
      display: flex;
      flex-wrap: nowrap;
      animation: ToRight 18s linear infinite;
      width: fit-content;
      .honour-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: auto;
        margin: 0 3rem;
        @media (max-width: 768px) {
          margin: 0 15px;
        }
        .honour-logo {
          height: 64px;
          width: 64px;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        }
        .honour-intro {
          white-space: nowrap;
          text-align: center;
          @media (max-width: 768px) {
            display: none;
          }
        }
      }
    }
  }
  .part-safeguard {
    .nav {
      display: flex;
      justify-content: center;
      gap: 2.875rem;
      flex-wrap: nowrap;
      .nav-item {
        text-decoration: none;
        border-radius: 1rem;
        background-color: #f8f7ff;
        color: #000;
        font-size: 1.5rem;
        flex: 1 1 calc(100% / 3);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1.25rem;
        cursor: pointer;
        &.active {
          background-color: #7a57ee;
          color: #fff;
          font-weight: 700;
        }
      }
    }
    .safeguard-box {
      position: relative;
      border-radius: 0.5rem;
      overflow: hidden;
      .feature-card {
        position: absolute;
        width: 100%;
        padding: 1.5rem;
        border-radius: 8px;
        background: linear-gradient(111.89deg, #ffffff -0.85%, rgba(255, 255, 255, 0.39) 78.51%);
        backdrop-filter: blur(16.5px);
        max-width: 250px;
        transition: transform 0.3s ease-in-out;
        &::after {
          content: "";
          position: absolute;
          inset: 0;
          border-radius: 8px;
          padding: 5px;
          background: linear-gradient(135.73deg, rgba(255, 255, 255, 0) 41.9%, rgba(255, 255, 255, 0.45) 118.83%);
          mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
          mask-composite: exclude;
          pointer-events: none;
        }
        &:hover {
          transform: translateY(-8px);
        }

        .feature-card-icon {
          width: 40%;
          position: absolute;
          top: 0;
          left: 0;
          transform: translate(-41%, -25%);
        }
        .feature-card-title {
          font-weight: 700;
          color: var(--color-title);
          text-align: center;
        }
        .feature-card-description {
          text-align: center;
          font-size: 0.875rem;
        }
      }
    }
    .safeguard-box-mobile {
      border-radius: 1rem;
      overflow: hidden;
      position: relative;

      .title {
        font-weight: 700;
        font-size: 20px;
        color: #000;
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding: 16px;
      }
      .feature-list {
        position: absolute;
        bottom: 0;
        left: 0;
        margin: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        border-radius: 8px;
        width: calc(100% - 32px);
        bottom: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(9px);
        padding: 12px;
        .feature-list-wrapper {
          display: flex;
          flex-direction: column;
          gap: 8px;
          width: auto;
          .feature-item {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 8px;
            font-size: 16px;
          }
        }
      }
    }
    .left-btn,
    .right-btn {
      position: absolute;
      z-index: 2;
      top: 34px;

      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #000;
      border-radius: 50%;
      color: #ffff;

      border: 1.5px solid rgba(255, 255, 255, 1);
      &:hover {
        background-color: #fff;

        color: #9281ff;
      }
    }
    .left-btn {
      left: 16px;
    }
    .right-btn {
      right: 16px;
    }
  }

  .part-digital {
    --digital-item-height: 87vh;
    height: calc(var(--digital-item-height) * 5);
    position: relative;
    background: rgba(244, 243, 255, 0.64);
    @media (max-width: 1280px) {
      height: auto;
    }

    .digital-wrapper {
      height: var(--digital-item-height);
      position: sticky;
      top: 72px;
      display: flex;
      flex-direction: column;
      @media (min-width: 2000px) {
        top: 10vh;
      }
      @media (max-width: 1280px) {
        height: auto;
        position: relative;
        top: unset;
        text-align: center;
      }
      @media (max-width: 576px) {
        .container {
          padding-right: 0;
        }
      }
    }
    h2 {
      margin-bottom: 6.875rem;
      margin-top: 4rem;
      @media (max-width: 1280px) {
        margin-top: 0;
        margin-bottom: 3rem;
      }
    }
    .family-title {
      font-weight: 700;
      font-size: 18px;
      color: #000;
      display: inline-block;
      padding: 4px 8px;
      background-color: #d7cdfa;
      border-radius: 999px;
      margin-bottom: 8px;
    }
    .digital-box {
      display: flex;
      background: linear-gradient(237.63deg, rgba(248, 247, 255, 0) 35.13%, rgba(248, 247, 255, 0.8) 88.35%),
        linear-gradient(211.11deg, #e2deff 14.16%, #e3dfff 42.27%);
      border-radius: 1rem;
      overflow-y: visible;
      justify-content: space-between;
      min-height: 418px;
      position: relative;
      @media (max-width: 1280px) {
        background: unset;
        overflow: hidden;
        display: block;
        border-radius: 16px;
      }
      @media (max-width: 576px) {
        margin: 0 auto;
        border-radius: 0;
      }
      .text-content {
        width: 45.84%;
        padding: 1.5rem 1.5rem 1.5rem 6.25rem;
        overflow: hidden;
        @media (max-width: 1600px) {
          padding: 1.5rem 1.5rem 1.5rem 2.25rem;
        }
        @media (max-width: 1280px) {
          width: 100%;
          padding: unset;
          border-radius: 0 0 16px 16px;
          overflow: hidden;
          .mobile-img-wrapper {
            border-radius: 16px;
            overflow: hidden;
          }
        }

        .digital-item {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          @media (max-width: 1280px) {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: auto;
            z-index: 3;
            background: #9788ffc9;
            backdrop-filter: blur(10px);
            padding: 10px 22px;
            display: block;
            text-align: center;
            border-radius: 0 0 16px 16px;
            overflow: hidden;
          }
          .digital-item-title {
            font-weight: 700;
            font-size: 2.5rem;
            color: #7a57ee;
            margin-bottom: 2rem;
            @media (max-width: 1280px) {
              font-size: 24px;
              line-height: 32px;
              font-weight: 700;
              color: #fff;
              margin-bottom: 0;
            }
          }
          .digital-item-description {
            font-size: 1.125rem;
            line-height: 2rem;
            padding-bottom: 1rem;
            @media (max-width: 1280px) {
              font-size: 16px;
              line-height: 20px;
              padding-bottom: 0;
              color: #fff;
            }
          }
        }
      }
      .img-content {
        width: 54.16%;
        overflow: visible;

        #digital-img-swiper {
          overflow: visible;
        }
        .img-item-wrapper {
          position: relative;
          height: 100%;
          img {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
          }
        }
      }
    }
  }

  .part-feature {
    background: rgba(244, 243, 255, 0.64);
    .feature-item {
      position: relative;
      border-radius: 1rem;
      overflow: hidden;
      @media (any-hover: hover) {
        &:hover {
          box-shadow: 0px 7px 14px 0px rgba(212, 207, 247, 1);
          .feature-detail-card {
            opacity: 1;
          }
        }
      }

      &-title {
        font-weight: 700;
        font-size: 1.25rem;
        padding: 1.5rem;
        position: absolute;
        left: 0;
        top: 0;
      }
      @media (min-width: 576px) {
        .feature-detail-card {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          z-index: 5;
          text-decoration: none;
          opacity: 0;
          transition: opacity 0.3s ease-in-out;
          background: linear-gradient(180deg, rgba(122, 87, 238, 0.6) 0%, #7a57ee 64.68%);

          backdrop-filter: blur(20px);

          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 1.5rem;
          padding: 1.5rem 3rem;

          margin: 0 auto;
          color: #fff;
          text-align: center;
        }

        .feature-detail-card-title {
          font-weight: 700;
          font-size: 1.5rem;
        }
        .feature-detail-card-description {
          font-size: 1.125rem;
        }
        .feature-detail-card-arrow {
          flex-shrink: 0;
          color: #fff;
          width: 2.5rem;
          height: 2.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          border: 2px solid rgba(255, 255, 255, 0.6);
          &:hover {
            border-color: #fff;
            background-color: #fff;
            color: #7a57ee;
          }
        }
      }
    }

    @media (max-width: 576px) {
      #feature-swiper {
        margin-left: -15px;
        margin-right: -15px;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          left: 0;
          bottom: 0;
          z-index: 2;
          width: 11.2%;
          height: 100%;
          background: linear-gradient(90deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);
          pointer-events: none;
        }
        &::after {
          content: "";
          position: absolute;
          right: 0;
          bottom: 0;
          z-index: 2;
          width: 11.2%;
          height: 100%;
          background: linear-gradient(270deg, rgba(246, 244, 255, 0.8) 22.45%, rgba(246, 244, 255, 0) 100%);
          pointer-events: none;
        }
      }
      #feature-swiper .swiper-slide {
        overflow: visible;
      }

      #feature-swiper .swiper-slide.swiper-slide-active {
        z-index: 5;
        .feature-item {
          overflow: visible;
          img {
            border: 4.28px solid #ffffff;
            box-shadow: 0px 10.69px 19.36px 0px #2803ec3d;
            border-radius: 1rem;
          }
        }
      }

      #feature-swiper .feature-detail-card {
        display: none;
      }
      .feature-item-mobile {
        height: 100%;
      }
      #feature-text-mobile-swiper .feature-detail-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-decoration: none;
      }

      .mobile-feature-text {
        margin-top: 2.125rem;
        border-radius: 1rem;
        background: linear-gradient(360deg, #7d58f9 15.62%, #a78dff 100.05%);
        box-shadow: 0px 6px 12.9px 0px #e3dffe;
        padding: 1rem;
        text-align: center;
        color: #fff;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%, -100%);
          width: 18px;
          height: 6px;
          background: url(https://famisafe.wondershare.com/images/images-2025/index/purple-top.png) no-repeat center center / contain;
        }
        .feature-detail-card-title {
          font-weight: 700;
          font-size: 18px;
          line-height: 32px;
          margin-bottom: 8px;
          color: #fff;
        }
        .feature-detail-card-description {
          font-size: 16px;
          line-height: 20px;
          color: #fff;
          margin-bottom: 8px;
        }
        .feature-detail-card-arrow {
          width: 32px;
          height: 32px;
          color: #7a57ee;
          background-color: #fff;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 auto;
        }
      }
    }
  }

  .part-geonection {
    .geonection-wrapper {
      background: url(https://famisafe.wondershare.com/images/images-2025/index/geonection-part-bg.png) no-repeat center center / cover;
      position: relative;
      border-radius: 1.5rem;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5.375rem 0 3.625rem;
      @media (max-width: 992px) {
        flex-direction: column;
        padding: 24px 0;
        justify-content: center;
        align-items: center;
        text-align: center;
      }
      .geonection-logo {
        position: absolute;
        top: 1.75rem;
        left: 1.5rem;
        z-index: 1;
        @media (max-width: 992px) {
          position: relative;
          width: 150px;
          top: unset;
          left: unset;
        }
      }
      .geonection-content {
        flex: 0 0 40.4%;
        position: relative;
        z-index: 1;
        padding-left: 5rem;
        @media (max-width: 1280px) {
          padding-left: 3rem;
        }
        @media (max-width: 992px) {
          flex: auto;
          padding-left: unset;
          padding: 0 16px;
        }
        .geonection-item-title {
          font-size: 2.5rem;
          font-weight: 700;
          @media (max-width: 992px) {
            font-size: 24px;
            line-height: 36px;
            margin-top: 16px;
            margin-bottom: 12px;
          }
        }
        .btn-green {
          background: linear-gradient(90.52deg, #92d45d 36.28%, #6ad018 75.06%);
          box-shadow: 0px 4px 4px 0px #ffffff40 inset, 0px -3px 4px 0px #ffffff40 inset;
          color: #000;
          font-size: 18px;
        }
      }
      .geonection-img {
        flex: 0 0 59.6%;
        position: relative;
        z-index: 1;
        @media (max-width: 992px) {
          flex: auto;
          margin-top: 36px;
        }
      }
    }
  }

  .part-customer {
    @media (max-width: 768px) {
      .mobile-container {
        max-width: 540px;
        margin: 0 auto;
      }
    }

    .img-container {
      position: relative;
      line-height: 0;
      overflow: hidden;
      height: 100%;
    }

    .img-container img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    @media (max-width: 1280px) {
      .assetsSwiper .box-style .img-container img {
        all: unset;
        max-width: 100%;
      }
    }

    .assetsSwiper .box-style .assets-title {
      position: absolute;
      width: 100%;
      text-align: center;
      font-size: 1.25rem;
      font-weight: 700;
      color: #fff;
      margin-bottom: 22px;
      padding: 0 16px;
      bottom: 0;
      left: 0;
      z-index: 2;
    }

    .assetsSwiper .swiper-slide.active .box-style .assets-title {
      display: none;
    }

    @media (max-width: 1280px) {
      .assetsSwiper .box-style .assets-title {
        display: none;
      }
    }

    @media (min-width: 1280px) {
      .assetsSwiper-box {
        position: relative;
      }

      .assetsSwiper .swiper-wrapper {
        aspect-ratio: 1920 / 456;
        gap: 14px;
        justify-content: space-between;
      }

      .assetsSwiper .swiper-slide {
        width: 12.2%;
        display: block;
        overflow: hidden;
        border-radius: 1rem;
        position: relative;
        transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
      }

      .assetsSwiper .swiper-slide.active {
        width: 48.13%;
        opacity: 1;
      }

      .assetsSwiper .swiper-slide .box-style {
        height: 100%;
        position: relative;
        border-radius: 1rem;
        overflow: hidden;
      }

      @keyframes fadeIn {
        from {
          visibility: hidden;
        }

        to {
          visibility: visible;
        }
      }

      .assetsSwiper .swiper-slide .box-style .customer-info-box {
        visibility: hidden;
      }

      .assetsSwiper .swiper-slide.active .box-style .customer-info-box {
        animation: fadeIn 0.01s ease-in-out;
        animation-delay: 0.8s;
        animation-fill-mode: forwards;
        position: absolute;
        margin: 24px;
        margin-bottom: 2.25rem;
        bottom: 0;
        left: 0;
        width: calc(100% - 24px * 2);
        background: rgba(0, 0, 0, 0.33);
        backdrop-filter: blur(10px);
        border-radius: 1rem;
        padding: 0.5rem 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 3rem;
      }

      .assetsSwiper .swiper-slide.active .box-style .customer-info-box .customer-info {
        font-size: 1rem;
        font-weight: 400;
        color: #fff;
        line-height: 100%;
      }

      .assetsSwiper .swiper-slide.active .box-style .customer-info-box {
      }

      .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 {
        position: absolute;
        left: 60px;
        top: 24px;
      }

      .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 .customer-info .customer-name {
        font-weight: 700;
        font-size: 1.25rem;
        line-height: 100%;
        color: #fff;
      }

      .assetsSwiper .swiper-slide.active .box-style .customer-info-box2 .customer-info .customer-desc {
        font-weight: 400;
        font-size: 1rem;
        line-height: 100%;
        color: #fff;
      }

      .assetsSwiper .swiper-slide:not(.active) .box-style::after {
        content: "";
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper {
        overflow: initial;
        padding-top: 24px;
      }
    }

    @media (max-width: 1280px) and (max-width: 768px) {
      .assetsSwiper {
        padding-top: 12px;
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper-box {
        padding: 0 15px;
      }

      .assetsSwiper .swiper-slide {
        opacity: 0.5;
      }

      .assetsSwiper .swiper-slide-active {
        opacity: 1;
      }

      .assetsSwiper .rounded-16 {
        border-radius: 8px;
      }
      .customer-info-box {
        position: absolute;
        margin: 16px;
        bottom: 0;
        left: 0;
        width: calc(100% - 16px * 2);
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(4px);
        border-radius: 8px;
        padding: 16px 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        .btn-wrapper {
          display: none;
        }
      }
      .customer-info-box2 {
        position: absolute;
        top: 16px;
        right: 16px;
        display: flex;
        align-items: stretch;
        justify-content: center;
        color: #fff;
        .customer-name {
          font-size: 18px;
          font-weight: 700;
        }
        .customer-desc {
          font-size: 16px;
          font-weight: 400;
        }
      }
    }
  }

  .part-saying {
    .saying-box {
      display: flex;
      justify-content: space-between;
      background: linear-gradient(180deg, rgba(254, 232, 226, 0) 23.47%, rgba(254, 232, 226, 0.86) 100%), linear-gradient(0deg, #7a57ee, #7a57ee);
      border-radius: 1.5rem;
      overflow: hidden;
      color: #fff;
      gap: 8.75rem;
      max-height: 732px;
      @media (max-width: 1600px) {
        gap: 3.75rem;
      }
      @media (max-width: 1280px) {
        flex-direction: column;
        gap: unset;
        max-height: unset;
      }
      .left-box {
        flex: 0 1 48%;
        padding: 2rem 4rem;
        padding-right: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        @media (max-width: 1280px) {
          flex: initial;
          padding: 30px 24px;
          text-align: center;
          align-items: center;
        }
        .saying-title {
          text-align: left;
          font-size: 3.5rem;
          line-height: 1.2;
          @media (max-width: 1280px) {
            text-align: center;
            font-size: 24px;
            font-weight: 700;
          }
        }
        .white-divider {
          width: 100%;
          height: 1px;
          background-color: #fff;
          opacity: 0.7;
          margin: 3rem 0;
          max-width: 395px;
          @media (max-width: 1280px) {
            display: none;
          }
        }
        .count-list {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #fff;
          flex-wrap: wrap;
          gap: 3.5rem 4rem;
          max-width: 344px;
          @media (max-width: 1280px) {
            max-width: unset;
            gap: 24px 50px;
            justify-content: space-around;
            margin-top: 24px;
          }
          .count-box {
            flex: 0 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            gap: 4px;
            color: #fff;
            max-width: 170px;
            @media (max-width: 576px) {
              flex: 0 0 40%;
            }
            .count {
              font-size: 2.5rem;
              font-weight: 700;
            }
            .count-desc {
              font-size: 1.25rem;
            }
          }
        }
      }
      .right-box {
        flex: 0 0 52%;
        padding-right: 3rem;
        display: flex;
        gap: 1.5rem;
        position: relative;
        @media (max-width: 1280px) {
          flex: initial;
          padding-right: 0;
          gap: 0;
          width: fit-content;
        }
        &::after {
          position: absolute;
          z-index: 1;
          bottom: 0;
          right: 0;
          content: "";
          width: 100%;
          height: 22.4%;
          background: linear-gradient(0deg, #ebd3e4 0%, rgba(211, 185, 230, 0) 93.33%);
          pointer-events: none;
          @media (max-width: 1280px) {
            display: none;
          }
        }
        &::before {
          position: absolute;
          z-index: 1;
          top: 0;
          right: 0;
          content: "";
          width: 100%;
          height: 22.4%;
          background: linear-gradient(180deg, #7a57ee 0%, rgba(122, 87, 238, 0) 93.33%);
          pointer-events: none;
          @media (max-width: 1280px) {
            display: none;
          }
        }

        .user-card-list {
          display: flex;
          flex-direction: column;
          height: fit-content;
          flex-wrap: nowrap;
          @media (max-width: 1280px) {
            flex-direction: row;
          }

          @keyframes marquee1 {
            0% {
              transform: translateY(0);
            }
            100% {
              transform: translateY(-50%);
            }
          }
          @keyframes marquee2 {
            0% {
              transform: translateY(-50%);
            }
            100% {
              transform: translateY(0);
            }
          }
          @keyframes marquee1-mobile {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-50%);
            }
          }

          &.list1 {
            animation: marquee1 40s linear infinite;
            @media (max-width: 1280px) {
              animation: marquee1-mobile 40s linear infinite;
            }
          }
          &.list2 {
            animation: marquee2 40s linear infinite;
            @media (max-width: 1280px) {
              animation: marquee2-mobile 40s linear infinite;
            }
          }
          &.list1-mobile {
            animation: marquee1-mobile 60s linear infinite;
            margin: 30px 0;
          }
          .user-card {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin: 1rem 0;
            background-color: #fff;
            border-radius: 1rem;
            overflow: hidden;
            padding: 1.5rem;
            color: #000;

            @media (max-width: 1280px) {
              margin: 0 5px;
              width: 276px;
              padding: 20px;
            }

            .useer-info {
              display: flex;
              gap: 0.875rem;
              align-items: center;
              .user-avatar {
                width: 3.625rem;
              }
              .user-name {
                font-size: 1.125rem;
                font-weight: 700;
                margin-bottom: 6px;
                line-height: 100%;
              }
            }
            .user-desc {
              font-size: 0.875rem;
              color: #000;
            }
          }
        }
      }
    }
  }
  .part-grow {
    @media (min-width: 1280px) {
      .swiper-wrapper {
        flex-wrap: wrap;
        gap: 8px;
        display: flex;
        .swiper-slide {
          &.what-new-slide {
            flex: 0 1 calc(61.2% - 8px);
          }
          &.tutorials-slide {
            flex: 0 1 calc(38.8% - 8px);
          }
          &.social-media-slide {
            flex: 0 1 calc(44.1% - 8px);
          }
          &.blogs-slide {
            flex: 0 1 calc(55.9% - 8px);
          }
        }
      }
    }
    .social-media-mobile-list {
      display: flex;
      gap: 16px;
      justify-content: center;
      align-items: center;
      margin-top: 24px;
    }
    .grow-box {
      border-radius: 1rem;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
      height: 100%;
      justify-content: space-between;
      @media (max-width: 1280px) {
        flex-direction: column;
        .content-wrapper {
          flex: auto !important;
          padding: 16px !important;
          text-align: center;
        }
        .img-wrapper {
          width: 100% !important;
          box-sizing: border-box;
        }
      }

      @keyframes jump {
        0% {
          transform: translateX(5px);
        }
        50% {
          transform: translateX(-3px);
        }
        100% {
          transform: translateX(5px);
        }
      }
      @media (any-hover: hover) {
        &:hover {
          .img-wrapper {
            transform: scale(1.1);
          }
          .black-arrow {
            animation: jump 1s infinite;
          }
        }
      }
      .card-link {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
      }
      .title {
        font-size: 2rem;
        font-weight: 700;
      }
    }
    .what-new {
      background: linear-gradient(300.68deg, #b7a3ff 21.43%, #f3ddff 55.08%, #ffe9dd 83.49%);
      .content-wrapper {
        flex: 0 1 46.52%;
        padding: 1.5rem 0;
        padding-left: 3.75rem;
      }
      .img-wrapper {
        width: 53.48%;
        transition: transform 0.3s linear;
        align-self: flex-end;
      }
    }
    .tutorials {
      background: linear-gradient(180deg, #d2d2ff 0%, #ece7fe 100%);

      .content-wrapper {
        flex: 0 1 51.4%;
        padding: 1rem;
        padding-left: 2.25rem;
      }
      .img-wrapper {
        width: 48.6%;
        transition: transform 0.3s linear;
        align-self: flex-end;
      }
    }
    .social-media {
      .social-media-list {
        display: flex;
        gap: 1.75rem;
        flex-wrap: nowrap;
        position: absolute;
        bottom: 40%;
        left: 17%;
        width: 65%;
        .social-media-item {
          flex: 1;
          transition: transform 0.3s linear;
          &:hover {
            transform: scale(1.4);
          }
        }
      }
    }
    .blogs {
      background: linear-gradient(313.48deg, #d3faf9 42.66%, #cbcbff 95.75%);

      .content-wrapper {
        flex: 0 1 41.03%;
        padding: 1rem;
        padding-left: 3.75rem;
      }
      .img-wrapper {
        width: 58.97%;
        transition: transform 0.3s linear;
        align-self: flex-end;
      }
    }
  }
  .part-protection {
    .protection-box {
      border-radius: 1.5rem;
      background: linear-gradient(299.31deg, rgba(215, 199, 255, 0.67) 13.16%, rgba(178, 165, 255, 0.67) 67.6%);
      padding-left: 80px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1.875rem;
      position: relative;
      @media (max-width: 1280px) {
        flex-direction: column;
        padding-left: 0;
        gap: 0px;
        justify-content: space-between;
        background: #e2dfff;
        padding: 30px;
      }
      .trusted-box,
      .privacy-box {
        flex: 1;
        border-radius: 8px;
        overflow: hidden;
        background: linear-gradient(141.94deg, #ffffff 21.96%, rgba(255, 255, 255, 0.7) 93.72%);
        padding: 1.5rem 2rem;
        text-align: center;

        @media (max-width: 1280px) {
          background: unset;
          padding-bottom: 0;
        }
        .title {
          font-size: 1.5rem;
          font-weight: 700;
          position: relative;
          text-align: center;
          display: inline-flex;
          margin-bottom: 1.25rem;
          @media (max-width: 1280px) {
            color: #7a57ee;
            margin-bottom: 16px;
          }
          &::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            transform: translate(-110%, -5%);
            aspect-ratio: 47 / 42;
            background: url(https://famisafe.wondershare.com/images/images-2025/index/feather-left.svg) no-repeat center center / contain;
            width: 2.625rem;
          }
          &::after {
            content: "";
            position: absolute;
            bottom: 0;
            right: 0;
            transform: translate(110%, -5%);
            aspect-ratio: 47 / 42;
            background: url(https://famisafe.wondershare.com/images/images-2025/index/feather-right.svg) no-repeat center center / contain;
            width: 2.625rem;
          }
        }
      }
      .purple-divider {
        width: 100%;
        height: 1px;
        background-color: #bbadfe;
        margin: 24px 0;
      }
      .purple-lock {
        width: 33.3%;
        @media (max-width: 1280px) {
          width: 122px;
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
  .part-footer {
    .footer-box {
      border-radius: 1rem;
      overflow: hidden;
      background-color: #e2dfff;
      background: url(https://famisafe.wondershare.com/images/images-2025/index/footer-bg.jpg) no-repeat center center / cover;
      margin: 0 2.625rem;
      padding: 6rem 3rem;
      text-align: center;
      @media (max-width: 768px) {
        margin: 0 15px;
        padding: 30px 15px;
      }
      .footer-logo {
      }
    }
  }
}
#modal-youtube .btn-action {
  background-color: #8c5bde;
  border-color: #8c5bde;
}
