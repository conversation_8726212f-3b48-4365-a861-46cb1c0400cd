import "./index.scss";

$(() => {
  if (window.innerWidth > 1279) {
    $(".assetsSwiper .swiper-slide").mouseenter(function () {
      $(this).addClass("active").siblings().removeClass("active");
      $(".assetsSwiper-box").css("--assetIndex", $(this).index());
    });
  } else {
    var assetsSwiper = new Swiper("#assetsSwiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      centeredSlides: true,
      lazy: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      loop: true,
      loopedSlides: 2,
      breakpoints: {
        768: {
          slidesPerView: 1.7,
          spaceBetween: 20,
        },
      },
      pagination: {
        el: ".assetsSwiper-pagination",
        clickable: true,
      },
    });
  }

  if (window.innerWidth < 992) {
    const devicesSwiper = new Swiper("#swiper-tips", {
      slidesPerView: 1.01,
      centeredSlides: true,
      spaceBetween: 15,
      loop: true,
      loopedSlides: 2,

      autoplay: {
        delay: 2500,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
      },
      pagination: {
        el: "#swiper-tips .swiper-pagination",
        clickable: true,
      },
    });
    const unlockSwiper = new Swiper("#swiper-unlock", {
      slidesPerView: 1.01,
      centeredSlides: true,
      spaceBetween: 15,
      loop: true,
      loopedSlides: 2,
      lazy: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
      },
      pagination: {
        el: "#swiper-unlock .swiper-pagination",
        clickable: true,
      },
    });
  }

  // 自动切换part-step标签页，每3秒切换一次
  let currentStepTabIndex = 0;
  const stepTabs = ["#step-1-tab", "#step-2-tab", "#step-3-tab"];
  const stepSwitchInterval = 3000;

  // 切换标签的函数
  const switchStepTab = () => {
    currentStepTabIndex = (currentStepTabIndex + 1) % stepTabs.length;
    $(stepTabs[currentStepTabIndex]).tab("show");
  };

  // 设置自动切换定时器
  let autoSwitchInterval = setInterval(switchStepTab, stepSwitchInterval);
  // 用户手动点击标签时重置计时器
  $(".part-step .nav-item").on("click", function () {
    clearInterval(autoSwitchInterval);

    const clickedTabId = $(this).attr("id");
    currentStepTabIndex = Math.max(0, stepTabs.indexOf(`#${clickedTabId}`));
    autoSwitchInterval = setInterval(switchStepTab, stepSwitchInterval);
  });

  var customerSwiper = new Swiper("#customer-swiper", {
    slidesPerView: 1,
    centeredSlides: true,
    spaceBetween: 10,
    loop: true,
    lazy: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    navigation: {
      nextEl: ".part-customer .right-btn",
      prevEl: ".part-customer .left-btn",
    },
    pagination: {
      el: "#customer-swiper .swiper-pagination",
      clickable: true,
    },
  });

  // 监听数字滚动部分是否可见
  function isElementFullyInViewport(el) {
    var rect = el.getBoundingClientRect();
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    var windowWidth = window.innerWidth || document.documentElement.clientWidth;
    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= windowHeight && rect.right <= windowWidth;
  }

  var stepVal = true;
  function handleScroll() {
    var myElement = $(".growth-numbers-item")[0]; // 获取DOM元素
    if (myElement && isElementFullyInViewport(myElement) && stepVal) {
      $(".count-num").countTo();
      $(".count-num-decimal").countTo({
        decimals: 2,
        separator: ".",
      });
      stepVal = false;
    }
  }
  // 使用防抖优化滚动事件
  var scrollTimeout;
  $(window).on("scroll", function () {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(handleScroll, 100);
  });
});
