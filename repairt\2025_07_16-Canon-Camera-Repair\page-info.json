{"pageId": "demo-page", "title": "演示页面", "description": "这是一个演示页面", "output": "Canon-Camera-Repair.html", "folderName": "2025_07_16-Canon-Camera-Repair", "buildTime": "2025-07-16T01:57:26.356Z", "buildDate": "2025/7/16", "buildHour": "09:57:26", "sourceFiles": [{"src": "main.html", "desc": "主要内容文件"}, {"src": "script.js", "desc": "JavaScript脚本"}, {"src": "style.scss", "desc": "样式文件"}], "cdnStyles": ["https://neveragain.allstatics.com/2019/assets/style/bootstrap-recoverit.min.css", "https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap", "https://neveragain.allstatics.com/2019/assets/vendor/swiper7-bundle.min.css", "https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,200..1000;1,200..1000&display=swap"], "cdnScripts": ["https://neveragain.allstatics.com/2019/assets/vendor/swiper7-bundle.min.js", "https://recoverit.wondershare.com/script/gsap.min.js", "https://recoverit.wondershare.com/script/ScrollTrigger.min.js"], "assetsConfig": {"baseUrl": "https://images.wondershare.com/repairit/images2025/Canon-Camera/", "videoBaseUrl": "https://repairit.wondershare.com/videos/video2025/video-repair/", "supportedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".svg", ".webp", ".mp4", ".webm", ".ogg", ".avi", ".mov", ".pdf", ".doc", ".docx", ".zip", ".rar"], "videoExtensions": [".mp4", ".webm", ".ogg", ".avi", ".mov"]}}