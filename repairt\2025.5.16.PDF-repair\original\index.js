import "./index.scss";

$(() => {
  // 自动切换part-step标签页，每3秒切换一次
  let currentStepTabIndex = 0;
  const stepTabs = ["#step-1-tab", "#step-2-tab", "#step-3-tab"];
  const stepSwitchInterval = 3000;

  // 切换标签的函数
  const switchStepTab = () => {
    currentStepTabIndex = (currentStepTabIndex + 1) % stepTabs.length;
    $(stepTabs[currentStepTabIndex]).tab("show");
  };

  // 设置自动切换定时器
  let autoSwitchInterval = setInterval(switchStepTab, stepSwitchInterval);
  // 用户手动点击标签时重置计时器
  $(".part-step .nav-item").on("click", function () {
    clearInterval(autoSwitchInterval);

    const clickedTabId = $(this).attr("id");
    currentStepTabIndex = Math.max(0, stepTabs.indexOf(`#${clickedTabId}`));
    autoSwitchInterval = setInterval(switchStepTab, stepSwitchInterval);
  });

  var customerSwiper = new Swiper("#customer-swiper", {
    slidesPerView: 1,
    centeredSlides: true,
    spaceBetween: 10,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    navigation: {
      nextEl: ".part-customer .right-btn",
      prevEl: ".part-customer .left-btn",
    },
    pagination: {
      el: "#customer-swiper .swiper-pagination",
      clickable: true,
    },
  });

  if (window.innerWidth < 992) {
    const devicesSwiper = new Swiper("#swiper-tips", {
      slidesPerView: 1.01,
      centeredSlides: true,
      spaceBetween: 15,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 2500,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
      },
      pagination: {
        el: "#swiper-tips .swiper-pagination",
        clickable: true,
      },
    });
  }
  if (window.innerWidth > 1279) {
    $(".assetsSwiper .swiper-slide").mouseenter(function () {
      $(this).addClass("active").siblings().removeClass("active");
      $(".assetsSwiper-box").css("--assetIndex", $(this).index());
    });
  } else {
    var assetsSwiper = new Swiper("#assetsSwiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      centeredSlides: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      loop: true,
      loopedSlides: 2,
      breakpoints: {
        768: {
          slidesPerView: 1,
          spaceBetween: 20,
          centeredSlides: true,
        },
      },
      pagination: {
        el: ".assetsSwiper-pagination",
        clickable: true,
      },
    });
  }
});
