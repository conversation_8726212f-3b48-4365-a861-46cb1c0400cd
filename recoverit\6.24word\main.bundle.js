/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 787:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

eval("\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\nvar injectStylesIntoStyleTag = __webpack_require__(72);\nvar injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleDomAPI.js\nvar styleDomAPI = __webpack_require__(825);\nvar styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertBySelector.js\nvar insertBySelector = __webpack_require__(659);\nvar insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\nvar setAttributesWithoutAttributes = __webpack_require__(56);\nvar setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertStyleElement.js\nvar insertStyleElement = __webpack_require__(540);\nvar insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);\n// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleTagTransform.js\nvar styleTagTransform = __webpack_require__(113);\nvar styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);\n// EXTERNAL MODULE: ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/index.scss\nvar cjs_js_src = __webpack_require__(2);\n;// ./src/index.scss\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (styleTagTransform_default());\noptions.setAttributes = (setAttributesWithoutAttributes_default());\noptions.insert = insertBySelector_default().bind(null, \"head\");\noptions.domAPI = (styleDomAPI_default());\noptions.insertStyleElement = (insertStyleElement_default());\n\nvar update = injectStylesIntoStyleTag_default()(cjs_js_src/* default */.A, options);\n\n\n\n\n       /* harmony default export */ const src = (cjs_js_src/* default */.A && cjs_js_src/* default */.A.locals ? cjs_js_src/* default */.A.locals : undefined);\n\n;// ./src/index.js\n\n$(() => {\n  // 新页面去除冲突样式文件\n  $('link[href=\"https://recoverit.wondershare.com/assets/app.bundle.css\"]').remove();\n  var customerSwiper = new Swiper(\"#customer-swiper\", {\n    slidesPerView: 1,\n    centeredSlides: true,\n    spaceBetween: 10,\n    loop: true,\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false\n    },\n    navigation: {\n      nextEl: \".part-customer .right-btn\",\n      prevEl: \".part-customer .left-btn\"\n    },\n    pagination: {\n      el: \"#customer-swiper .swiper-pagination\",\n      clickable: true\n    }\n  });\n  var methodsSwiper = new Swiper(\".swiper-methods\", {\n    loop: true,\n    allowTouchMove: false,\n    on: {\n      slideChange: function () {\n        $(\".methods-nav .col-md-4\").eq(this.realIndex).addClass(\"active\").siblings().removeClass(\"active\");\n      }\n    },\n    navigation: {\n      prevEl: \".slidebtn-methods-prev\",\n      nextEl: \".slidebtn-methods-next\"\n    }\n  });\n  $(\".methods-nav .col-md-4\").on(\"click\", function () {\n    methodsSwiper.slideTo($(this).index() + 1);\n  });\n\n  // pc端卡片 mobile端swiper\n  if (window.innerWidth < 992) {\n    const tipSwiper = new Swiper(\"#best-swiper\", {\n      slidesPerView: 1,\n      centeredSlides: true,\n      spaceBetween: 15,\n      loop: true,\n      loopedSlides: 2,\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false\n      },\n      breakpoints: {\n        768: {\n          slidesPerView: 2\n        }\n      },\n      pagination: {\n        el: \"#best-swiper .swiper-pagination\",\n        clickable: true\n      }\n    });\n  }\n\n  // 监听数字滚动部分是否可见\n  function isElementFullyInViewport(el) {\n    var rect = el.getBoundingClientRect();\n    var windowHeight = window.innerHeight || document.documentElement.clientHeight;\n    var windowWidth = window.innerWidth || document.documentElement.clientWidth;\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= windowHeight && rect.right <= windowWidth;\n  }\n  var stepVal = true;\n  function handleScroll() {\n    var myElement = $(\".count-box\")[0]; // 获取DOM元素\n    if (myElement && isElementFullyInViewport(myElement) && stepVal) {\n      $(\".count-num\").countTo();\n      stepVal = false;\n    }\n  }\n\n  // 使用防抖优化滚动事件\n  var scrollTimeout;\n  $(window).on(\"scroll\", function () {\n    clearTimeout(scrollTimeout);\n    scrollTimeout = setTimeout(handleScroll, 100);\n  });\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///787\n");

/***/ }),

/***/ 2:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(354);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(314);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `*{margin:0;padding:0;box-sizing:border-box}main{background-color:#f5f8ff;color:#000}main h1,main h2,main h3,main h4,main h5,main h6,main p,main div,main span,main ul,main li{margin-bottom:0;font-family:\"Mulish\",sans-serif}main h1,main h2{text-align:center}main h2{font-size:2.25rem;font-weight:800}@media(max-width: 576px){main .display-3{font-size:2.5rem}}main .opacity-7{opacity:.7}main .opacity-6{opacity:.6}main .text-blue{color:#3e90ff}main .text-gradient{color:transparent;background:linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%);-webkit-background-clip:text;background-clip:text}main .btn-wrapper{display:flex;justify-content:center;gap:1rem}@media(max-width: 768px){main .btn-wrapper{flex-direction:column;gap:8px;align-items:center}}main .btn{margin:0;display:flex;align-items:center;justify-content:center;text-transform:capitalize}main .btn svg{max-width:100%;height:100%}main .btn.btn-action{background-color:#0085ff;border-color:#0085ff;min-width:397px}main .btn.btn-action:hover,main .btn.btn-action:focus,main .btn.btn-action:active{color:#fff;background-color:#005dd9;border-color:#0057cc}@media(max-width: 768px){main .btn{display:block;min-width:unset !important}}main .part-banner{position:relative}main .part-banner .small-title{color:#13171a;font-size:1.875rem;margin-bottom:1rem;font-weight:700;line-height:90%}@media(max-width: 576px){main .part-banner .small-title{font-size:1.5rem}}@media(max-width: 768px){main .part-banner{background:linear-gradient(180deg, #c0eeff 0%, #f8fbff 96.46%)}}main .part-banner .banner-left-download{z-index:10;position:absolute;height:100%;top:0;left:0;width:33%}@media(max-width: 768px){main .part-banner .banner-left-download{display:none}}main .part-banner .banner-right-download{z-index:10;position:absolute;height:100%;top:0;right:0;width:33%}@media(max-width: 768px){main .part-banner .banner-right-download{display:none}}main .part-banner .video-wrapper{line-height:0;font-size:0}main .part-banner .video-wrapper video{height:100%;width:100%;object-fit:cover;min-height:533px}@media(max-width: 768px){main .part-banner .video-wrapper{display:none}}main .part-banner .part-banner-content{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;margin:0 auto}@media(max-width: 768px){main .part-banner .part-banner-content{position:relative;padding:3rem 0;text-align:center}}main .part-banner .part-banner-content h1{color:#13171a;line-height:110%}@media(max-width: 576px){main .part-banner .part-banner-content h1{font-size:28px}}main .part-banner .part-banner-content h1 span{color:#05a5ff}main .part-banner .part-banner-content h2{font-size:1.875rem;font-weight:700;line-height:100%;color:#13171a}@media(max-width: 576px){main .part-banner .part-banner-content h2{font-size:1.25rem;margin-bottom:1rem}}@media(max-width: 768px){main .part-banner .part-banner-content .btn{min-width:unset}}main .part-banner .part-banner-content .logo-list{display:flex;align-items:center;justify-content:center;gap:14.4px}main .part-banner .part-banner-content .logo-list .split-line{position:relative;height:16px;width:2px;top:70%;border-radius:1.5px;background-color:rgba(0,0,0,.7)}@media(max-width: 768px){main .part-banner .part-banner-content .logo-list{flex-wrap:wrap}}@media(max-width: 768px){main .part-banner .part-banner-content .logo-list .logo-img{flex:1;max-height:24px;object-fit:contain}}main .part-format .video-wrapper{line-height:0;font-size:0;border-radius:16px;overflow:hidden}main .part-format .video-wrapper video{width:100%;height:100%;object-fit:cover;filter:grayscale(0);clip-path:fill-box}main .part-files .file-box{height:100%;display:flex;flex-direction:column;border-radius:1rem;overflow:hidden}main .part-files .file-box .file-box-content{background-color:#fff;padding:2rem;flex:1;display:flex;flex-direction:column}main .part-files .file-box .file-box-content p{color:#787878}@media(max-width: 576px){main .part-files .file-box .file-box-content{padding:8px}}main .part-files .file-box .file-box-content .box-title{font-weight:700;font-size:1.25rem;color:#000;text-decoration:none;display:inline-block;margin-bottom:.75rem}@media(max-width: 576px){main .part-files .col-6{padding-right:8px;padding-left:8px}main .part-files .col-6:nth-child(odd){padding-right:4px}main .part-files .col-6:nth-child(even){padding-left:4px}}main .part-devices .device-list{max-width:1310px;margin:0 auto;display:flex;justify-content:space-around;gap:1rem}@media(max-width: 768px){main .part-devices .device-list{flex-wrap:wrap}}main .part-devices .device-list .device-item{flex:1;display:flex;flex-direction:column;align-items:center;gap:1.5rem;max-width:7.5rem;text-align:center}@media(max-width: 768px){main .part-devices .device-list .device-item{flex:1 1 20%}}main .part-devices .device-list .device-item img{transition:transform .2s linear}@media(any-hover: hover){main .part-devices .device-list .device-item img:hover{transform:scale(1.3)}}main .part-tech .tech-wrapper{border-radius:2.5rem;overflow:hidden;background:url(https://images.wondershare.com/recoverit/images2025/PPT/tech-bg.jpg) no-repeat center center/cover;padding:4.375rem 2rem}main .part-tech .tech-wrapper .tech-wrapper-inner{max-width:786px;margin:0 auto;display:flex;flex-direction:column;gap:2rem}main .part-tech .tech-wrapper .tech-wrapper-inner .tech-item{display:flex;justify-content:center;align-items:center;gap:80px;color:#fff}@media(max-width: 768px){main .part-tech .tech-wrapper .tech-wrapper-inner .tech-item{flex-direction:column;gap:1rem}}main .part-tech .tech-wrapper .tech-wrapper-inner .tech-item .left-content{width:202px;display:flex;flex-direction:column;justify-content:center;align-items:center;gap:12px;color:#fff}main .part-tech .tech-wrapper .tech-wrapper-inner .tech-item .right-content{flex:1;text-align:left;font-weight:500;color:#fff}main .part-tech .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail{display:flex;align-items:flex-start;gap:.75rem}@media(max-width: 768px){main .part-tech .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail{justify-content:center;text-align:center}}main .part-tech .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail .sys-title{font-weight:700;font-size:1.125rem}main .part-tech .tech-wrapper .tech-wrapper-inner .tech-item-dividing{width:100%;border-bottom:1px dashed rgba(255,255,255,.5)}main .part-methods{background-color:#e4efff}main .part-methods .product-box{background-color:#fff;padding:1.5rem 2rem;border-radius:1rem;display:flex;justify-content:space-between;align-items:center}main .part-methods .product-box .btn-action{width:auto;min-width:unset;background:linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%);border:transparent}main .part-methods .product-box .btn-action:hover{color:#fff;background:#0085ff !important}@media(max-width: 768px){main .part-methods .product-box{flex-direction:column;align-items:stretch;gap:8px}}main .part-methods .product-box .left-content{display:flex;gap:.75rem;align-items:center}main .part-methods .product-box .left-content .icon-wrapper{width:3rem}main .part-methods .methods-compare{background:#fff;background:rgba(255,255,255,.501961);border-radius:1rem}main .part-methods .methods-compare .feature-item{display:flex;align-items:center;gap:.5rem;margin-top:.5rem}main .part-methods .methods-nav-item{border:1px solid #1a8dff;font-size:1.25rem;border-radius:.5rem;color:#1a8dff;cursor:pointer;text-align:center;height:100%;display:flex;align-items:center;justify-content:center}main .part-methods .methods-nav .col-md-4.active .methods-nav-item{background-color:#1a8dff;color:#fff;font-weight:700}main .part-methods .methods-warning{padding:1.125rem 3rem;border:1px solid #1a8dff;border-radius:.75rem;color:rgba(0,0,0,.7)}@media(max-width: 576px){main .part-methods .methods-warning{padding:1rem 1rem}}main .part-methods .slidebtn-methods{display:inline-block;width:48px;height:49px;background-image:url(\"https://images.wondershare.com/recoverit/images2025/ziprecoverit/icon-arrow-1.png\");background-repeat:no-repeat;background-size:100% 100%;position:absolute;top:40%;cursor:pointer}main .part-methods .slidebtn-methods:hover{background-image:url(\"https://images.wondershare.com/recoverit/images2025/ziprecoverit/icon-arrow-2.png\");transform:rotate(180deg)}main .part-methods .slidebtn-methods-prev{left:-67px}main .part-methods .slidebtn-methods-next{right:-67px;transform:rotate(180deg)}main .part-methods .slidebtn-methods-next:hover{transform:rotate(0)}main .part-how .nav{display:flex;flex-wrap:nowrap;gap:12px;padding-top:3rem;padding-bottom:1.875rem}@media(max-width: 768px){main .part-how .nav{padding-top:1.5rem}}main .part-how .nav .nav-item{flex:1 1 50%;text-align:center;padding:1rem;border-radius:1rem;background-color:#fff;border:1px solid #b5dae8;font-weight:600;font-size:1.125rem;color:#000;text-decoration:none;transition:unset;display:flex;align-items:center;justify-content:center}main .part-how .nav .nav-item.active{background:linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),linear-gradient(0deg, #0055fb, #0055fb);color:#fff}main .part-how .how-box{display:flex;border-radius:16px;overflow:hidden;background-color:#fff;padding:2.5rem 3.5rem;gap:2.5rem;justify-content:center;align-items:center}@media(max-width: 992px){main .part-how .how-box{flex-direction:column;padding:1.5rem 1.5rem;gap:1.5rem}}main .part-how .how-box .content-wrapper{flex:1 1 46%;width:100%}main .part-how .how-box .content-wrapper .advantages-box{padding:1.5rem 1rem;background-color:#f6fff7;position:relative;border-radius:12px;overflow:hidden}main .part-how .how-box .content-wrapper .advantages-box::before{content:\"Pros\";position:absolute;top:0;right:0;font-size:1.125rem;font-weight:500;line-height:100%;color:#fff;background-color:#0cad73;border-radius:0 12px 0 12px;padding:4px .75rem}main .part-how .how-box .content-wrapper .advantages-box .advantages-list{display:flex;gap:1.25rem;flex-direction:column}main .part-how .how-box .content-wrapper .advantages-box .advantages-list .advantage-item{gap:12px;display:flex;align-items:center;line-height:100%;font-size:1.125rem;font-weight:500}main .part-how .how-box .content-wrapper .disadvantages-box{padding:1.5rem 1rem;background-color:#fff9fc;position:relative;border-radius:12px;overflow:hidden;min-height:105px}main .part-how .how-box .content-wrapper .disadvantages-box::before{content:\"Cons\";position:absolute;top:0;right:0;font-size:1.125rem;font-weight:500;line-height:100%;color:#fff;background-color:#ff4a75;border-radius:0 12px 0 12px;padding:4px .75rem}main .part-how .how-box .content-wrapper .disadvantages-box .disadvantages-list{display:flex;gap:1.25rem;flex-direction:column}main .part-how .how-box .content-wrapper .disadvantages-box .disadvantages-list .disadvantage-item{gap:12px;display:flex;align-items:center;line-height:100%;font-size:1.125rem;font-weight:500}main .part-how .how-box .video-wrapper{line-height:0;font-size:0;border-radius:1.25rem;overflow:hidden;flex:0 0 54%;height:100%;width:100%}main .part-customer .customer-wrapper{border-radius:2rem;overflow:hidden;background-color:#fff;display:flex;flex-direction:column;height:100%;position:relative}main .part-customer .customer-wrapper .customer-info-wrapper{position:absolute;top:50%;left:8.7%;min-height:80%;padding:1.25rem 1.875rem;transform:translateY(-50%);background:rgba(255,255,255,.7);backdrop-filter:blur(35px);border-radius:1.5rem;max-width:460px}@media(max-width: 1600px){main .part-customer .customer-wrapper .customer-info-wrapper{max-width:460px}}@media(max-width: 992px){main .part-customer .customer-wrapper .customer-info-wrapper{position:relative;transform:unset;top:initial;left:initial;right:initial !important;min-height:unset;padding:1.25rem 1.875rem;max-width:100%;border-radius:0;padding:1rem}}main .part-customer .customer-wrapper .customer-info-wrapper.right{left:unset;right:8.7%}main .part-customer .customer-wrapper .customer-info-wrapper .customer-info-list{display:flex;align-items:center;gap:24px;padding-left:14px}@media(max-width: 576px){main .part-customer .customer-wrapper .customer-info-wrapper .customer-info-list{gap:18px}}main .part-customer .customer-wrapper .customer-info-wrapper .customer-detail .detail-title{color:#0080ff;font-size:1.25rem;font-weight:700;margin-bottom:.75rem;margin-top:1.25rem}main .part-customer .customer-wrapper .customer-info-list .customer-title,main .part-customer .customer-wrapper .customer-info-list .customer-profession,main .part-customer .customer-wrapper .customer-info-list .customer-age{position:relative;background-color:#bcd3e9;border-radius:0 6px 6px 0;padding-right:8px;padding-left:5px;font-size:14px;color:#000;font-weight:600;height:31px;display:flex;align-items:center;white-space:nowrap}@media(max-width: 576px){main .part-customer .customer-wrapper .customer-info-list .customer-title,main .part-customer .customer-wrapper .customer-info-list .customer-profession,main .part-customer .customer-wrapper .customer-info-list .customer-age{font-size:11px}}main .part-customer .customer-wrapper .customer-info-list .customer-title::before,main .part-customer .customer-wrapper .customer-info-list .customer-profession::before,main .part-customer .customer-wrapper .customer-info-list .customer-age::before{content:\"\";position:absolute;height:100%;aspect-ratio:28/62;left:0;top:0;transform:translateX(-88%);background:url(https://images.wondershare.com/recoverit/images2025/PPT/left-tip.png) no-repeat center center/contain}main .part-customer .left-btn,main .part-customer .right-btn{background-color:silver;width:2.25rem;aspect-ratio:1/1;display:flex;justify-content:center;align-items:center;border-radius:50%;cursor:pointer;position:absolute;top:36%}@media(max-width: 576px){main .part-customer .left-btn,main .part-customer .right-btn{display:none}}main .part-customer .left-btn:hover,main .part-customer .right-btn:hover{background-color:#006dff}main .part-customer .right-btn{right:-3.25rem}@media(max-width: 768px){main .part-customer .right-btn{right:1.55rem}}main .part-customer .left-btn{left:-3.25rem}@media(max-width: 768px){main .part-customer .left-btn{left:1.55rem}}main .part-count .count-list{display:flex;align-items:center;justify-content:center;color:#fff}@media(max-width: 768px){main .part-count .count-list{flex-wrap:wrap;gap:4px}}main .part-count .count-list .count-box{flex:1 1 auto;display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative}@media(max-width: 768px){main .part-count .count-list .count-box{flex:1 1 45%}}main .part-count .count-list .count-box .count{display:flex;align-items:top;justify-content:center;font-weight:800;font-size:4rem;line-height:130%;letter-spacing:0%;text-align:center;color:#3e90ff}@media(max-width: 1280px){main .part-count .count-list .count-box .count{font-size:3.5rem}}main .part-count .count-list .count-box .count .count-num{font-weight:800;font-size:4rem;line-height:130%;letter-spacing:0%;text-align:center;color:#3e90ff}@media(max-width: 1280px){main .part-count .count-list .count-box .count .count-num{font-size:3.5rem}}main .part-count .count-list .count-box .count .count-plus{font-weight:700;font-size:2.5rem;line-height:130%;letter-spacing:0%;text-align:center;color:#3e90ff}@media(max-width: 1280px){main .part-count .count-list .count-box .count .count-plus{font-size:2rem}}main .part-count .count-list .count-box .count-desc{font-weight:400;font-size:1.5rem;line-height:130%;letter-spacing:0%;text-align:center;color:#616161}@media(min-width: 992px){main .part-bestSwiper .swiper-wrapper{gap:1rem}main .part-bestSwiper .swiper-wrapper .swiper-slide{flex:1 1 calc(25% - 1rem * 3)}}main .part-bestSwiper .best-box{height:100%;border-radius:1rem;background-color:#fff;padding:2.5rem 2rem;overflow:hidden;position:relative;color:#000}main .part-bestSwiper .best-box .box-download{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:5}main .part-bestSwiper .best-box .download-icon{position:absolute;right:1rem;top:1rem;width:1.5rem}main .part-bestSwiper .best-box .download-icon .active-img{display:none}main .part-bestSwiper .best-box .best-icon{width:3.5rem}main .part-bestSwiper .best-box .best-icon .active-img{display:none}main .part-bestSwiper .best-box .best-item-title{font-weight:700;font-size:1.125rem;line-height:100%;color:inherit;margin-bottom:6px}main .part-bestSwiper .best-box .best-item-desc{font-size:.875rem;line-height:100%;color:inherit;opacity:.8}@media(any-hover: hover){main .part-bestSwiper .best-box:has(.box-download:hover){background-color:#3e90ff;color:#fff}main .part-bestSwiper .best-box:has(.box-download:hover) .best-icon .active-img{display:inline-block}main .part-bestSwiper .best-box:has(.box-download:hover) .best-icon .default-img{display:none}main .part-bestSwiper .best-box:has(.box-download:hover) .download-icon .active-img{display:inline-block}main .part-bestSwiper .best-box:has(.box-download:hover) .download-icon .default-img{display:none}}main .part-links .part-links-line{height:100%;display:flex;flex-direction:column;justify-content:center}main .part-links .line-border{border-right:1px solid rgba(0,0,0,.3);border-left:1px solid rgba(0,0,0,.3)}@media(max-width: 1280px){main .part-links .line-border{border-right:unset}}@media(max-width: 768px){main .part-links .line-border{border-left:unset}}main .part-links .text-link{font-size:.875rem;color:rgba(0,0,0,.7);margin-top:1.5rem;display:block;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}main .part-links .text-link:hover{color:#0055fb}main .part-links .part-links-videos{height:100%;display:flex;flex-direction:column;justify-content:space-between}main .part-links .part-links-videos .video-wrapper{border-radius:.75rem}@media(max-width: 1280px){main .part-links .part-links-videos{flex-direction:row;padding-top:2rem}}@media(max-width: 576px){main .part-links .part-links-videos{display:block}}main .part-links .text-line4{display:-webkit-box;-webkit-line-clamp:4;-webkit-box-orient:vertical;overflow:hidden}main .part-footer .footer-box{border-radius:1rem;background:url(https://images.wondershare.com/recoverit/images2025/drfone/footer.jpg) no-repeat center center/cover;margin:0 -2.5rem;padding:3.5rem 1.5rem;text-align:center}@media(max-width: 768px){main .part-footer .footer-box{margin:0}}@media(max-width: 768px){main .part-footer .logo-icon img{height:3rem}}main .part-footer .btn-wrapper .btn-white{color:#0196ff !important}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/index.scss\"],\"names\":[],\"mappings\":\"AAAA,EACE,QAAA,CACA,SAAA,CACA,qBAAA,CAGF,KACE,wBAAA,CACA,UAAA,CAEA,0FAWE,eAAA,CACA,+BAAA,CAGF,gBAEE,iBAAA,CAGF,QACE,iBAAA,CACA,eAAA,CAIA,yBADF,gBAEI,gBAAA,CAAA,CAIJ,gBACE,UAAA,CAGF,gBACE,UAAA,CAGF,gBACE,aAAA,CAGF,oBACE,iBAAA,CACA,mEAAA,CAEA,4BAAA,CACA,oBAAA,CAGF,kBACE,YAAA,CACA,sBAAA,CACA,QAAA,CACA,yBAJF,kBAKI,qBAAA,CACA,OAAA,CACA,kBAAA,CAAA,CAGJ,UACE,QAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,yBAAA,CACA,cACE,cAAA,CACA,WAAA,CAEF,qBACE,wBAAA,CACA,oBAAA,CACA,eAAA,CAEA,kFAGE,UAAA,CACA,wBAAA,CACA,oBAAA,CAGJ,yBAvBF,UAwBI,aAAA,CACA,0BAAA,CAAA,CAIJ,kBACE,iBAAA,CACA,+BACE,aAAA,CACA,kBAAA,CACA,kBAAA,CACA,eAAA,CACA,eAAA,CACA,yBANF,+BAOI,gBAAA,CAAA,CAKN,yBACE,kBACE,8DAAA,CAAA,CAIJ,wCACE,UAAA,CACA,iBAAA,CACA,WAAA,CACA,KAAA,CACA,MAAA,CACA,SAAA,CACA,yBAPF,wCAQI,YAAA,CAAA,CAGJ,yCACE,UAAA,CACA,iBAAA,CACA,WAAA,CACA,KAAA,CACA,OAAA,CACA,SAAA,CACA,yBAPF,yCAQI,YAAA,CAAA,CAIJ,iCACE,aAAA,CACA,WAAA,CAGF,uCACE,WAAA,CACA,UAAA,CACA,gBAAA,CACA,gBAAA,CAGF,yBACE,iCACE,YAAA,CAAA,CAIJ,uCACE,iBAAA,CACA,KAAA,CACA,MAAA,CACA,UAAA,CACA,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,kBAAA,CACA,aAAA,CAGF,yBACE,uCACE,iBAAA,CACA,cAAA,CACA,iBAAA,CAAA,CAIJ,0CACE,aAAA,CACA,gBAAA,CAGF,yBACE,0CACE,cAAA,CAAA,CAIJ,+CACE,aAAA,CAGF,0CACE,kBAAA,CACA,eAAA,CACA,gBAAA,CACA,aAAA,CAGF,yBACE,0CACE,iBAAA,CACA,kBAAA,CAAA,CAIJ,yBACE,4CACE,eAAA,CAAA,CAIJ,kDACE,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,UAAA,CAEA,8DACE,iBAAA,CACA,WAAA,CACA,SAAA,CACA,OAAA,CACA,mBAAA,CACA,+BAAA,CAIJ,yBACE,kDACE,cAAA,CAAA,CAIJ,yBACE,4DACE,MAAA,CACA,eAAA,CACA,kBAAA,CAAA,CAKF,iCACE,aAAA,CACA,WAAA,CACA,kBAAA,CACA,eAAA,CAEA,uCACE,UAAA,CACA,WAAA,CACA,gBAAA,CAEA,mBAAA,CAEA,kBAAA,CAMJ,2BACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,eAAA,CAGF,6CACE,qBAAA,CACA,YAAA,CACA,MAAA,CACA,YAAA,CACA,qBAAA,CAEA,+CACE,aAAA,CAIJ,yBACE,6CACE,WAAA,CAAA,CAIJ,wDACE,eAAA,CACA,iBAAA,CACA,UAAA,CACA,oBAAA,CACA,oBAAA,CACA,oBAAA,CAGF,yBACE,wBACE,iBAAA,CACA,gBAAA,CAGF,uCACE,iBAAA,CAGF,wCACE,gBAAA,CAAA,CAMJ,gCACE,gBAAA,CACA,aAAA,CACA,YAAA,CACA,4BAAA,CACA,QAAA,CACA,yBANF,gCAOI,cAAA,CAAA,CAEF,6CACE,MAAA,CACA,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,UAAA,CACA,gBAAA,CACA,iBAAA,CACA,yBARF,6CASI,YAAA,CAAA,CAEF,iDACE,+BAAA,CACA,yBACE,uDACE,oBAAA,CAAA,CASV,8BACE,oBAAA,CACA,eAAA,CACA,iHAAA,CACA,qBAAA,CAGF,kDACE,eAAA,CACA,aAAA,CACA,YAAA,CACA,qBAAA,CACA,QAAA,CAGF,6DACE,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,QAAA,CACA,UAAA,CAGF,yBACE,6DACE,qBAAA,CACA,QAAA,CAAA,CAIJ,2EACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CACA,kBAAA,CACA,QAAA,CACA,UAAA,CAGF,4EACE,MAAA,CACA,eAAA,CACA,eAAA,CACA,UAAA,CAGF,8FACE,YAAA,CACA,sBAAA,CACA,UAAA,CAGF,yBACE,8FACE,sBAAA,CACA,iBAAA,CAAA,CAIJ,yGACE,eAAA,CACA,kBAAA,CAGF,sEACE,UAAA,CACA,6CAAA,CAIJ,mBACE,wBAAA,CAEA,gCACE,qBAAA,CACA,mBAAA,CACA,kBAAA,CACA,YAAA,CACA,6BAAA,CACA,kBAAA,CACA,4CACE,UAAA,CACA,eAAA,CACA,mEAAA,CACA,kBAAA,CACA,kDACE,UAAA,CACA,6BAAA,CAKN,yBACE,gCACE,qBAAA,CACA,mBAAA,CACA,OAAA,CAAA,CAIJ,8CACE,YAAA,CACA,UAAA,CACA,kBAAA,CAGF,4DACE,UAAA,CAGF,oCACE,eAAA,CACA,oCAAA,CACA,kBAAA,CACA,kDACE,YAAA,CACA,kBAAA,CACA,SAAA,CACA,gBAAA,CAIJ,qCACE,wBAAA,CACA,iBAAA,CACA,mBAAA,CACA,aAAA,CACA,cAAA,CACA,iBAAA,CACA,WAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CAGF,mEACE,wBAAA,CACA,UAAA,CACA,eAAA,CAGF,oCACE,qBAAA,CACA,wBAAA,CACA,oBAAA,CACA,oBAAA,CAGF,yBACE,oCACE,iBAAA,CAAA,CAIJ,qCACE,oBAAA,CACA,UAAA,CACA,WAAA,CACA,yGAAA,CACA,2BAAA,CACA,yBAAA,CACA,iBAAA,CACA,OAAA,CACA,cAAA,CAGF,2CACE,yGAAA,CACA,wBAAA,CAGF,0CACE,UAAA,CAGF,0CACE,WAAA,CACA,wBAAA,CAGF,gDACE,mBAAA,CAIF,oBACE,YAAA,CACA,gBAAA,CAEA,QAAA,CACA,gBAAA,CACA,uBAAA,CAGF,yBACE,oBACE,kBAAA,CAAA,CAIJ,8BACE,YAAA,CACA,iBAAA,CACA,YAAA,CACA,kBAAA,CACA,qBAAA,CACA,wBAAA,CACA,eAAA,CACA,kBAAA,CACA,UAAA,CACA,oBAAA,CACA,gBAAA,CACA,YAAA,CACA,kBAAA,CACA,sBAAA,CAGF,qCACE,2GAAA,CACA,UAAA,CAGF,wBACE,YAAA,CACA,kBAAA,CACA,eAAA,CACA,qBAAA,CACA,qBAAA,CACA,UAAA,CACA,sBAAA,CACA,kBAAA,CAGF,yBACE,wBACE,qBAAA,CACA,qBAAA,CACA,UAAA,CAAA,CAIJ,yCACE,YAAA,CACA,UAAA,CAGF,yDACE,mBAAA,CACA,wBAAA,CACA,iBAAA,CACA,kBAAA,CACA,eAAA,CAGF,iEACE,cAAA,CACA,iBAAA,CACA,KAAA,CACA,OAAA,CACA,kBAAA,CACA,eAAA,CACA,gBAAA,CACA,UAAA,CACA,wBAAA,CACA,2BAAA,CACA,kBAAA,CAGF,0EACE,YAAA,CACA,WAAA,CACA,qBAAA,CAGF,0FACE,QAAA,CACA,YAAA,CACA,kBAAA,CACA,gBAAA,CACA,kBAAA,CACA,eAAA,CAGF,4DACE,mBAAA,CACA,wBAAA,CACA,iBAAA,CACA,kBAAA,CACA,eAAA,CACA,gBAAA,CAGF,oEACE,cAAA,CACA,iBAAA,CACA,KAAA,CACA,OAAA,CACA,kBAAA,CACA,eAAA,CACA,gBAAA,CACA,UAAA,CACA,wBAAA,CACA,2BAAA,CACA,kBAAA,CAGF,gFACE,YAAA,CACA,WAAA,CACA,qBAAA,CAGF,mGACE,QAAA,CACA,YAAA,CACA,kBAAA,CACA,gBAAA,CACA,kBAAA,CACA,eAAA,CAGF,uCACE,aAAA,CACA,WAAA,CACA,qBAAA,CACA,eAAA,CACA,YAAA,CACA,WAAA,CACA,UAAA,CAKF,sCACE,kBAAA,CACA,eAAA,CACA,qBAAA,CACA,YAAA,CACA,qBAAA,CACA,WAAA,CACA,iBAAA,CACA,6DACE,iBAAA,CACA,OAAA,CACA,SAAA,CACA,cAAA,CACA,wBAAA,CACA,0BAAA,CACA,+BAAA,CACA,0BAAA,CACA,oBAAA,CACA,eAAA,CAEA,0BAZF,6DAaI,eAAA,CAAA,CAEF,yBAfF,6DAgBI,iBAAA,CACA,eAAA,CACA,WAAA,CACA,YAAA,CACA,wBAAA,CACA,gBAAA,CACA,wBAAA,CACA,cAAA,CACA,eAAA,CACA,YAAA,CAAA,CAGF,mEACE,UAAA,CACA,UAAA,CAEF,iFACE,YAAA,CACA,kBAAA,CACA,QAAA,CACA,iBAAA,CACA,yBALF,iFAMI,QAAA,CAAA,CAIF,4FACE,aAAA,CACA,iBAAA,CACA,eAAA,CACA,oBAAA,CACA,kBAAA,CAMR,iOAGE,iBAAA,CACA,wBAAA,CACA,yBAAA,CACA,iBAAA,CACA,gBAAA,CACA,cAAA,CACA,UAAA,CACA,eAAA,CACA,WAAA,CACA,YAAA,CACA,kBAAA,CACA,kBAAA,CACA,yBAfF,iOAgBI,cAAA,CAAA,CAIJ,yPAGE,UAAA,CACA,iBAAA,CACA,WAAA,CACA,kBAAA,CACA,MAAA,CACA,KAAA,CACA,0BAAA,CACA,oHAAA,CAGF,6DAEE,uBAAA,CACA,aAAA,CACA,gBAAA,CACA,YAAA,CACA,sBAAA,CACA,kBAAA,CACA,iBAAA,CACA,cAAA,CACA,iBAAA,CACA,OAAA,CAGF,yBACE,6DAEE,YAAA,CAAA,CAIJ,yEAEE,wBAAA,CAGF,+BACE,cAAA,CAGF,yBACE,+BACE,aAAA,CAAA,CAIJ,8BACE,aAAA,CAGF,yBACE,8BACE,YAAA,CAAA,CAMJ,6BACE,YAAA,CACA,kBAAA,CACA,sBAAA,CACA,UAAA,CAGF,yBACE,6BACE,cAAA,CACA,OAAA,CAAA,CAIJ,wCACE,aAAA,CACA,YAAA,CACA,qBAAA,CACA,kBAAA,CACA,sBAAA,CACA,iBAAA,CAGF,yBACE,wCACE,YAAA,CAAA,CAIJ,+CACE,YAAA,CACA,eAAA,CACA,sBAAA,CACA,eAAA,CACA,cAAA,CACA,gBAAA,CACA,iBAAA,CACA,iBAAA,CACA,aAAA,CAGF,0BACE,+CACE,gBAAA,CAAA,CAIJ,0DACE,eAAA,CACA,cAAA,CACA,gBAAA,CACA,iBAAA,CACA,iBAAA,CACA,aAAA,CAGF,0BACE,0DACE,gBAAA,CAAA,CAIJ,2DACE,eAAA,CACA,gBAAA,CACA,gBAAA,CACA,iBAAA,CACA,iBAAA,CACA,aAAA,CAGF,0BACE,2DACE,cAAA,CAAA,CAIJ,oDACE,eAAA,CACA,gBAAA,CACA,gBAAA,CACA,iBAAA,CACA,iBAAA,CACA,aAAA,CAKF,yBACE,sCACE,QAAA,CAGF,oDACE,6BAAA,CAAA,CAGJ,gCACE,WAAA,CACA,kBAAA,CACA,qBAAA,CACA,mBAAA,CACA,eAAA,CACA,iBAAA,CACA,UAAA,CACA,8CACE,iBAAA,CACA,QAAA,CACA,MAAA,CACA,UAAA,CACA,WAAA,CACA,SAAA,CAEF,+CACE,iBAAA,CACA,UAAA,CACA,QAAA,CACA,YAAA,CACA,2DACE,YAAA,CAGJ,2CACE,YAAA,CACA,uDACE,YAAA,CAGJ,iDACE,eAAA,CACA,kBAAA,CACA,gBAAA,CACA,aAAA,CACA,iBAAA,CAEF,gDACE,iBAAA,CACA,gBAAA,CACA,aAAA,CACA,UAAA,CAGF,yBACE,yDACE,wBAAA,CACA,UAAA,CACA,gFACE,oBAAA,CAEF,iFACE,YAAA,CAEF,oFACE,oBAAA,CAEF,qFACE,YAAA,CAAA,CAQR,kCACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,sBAAA,CAGF,8BACE,qCAAA,CACA,oCAAA,CAGF,0BACE,8BACE,kBAAA,CAAA,CAIJ,yBACE,8BACE,iBAAA,CAAA,CAIJ,4BACE,iBAAA,CACA,oBAAA,CACA,iBAAA,CACA,aAAA,CACA,eAAA,CACA,kBAAA,CACA,sBAAA,CAGF,kCACE,aAAA,CAGF,oCACE,WAAA,CACA,YAAA,CACA,qBAAA,CACA,6BAAA,CAGF,mDACE,oBAAA,CAGF,0BACE,oCACE,kBAAA,CACA,gBAAA,CAAA,CAIJ,yBACE,oCACE,aAAA,CAAA,CAIJ,6BACE,mBAAA,CACA,oBAAA,CACA,2BAAA,CACA,eAAA,CAGJ,8BACE,kBAAA,CACA,mHAAA,CACA,gBAAA,CACA,qBAAA,CACA,iBAAA,CAGF,yBACE,8BACE,QAAA,CAAA,CAIJ,yBACE,iCACE,WAAA,CAAA,CAIJ,0CACE,wBAAA\",\"sourcesContent\":[\"* {\\r\\n  margin: 0;\\r\\n  padding: 0;\\r\\n  box-sizing: border-box;\\r\\n}\\r\\n\\r\\nmain {\\r\\n  background-color: #f5f8ff;\\r\\n  color: #000;\\r\\n\\r\\n  h1,\\r\\n  h2,\\r\\n  h3,\\r\\n  h4,\\r\\n  h5,\\r\\n  h6,\\r\\n  p,\\r\\n  div,\\r\\n  span,\\r\\n  ul,\\r\\n  li {\\r\\n    margin-bottom: 0;\\r\\n    font-family: \\\"Mulish\\\", sans-serif;\\r\\n  }\\r\\n\\r\\n  h1,\\r\\n  h2 {\\r\\n    text-align: center;\\r\\n  }\\r\\n\\r\\n  h2 {\\r\\n    font-size: 2.25rem;\\r\\n    font-weight: 800;\\r\\n  }\\r\\n\\r\\n  .display-3 {\\r\\n    @media (max-width: 576px) {\\r\\n      font-size: 2.5rem;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .opacity-7 {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n\\r\\n  .opacity-6 {\\r\\n    opacity: 0.6;\\r\\n  }\\r\\n\\r\\n  .text-blue {\\r\\n    color: #3e90ff;\\r\\n  }\\r\\n\\r\\n  .text-gradient {\\r\\n    color: transparent;\\r\\n    background: linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%);\\r\\n\\r\\n    -webkit-background-clip: text;\\r\\n    background-clip: text;\\r\\n  }\\r\\n\\r\\n  .btn-wrapper {\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    gap: 1rem;\\r\\n    @media (max-width: 768px) {\\r\\n      flex-direction: column;\\r\\n      gap: 8px;\\r\\n      align-items: center;\\r\\n    }\\r\\n  }\\r\\n  .btn {\\r\\n    margin: 0;\\r\\n    display: flex;\\r\\n    align-items: center;\\r\\n    justify-content: center;\\r\\n    text-transform: capitalize;\\r\\n    svg {\\r\\n      max-width: 100%;\\r\\n      height: 100%;\\r\\n    }\\r\\n    &.btn-action {\\r\\n      background-color: #0085ff;\\r\\n      border-color: #0085ff;\\r\\n      min-width: 397px;\\r\\n\\r\\n      &:hover,\\r\\n      &:focus,\\r\\n      &:active {\\r\\n        color: #fff;\\r\\n        background-color: #005dd9;\\r\\n        border-color: #0057cc;\\r\\n      }\\r\\n    }\\r\\n    @media (max-width: 768px) {\\r\\n      display: block;\\r\\n      min-width: unset !important;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-banner {\\r\\n    position: relative;\\r\\n    .small-title {\\r\\n      color: #13171a;\\r\\n      font-size: 1.875rem;\\r\\n      margin-bottom: 1rem;\\r\\n      font-weight: 700;\\r\\n      line-height: 90%;\\r\\n      @media (max-width: 576px) {\\r\\n        font-size: 1.5rem;\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .part-banner {\\r\\n      background: linear-gradient(180deg, #c0eeff 0%, #f8fbff 96.46%);\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-banner .banner-left-download {\\r\\n    z-index: 10;\\r\\n    position: absolute;\\r\\n    height: 100%;\\r\\n    top: 0;\\r\\n    left: 0;\\r\\n    width: 33%;\\r\\n    @media (max-width: 768px) {\\r\\n      display: none;\\r\\n    }\\r\\n  }\\r\\n  .part-banner .banner-right-download {\\r\\n    z-index: 10;\\r\\n    position: absolute;\\r\\n    height: 100%;\\r\\n    top: 0;\\r\\n    right: 0;\\r\\n    width: 33%;\\r\\n    @media (max-width: 768px) {\\r\\n      display: none;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-banner .video-wrapper {\\r\\n    line-height: 0;\\r\\n    font-size: 0;\\r\\n  }\\r\\n\\r\\n  .part-banner .video-wrapper video {\\r\\n    height: 100%;\\r\\n    width: 100%;\\r\\n    object-fit: cover;\\r\\n    min-height: 533px;\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .part-banner .video-wrapper {\\r\\n      display: none;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-banner .part-banner-content {\\r\\n    position: absolute;\\r\\n    top: 0;\\r\\n    left: 0;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    display: flex;\\r\\n    flex-direction: column;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    margin: 0 auto;\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .part-banner .part-banner-content {\\r\\n      position: relative;\\r\\n      padding: 3rem 0;\\r\\n      text-align: center;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-banner .part-banner-content h1 {\\r\\n    color: #13171a;\\r\\n    line-height: 110%;\\r\\n  }\\r\\n\\r\\n  @media (max-width: 576px) {\\r\\n    .part-banner .part-banner-content h1 {\\r\\n      font-size: 28px;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-banner .part-banner-content h1 span {\\r\\n    color: #05a5ff;\\r\\n  }\\r\\n\\r\\n  .part-banner .part-banner-content h2 {\\r\\n    font-size: 1.875rem;\\r\\n    font-weight: 700;\\r\\n    line-height: 100%;\\r\\n    color: #13171a;\\r\\n  }\\r\\n\\r\\n  @media (max-width: 576px) {\\r\\n    .part-banner .part-banner-content h2 {\\r\\n      font-size: 1.25rem;\\r\\n      margin-bottom: 1rem;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .part-banner .part-banner-content .btn {\\r\\n      min-width: unset;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-banner .part-banner-content .logo-list {\\r\\n    display: flex;\\r\\n    align-items: center;\\r\\n    justify-content: center;\\r\\n    gap: 14.4px;\\r\\n\\r\\n    .split-line {\\r\\n      position: relative;\\r\\n      height: 16px;\\r\\n      width: 2px;\\r\\n      top: 70%;\\r\\n      border-radius: 1.5px;\\r\\n      background-color: rgba($color: #000000, $alpha: 0.7);\\r\\n    }\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .part-banner .part-banner-content .logo-list {\\r\\n      flex-wrap: wrap;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .part-banner .part-banner-content .logo-list .logo-img {\\r\\n      flex: 1;\\r\\n      max-height: 24px;\\r\\n      object-fit: contain;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-format {\\r\\n    .video-wrapper {\\r\\n      line-height: 0;\\r\\n      font-size: 0;\\r\\n      border-radius: 16px;\\r\\n      overflow: hidden;\\r\\n\\r\\n      video {\\r\\n        width: 100%;\\r\\n        height: 100%;\\r\\n        object-fit: cover;\\r\\n        // google去黑线\\r\\n        filter: grayscale(0);\\r\\n        // 火狐去黑线\\r\\n        clip-path: fill-box;\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-files {\\r\\n    .file-box {\\r\\n      height: 100%;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n      border-radius: 1rem;\\r\\n      overflow: hidden;\\r\\n    }\\r\\n\\r\\n    .file-box .file-box-content {\\r\\n      background-color: #fff;\\r\\n      padding: 2rem;\\r\\n      flex: 1;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n\\r\\n      p {\\r\\n        color: #787878;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    @media (max-width: 576px) {\\r\\n      .file-box .file-box-content {\\r\\n        padding: 8px;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .file-box .file-box-content .box-title {\\r\\n      font-weight: 700;\\r\\n      font-size: 1.25rem;\\r\\n      color: #000;\\r\\n      text-decoration: none;\\r\\n      display: inline-block;\\r\\n      margin-bottom: 0.75rem;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 576px) {\\r\\n      .col-6 {\\r\\n        padding-right: 8px;\\r\\n        padding-left: 8px;\\r\\n      }\\r\\n\\r\\n      .col-6:nth-child(odd) {\\r\\n        padding-right: 4px;\\r\\n      }\\r\\n\\r\\n      .col-6:nth-child(even) {\\r\\n        padding-left: 4px;\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-devices {\\r\\n    .device-list {\\r\\n      max-width: 1310px;\\r\\n      margin: 0 auto;\\r\\n      display: flex;\\r\\n      justify-content: space-around;\\r\\n      gap: 1rem;\\r\\n      @media (max-width: 768px) {\\r\\n        flex-wrap: wrap;\\r\\n      }\\r\\n      .device-item {\\r\\n        flex: 1;\\r\\n        display: flex;\\r\\n        flex-direction: column;\\r\\n        align-items: center;\\r\\n        gap: 1.5rem;\\r\\n        max-width: 7.5rem;\\r\\n        text-align: center;\\r\\n        @media (max-width: 768px) {\\r\\n          flex: 1 1 20%;\\r\\n        }\\r\\n        img {\\r\\n          transition: transform 0.2s linear;\\r\\n          @media (any-hover: hover) {\\r\\n            &:hover {\\r\\n              transform: scale(1.3);\\r\\n            }\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-tech {\\r\\n    .tech-wrapper {\\r\\n      border-radius: 2.5rem;\\r\\n      overflow: hidden;\\r\\n      background: url(https://images.wondershare.com/recoverit/images2025/PPT/tech-bg.jpg) no-repeat center center / cover;\\r\\n      padding: 4.375rem 2rem;\\r\\n    }\\r\\n\\r\\n    .tech-wrapper .tech-wrapper-inner {\\r\\n      max-width: 786px;\\r\\n      margin: 0 auto;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n      gap: 2rem;\\r\\n    }\\r\\n\\r\\n    .tech-wrapper .tech-wrapper-inner .tech-item {\\r\\n      display: flex;\\r\\n      justify-content: center;\\r\\n      align-items: center;\\r\\n      gap: 80px;\\r\\n      color: #fff;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .tech-wrapper .tech-wrapper-inner .tech-item {\\r\\n        flex-direction: column;\\r\\n        gap: 1rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .tech-wrapper .tech-wrapper-inner .tech-item .left-content {\\r\\n      width: 202px;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n      justify-content: center;\\r\\n      align-items: center;\\r\\n      gap: 12px;\\r\\n      color: #fff;\\r\\n    }\\r\\n\\r\\n    .tech-wrapper .tech-wrapper-inner .tech-item .right-content {\\r\\n      flex: 1;\\r\\n      text-align: left;\\r\\n      font-weight: 500;\\r\\n      color: #fff;\\r\\n    }\\r\\n\\r\\n    .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail {\\r\\n      display: flex;\\r\\n      align-items: flex-start;\\r\\n      gap: 0.75rem;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail {\\r\\n        justify-content: center;\\r\\n        text-align: center;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .tech-wrapper .tech-wrapper-inner .tech-item .right-content .tech-item-detail .sys-title {\\r\\n      font-weight: 700;\\r\\n      font-size: 1.125rem;\\r\\n    }\\r\\n\\r\\n    .tech-wrapper .tech-wrapper-inner .tech-item-dividing {\\r\\n      width: 100%;\\r\\n      border-bottom: 1px dashed rgba(255, 255, 255, 0.5);\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-methods {\\r\\n    background-color: #e4efff;\\r\\n\\r\\n    .product-box {\\r\\n      background-color: #fff;\\r\\n      padding: 1.5rem 2rem;\\r\\n      border-radius: 1rem;\\r\\n      display: flex;\\r\\n      justify-content: space-between;\\r\\n      align-items: center;\\r\\n      .btn-action {\\r\\n        width: auto;\\r\\n        min-width: unset;\\r\\n        background: linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%);\\r\\n        border: transparent;\\r\\n        &:hover {\\r\\n          color: #fff;\\r\\n          background: #0085ff !important;\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .product-box {\\r\\n        flex-direction: column;\\r\\n        align-items: stretch;\\r\\n        gap: 8px;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .product-box .left-content {\\r\\n      display: flex;\\r\\n      gap: 0.75rem;\\r\\n      align-items: center;\\r\\n    }\\r\\n\\r\\n    .product-box .left-content .icon-wrapper {\\r\\n      width: 3rem;\\r\\n    }\\r\\n\\r\\n    .methods-compare {\\r\\n      background: #fff;\\r\\n      background: rgba(255, 255, 255, 0.501961);\\r\\n      border-radius: 1rem;\\r\\n      .feature-item {\\r\\n        display: flex;\\r\\n        align-items: center;\\r\\n        gap: 0.5rem;\\r\\n        margin-top: 0.5rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .methods-nav-item {\\r\\n      border: 1px solid #1a8dff;\\r\\n      font-size: 1.25rem;\\r\\n      border-radius: 0.5rem;\\r\\n      color: #1a8dff;\\r\\n      cursor: pointer;\\r\\n      text-align: center;\\r\\n      height: 100%;\\r\\n      display: flex;\\r\\n      align-items: center;\\r\\n      justify-content: center;\\r\\n    }\\r\\n\\r\\n    .methods-nav .col-md-4.active .methods-nav-item {\\r\\n      background-color: #1a8dff;\\r\\n      color: #fff;\\r\\n      font-weight: 700;\\r\\n    }\\r\\n\\r\\n    .methods-warning {\\r\\n      padding: 1.125rem 3rem;\\r\\n      border: 1px solid #1a8dff;\\r\\n      border-radius: 0.75rem;\\r\\n      color: rgba(0, 0, 0, 0.7);\\r\\n    }\\r\\n\\r\\n    @media (max-width: 576px) {\\r\\n      .methods-warning {\\r\\n        padding: 1rem 1rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .slidebtn-methods {\\r\\n      display: inline-block;\\r\\n      width: 48px;\\r\\n      height: 49px;\\r\\n      background-image: url(\\\"https://images.wondershare.com/recoverit/images2025/ziprecoverit/icon-arrow-1.png\\\");\\r\\n      background-repeat: no-repeat;\\r\\n      background-size: 100% 100%;\\r\\n      position: absolute;\\r\\n      top: 40%;\\r\\n      cursor: pointer;\\r\\n    }\\r\\n\\r\\n    .slidebtn-methods:hover {\\r\\n      background-image: url(\\\"https://images.wondershare.com/recoverit/images2025/ziprecoverit/icon-arrow-2.png\\\");\\r\\n      transform: rotate(180deg);\\r\\n    }\\r\\n\\r\\n    .slidebtn-methods-prev {\\r\\n      left: -67px;\\r\\n    }\\r\\n\\r\\n    .slidebtn-methods-next {\\r\\n      right: -67px;\\r\\n      transform: rotate(180deg);\\r\\n    }\\r\\n\\r\\n    .slidebtn-methods-next:hover {\\r\\n      transform: rotate(0);\\r\\n    }\\r\\n  }\\r\\n  .part-how {\\r\\n    .nav {\\r\\n      display: flex;\\r\\n      flex-wrap: nowrap;\\r\\n\\r\\n      gap: 12px;\\r\\n      padding-top: 3rem;\\r\\n      padding-bottom: 1.875rem;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .nav {\\r\\n        padding-top: 1.5rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .nav .nav-item {\\r\\n      flex: 1 1 50%;\\r\\n      text-align: center;\\r\\n      padding: 1rem;\\r\\n      border-radius: 1rem;\\r\\n      background-color: #fff;\\r\\n      border: 1px solid #b5dae8;\\r\\n      font-weight: 600;\\r\\n      font-size: 1.125rem;\\r\\n      color: #000;\\r\\n      text-decoration: none;\\r\\n      transition: unset;\\r\\n      display: flex;\\r\\n      align-items: center;\\r\\n      justify-content: center;\\r\\n    }\\r\\n\\r\\n    .nav .nav-item.active {\\r\\n      background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(0deg, #0055fb, #0055fb);\\r\\n      color: #fff;\\r\\n    }\\r\\n\\r\\n    .how-box {\\r\\n      display: flex;\\r\\n      border-radius: 16px;\\r\\n      overflow: hidden;\\r\\n      background-color: #fff;\\r\\n      padding: 2.5rem 3.5rem;\\r\\n      gap: 2.5rem;\\r\\n      justify-content: center;\\r\\n      align-items: center;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 992px) {\\r\\n      .how-box {\\r\\n        flex-direction: column;\\r\\n        padding: 1.5rem 1.5rem;\\r\\n        gap: 1.5rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper {\\r\\n      flex: 1 1 46%;\\r\\n      width: 100%;\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper .advantages-box {\\r\\n      padding: 1.5rem 1rem;\\r\\n      background-color: #f6fff7;\\r\\n      position: relative;\\r\\n      border-radius: 12px;\\r\\n      overflow: hidden;\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper .advantages-box::before {\\r\\n      content: \\\"Pros\\\";\\r\\n      position: absolute;\\r\\n      top: 0;\\r\\n      right: 0;\\r\\n      font-size: 1.125rem;\\r\\n      font-weight: 500;\\r\\n      line-height: 100%;\\r\\n      color: #fff;\\r\\n      background-color: #0cad73;\\r\\n      border-radius: 0 12px 0 12px;\\r\\n      padding: 4px 0.75rem;\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper .advantages-box .advantages-list {\\r\\n      display: flex;\\r\\n      gap: 1.25rem;\\r\\n      flex-direction: column;\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper .advantages-box .advantages-list .advantage-item {\\r\\n      gap: 12px;\\r\\n      display: flex;\\r\\n      align-items: center;\\r\\n      line-height: 100%;\\r\\n      font-size: 1.125rem;\\r\\n      font-weight: 500;\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper .disadvantages-box {\\r\\n      padding: 1.5rem 1rem;\\r\\n      background-color: #fff9fc;\\r\\n      position: relative;\\r\\n      border-radius: 12px;\\r\\n      overflow: hidden;\\r\\n      min-height: 105px;\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper .disadvantages-box::before {\\r\\n      content: \\\"Cons\\\";\\r\\n      position: absolute;\\r\\n      top: 0;\\r\\n      right: 0;\\r\\n      font-size: 1.125rem;\\r\\n      font-weight: 500;\\r\\n      line-height: 100%;\\r\\n      color: #fff;\\r\\n      background-color: #ff4a75;\\r\\n      border-radius: 0 12px 0 12px;\\r\\n      padding: 4px 0.75rem;\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper .disadvantages-box .disadvantages-list {\\r\\n      display: flex;\\r\\n      gap: 1.25rem;\\r\\n      flex-direction: column;\\r\\n    }\\r\\n\\r\\n    .how-box .content-wrapper .disadvantages-box .disadvantages-list .disadvantage-item {\\r\\n      gap: 12px;\\r\\n      display: flex;\\r\\n      align-items: center;\\r\\n      line-height: 100%;\\r\\n      font-size: 1.125rem;\\r\\n      font-weight: 500;\\r\\n    }\\r\\n\\r\\n    .how-box .video-wrapper {\\r\\n      line-height: 0;\\r\\n      font-size: 0;\\r\\n      border-radius: 1.25rem;\\r\\n      overflow: hidden;\\r\\n      flex: 0 0 54%;\\r\\n      height: 100%;\\r\\n      width: 100%;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-customer {\\r\\n    .customer-wrapper {\\r\\n      border-radius: 2rem;\\r\\n      overflow: hidden;\\r\\n      background-color: #fff;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n      height: 100%;\\r\\n      position: relative;\\r\\n      .customer-info-wrapper {\\r\\n        position: absolute;\\r\\n        top: 50%;\\r\\n        left: 8.7%;\\r\\n        min-height: 80%;\\r\\n        padding: 1.25rem 1.875rem;\\r\\n        transform: translateY(-50%);\\r\\n        background: rgba(255, 255, 255, 0.7);\\r\\n        backdrop-filter: blur(35px);\\r\\n        border-radius: 1.5rem;\\r\\n        max-width: 460px;\\r\\n\\r\\n        @media (max-width: 1600px) {\\r\\n          max-width: 460px;\\r\\n        }\\r\\n        @media (max-width: 992px) {\\r\\n          position: relative;\\r\\n          transform: unset;\\r\\n          top: initial;\\r\\n          left: initial;\\r\\n          right: initial !important;\\r\\n          min-height: unset;\\r\\n          padding: 1.25rem 1.875rem;\\r\\n          max-width: 100%;\\r\\n          border-radius: 0;\\r\\n          padding: 1rem;\\r\\n        }\\r\\n\\r\\n        &.right {\\r\\n          left: unset;\\r\\n          right: 8.7%;\\r\\n        }\\r\\n        .customer-info-list {\\r\\n          display: flex;\\r\\n          align-items: center;\\r\\n          gap: 24px;\\r\\n          padding-left: 14px;\\r\\n          @media (max-width: 576px) {\\r\\n            gap: 18px;\\r\\n          }\\r\\n        }\\r\\n        .customer-detail {\\r\\n          .detail-title {\\r\\n            color: #0080ff;\\r\\n            font-size: 1.25rem;\\r\\n            font-weight: 700;\\r\\n            margin-bottom: 0.75rem;\\r\\n            margin-top: 1.25rem;\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .customer-wrapper .customer-info-list .customer-title,\\r\\n    .customer-wrapper .customer-info-list .customer-profession,\\r\\n    .customer-wrapper .customer-info-list .customer-age {\\r\\n      position: relative;\\r\\n      background-color: #bcd3e9;\\r\\n      border-radius: 0 6px 6px 0;\\r\\n      padding-right: 8px;\\r\\n      padding-left: 5px;\\r\\n      font-size: 14px;\\r\\n      color: #000;\\r\\n      font-weight: 600;\\r\\n      height: 31px;\\r\\n      display: flex;\\r\\n      align-items: center;\\r\\n      white-space: nowrap;\\r\\n      @media (max-width: 576px) {\\r\\n        font-size: 11px;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .customer-wrapper .customer-info-list .customer-title::before,\\r\\n    .customer-wrapper .customer-info-list .customer-profession::before,\\r\\n    .customer-wrapper .customer-info-list .customer-age::before {\\r\\n      content: \\\"\\\";\\r\\n      position: absolute;\\r\\n      height: 100%;\\r\\n      aspect-ratio: 28 / 62;\\r\\n      left: 0;\\r\\n      top: 0;\\r\\n      transform: translateX(-88%);\\r\\n      background: url(https://images.wondershare.com/recoverit/images2025/PPT/left-tip.png) no-repeat center center / contain;\\r\\n    }\\r\\n\\r\\n    .left-btn,\\r\\n    .right-btn {\\r\\n      background-color: #c0c0c0;\\r\\n      width: 2.25rem;\\r\\n      aspect-ratio: 1 / 1;\\r\\n      display: flex;\\r\\n      justify-content: center;\\r\\n      align-items: center;\\r\\n      border-radius: 50%;\\r\\n      cursor: pointer;\\r\\n      position: absolute;\\r\\n      top: 36%;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 576px) {\\r\\n      .left-btn,\\r\\n      .right-btn {\\r\\n        display: none;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .left-btn:hover,\\r\\n    .right-btn:hover {\\r\\n      background-color: #006dff;\\r\\n    }\\r\\n\\r\\n    .right-btn {\\r\\n      right: -3.25rem;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .right-btn {\\r\\n        right: 1.55rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .left-btn {\\r\\n      left: -3.25rem;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .left-btn {\\r\\n        left: 1.55rem;\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-count {\\r\\n    .count-list {\\r\\n      display: flex;\\r\\n      align-items: center;\\r\\n      justify-content: center;\\r\\n      color: #fff;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .count-list {\\r\\n        flex-wrap: wrap;\\r\\n        gap: 4px;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .count-list .count-box {\\r\\n      flex: 1 1 auto;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n      align-items: center;\\r\\n      justify-content: center;\\r\\n      position: relative;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .count-list .count-box {\\r\\n        flex: 1 1 45%;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .count-list .count-box .count {\\r\\n      display: flex;\\r\\n      align-items: top;\\r\\n      justify-content: center;\\r\\n      font-weight: 800;\\r\\n      font-size: 4rem;\\r\\n      line-height: 130%;\\r\\n      letter-spacing: 0%;\\r\\n      text-align: center;\\r\\n      color: #3e90ff;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 1280px) {\\r\\n      .count-list .count-box .count {\\r\\n        font-size: 3.5rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .count-list .count-box .count .count-num {\\r\\n      font-weight: 800;\\r\\n      font-size: 4rem;\\r\\n      line-height: 130%;\\r\\n      letter-spacing: 0%;\\r\\n      text-align: center;\\r\\n      color: #3e90ff;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 1280px) {\\r\\n      .count-list .count-box .count .count-num {\\r\\n        font-size: 3.5rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .count-list .count-box .count .count-plus {\\r\\n      font-weight: 700;\\r\\n      font-size: 2.5rem;\\r\\n      line-height: 130%;\\r\\n      letter-spacing: 0%;\\r\\n      text-align: center;\\r\\n      color: #3e90ff;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 1280px) {\\r\\n      .count-list .count-box .count .count-plus {\\r\\n        font-size: 2rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .count-list .count-box .count-desc {\\r\\n      font-weight: 400;\\r\\n      font-size: 1.5rem;\\r\\n      line-height: 130%;\\r\\n      letter-spacing: 0%;\\r\\n      text-align: center;\\r\\n      color: #616161;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-bestSwiper {\\r\\n    @media (min-width: 992px) {\\r\\n      .swiper-wrapper {\\r\\n        gap: 1rem;\\r\\n      }\\r\\n\\r\\n      .swiper-wrapper .swiper-slide {\\r\\n        flex: 1 1 calc(25% - 1rem * 3);\\r\\n      }\\r\\n    }\\r\\n    .best-box {\\r\\n      height: 100%;\\r\\n      border-radius: 1rem;\\r\\n      background-color: #fff;\\r\\n      padding: 2.5rem 2rem;\\r\\n      overflow: hidden;\\r\\n      position: relative;\\r\\n      color: #000;\\r\\n      .box-download {\\r\\n        position: absolute;\\r\\n        bottom: 0;\\r\\n        left: 0;\\r\\n        width: 100%;\\r\\n        height: 100%;\\r\\n        z-index: 5;\\r\\n      }\\r\\n      .download-icon {\\r\\n        position: absolute;\\r\\n        right: 1rem;\\r\\n        top: 1rem;\\r\\n        width: 1.5rem;\\r\\n        .active-img {\\r\\n          display: none;\\r\\n        }\\r\\n      }\\r\\n      .best-icon {\\r\\n        width: 3.5rem;\\r\\n        .active-img {\\r\\n          display: none;\\r\\n        }\\r\\n      }\\r\\n      .best-item-title {\\r\\n        font-weight: 700;\\r\\n        font-size: 1.125rem;\\r\\n        line-height: 100%;\\r\\n        color: inherit;\\r\\n        margin-bottom: 6px;\\r\\n      }\\r\\n      .best-item-desc {\\r\\n        font-size: 0.875rem;\\r\\n        line-height: 100%;\\r\\n        color: inherit;\\r\\n        opacity: 0.8;\\r\\n      }\\r\\n\\r\\n      @media (any-hover: hover) {\\r\\n        &:has(.box-download:hover) {\\r\\n          background-color: #3e90ff;\\r\\n          color: #fff;\\r\\n          .best-icon .active-img {\\r\\n            display: inline-block;\\r\\n          }\\r\\n          .best-icon .default-img {\\r\\n            display: none;\\r\\n          }\\r\\n          .download-icon .active-img {\\r\\n            display: inline-block;\\r\\n          }\\r\\n          .download-icon .default-img {\\r\\n            display: none;\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-links {\\r\\n    .part-links-line {\\r\\n      height: 100%;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n      justify-content: center;\\r\\n    }\\r\\n\\r\\n    .line-border {\\r\\n      border-right: 1px solid rgba(0, 0, 0, 0.3);\\r\\n      border-left: 1px solid rgba(0, 0, 0, 0.3);\\r\\n    }\\r\\n\\r\\n    @media (max-width: 1280px) {\\r\\n      .line-border {\\r\\n        border-right: unset;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    @media (max-width: 768px) {\\r\\n      .line-border {\\r\\n        border-left: unset;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .text-link {\\r\\n      font-size: 0.875rem;\\r\\n      color: rgba(0, 0, 0, 0.7);\\r\\n      margin-top: 1.5rem;\\r\\n      display: block;\\r\\n      overflow: hidden;\\r\\n      white-space: nowrap;\\r\\n      text-overflow: ellipsis;\\r\\n    }\\r\\n\\r\\n    .text-link:hover {\\r\\n      color: #0055fb;\\r\\n    }\\r\\n\\r\\n    .part-links-videos {\\r\\n      height: 100%;\\r\\n      display: flex;\\r\\n      flex-direction: column;\\r\\n      justify-content: space-between;\\r\\n    }\\r\\n\\r\\n    .part-links-videos .video-wrapper {\\r\\n      border-radius: 0.75rem;\\r\\n    }\\r\\n\\r\\n    @media (max-width: 1280px) {\\r\\n      .part-links-videos {\\r\\n        flex-direction: row;\\r\\n        padding-top: 2rem;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    @media (max-width: 576px) {\\r\\n      .part-links-videos {\\r\\n        display: block;\\r\\n      }\\r\\n    }\\r\\n\\r\\n    .text-line4 {\\r\\n      display: -webkit-box;\\r\\n      -webkit-line-clamp: 4;\\r\\n      -webkit-box-orient: vertical;\\r\\n      overflow: hidden;\\r\\n    }\\r\\n  }\\r\\n  .part-footer .footer-box {\\r\\n    border-radius: 1rem;\\r\\n    background: url(https://images.wondershare.com/recoverit/images2025/drfone/footer.jpg) no-repeat center center/cover;\\r\\n    margin: 0 -2.5rem;\\r\\n    padding: 3.5rem 1.5rem;\\r\\n    text-align: center;\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .part-footer .footer-box {\\r\\n      margin: 0;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  @media (max-width: 768px) {\\r\\n    .part-footer .logo-icon img {\\r\\n      height: 3rem;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  .part-footer .btn-wrapper .btn-white {\\r\\n    color: #0196ff !important;\\r\\n  }\\r\\n}\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2\n");

/***/ }),

/***/ 314:
/***/ ((module) => {

eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///314\n");

/***/ }),

/***/ 354:
/***/ ((module) => {

eval("\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzU0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsY0FBYztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2Nzcy1sb2FkZXIvZGlzdC9ydW50aW1lL3NvdXJjZU1hcHMuanM/YWYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoaXRlbSkge1xuICB2YXIgY29udGVudCA9IGl0ZW1bMV07XG4gIHZhciBjc3NNYXBwaW5nID0gaXRlbVszXTtcbiAgaWYgKCFjc3NNYXBwaW5nKSB7XG4gICAgcmV0dXJuIGNvbnRlbnQ7XG4gIH1cbiAgaWYgKHR5cGVvZiBidG9hID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB2YXIgYmFzZTY0ID0gYnRvYSh1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoY3NzTWFwcGluZykpKSk7XG4gICAgdmFyIGRhdGEgPSBcInNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LFwiLmNvbmNhdChiYXNlNjQpO1xuICAgIHZhciBzb3VyY2VNYXBwaW5nID0gXCIvKiMgXCIuY29uY2F0KGRhdGEsIFwiICovXCIpO1xuICAgIHJldHVybiBbY29udGVudF0uY29uY2F0KFtzb3VyY2VNYXBwaW5nXSkuam9pbihcIlxcblwiKTtcbiAgfVxuICByZXR1cm4gW2NvbnRlbnRdLmpvaW4oXCJcXG5cIik7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///354\n");

/***/ }),

/***/ 72:
/***/ ((module) => {

eval("\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///72\n");

/***/ }),

/***/ 659:
/***/ ((module) => {

eval("\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU5LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanM/YjIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIG1lbW8gPSB7fTtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBnZXRUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0eXBlb2YgbWVtb1t0YXJnZXRdID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIHN0eWxlVGFyZ2V0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcih0YXJnZXQpO1xuXG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIHJldHVybiBoZWFkIG9mIGlmcmFtZSBpbnN0ZWFkIG9mIGlmcmFtZSBpdHNlbGZcbiAgICBpZiAod2luZG93LkhUTUxJRnJhbWVFbGVtZW50ICYmIHN0eWxlVGFyZ2V0IGluc3RhbmNlb2Ygd2luZG93LkhUTUxJRnJhbWVFbGVtZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUaGlzIHdpbGwgdGhyb3cgYW4gZXhjZXB0aW9uIGlmIGFjY2VzcyB0byBpZnJhbWUgaXMgYmxvY2tlZFxuICAgICAgICAvLyBkdWUgdG8gY3Jvc3Mtb3JpZ2luIHJlc3RyaWN0aW9uc1xuICAgICAgICBzdHlsZVRhcmdldCA9IHN0eWxlVGFyZ2V0LmNvbnRlbnREb2N1bWVudC5oZWFkO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgICAgICBzdHlsZVRhcmdldCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG1lbW9bdGFyZ2V0XSA9IHN0eWxlVGFyZ2V0O1xuICB9XG4gIHJldHVybiBtZW1vW3RhcmdldF07XG59XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gaW5zZXJ0QnlTZWxlY3RvcihpbnNlcnQsIHN0eWxlKSB7XG4gIHZhciB0YXJnZXQgPSBnZXRUYXJnZXQoaW5zZXJ0KTtcbiAgaWYgKCF0YXJnZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBmaW5kIGEgc3R5bGUgdGFyZ2V0LiBUaGlzIHByb2JhYmx5IG1lYW5zIHRoYXQgdGhlIHZhbHVlIGZvciB0aGUgJ2luc2VydCcgcGFyYW1ldGVyIGlzIGludmFsaWQuXCIpO1xuICB9XG4gIHRhcmdldC5hcHBlbmRDaGlsZChzdHlsZSk7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydEJ5U2VsZWN0b3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///659\n");

/***/ }),

/***/ 540:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTQwLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRTdHlsZUVsZW1lbnQuanM/ZGU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBpbnNlcnRTdHlsZUVsZW1lbnQob3B0aW9ucykge1xuICB2YXIgZWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgb3B0aW9ucy5zZXRBdHRyaWJ1dGVzKGVsZW1lbnQsIG9wdGlvbnMuYXR0cmlidXRlcyk7XG4gIG9wdGlvbnMuaW5zZXJ0KGVsZW1lbnQsIG9wdGlvbnMub3B0aW9ucyk7XG4gIHJldHVybiBlbGVtZW50O1xufVxubW9kdWxlLmV4cG9ydHMgPSBpbnNlcnRTdHlsZUVsZW1lbnQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///540\n");

/***/ }),

/***/ 56:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLGNBQWMsS0FBd0MsR0FBRyxzQkFBaUIsR0FBRyxDQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanM/ZGRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMoc3R5bGVFbGVtZW50KSB7XG4gIHZhciBub25jZSA9IHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gXCJ1bmRlZmluZWRcIiA/IF9fd2VicGFja19ub25jZV9fIDogbnVsbDtcbiAgaWYgKG5vbmNlKSB7XG4gICAgc3R5bGVFbGVtZW50LnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///56\n");

/***/ }),

/***/ 825:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n  var needLayer = typeof obj.layer !== \"undefined\";\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += \"}\";\n  }\n  if (obj.media) {\n    css += \"}\";\n  }\n  if (obj.supports) {\n    css += \"}\";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === \"undefined\") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///825\n");

/***/ }),

/***/ 113:
/***/ ((module) => {

eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTEzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL3N0eWxlLWxvYWRlci9kaXN0L3J1bnRpbWUvc3R5bGVUYWdUcmFuc2Zvcm0uanM/MWRkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzdHlsZVRhZ1RyYW5zZm9ybShjc3MsIHN0eWxlRWxlbWVudCkge1xuICBpZiAoc3R5bGVFbGVtZW50LnN0eWxlU2hlZXQpIHtcbiAgICBzdHlsZUVsZW1lbnQuc3R5bGVTaGVldC5jc3NUZXh0ID0gY3NzO1xuICB9IGVsc2Uge1xuICAgIHdoaWxlIChzdHlsZUVsZW1lbnQuZmlyc3RDaGlsZCkge1xuICAgICAgc3R5bGVFbGVtZW50LnJlbW92ZUNoaWxkKHN0eWxlRWxlbWVudC5maXJzdENoaWxkKTtcbiAgICB9XG4gICAgc3R5bGVFbGVtZW50LmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZVRleHROb2RlKGNzcykpO1xuICB9XG59XG5tb2R1bGUuZXhwb3J0cyA9IHN0eWxlVGFnVHJhbnNmb3JtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///113\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__(787);
/******/ 	
/******/ })()
;