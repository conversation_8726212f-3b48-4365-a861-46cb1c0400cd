import "./index.scss";

$(() => {
  // 文字轮播
  const bannerTextSwiper = new Swiper("#banner-text-swiper", {
    slidesPerView: 1,
    spaceBetween: 10,
    loop: true,
    direction: "vertical",
    allowTouchMove: false, // 禁止手动滑动
    autoplay: {
      delay: 2500,
      disableOnInteraction: false,
    },
  });
  if (window.innerWidth > 1280) {
    var toolsSwiper = new Swiper("#swiper-tools", {
      slidesPerView: 1,
      spaceBetween: 30,
      allowTouchMove: false, // 禁止手动滑动
    });
  } else {
    var toolsSwiper = new Swiper("#swiper-tools", {
      slidesPerView: 1,
      spaceBetween: 30,
      loop: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      pagination: {
        el: "#swiper-tools .swiper-pagination",
        clickable: true,
      },
    });
  }

  if (window.innerWidth < 992) {
    const photoDevicesSwiper = new Swiper("#swiper-photo-devices", {
      slidesPerView: 1.01,
      centeredSlides: true,
      spaceBetween: 15,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 2500,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
      },
      pagination: {
        el: "#swiper-photo-devices .swiper-pagination",
        clickable: true,
      },
    });
    const featureSwiper = new Swiper("#swiper-feature", {
      slidesPerView: 1.0,
      centeredSlides: true,
      spaceBetween: 15,
      loop: true,
      loopedSlides: 2,
      autoplay: {
        delay: 2500,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
      },
      pagination: {
        el: "#swiper-feature .swiper-pagination",
        clickable: true,
      },
    });
  }

  // 进度条设置
  if (window.innerWidth > 1280) {
    $(window).scroll(function () {
      var scrollTop = $(this).scrollTop();
      // 人物版块滚动逻辑
      var whyGridLength = toolsSwiper.snapGrid.length;
      var whyMin = $(".why").offset().top;
      var whyMax = whyMin + $(".why").innerHeight() - $(window).height();
      var whyNowIndex;
      if (scrollTop < whyMin) {
        $(".why-progress-wrapper .why-progress").first().find(".why-progress-inner").css("width", "0%");
      } else if (scrollTop > whyMax) {
        $(".why-progress-wrapper .why-progress").last().find(".why-progress-inner").css("width", "100%");
      } else {
        var step = (scrollTop - whyMin) / ($(".why").innerHeight() - $(window).height());
        var totalProgress = step * whyGridLength;
        var fractionalPart = totalProgress % 1;
        var progress = fractionalPart * 100;
        whyNowIndex = Math.floor(step * whyGridLength);
        $(".why-progress-wrapper .why-progress")
          .eq(whyNowIndex)
          .find(".why-progress-inner")
          .css("width", progress + "%");
        fixProgress(whyNowIndex);
      }
      if (whyNowIndex > toolsSwiper.snapIndex) {
        toolsSwiper.slideNext();
      } else if (whyNowIndex < toolsSwiper.snapIndex) {
        toolsSwiper.slidePrev();
      }

      $(".why-progress-wrapper .why-progress").on("click", function () {
        whyNowIndex = $(this).index();
        $(window).scrollTop(whyMin + ($(".why").innerHeight() / 3) * whyNowIndex);
        fixProgress(whyNowIndex);
        toolsSwiper.slideTo(whyNowIndex);
        setTimeout(() => {
          $(".why-progress-wrapper .why-progress").eq(whyNowIndex).find(".why-progress-inner").css("width", "100%");
        }, 1);
      });
    });

    function fixProgress(index) {
      switch (index) {
        case 0:
          $(".why-progress-wrapper .why-progress").eq(1).find(".why-progress-inner").css("width", "0%");
          $(".why-progress-wrapper .why-progress").eq(2).find(".why-progress-inner").css("width", "0%");

          break;
        case 1:
          $(".why-progress-wrapper .why-progress").eq(0).find(".why-progress-inner").css("width", "100%");
          $(".why-progress-wrapper .why-progress").eq(2).find(".why-progress-inner").css("width", "0%");

          break;
        case 2:
          $(".why-progress-wrapper .why-progress").eq(0).find(".why-progress-inner").css("width", "100%");
          $(".why-progress-wrapper .why-progress").eq(1).find(".why-progress-inner").css("width", "100%");

          break;

        default:
          break;
      }
    }
  } else {
    $(".why-progress-wrapper .why-progress").on("click", function () {
      var whyNowIndex = $(this).index();
      toolsSwiper.slideTo(whyNowIndex);
      setTimeout(() => {
        $(".why-progress-wrapper .why-progress").eq(whyNowIndex).find(".why-progress-inner").css("width", "100%");
        $(".why-progress-wrapper .why-progress").eq(whyNowIndex).siblings().find(".why-progress-inner").css("width", "0");
      }, 1);
    });
  }

  const storiesSwiper = new Swiper("#stories-swiper", {
    slidesPerView: 1,
    centeredSlides: true,
    spaceBetween: 30,
    loop: true,
    pagination: {
      el: "#stories-swiper .swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      1280: {
        slidesPerView: 1.9,
        spaceBetween: 30,
      },
    },
  });
});
