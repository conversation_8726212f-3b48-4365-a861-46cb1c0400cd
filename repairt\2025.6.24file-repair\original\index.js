import "./index.scss";

$(() => {
  // 文字轮播
  const bannerTextSwiper = new Swiper("#banner-text-swiper", {
    slidesPerView: 1,
    spaceBetween: 10,
    loop: true,
    direction: "vertical",
    allowTouchMove: false, // 禁止手动滑动
    autoplay: {
      delay: 2500,
      disableOnInteraction: false,
    },
  });
  if (window.innerWidth > 1280) {
    $(".document-wrapper .document-card").on("mouseenter", function () {
      $(this).addClass("active").siblings().removeClass("active");
    });
    $(".archive-wrapper .archive-card").on("mouseenter", function () {
      $(this).addClass("active").siblings().removeClass("active");
    });
  } else {
    $(".document-wrapper .document-card").addClass("active");
    $(".archive-wrapper .archive-card").addClass("active");
  }

  const corruptionSwiper = new Swiper("#corruption-swiper", {
    slidesPerView: 1,
    spaceBetween: 10,
    loop: true,
    breakpoints: {
      1600: {
        slidesPerView: 4,
      },
      1280: {
        slidesPerView: 3.5,
        spaceBetween: 20,
      },
      992: {
        slidesPerView: 3,
        spaceBetween: 20,
      },
      576: {
        slidesPerView: 2,
        spaceBetween: 10,
      },
    },
    pagination: {
      el: ".part-corruption .swiper-pagination-number",
      type: "custom",
      renderCustom: function (swiper, current, total) {
        return current + "/" + total;
      },
    },
    navigation: {
      nextEl: ".part-corruption .swiper-next",
      prevEl: ".part-corruption .swiper-prev",
    },
  });
  if (window.innerWidth < 1280) {
    const cardSwiper = new Swiper("#card-swiper", {
      slidesPerView: 1,
      spaceBetween: 30,
      loop: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 3,
          spaceBetween: 30,
        },
      },
      pagination: {
        el: "#card-swiper .swiper-pagination",
        clickable: true,
      },
    });
  }

  // 进度条设置

  const storiesSwiper = new Swiper("#stories-swiper", {
    slidesPerView: 1,
    centeredSlides: true,
    spaceBetween: 30,
    loop: true,
    pagination: {
      el: "#stories-swiper .swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      1280: {
        slidesPerView: 1.9,
        spaceBetween: 30,
      },
    },
  });
});
