* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// main {
//   background-color: #f5f8ff;
//   color: #000;

//   h1,
//   h2,
//   h3,
//   h4,
//   h5,
//   h6,
//   p,
//   div,
//   span {
//     margin-bottom: 0;
//   }

// h1,
// h2,
// h3 {
//   text-align: center;
// }
// h2 {
//   font-size: 2.25rem;
//   font-weight: 800;
// }

// .opacity-7 {
//   opacity: 0.7;
// }

// .blue-text {
//   color: #0055fb;
// }

// .btn-wrapper {
//   display: flex;

//   justify-content: center;
//   gap: 1rem;
//   @media (max-width: 768px) {
//     flex-direction: column;
//     gap: 8px;
//   }
//   .btn {
//     margin: 0;
//     border-radius: 8px;
//     text-transform: capitalize;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     min-width: 228px;
//     @media (max-width: 768px) {
//       display: block;
//       vertical-align: baseline;
//     }
//   }
// }
// .btn-download {
//   background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);
//   border: none;
//   color: #fff;
//   background-color: #0458ff;

//   &:hover {
//     color: #fff;
//     background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),
//       linear-gradient(0deg, #0055fb, #0055fb);
//     background-color: #0458ff;
//   }
// }
main {
  background-color: #f5f8ff;
}
main h1,
main h2,
main h3,
main h4,
main h5,
main h6,
main p,
main div {
  margin-bottom: 0;
  color: #000;
  font-family: "Mulish", sans-serif;
}

main h1,
main h2 {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
}

@media (max-width: 768px) {
  main h1,
  main h2 {
    font-size: 24px;
    text-align: center;
  }
}

@media (max-width: 576px) {
  main .display-3 {
    font-size: 2.5rem;
  }
}

main .opacity-7 {
  opacity: 0.7;
}

main .text-blue {
  color: #2a80ff;
}

main .btn-wrapper {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

@media (max-width: 768px) {
  main .btn-wrapper {
    flex-direction: column;
    gap: 8px;
  }
}

main .btn {
  margin: 0;
  border-radius: 12px;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  justify-content: center;
  // .wsc-icon {
  //   display: flex;
  //   align-items: center;
  // }
}

@media (min-width: 992px) {
  main .btn {
    height: 51px;
  }
  main .btn.btn-lg {
    height: 4rem;
  }
}
// @media (max-width: 576px) {
//   main .btn {
//     height: 40px;
//   }
// }

@media (max-width: 768px) {
  main .btn {
    display: block;
    vertical-align: baseline;
  }
}

main .gradient-text {
  background: linear-gradient(138.99deg, #0087ff 8.45%, #6ad6ff 89.48%);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}

main .btn-download {
  background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);
  border: none;
  color: #fff;
  background-color: #0458ff;
}

main .btn-download:hover {
  color: #fff;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),
    linear-gradient(0deg, #0055fb, #0055fb);
  background-color: #0458ff;
}

main .part-banner {
  position: relative;
  .small-title {
    color: #13171a;
    font-size: 1.875rem;
    margin-bottom: 1rem;
    font-weight: 700;
    line-height: 90%;
  }
}

@media (max-width: 768px) {
  main .part-banner {
    background: linear-gradient(180deg, #c0eeff 0%, #f8fbff 96.46%);
  }
}

main .part-banner .banner-left-download {
  z-index: 10;
  position: absolute;
  height: 100%;
  top: 0;
  left: 0;
  width: 33%;
  @media (max-width: 768px) {
    display: none;
  }
}
main .part-banner .banner-right-download {
  z-index: 10;
  position: absolute;
  height: 100%;
  top: 0;
  right: 0;
  width: 33%;
  @media (max-width: 768px) {
    display: none;
  }
}

main .part-banner .video-wrapper {
  line-height: 0;
  font-size: 0;
}

main .part-banner .video-wrapper video {
  height: 100%;
  width: 100%;
  object-fit: cover;
  min-height: 533px;
}

@media (max-width: 768px) {
  main .part-banner .video-wrapper {
    display: none;
  }
}

main .part-banner .part-banner-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

@media (max-width: 768px) {
  main .part-banner .part-banner-content {
    position: relative;
    padding: 3rem 0;
    text-align: center;
  }
}

main .part-banner .part-banner-content h1 {
  color: #13171a;
  line-height: 110%;
}

@media (max-width: 576px) {
  main .part-banner .part-banner-content h1 {
    font-size: 26px;
  }
}

main .part-banner .part-banner-content h1 span {
  color: transparent;
  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(0deg, #0055fb, #0055fb);
  background-clip: text;
  -webkit-background-clip: text;
}

main .part-banner .part-banner-content h2 {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 100%;
  color: #13171a;
}

@media (max-width: 576px) {
  main .part-banner .part-banner-content h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }
}

main .part-banner .part-banner-content .btn {
  min-width: 289px;
}

@media (max-width: 768px) {
  main .part-banner .part-banner-content .btn {
    min-width: unset;
  }
}

main .part-banner .part-banner-content .logo-list {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 14.4px;

  .split-line {
    position: relative;
    height: 16px;
    width: 2px;
    top: 70%;
    border-radius: 1.5px;
    background-color: rgba($color: #000000, $alpha: 0.7);
  }
}

@media (max-width: 768px) {
  main .part-banner .part-banner-content .logo-list {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  main .part-banner .part-banner-content .logo-list .logo-img {
    flex: 1;
    max-height: 24px;
    object-fit: contain;
  }
}

main .part-files {
  background-color: #fff;
}

main .part-files .file-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 1rem;
  overflow: hidden;
  .btn-outline-action {
    border: 1px solid #2e8eff;
    color: #2e8eff;
    &:hover,
    &:focus {
      background-color: #2e8eff;
      color: #fff;
    }
  }
}

main .part-files .file-box .file-box-content {
  background-color: #f9f9f9;
  padding: 1.5rem 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  p {
    font-size: 0.875rem;
    color: #5f5f5f;
    .content-title {
      font-weight: 700;
      color: #000;
    }
  }
}

@media (max-width: 576px) {
  main .part-files .file-box .file-box-content {
    padding: 8px;
  }
}

main .part-files .file-box .file-box-content .box-title {
  font-weight: 700;
  font-size: 1.25rem;
  color: #000;
  text-decoration: none;
  display: inline-block;
}

main .part-files .file-box .file-box-content .box-title:hover {
  text-decoration: underline;
}

@media (max-width: 576px) {
  main .part-files .col-6 {
    padding-right: 8px;
    padding-left: 8px;
  }

  main .part-files .col-6:nth-child(odd) {
    padding-right: 4px;
  }

  main .part-files .col-6:nth-child(even) {
    padding-left: 4px;
  }
}

.part-step {
  background: url(https://images.wondershare.com/repairit/images2025/PDF-repair/step-bg.svg) no-repeat center center / cover;
  .nav {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    height: 100%;
    @media (max-width: 768px) {
      gap: 1rem;
    }
  }

  .nav .nav-item {
    flex: 1;
    &.active .step-item {
      position: relative;
      box-shadow: 0px 8px 12px 0px #7cc5dc3d;
      border-color: transparent;
      border: unset;
      &::after {
        content: "";
        position: absolute;
        inset: 0;
        border-radius: 1rem;
        padding: 1px;
        background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
        mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
        mask-composite: exclude;
        pointer-events: none;
      }

      .step-item-number {
        font-size: 2.625rem;
        font-weight: 700;
      }
      .right-content {
        .title {
          font-size: 1.25rem;
          font-weight: 700;
        }
        .detail {
          display: block;
        }
      }
    }
  }

  .nav .nav-item .step-item {
    height: 100%;
    border-radius: 1rem;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 1.25rem;
    padding: 1.5rem;
    color: #000;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #bce0fe;
    .step-item-number {
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
    }
    .right-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      .detail {
        display: none;
        color: #787878;
        font-size: 14px;
      }
    }
  }

  .feature-list {
    display: flex;
    justify-content: center;
    gap: 1rem;
    @media (max-width: 768px) {
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
    }
    .feature-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1.5rem 1rem;
      gap: 4px;
      @media (max-width: 768px) {
        padding: 0 1rem;
        img {
          width: 36px;
        }
      }
      .feature-item-detail {
        font-size: 1.25rem;
        font-weight: 500;
        color: #444444;
      }
    }
  }
  .note-box {
    background-color: #ecfaff;
    border-radius: 1.375rem;
    padding: 1.125rem 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.9375rem;
    .left-icon {
      flex-shrink: 0;
    }
    .right-content {
      .content-detail {
        font-size: 0.875rem;
        font-weight: 500;
        color: #636363;
      }
    }
  }
}

main .part-highlights {
  background-color: #f5f8ff;
  .btn-white {
    color: #0c7dfa;
  }
  .blue-cricle1 {
    position: absolute;
    left: 41%;
    bottom: 42%;
    width: 23%;
    animation: icon-rotate 3s linear infinite;
  }
  .blue-cricle2 {
    position: absolute;
    left: 40%;
    bottom: 42%;
    width: 21%;
    animation: icon-rotate 3s linear infinite;
  }
  .win-icon-link {
    position: absolute;
    left: 56%;
    bottom: 55%;
    width: 27%;
  }
  .mac-icon-link {
    position: absolute;
    left: 22%;
    bottom: 55%;
    width: 27%;
  }
}

main .part-highlights .assetsSwiper-box {
  position: relative;
  margin-bottom: 5rem;
}

@media (min-width: 1280px) {
  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-wrapper {
    gap: 16px;
    justify-content: space-between;
    @media (max-width: 1600px) {
      gap: 8px;
    }
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide {
    width: 7.1%;
    display: block;
    height: auto;
    overflow: hidden;
    border-radius: 1rem;
    position: relative;
    transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
    min-height: 430px;
    @media (max-width: 1600px) {
      min-height: 400px;
    }
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style {
    padding: 1.875rem 1.5rem;
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 1.5rem;
    color: #fff;
    background: linear-gradient(269.24deg, #6fb3ff -4.48%, #1989ff 54.28%);
    overflow: hidden;
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .right-icon {
    position: absolute;
    right: 2.1rem;
    top: 1.875rem;
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .box-title {
    color: #fff;
    bottom: 1.125rem;
    left: 2.125rem;
    position: absolute;
    font-size: 1.25rem;
    writing-mode: sideways-lr;
    height: 525px;
    z-index: 3;
    width: 0%;
    transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content {
    opacity: 0;
    height: calc(100% - 3.875rem);
    width: 100%;
    display: flex;
    margin-top: 3.875rem;
    padding-top: 2.25rem;
    border-top: 1px solid rgba($color: #f2f2f2, $alpha: 0.3);
    justify-content: space-between;
    // gap: 3rem;
    min-width: 774px;
    transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
  }
}

@media (min-width: 1280px) and (max-width: 1600px) {
  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content {
    min-width: 634px;
  }
}

@media (min-width: 1280px) {
  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .left-img-wrapper {
    flex: 0 0 45%;
    margin-top: -2.25rem;
    margin-left: -1.5rem;
    margin-bottom: -1.875rem;
    @media (max-width: 1600px) {
      flex: 0 0 51%;
    }
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .right-detial-wrapper {
    flex: 1;
    padding-top: 1.5rem;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    color: #fff;
    @media (max-width: 1600px) {
      padding-top: unset;
    }
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active {
    width: 58.3%;
    opacity: 1;
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active .box-style .box-title {
    transform: rotate(90deg);
    transform-origin: bottom left;
    bottom: 95%;
    font-size: 2rem;
    font-weight: 600;
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide.active .box-style .card-content {
    opacity: 1;
  }

  @keyframes fadeIn {
    from {
      visibility: hidden;
    }

    to {
      visibility: visible;
    }
  }
}

@media (max-width: 1280px) {
  main .part-highlights .assetsSwiper-box .box-style {
    height: 100%;
    background: #1888fe;

    border-radius: 1.5rem;
    overflow: hidden;
    padding: 1.5rem;
  }

  main .part-highlights .assetsSwiper-box .box-style .top-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #f2f2f2;
  }

  main .part-highlights .assetsSwiper-box .box-style .top-content .right-icon {
    width: 2.5rem;
  }

  main .part-highlights .assetsSwiper-box .box-style .top-content .box-title {
    font-size: 2rem;
    font-weight: 600;
    color: #fff;
  }
}

@media (max-width: 1280px) and (max-width: 768px) {
  main .part-highlights .assetsSwiper-box .box-style .top-content .box-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 1280px) {
  main .part-highlights .assetsSwiper-box .box-style .card-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 1rem;
  }
}

@media (max-width: 1280px) and (max-width: 768px) {
  main .part-highlights .assetsSwiper-box .box-style .card-content {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 1280px) {
  main .part-highlights .assetsSwiper-box .box-style .card-content .left-img-wrapper {
    flex: 0 0 35%;
    max-width: 240px;
  }
}

@media (max-width: 1280px) and (max-width: 768px) {
  main .part-highlights .assetsSwiper-box .box-style .card-content .left-img-wrapper {
    order: 2;
  }
}

@media (max-width: 1280px) {
  main .part-highlights .assetsSwiper-box .box-style .card-content .right-detial-wrapper {
    flex: 1;
    padding-top: 1.5rem;
    display: flex;
    justify-content: space-between;

    flex-direction: column;
  }
}

@media (max-width: 1280px) and (max-width: 768px) {
  main .part-highlights .assetsSwiper-box .box-style .card-content .right-detial-wrapper {
    order: 1;
  }
}

@media (max-width: 1280px) {
  main .part-highlights .assetsSwiper-box .assetsSwiper .swiper-slide {
    height: auto;
  }

  main .part-highlights .assetsSwiper-box .assetsSwiper .rounded-16 {
    border-radius: 8px;
  }
}

@keyframes move {
  0% {
    transform: translateX(5px);
  }

  50% {
    transform: translateX(-3px);
  }

  100% {
    transform: translateX(5px);
  }
}

main .part-customer {
  background-color: #f5f8ff;
}

main .part-customer .customer-wrapper {
  border-radius: 2rem;
  overflow: hidden;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
}

main .part-customer .customer-wrapper .customer-img {
  position: relative;
}

main .part-customer .customer-wrapper .customer-img .customer-info-list {
  position: absolute;
  top: 20px;
  left: 40px;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #fff;
  font-weight: 600;
  gap: 24px;
}

@media (max-width: 768px) {
  main .part-customer .customer-wrapper .customer-img .customer-info-list {
    display: none;
  }
}

main .part-customer .customer-wrapper .customer-img .customer-info-list.right {
  right: 32px;
  left: unset;
}

main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-title,
main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-profession,
main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-age {
  position: relative;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.21);
  border-radius: 0 3px 3px 0;
  padding-right: 6px;
}

main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-title::before,
main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-profession::before,
main .part-customer .customer-wrapper .customer-img .customer-info-list .customer-age::before {
  content: "";
  position: absolute;
  height: 100%;
  aspect-ratio: 22 / 31;
  left: 0;
  top: 0;
  transform: translateX(-97%);
  background: url(https://images.wondershare.com/repairit/images2025/PPT-repair/left-tip.png) no-repeat center center/contain;
}

main .part-customer .customer-wrapper .customer-detail {
  flex: 1;
  display: flex;
  gap: 0.5rem;
}

@media (max-width: 992px) {
  main .part-customer .customer-wrapper .customer-detail {
    flex-direction: column;
  }
}

main .part-customer .customer-wrapper .customer-detail .problem-wrapper {
  flex: 1 1 41.2%;
  padding: 1.875rem 1.5rem;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 12px;
}

@media (max-width: 992px) {
  main .part-customer .customer-wrapper .customer-detail .problem-wrapper {
    padding: 1rem;
    padding-bottom: 0;
  }
}

main .part-customer .customer-wrapper .customer-detail .problem-wrapper .left-icon {
  width: 4.5rem;
  flex-shrink: 0;
}

@media (max-width: 576px) {
  main .part-customer .customer-wrapper .customer-detail .problem-wrapper .left-icon {
    width: 2.5rem;
  }
}

main .part-customer .customer-wrapper .customer-detail .problem-wrapper .right-content .title {
  font-weight: 800;
  font-size: 1.5rem;
  color: #000;
  margin-bottom: 12px;
}

main .part-customer .customer-wrapper .customer-detail .problem-wrapper .right-content .detail {
  font-size: 1.125rem;
  color: #3c3c3c;
}

main .part-customer .customer-wrapper .customer-detail .customer-detail-dividing {
  border-right: 1px dashed rgba(0, 0, 0, 0.07);
}

main .part-customer .customer-wrapper .customer-detail .how-wrapper {
  flex: 1 1 58.8%;
  padding: 1.875rem 1.5rem;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 12px;
}

@media (max-width: 992px) {
  main .part-customer .customer-wrapper .customer-detail .how-wrapper {
    padding: 1rem;
    padding-top: 0;
  }
}

@keyframes icon-rotate {
  0% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

main .part-customer .customer-wrapper .customer-detail .how-wrapper .left-icon {
  text-decoration: none;
  animation: icon-rotate 3s linear infinite;
  flex-shrink: 0;
  width: 4.5rem;
}

@media (max-width: 576px) {
  main .part-customer .customer-wrapper .customer-detail .how-wrapper .left-icon {
    width: 2.5rem;
  }
}

main .part-customer .customer-wrapper .customer-detail .how-wrapper .right-content .title {
  font-weight: 800;
  font-size: 1.5rem;
  color: #000;
  margin-bottom: 12px;
}

main .part-customer .customer-wrapper .customer-detail .how-wrapper .right-content .detail {
  font-size: 1.125rem;
  color: #3c3c3c;
}

main .part-customer .left-btn,
main .part-customer .right-btn {
  background-color: #c0c0c0;
  width: 2.25rem;
  aspect-ratio: 1 / 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
  position: absolute;
  top: 36%;
}

@media (max-width: 576px) {
  main .part-customer .left-btn,
  main .part-customer .right-btn {
    display: none;
  }
}

main .part-customer .left-btn:hover,
main .part-customer .right-btn:hover {
  background-color: #006dff;
}

main .part-customer .right-btn {
  right: -3.25rem;
}

@media (max-width: 768px) {
  main .part-customer .right-btn {
    right: 1.55rem;
  }
}

main .part-customer .left-btn {
  left: -3.25rem;
}

@media (max-width: 768px) {
  main .part-customer .left-btn {
    left: 1.55rem;
  }
}

.part-faq {
  .accordion-item {
    padding: 2rem 0;
    border-bottom: 1px solid #e2e2e2;
    @media (max-width: 576px) {
      padding: 1.5rem 0;
    }
  }
  .open-icon {
    display: none;
    color: inherit;
  }
  .close-icon {
    display: block;
    color: inherit;
  }

  .accordion-item [aria-expanded="true"] .faq-title {
    font-weight: 700;
    color: #4d99ff;
  }

  .accordion-item [aria-expanded="true"] .open-icon {
    display: block;
  }
  .accordion-item [aria-expanded="true"] .close-icon {
    display: none;
  }

  .accordion-item .faq-title {
    display: flex;
    align-items: center;
    justify-content: left;
    gap: 8px;
    flex-shrink: 0;
    max-width: 95%;
    font-weight: 600;
    font-size: 1.5rem;
    @media (max-width: 576px) {
      font-size: 1.25rem;
      max-width: 90%;
    }
    svg {
      flex-shrink: 0;
    }
    .title-desc {
      color: inherit;
    }
  }
}

@media (min-width: 992px) {
  main .part-tip #swiper-tips .swiper-wrapper {
    gap: 1.875rem;
    flex-wrap: wrap;
  }

  main .part-tip #swiper-tips .swiper-wrapper .swiper-slide {
    flex: 1 1 calc(33% - 1.875rem);
  }
}

main .part-tip .tip-item {
  border-radius: 2rem;
  position: relative;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 3rem 2rem;
  color: #000;
  z-index: 3;
  transition: all 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background-color: #fff;
}

main .part-tip .tip-item:hover {
  box-shadow: 0px 0px 12px 0px #00d1ff4d;
}

main .part-tip .tip-item:hover::after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 2rem;
  padding: 2px;
  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(127.35deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 36.09%);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-image: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask-composite: exclude;
}

main .part-tip .tip-item:hover .text-detail {
  top: 2px;
}

main .part-tip .tip-item .tip-icon {
  height: 6rem;
  width: 6rem;
}

main .part-tip .tip-item .text-detail {
  position: absolute;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  padding: 0rem 2rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  z-index: 2;
  border-radius: 2rem;
  left: 2px;
  top: 100%;
  overflow: hidden;
  background-image: url(https://images.wondershare.com/repairit/images2025/PPT-repair/tip-card-bg.svg) !important;
  transition: all 0.3s;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

main .part-links .part-links-line {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

main .part-links .line-border {
  border-right: 1px solid rgba(0, 0, 0, 0.3);
  border-left: 1px solid rgba(0, 0, 0, 0.3);
}

@media (max-width: 1280px) {
  main .part-links .line-border {
    border-right: unset;
  }
}

@media (max-width: 768px) {
  main .part-links .line-border {
    border-left: unset;
  }
}

main .part-links .text-link {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.7);
  margin-top: 1.5rem;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

main .part-links .text-link:hover {
  color: #0055fb;
}

main .part-links .part-links-videos {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

main .part-links .part-links-videos .video-wrapper {
  border-radius: 0.75rem;
}

@media (max-width: 1280px) {
  main .part-links .part-links-videos {
    flex-direction: row;
    padding-top: 2rem;
  }
}

@media (max-width: 576px) {
  main .part-links .part-links-videos {
    display: block;
  }
}

main .part-links .text-line4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes changeWidth {
  0% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

main .part-feature .intelligence-content {
  position: absolute;
  top: 0;
  left: 0%;
  z-index: 2;
}

main .part-feature .intelligence-item {
  border-radius: 1.5rem;
  overflow: hidden;
  background-color: #fff;
  color: #000;
}

main .part-feature .intelligence-item .compare-before {
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0;
  top: 0;
  background-size: auto 100%;
  background-repeat: no-repeat;
  z-index: 2;
  animation: changeWidth 6s linear infinite;
}

main .part-feature .intelligence-item .compare-before::after {
  content: "";
  width: 2px;
  height: 100%;
  background: #fff;
  position: absolute;
  right: 0;
  top: 0;
}

main .part-feature .intelligence-item .compare-before::before {
  content: "";
  background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 5rem;
  height: 3rem;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  z-index: 3;
}

@media (max-width: 768px) {
  main .part-feature .intelligence-item .compare-before::before {
    width: 3rem;
    height: 2rem;
  }
}

main .part-feature .intelligence-item .compare-before.compare-before-1 {
  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/word-file-repair-before.jpg);
}

main .part-feature .intelligence-item .compare-before.compare-before-2 {
  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/excel-file-repair-before.jpg);
  animation: changeWidth 8s linear infinite;
}

main .part-feature .intelligence-item .compare-before.compare-before-3 {
  background-image: url(https://images.wondershare.com/repairit/images2025/PDF-repair/powerpoint-file-repair-before.jpg);
  animation: changeWidth 7s linear infinite;
}

main .part-feature .intelligence-item .item-link {
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 768px) {
  main .part-feature .intelligence-item .item-link {
    margin-bottom: 0.5rem;
  }
}

main .part-feature .intelligence-item .item-link .normal-arrow {
  display: inline;
}

@media (max-width: 768px) {
  main .part-feature .intelligence-item .item-link .normal-arrow,
  .active-arrow {
    height: 2.5rem;
    width: 2.5rem;
  }
}

main .part-feature .intelligence-item .item-link .active-arrow {
  display: none;
}

main .part-feature .intelligence-item .item-link:hover {
  color: #0458ff;
}

main .part-feature .intelligence-item .item-link:hover .normal-arrow {
  display: none;
}

main .part-feature .intelligence-item .item-link:hover .active-arrow {
  display: inline;
}

main .part-footer {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

main .part-footer .btn-download {
  padding-top: 18.5px;
  padding-bottom: 18.5px;
}

@media (max-width: 992px) {
  main .part-footer .btn-download {
    padding-top: 15.2px;
    padding-bottom: 15.2px;
  }
}

main .part-footer .part-footer-logo {
  height: 4rem;
  width: 14.5rem;
  margin: 0 auto;
}

@media (max-width: 576px) {
  main .part-footer .display-2 {
    font-size: 2.25rem;
  }

  main .part-footer a {
    display: block;
  }

  main .part-footer .btn-outline-action {
    background-color: #fff;
    vertical-align: text-bottom;
    &:hover {
      color: #fff;
      background-color: #006dff;
      border-color: #006dff;
    }
  }
}
